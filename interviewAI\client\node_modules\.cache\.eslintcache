[{"C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\index.js": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\App.jsx": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\admin-home\\index.jsx": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\home\\index.jsx": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\login\\index.jsx": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\signup\\index.jsx": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\SpeechToText.jsx": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\InterviewForm.jsx": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\InterviewSession.jsx": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\Header.jsx": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\Footer.jsx": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\VoiceTranscriber.jsx": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\utils\\Apicalls.jsx": "13", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\Spinner\\index.jsx": "14", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\utils\\env.js": "15", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\MeetingTranscriber.jsx": "16", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\interview\\index.jsx": "17", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\InterviewAssistant.jsx": "18", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\AutomatedInterviewPage.jsx": "19", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\AutomatedInterview.jsx": "20", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\utils\\speechToText.js": "21", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\SimpleTranscriber.jsx": "22", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\live-transcription\\LiveTranscription.jsx": "23", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\DiarizedInterviewPage.jsx": "24", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\DiarizedTranscription.jsx": "25", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\live-transcription\\AutomaticDiarization.jsx": "26", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\live-transcription\\GoogleSpeechDiarization.jsx": "27", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\live-transcription\\DeepgramTranscriptionPage.jsx": "28", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\DeepgramLiveTranscript.jsx": "29"}, {"size": 250, "mtime": 1747115239809, "results": "30", "hashOfConfig": "31"}, {"size": 2072, "mtime": 1747880138412, "results": "32", "hashOfConfig": "31"}, {"size": 4392, "mtime": 1747880237364, "results": "33", "hashOfConfig": "31"}, {"size": 12893, "mtime": 1747848945319, "results": "34", "hashOfConfig": "31"}, {"size": 4566, "mtime": 1747835388111, "results": "35", "hashOfConfig": "31"}, {"size": 8143, "mtime": 1747551471594, "results": "36", "hashOfConfig": "31"}, {"size": 16828, "mtime": 1747848233168, "results": "37", "hashOfConfig": "31"}, {"size": 6217, "mtime": 1747159017468, "results": "38", "hashOfConfig": "31"}, {"size": 3839, "mtime": 1747145275285, "results": "39", "hashOfConfig": "31"}, {"size": 3799, "mtime": 1747835910899, "results": "40", "hashOfConfig": "31"}, {"size": 2459, "mtime": 1747460004752, "results": "41", "hashOfConfig": "31"}, {"size": 29494, "mtime": 1747719906276, "results": "42", "hashOfConfig": "31"}, {"size": 1616, "mtime": 1747373023486, "results": "43", "hashOfConfig": "31"}, {"size": 155, "mtime": 1747501294234, "results": "44", "hashOfConfig": "31"}, {"size": 1273, "mtime": 1747880050596, "results": "45", "hashOfConfig": "31"}, {"size": 18613, "mtime": 1747677434844, "results": "46", "hashOfConfig": "31"}, {"size": 267, "mtime": 1747740181362, "results": "47", "hashOfConfig": "31"}, {"size": 17325, "mtime": 1747802122002, "results": "48", "hashOfConfig": "31"}, {"size": 1778, "mtime": 1747766861224, "results": "49", "hashOfConfig": "31"}, {"size": 32032, "mtime": 1747767759545, "results": "50", "hashOfConfig": "31"}, {"size": 5683, "mtime": 1747755722238, "results": "51", "hashOfConfig": "31"}, {"size": 14104, "mtime": 1747767778854, "results": "52", "hashOfConfig": "31"}, {"size": 5612, "mtime": 1747807474926, "results": "53", "hashOfConfig": "31"}, {"size": 6697, "mtime": 1747848125547, "results": "54", "hashOfConfig": "31"}, {"size": 11266, "mtime": 1747826555846, "results": "55", "hashOfConfig": "31"}, {"size": 10246, "mtime": 1747810926727, "results": "56", "hashOfConfig": "31"}, {"size": 27860, "mtime": 1747815069783, "results": "57", "hashOfConfig": "31"}, {"size": 4110, "mtime": 1747880079167, "results": "58", "hashOfConfig": "31"}, {"size": 20804, "mtime": 1747879944246, "results": "59", "hashOfConfig": "31"}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "32xn06", {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\App.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\admin-home\\index.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\home\\index.jsx", [], ["147", "148"], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\login\\index.jsx", [], ["149"], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\signup\\index.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\SpeechToText.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\InterviewForm.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\InterviewSession.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\Header.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\Footer.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\VoiceTranscriber.jsx", [], ["150"], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\utils\\Apicalls.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\Spinner\\index.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\utils\\env.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\MeetingTranscriber.jsx", [], ["151"], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\interview\\index.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\InterviewAssistant.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\AutomatedInterviewPage.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\AutomatedInterview.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\utils\\speechToText.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\SimpleTranscriber.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\live-transcription\\LiveTranscription.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\DiarizedInterviewPage.jsx", [], ["152"], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\DiarizedTranscription.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\live-transcription\\AutomaticDiarization.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\live-transcription\\GoogleSpeechDiarization.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\live-transcription\\DeepgramTranscriptionPage.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\DeepgramLiveTranscript.jsx", ["153", "154", "155", "156"], [], {"ruleId": "157", "severity": 1, "message": "158", "line": 12, "column": 10, "nodeType": "159", "messageId": "160", "endLine": 12, "endColumn": 18, "suppressions": "161"}, {"ruleId": "157", "severity": 1, "message": "162", "line": 14, "column": 9, "nodeType": "159", "messageId": "160", "endLine": 14, "endColumn": 17, "suppressions": "163"}, {"ruleId": "157", "severity": 1, "message": "164", "line": 3, "column": 23, "nodeType": "159", "messageId": "160", "endLine": 3, "endColumn": 27, "suppressions": "165"}, {"ruleId": "166", "severity": 1, "message": "167", "line": 604, "column": 31, "nodeType": "168", "messageId": "169", "endLine": 608, "endColumn": 20, "suppressions": "170"}, {"ruleId": "166", "severity": 1, "message": "167", "line": 412, "column": 29, "nodeType": "168", "messageId": "169", "endLine": 416, "endColumn": 18, "suppressions": "171"}, {"ruleId": "157", "severity": 1, "message": "172", "line": 8, "column": 10, "nodeType": "159", "messageId": "160", "endLine": 8, "endColumn": 28, "suppressions": "173"}, {"ruleId": "174", "severity": 1, "message": "175", "line": 88, "column": 6, "nodeType": "176", "endLine": 88, "endColumn": 38, "suggestions": "177"}, {"ruleId": "174", "severity": 1, "message": "175", "line": 128, "column": 6, "nodeType": "176", "endLine": 128, "endColumn": 38, "suggestions": "178"}, {"ruleId": "157", "severity": 1, "message": "179", "line": 211, "column": 20, "nodeType": "159", "messageId": "160", "endLine": 211, "endColumn": 26}, {"ruleId": "174", "severity": 1, "message": "175", "line": 299, "column": 6, "nodeType": "176", "endLine": 299, "endColumn": 44, "suggestions": "180"}, "no-unused-vars", "'userData' is assigned a value but never used.", "Identifier", "unusedVar", ["181"], "'navigate' is assigned a value but never used.", ["182"], "'Link' is defined but never used.", ["183"], "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'localResult'.", "ArrowFunctionExpression", "unsafeRefs", ["184"], ["185"], "'transcriptSegments' is assigned a value but never used.", ["186"], "react-hooks/exhaustive-deps", "React Hook useCallback has a missing dependency: 'stopRecording'. Either include it or remove the dependency array.", "ArrayExpression", ["187"], ["188"], "'buffer' is assigned a value but never used.", ["189"], {"kind": "190", "justification": "191"}, {"kind": "190", "justification": "191"}, {"kind": "190", "justification": "191"}, {"kind": "190", "justification": "191"}, {"kind": "190", "justification": "191"}, {"kind": "190", "justification": "191"}, {"desc": "192", "fix": "193"}, {"desc": "192", "fix": "194"}, {"desc": "195", "fix": "196"}, "directive", "", "Update the dependencies array to be: [stopRecording]", {"range": "197", "text": "198"}, {"range": "199", "text": "198"}, "Update the dependencies array to be: [sessionExpired, stopRecording, verifyDeepgramApiKey]", {"range": "200", "text": "201"}, [3417, 3449], "[stopRecording]", [4492, 4524], [9805, 9843], "[sessionExpired, stopRecording, verifyD<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>]"]