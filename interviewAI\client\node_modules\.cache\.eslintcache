[{"C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\index.js": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\App.jsx": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\admin-home\\index.jsx": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\home\\index.jsx": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\login\\index.jsx": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\signup\\index.jsx": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\SpeechToText.jsx": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\InterviewForm.jsx": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\InterviewSession.jsx": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\Header.jsx": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\Footer.jsx": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\VoiceTranscriber.jsx": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\utils\\Apicalls.jsx": "13", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\Spinner\\index.jsx": "14", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\utils\\env.js": "15", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\MeetingTranscriber.jsx": "16", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\interview\\index.jsx": "17", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\InterviewAssistant.jsx": "18", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\AutomatedInterviewPage.jsx": "19", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\AutomatedInterview.jsx": "20", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\utils\\speechToText.js": "21", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\SimpleTranscriber.jsx": "22"}, {"size": 250, "mtime": 1747115239809, "results": "23", "hashOfConfig": "24"}, {"size": 1167, "mtime": 1747746115485, "results": "25", "hashOfConfig": "24"}, {"size": 3982, "mtime": 1747674646130, "results": "26", "hashOfConfig": "24"}, {"size": 11175, "mtime": 1747752917937, "results": "27", "hashOfConfig": "24"}, {"size": 3887, "mtime": 1747373023484, "results": "28", "hashOfConfig": "24"}, {"size": 8143, "mtime": 1747551471594, "results": "29", "hashOfConfig": "24"}, {"size": 12043, "mtime": 1747727811246, "results": "30", "hashOfConfig": "24"}, {"size": 6217, "mtime": 1747159017468, "results": "31", "hashOfConfig": "24"}, {"size": 3839, "mtime": 1747145275285, "results": "32", "hashOfConfig": "24"}, {"size": 2825, "mtime": 1747746158935, "results": "33", "hashOfConfig": "24"}, {"size": 2459, "mtime": 1747460004752, "results": "34", "hashOfConfig": "24"}, {"size": 29494, "mtime": 1747719906276, "results": "35", "hashOfConfig": "24"}, {"size": 1616, "mtime": 1747373023486, "results": "36", "hashOfConfig": "24"}, {"size": 155, "mtime": 1747501294234, "results": "37", "hashOfConfig": "24"}, {"size": 689, "mtime": 1747754616388, "results": "38", "hashOfConfig": "24"}, {"size": 18613, "mtime": 1747677434844, "results": "39", "hashOfConfig": "24"}, {"size": 267, "mtime": 1747740181362, "results": "40", "hashOfConfig": "24"}, {"size": 16879, "mtime": 1747743820546, "results": "41", "hashOfConfig": "24"}, {"size": 1778, "mtime": 1747766861224, "results": "42", "hashOfConfig": "24"}, {"size": 26131, "mtime": 1747766775062, "results": "43", "hashOfConfig": "24"}, {"size": 5683, "mtime": 1747755722238, "results": "44", "hashOfConfig": "24"}, {"size": 4033, "mtime": 1747766806029, "results": "45", "hashOfConfig": "24"}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "32xn06", {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\App.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\admin-home\\index.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\home\\index.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\login\\index.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\signup\\index.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\SpeechToText.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\InterviewForm.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\InterviewSession.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\Header.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\Footer.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\VoiceTranscriber.jsx", [], ["112"], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\utils\\Apicalls.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\Spinner\\index.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\utils\\env.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\MeetingTranscriber.jsx", [], ["113"], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\interview\\index.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\InterviewAssistant.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\AutomatedInterviewPage.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\AutomatedInterview.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\utils\\speechToText.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\SimpleTranscriber.jsx", [], [], {"ruleId": "114", "severity": 1, "message": "115", "line": 604, "column": 31, "nodeType": "116", "messageId": "117", "endLine": 608, "endColumn": 20, "suppressions": "118"}, {"ruleId": "114", "severity": 1, "message": "115", "line": 412, "column": 29, "nodeType": "116", "messageId": "117", "endLine": 416, "endColumn": 18, "suppressions": "119"}, "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'localResult'.", "ArrowFunctionExpression", "unsafeRefs", ["120"], ["121"], {"kind": "122", "justification": "123"}, {"kind": "122", "justification": "123"}, "directive", ""]