[{"C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\index.js": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\App.jsx": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\admin-home\\index.jsx": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\home\\index.jsx": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\login\\index.jsx": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\signup\\index.jsx": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\SpeechToText.jsx": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\InterviewForm.jsx": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\InterviewSession.jsx": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\Header.jsx": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\Footer.jsx": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\VoiceTranscriber.jsx": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\utils\\Apicalls.jsx": "13", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\Spinner\\index.jsx": "14", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\utils\\env.js": "15", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\MeetingTranscriber.jsx": "16", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\interview\\index.jsx": "17", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\InterviewAssistant.jsx": "18", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\AutomatedInterviewPage.jsx": "19", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\AutomatedInterview.jsx": "20", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\utils\\speechToText.js": "21", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\SimpleTranscriber.jsx": "22", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\live-transcription\\LiveTranscription.jsx": "23", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\DiarizedInterviewPage.jsx": "24", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\DiarizedTranscription.jsx": "25", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\live-transcription\\AutomaticDiarization.jsx": "26", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\live-transcription\\GoogleSpeechDiarization.jsx": "27", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\live-transcription\\DeepgramTranscriptionPage.jsx": "28", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\DeepgramLiveTranscript.jsx": "29"}, {"size": 250, "mtime": 1747115239809, "results": "30", "hashOfConfig": "31"}, {"size": 2072, "mtime": 1747880138412, "results": "32", "hashOfConfig": "31"}, {"size": 4392, "mtime": 1747880237364, "results": "33", "hashOfConfig": "31"}, {"size": 12609, "mtime": 1747942010605, "results": "34", "hashOfConfig": "31"}, {"size": 4563, "mtime": 1747890662204, "results": "35", "hashOfConfig": "31"}, {"size": 8143, "mtime": 1747551471594, "results": "36", "hashOfConfig": "31"}, {"size": 16828, "mtime": 1747848233168, "results": "37", "hashOfConfig": "31"}, {"size": 6217, "mtime": 1747159017468, "results": "38", "hashOfConfig": "31"}, {"size": 3839, "mtime": 1747145275285, "results": "39", "hashOfConfig": "31"}, {"size": 4656, "mtime": 1747941989219, "results": "40", "hashOfConfig": "31"}, {"size": 2541, "mtime": 1747939171922, "results": "41", "hashOfConfig": "31"}, {"size": 29494, "mtime": 1747719906276, "results": "42", "hashOfConfig": "31"}, {"size": 1616, "mtime": 1747373023486, "results": "43", "hashOfConfig": "31"}, {"size": 155, "mtime": 1747501294234, "results": "44", "hashOfConfig": "31"}, {"size": 1273, "mtime": 1747880050596, "results": "45", "hashOfConfig": "31"}, {"size": 18613, "mtime": 1747677434844, "results": "46", "hashOfConfig": "31"}, {"size": 267, "mtime": 1747740181362, "results": "47", "hashOfConfig": "31"}, {"size": 17325, "mtime": 1747802122002, "results": "48", "hashOfConfig": "31"}, {"size": 1778, "mtime": 1747766861224, "results": "49", "hashOfConfig": "31"}, {"size": 32032, "mtime": 1747767759545, "results": "50", "hashOfConfig": "31"}, {"size": 5683, "mtime": 1747755722238, "results": "51", "hashOfConfig": "31"}, {"size": 14104, "mtime": 1747767778854, "results": "52", "hashOfConfig": "31"}, {"size": 5612, "mtime": 1747807474926, "results": "53", "hashOfConfig": "31"}, {"size": 6697, "mtime": 1747890086472, "results": "54", "hashOfConfig": "31"}, {"size": 11266, "mtime": 1747826555846, "results": "55", "hashOfConfig": "31"}, {"size": 10246, "mtime": 1747810926727, "results": "56", "hashOfConfig": "31"}, {"size": 27860, "mtime": 1747815069783, "results": "57", "hashOfConfig": "31"}, {"size": 4633, "mtime": 1747880957313, "results": "58", "hashOfConfig": "31"}, {"size": 15901, "mtime": 1747889145044, "results": "59", "hashOfConfig": "31"}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "32xn06", {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 19, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\App.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\admin-home\\index.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\home\\index.jsx", [], ["147", "148"], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\login\\index.jsx", [], ["149"], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\signup\\index.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\SpeechToText.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\InterviewForm.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\InterviewSession.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\Header.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\Footer.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\VoiceTranscriber.jsx", [], ["150"], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\utils\\Apicalls.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\Spinner\\index.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\utils\\env.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\MeetingTranscriber.jsx", [], ["151"], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\interview\\index.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\InterviewAssistant.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\AutomatedInterviewPage.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\AutomatedInterview.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\utils\\speechToText.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\SimpleTranscriber.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\live-transcription\\LiveTranscription.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\DiarizedInterviewPage.jsx", [], ["152"], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\DiarizedTranscription.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\live-transcription\\AutomaticDiarization.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\live-transcription\\GoogleSpeechDiarization.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\live-transcription\\DeepgramTranscriptionPage.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\DeepgramLiveTranscript.jsx", ["153", "154", "155", "156", "157", "158", "159", "160", "161", "162", "163", "164", "165", "166", "167", "168", "169", "170", "171"], [], {"ruleId": "172", "severity": 1, "message": "173", "line": 12, "column": 10, "nodeType": "174", "messageId": "175", "endLine": 12, "endColumn": 18, "suppressions": "176"}, {"ruleId": "172", "severity": 1, "message": "177", "line": 14, "column": 9, "nodeType": "174", "messageId": "175", "endLine": 14, "endColumn": 17, "suppressions": "178"}, {"ruleId": "172", "severity": 1, "message": "179", "line": 3, "column": 23, "nodeType": "174", "messageId": "175", "endLine": 3, "endColumn": 27, "suppressions": "180"}, {"ruleId": "181", "severity": 1, "message": "182", "line": 604, "column": 31, "nodeType": "183", "messageId": "184", "endLine": 608, "endColumn": 20, "suppressions": "185"}, {"ruleId": "181", "severity": 1, "message": "182", "line": 412, "column": 29, "nodeType": "183", "messageId": "184", "endLine": 416, "endColumn": 18, "suppressions": "186"}, {"ruleId": "172", "severity": 1, "message": "187", "line": 8, "column": 10, "nodeType": "174", "messageId": "175", "endLine": 8, "endColumn": 28, "suppressions": "188"}, {"ruleId": "172", "severity": 1, "message": "189", "line": 6, "column": 7, "nodeType": "174", "messageId": "175", "endLine": 6, "endColumn": 23}, {"ruleId": "172", "severity": 1, "message": "190", "line": 11, "column": 10, "nodeType": "174", "messageId": "175", "endLine": 11, "endColumn": 21}, {"ruleId": "172", "severity": 1, "message": "191", "line": 12, "column": 10, "nodeType": "174", "messageId": "175", "endLine": 12, "endColumn": 22}, {"ruleId": "172", "severity": 1, "message": "192", "line": 13, "column": 10, "nodeType": "174", "messageId": "175", "endLine": 13, "endColumn": 21}, {"ruleId": "172", "severity": 1, "message": "193", "line": 14, "column": 10, "nodeType": "174", "messageId": "175", "endLine": 14, "endColumn": 25}, {"ruleId": "172", "severity": 1, "message": "194", "line": 17, "column": 10, "nodeType": "174", "messageId": "175", "endLine": 17, "endColumn": 21}, {"ruleId": "172", "severity": 1, "message": "195", "line": 24, "column": 10, "nodeType": "174", "messageId": "175", "endLine": 24, "endColumn": 30}, {"ruleId": "172", "severity": 1, "message": "196", "line": 25, "column": 10, "nodeType": "174", "messageId": "175", "endLine": 25, "endColumn": 27}, {"ruleId": "172", "severity": 1, "message": "197", "line": 36, "column": 9, "nodeType": "174", "messageId": "175", "endLine": 36, "endColumn": 26}, {"ruleId": "172", "severity": 1, "message": "198", "line": 100, "column": 9, "nodeType": "174", "messageId": "175", "endLine": 100, "endColumn": 33}, {"ruleId": "172", "severity": 1, "message": "199", "line": 105, "column": 9, "nodeType": "174", "messageId": "175", "endLine": 105, "endColumn": 29}, {"ruleId": "172", "severity": 1, "message": "200", "line": 234, "column": 9, "nodeType": "174", "messageId": "175", "endLine": 234, "endColumn": 23}, {"ruleId": "172", "severity": 1, "message": "201", "line": 367, "column": 9, "nodeType": "174", "messageId": "175", "endLine": 367, "endColumn": 28}, {"ruleId": "172", "severity": 1, "message": "202", "line": 377, "column": 9, "nodeType": "174", "messageId": "175", "endLine": 377, "endColumn": 18}, {"ruleId": "172", "severity": 1, "message": "203", "line": 453, "column": 9, "nodeType": "174", "messageId": "175", "endLine": 453, "endColumn": 25}, {"ruleId": "172", "severity": 1, "message": "204", "line": 460, "column": 9, "nodeType": "174", "messageId": "175", "endLine": 460, "endColumn": 25}, {"ruleId": "172", "severity": 1, "message": "205", "line": 466, "column": 9, "nodeType": "174", "messageId": "175", "endLine": 466, "endColumn": 19}, {"ruleId": "172", "severity": 1, "message": "206", "line": 472, "column": 9, "nodeType": "174", "messageId": "175", "endLine": 472, "endColumn": 31}, {"ruleId": "172", "severity": 1, "message": "207", "line": 484, "column": 9, "nodeType": "174", "messageId": "175", "endLine": 484, "endColumn": 27}, "no-unused-vars", "'userData' is assigned a value but never used.", "Identifier", "unusedVar", ["208"], "'navigate' is assigned a value but never used.", ["209"], "'Link' is defined but never used.", ["210"], "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'localResult'.", "ArrowFunctionExpression", "unsafeRefs", ["211"], ["212"], "'transcriptSegments' is assigned a value but never used.", ["213"], "'DEEPGRAM_API_KEY' is assigned a value but never used.", "'isRecording' is assigned a value but never used.", "'apiKeyStatus' is assigned a value but never used.", "'apiKeyError' is assigned a value but never used.", "'showApiKeyError' is assigned a value but never used.", "'gptResponse' is assigned a value but never used.", "'sessionTimeRemaining' is assigned a value but never used.", "'showPaymentDialog' is assigned a value but never used.", "'formatSessionTime' is assigned a value but never used.", "'handlePaymentDialogClose' is assigned a value but never used.", "'handlePaymentConfirm' is assigned a value but never used.", "'startRecording' is assigned a value but never used.", "'handleTextSelection' is assigned a value but never used.", "'sendToGPT' is assigned a value but never used.", "'clearTranscripts' is assigned a value but never used.", "'clearGptResponse' is assigned a value but never used.", "'handleExit' is assigned a value but never used.", "'handleApiKeyErrorClose' is assigned a value but never used.", "'downloadTranscript' is assigned a value but never used.", {"kind": "214", "justification": "215"}, {"kind": "214", "justification": "215"}, {"kind": "214", "justification": "215"}, {"kind": "214", "justification": "215"}, {"kind": "214", "justification": "215"}, {"kind": "214", "justification": "215"}, "directive", ""]