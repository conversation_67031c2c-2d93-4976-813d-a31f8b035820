[{"C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\index.js": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\App.jsx": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\admin-home\\index.jsx": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\home\\index.jsx": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\login\\index.jsx": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\signup\\index.jsx": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\SpeechToText.jsx": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\InterviewForm.jsx": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\InterviewSession.jsx": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\Header.jsx": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\Footer.jsx": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\VoiceTranscriber.jsx": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\utils\\Apicalls.jsx": "13", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\Spinner\\index.jsx": "14", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\utils\\env.js": "15", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\MeetingTranscriber.jsx": "16", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\interview\\index.jsx": "17", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\InterviewAssistant.jsx": "18", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\AutomatedInterviewPage.jsx": "19", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\AutomatedInterview.jsx": "20", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\utils\\speechToText.js": "21", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\SimpleTranscriber.jsx": "22", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\live-transcription\\LiveTranscription.jsx": "23", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\DiarizedInterviewPage.jsx": "24", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\DiarizedTranscription.jsx": "25", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\live-transcription\\AutomaticDiarization.jsx": "26", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\live-transcription\\GoogleSpeechDiarization.jsx": "27"}, {"size": 250, "mtime": 1747115239809, "results": "28", "hashOfConfig": "29"}, {"size": 1821, "mtime": 1747833615618, "results": "30", "hashOfConfig": "29"}, {"size": 3982, "mtime": 1747674646130, "results": "31", "hashOfConfig": "29"}, {"size": 11659, "mtime": 1747833561990, "results": "32", "hashOfConfig": "29"}, {"size": 4380, "mtime": 1747833903246, "results": "33", "hashOfConfig": "29"}, {"size": 8143, "mtime": 1747551471594, "results": "34", "hashOfConfig": "29"}, {"size": 12043, "mtime": 1747727811246, "results": "35", "hashOfConfig": "29"}, {"size": 6217, "mtime": 1747159017468, "results": "36", "hashOfConfig": "29"}, {"size": 3839, "mtime": 1747145275285, "results": "37", "hashOfConfig": "29"}, {"size": 2744, "mtime": 1747825450461, "results": "38", "hashOfConfig": "29"}, {"size": 2459, "mtime": 1747460004752, "results": "39", "hashOfConfig": "29"}, {"size": 29494, "mtime": 1747719906276, "results": "40", "hashOfConfig": "29"}, {"size": 1616, "mtime": 1747373023486, "results": "41", "hashOfConfig": "29"}, {"size": 155, "mtime": 1747501294234, "results": "42", "hashOfConfig": "29"}, {"size": 689, "mtime": 1747754616388, "results": "43", "hashOfConfig": "29"}, {"size": 18613, "mtime": 1747677434844, "results": "44", "hashOfConfig": "29"}, {"size": 267, "mtime": 1747740181362, "results": "45", "hashOfConfig": "29"}, {"size": 17325, "mtime": 1747802122002, "results": "46", "hashOfConfig": "29"}, {"size": 1778, "mtime": 1747766861224, "results": "47", "hashOfConfig": "29"}, {"size": 32032, "mtime": 1747767759545, "results": "48", "hashOfConfig": "29"}, {"size": 5683, "mtime": 1747755722238, "results": "49", "hashOfConfig": "29"}, {"size": 14104, "mtime": 1747767778854, "results": "50", "hashOfConfig": "29"}, {"size": 5612, "mtime": 1747807474926, "results": "51", "hashOfConfig": "29"}, {"size": 6701, "mtime": 1747810475650, "results": "52", "hashOfConfig": "29"}, {"size": 11266, "mtime": 1747826555846, "results": "53", "hashOfConfig": "29"}, {"size": 10246, "mtime": 1747810926727, "results": "54", "hashOfConfig": "29"}, {"size": 27860, "mtime": 1747815069783, "results": "55", "hashOfConfig": "29"}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "32xn06", {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\App.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\admin-home\\index.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\home\\index.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\login\\index.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\signup\\index.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\SpeechToText.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\InterviewForm.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\InterviewSession.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\Header.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\Footer.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\VoiceTranscriber.jsx", [], ["137"], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\utils\\Apicalls.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\Spinner\\index.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\utils\\env.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\MeetingTranscriber.jsx", [], ["138"], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\interview\\index.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\InterviewAssistant.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\AutomatedInterviewPage.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\AutomatedInterview.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\utils\\speechToText.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\SimpleTranscriber.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\live-transcription\\LiveTranscription.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\DiarizedInterviewPage.jsx", [], ["139"], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\DiarizedTranscription.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\live-transcription\\AutomaticDiarization.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\live-transcription\\GoogleSpeechDiarization.jsx", [], [], {"ruleId": "140", "severity": 1, "message": "141", "line": 604, "column": 31, "nodeType": "142", "messageId": "143", "endLine": 608, "endColumn": 20, "suppressions": "144"}, {"ruleId": "140", "severity": 1, "message": "141", "line": 412, "column": 29, "nodeType": "142", "messageId": "143", "endLine": 416, "endColumn": 18, "suppressions": "145"}, {"ruleId": "146", "severity": 1, "message": "147", "line": 8, "column": 10, "nodeType": "148", "messageId": "149", "endLine": 8, "endColumn": 28, "suppressions": "150"}, "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'localResult'.", "ArrowFunctionExpression", "unsafeRefs", ["151"], ["152"], "no-unused-vars", "'transcriptSegments' is assigned a value but never used.", "Identifier", "unusedVar", ["153"], {"kind": "154", "justification": "155"}, {"kind": "154", "justification": "155"}, {"kind": "154", "justification": "155"}, "directive", ""]