[{"C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\index.js": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\App.jsx": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\admin-home\\index.jsx": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\home\\index.jsx": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\login\\index.jsx": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\signup\\index.jsx": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\SpeechToText.jsx": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\InterviewForm.jsx": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\InterviewSession.jsx": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\Header.jsx": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\Footer.jsx": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\VoiceTranscriber.jsx": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\utils\\Apicalls.jsx": "13", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\Spinner\\index.jsx": "14", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\utils\\env.js": "15", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\MeetingTranscriber.jsx": "16", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\interview\\index.jsx": "17", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\InterviewAssistant.jsx": "18", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\AutomatedInterviewPage.jsx": "19", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\AutomatedInterview.jsx": "20", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\utils\\speechToText.js": "21", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\SimpleTranscriber.jsx": "22", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\live-transcription\\LiveTranscription.jsx": "23", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\DiarizedInterviewPage.jsx": "24", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\DiarizedTranscription.jsx": "25", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\live-transcription\\AutomaticDiarization.jsx": "26", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\live-transcription\\GoogleSpeechDiarization.jsx": "27", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\live-transcription\\DeepgramTranscriptionPage.jsx": "28", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\DeepgramLiveTranscript.jsx": "29", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\signup\\SignupPage.jsx": "30", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\signup\\MyProfilePage.jsx": "31", "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\home\\PlansPage.jsx": "32"}, {"size": 250, "mtime": 1747115239809, "results": "33", "hashOfConfig": "34"}, {"size": 2443, "mtime": 1747979167738, "results": "35", "hashOfConfig": "34"}, {"size": 4768, "mtime": 1748019055332, "results": "36", "hashOfConfig": "34"}, {"size": 9839, "mtime": 1747978958300, "results": "37", "hashOfConfig": "34"}, {"size": 6170, "mtime": 1747977947697, "results": "38", "hashOfConfig": "34"}, {"size": 8143, "mtime": 1747551471594, "results": "39", "hashOfConfig": "34"}, {"size": 16944, "mtime": 1748019103249, "results": "40", "hashOfConfig": "34"}, {"size": 14452, "mtime": 1748018842092, "results": "41", "hashOfConfig": "34"}, {"size": 6893, "mtime": 1748018012963, "results": "42", "hashOfConfig": "34"}, {"size": 4964, "mtime": 1747978168869, "results": "43", "hashOfConfig": "34"}, {"size": 3520, "mtime": 1747942248839, "results": "44", "hashOfConfig": "34"}, {"size": 29494, "mtime": 1747719906276, "results": "45", "hashOfConfig": "34"}, {"size": 3393, "mtime": 1747976135265, "results": "46", "hashOfConfig": "34"}, {"size": 155, "mtime": 1747501294234, "results": "47", "hashOfConfig": "34"}, {"size": 1273, "mtime": 1747880050596, "results": "48", "hashOfConfig": "34"}, {"size": 18613, "mtime": 1747677434844, "results": "49", "hashOfConfig": "34"}, {"size": 267, "mtime": 1747740181362, "results": "50", "hashOfConfig": "34"}, {"size": 17325, "mtime": 1747802122002, "results": "51", "hashOfConfig": "34"}, {"size": 1778, "mtime": 1747766861224, "results": "52", "hashOfConfig": "34"}, {"size": 32032, "mtime": 1747767759545, "results": "53", "hashOfConfig": "34"}, {"size": 5683, "mtime": 1747755722238, "results": "54", "hashOfConfig": "34"}, {"size": 14104, "mtime": 1747767778854, "results": "55", "hashOfConfig": "34"}, {"size": 5612, "mtime": 1747807474926, "results": "56", "hashOfConfig": "34"}, {"size": 6697, "mtime": 1747890086472, "results": "57", "hashOfConfig": "34"}, {"size": 11266, "mtime": 1747826555846, "results": "58", "hashOfConfig": "34"}, {"size": 10246, "mtime": 1747810926727, "results": "59", "hashOfConfig": "34"}, {"size": 27860, "mtime": 1747815069783, "results": "60", "hashOfConfig": "34"}, {"size": 4633, "mtime": 1747880957313, "results": "61", "hashOfConfig": "34"}, {"size": 15901, "mtime": 1747889145044, "results": "62", "hashOfConfig": "34"}, {"size": 5182, "mtime": 1748017012980, "results": "63", "hashOfConfig": "34"}, {"size": 3721, "mtime": 1747978393799, "results": "64", "hashOfConfig": "34"}, {"size": 3720, "mtime": 1747979077518, "results": "65", "hashOfConfig": "34"}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "32xn06", {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 19, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\App.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\admin-home\\index.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\home\\index.jsx", [], ["162", "163"], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\login\\index.jsx", [], ["164"], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\signup\\index.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\SpeechToText.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\InterviewForm.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\InterviewSession.jsx", ["165"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\Header.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\Footer.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\VoiceTranscriber.jsx", [], ["166"], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\utils\\Apicalls.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\Spinner\\index.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\utils\\env.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\MeetingTranscriber.jsx", [], ["167"], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\interview\\index.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\InterviewAssistant.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\AutomatedInterviewPage.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\AutomatedInterview.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\utils\\speechToText.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\SimpleTranscriber.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\live-transcription\\LiveTranscription.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\DiarizedInterviewPage.jsx", [], ["168"], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\DiarizedTranscription.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\live-transcription\\AutomaticDiarization.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\live-transcription\\GoogleSpeechDiarization.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\live-transcription\\DeepgramTranscriptionPage.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\components\\DeepgramLiveTranscript.jsx", ["169", "170", "171", "172", "173", "174", "175", "176", "177", "178", "179", "180", "181", "182", "183", "184", "185", "186", "187"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\signup\\SignupPage.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\signup\\MyProfilePage.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\interview-ai-app\\interviewAI\\client\\src\\pages\\home\\PlansPage.jsx", [], [], {"ruleId": "188", "severity": 1, "message": "189", "line": 12, "column": 10, "nodeType": "190", "messageId": "191", "endLine": 12, "endColumn": 18, "suppressions": "192"}, {"ruleId": "188", "severity": 1, "message": "193", "line": 14, "column": 9, "nodeType": "190", "messageId": "191", "endLine": 14, "endColumn": 17, "suppressions": "194"}, {"ruleId": "188", "severity": 1, "message": "195", "line": 3, "column": 23, "nodeType": "190", "messageId": "191", "endLine": 3, "endColumn": 27, "suppressions": "196"}, {"ruleId": "197", "severity": 1, "message": "198", "line": 57, "column": 6, "nodeType": "199", "endLine": 57, "endColumn": 8, "suggestions": "200"}, {"ruleId": "201", "severity": 1, "message": "202", "line": 604, "column": 31, "nodeType": "203", "messageId": "204", "endLine": 608, "endColumn": 20, "suppressions": "205"}, {"ruleId": "201", "severity": 1, "message": "202", "line": 412, "column": 29, "nodeType": "203", "messageId": "204", "endLine": 416, "endColumn": 18, "suppressions": "206"}, {"ruleId": "188", "severity": 1, "message": "207", "line": 8, "column": 10, "nodeType": "190", "messageId": "191", "endLine": 8, "endColumn": 28, "suppressions": "208"}, {"ruleId": "188", "severity": 1, "message": "209", "line": 6, "column": 7, "nodeType": "190", "messageId": "191", "endLine": 6, "endColumn": 23}, {"ruleId": "188", "severity": 1, "message": "210", "line": 11, "column": 10, "nodeType": "190", "messageId": "191", "endLine": 11, "endColumn": 21}, {"ruleId": "188", "severity": 1, "message": "211", "line": 12, "column": 10, "nodeType": "190", "messageId": "191", "endLine": 12, "endColumn": 22}, {"ruleId": "188", "severity": 1, "message": "212", "line": 13, "column": 10, "nodeType": "190", "messageId": "191", "endLine": 13, "endColumn": 21}, {"ruleId": "188", "severity": 1, "message": "213", "line": 14, "column": 10, "nodeType": "190", "messageId": "191", "endLine": 14, "endColumn": 25}, {"ruleId": "188", "severity": 1, "message": "214", "line": 17, "column": 10, "nodeType": "190", "messageId": "191", "endLine": 17, "endColumn": 21}, {"ruleId": "188", "severity": 1, "message": "215", "line": 24, "column": 10, "nodeType": "190", "messageId": "191", "endLine": 24, "endColumn": 30}, {"ruleId": "188", "severity": 1, "message": "216", "line": 25, "column": 10, "nodeType": "190", "messageId": "191", "endLine": 25, "endColumn": 27}, {"ruleId": "188", "severity": 1, "message": "217", "line": 36, "column": 9, "nodeType": "190", "messageId": "191", "endLine": 36, "endColumn": 26}, {"ruleId": "188", "severity": 1, "message": "218", "line": 100, "column": 9, "nodeType": "190", "messageId": "191", "endLine": 100, "endColumn": 33}, {"ruleId": "188", "severity": 1, "message": "219", "line": 105, "column": 9, "nodeType": "190", "messageId": "191", "endLine": 105, "endColumn": 29}, {"ruleId": "188", "severity": 1, "message": "220", "line": 234, "column": 9, "nodeType": "190", "messageId": "191", "endLine": 234, "endColumn": 23}, {"ruleId": "188", "severity": 1, "message": "221", "line": 367, "column": 9, "nodeType": "190", "messageId": "191", "endLine": 367, "endColumn": 28}, {"ruleId": "188", "severity": 1, "message": "222", "line": 377, "column": 9, "nodeType": "190", "messageId": "191", "endLine": 377, "endColumn": 18}, {"ruleId": "188", "severity": 1, "message": "223", "line": 453, "column": 9, "nodeType": "190", "messageId": "191", "endLine": 453, "endColumn": 25}, {"ruleId": "188", "severity": 1, "message": "224", "line": 460, "column": 9, "nodeType": "190", "messageId": "191", "endLine": 460, "endColumn": 25}, {"ruleId": "188", "severity": 1, "message": "225", "line": 466, "column": 9, "nodeType": "190", "messageId": "191", "endLine": 466, "endColumn": 19}, {"ruleId": "188", "severity": 1, "message": "226", "line": 472, "column": 9, "nodeType": "190", "messageId": "191", "endLine": 472, "endColumn": 31}, {"ruleId": "188", "severity": 1, "message": "227", "line": 484, "column": 9, "nodeType": "190", "messageId": "191", "endLine": 484, "endColumn": 27}, "no-unused-vars", "'userData' is assigned a value but never used.", "Identifier", "unusedVar", ["228"], "'navigate' is assigned a value but never used.", ["229"], "'Link' is defined but never used.", ["230"], "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'sessionTimeRemaining'. Either include it or remove the dependency array. You can also replace multiple useState variables with useReducer if 'setSessionTimeDisplay' needs the current value of 'sessionTimeRemaining'.", "ArrayExpression", ["231"], "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'localResult'.", "ArrowFunctionExpression", "unsafeRefs", ["232"], ["233"], "'transcriptSegments' is assigned a value but never used.", ["234"], "'DEEPGRAM_API_KEY' is assigned a value but never used.", "'isRecording' is assigned a value but never used.", "'apiKeyStatus' is assigned a value but never used.", "'apiKeyError' is assigned a value but never used.", "'showApiKeyError' is assigned a value but never used.", "'gptResponse' is assigned a value but never used.", "'sessionTimeRemaining' is assigned a value but never used.", "'showPaymentDialog' is assigned a value but never used.", "'formatSessionTime' is assigned a value but never used.", "'handlePaymentDialogClose' is assigned a value but never used.", "'handlePaymentConfirm' is assigned a value but never used.", "'startRecording' is assigned a value but never used.", "'handleTextSelection' is assigned a value but never used.", "'sendToGPT' is assigned a value but never used.", "'clearTranscripts' is assigned a value but never used.", "'clearGptResponse' is assigned a value but never used.", "'handleExit' is assigned a value but never used.", "'handleApiKeyErrorClose' is assigned a value but never used.", "'downloadTranscript' is assigned a value but never used.", {"kind": "235", "justification": "236"}, {"kind": "235", "justification": "236"}, {"kind": "235", "justification": "236"}, {"desc": "237", "fix": "238"}, {"kind": "235", "justification": "236"}, {"kind": "235", "justification": "236"}, {"kind": "235", "justification": "236"}, "directive", "", "Update the dependencies array to be: [sessionTimeRemaining]", {"range": "239", "text": "240"}, [1830, 1832], "[sessionTimeRemaining]"]