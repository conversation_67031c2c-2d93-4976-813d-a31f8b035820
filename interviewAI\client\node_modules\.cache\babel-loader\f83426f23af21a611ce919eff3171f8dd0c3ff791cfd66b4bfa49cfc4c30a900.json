{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\interview-ai-app\\\\interviewAI\\\\client\\\\src\\\\App.jsx\";\nimport React from \"react\";\nimport { BrowserRouter, Route, Routes, Navigate } from \"react-router-dom\";\nimport \"./App.css\";\nimport 'bootstrap/dist/css/bootstrap.min.css';\nimport HomeScreen from \"./pages/home/<USER>\";\nimport LoginScreen from \"./pages/login/index\";\nimport AdminHomeScreen from \"./pages/admin-home/index\";\nimport SignUpScreen from \"./pages/signup/index\";\nimport InterviewPage from \"./pages/interview/index\";\nimport AutomatedInterviewPage from \"./pages/AutomatedInterviewPage\";\nimport LiveTranscriptionPage from \"./pages/live-transcription/LiveTranscription\";\nimport DiarizedInterviewPage from \"./pages/DiarizedInterviewPage\";\nimport AutomaticDiarization from \"./pages/live-transcription/AutomaticDiarization\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(BrowserRouter, {\n    children: /*#__PURE__*/_jsxDEV(Routes, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        path: \"/\",\n        element: /*#__PURE__*/_jsxDEV(HomeScreen, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 34\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/login\",\n        element: /*#__PURE__*/_jsxDEV(LoginScreen, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 39\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/home\",\n        element: /*#__PURE__*/_jsxDEV(AdminHomeScreen, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 38\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/signup/:id\",\n        element: /*#__PURE__*/_jsxDEV(SignUpScreen, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 44\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/interview\",\n        element: /*#__PURE__*/_jsxDEV(InterviewPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 43\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/automated-interview\",\n        element: /*#__PURE__*/_jsxDEV(AutomatedInterviewPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 53\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/live-transcription\",\n        element: /*#__PURE__*/_jsxDEV(LiveTranscriptionPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 52\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/diarized-interview\",\n        element: /*#__PURE__*/_jsxDEV(DiarizedInterviewPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 52\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/automatic-diarization\",\n        element: /*#__PURE__*/_jsxDEV(AutomaticDiarization, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 55\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"*\",\n        element: /*#__PURE__*/_jsxDEV(Navigate, {\n          to: \"/\",\n          replace: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 34\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Route", "Routes", "Navigate", "HomeScreen", "LoginScreen", "AdminHomeScreen", "SignUpScreen", "InterviewPage", "AutomatedInterviewPage", "LiveTranscriptionPage", "DiarizedInterviewPage", "AutomaticDiarization", "jsxDEV", "_jsxDEV", "App", "children", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "replace", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/interview-ai-app/interviewAI/client/src/App.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { BrowserRouter, Route, Routes, Navigate } from \"react-router-dom\";\r\nimport \"./App.css\";\r\nimport 'bootstrap/dist/css/bootstrap.min.css';\r\n\r\nimport HomeScreen from \"./pages/home/<USER>\";\r\nimport LoginScreen from \"./pages/login/index\";\r\nimport AdminHomeScreen from \"./pages/admin-home/index\";\r\nimport SignUpScreen from \"./pages/signup/index\";\r\nimport InterviewPage from \"./pages/interview/index\";\r\nimport AutomatedInterviewPage from \"./pages/AutomatedInterviewPage\";\r\nimport LiveTranscriptionPage from \"./pages/live-transcription/LiveTranscription\";\r\nimport DiarizedInterviewPage from \"./pages/DiarizedInterviewPage\";\r\nimport AutomaticDiarization from \"./pages/live-transcription/AutomaticDiarization\";\r\n\r\nfunction App() {\r\n\r\n  return (\r\n    <BrowserRouter>\r\n      <Routes>\r\n        <Route path=\"/\" element={<HomeScreen />} />\r\n        <Route path=\"/login\" element={<LoginScreen />} />\r\n        <Route path=\"/home\" element={<AdminHomeScreen />} />\r\n        <Route path=\"/signup/:id\" element={<SignUpScreen />} />\r\n        <Route path=\"/interview\" element={<InterviewPage />} />\r\n        <Route path=\"/automated-interview\" element={<AutomatedInterviewPage />} />\r\n        <Route path=\"/live-transcription\" element={<LiveTranscriptionPage />} />\r\n        <Route path=\"/diarized-interview\" element={<DiarizedInterviewPage />} />\r\n        <Route path=\"/automatic-diarization\" element={<AutomaticDiarization />} />\r\n        {/* <Route path=\"/launch\" element={<LaunchScreen />} /> */}\r\n\r\n        <Route path=\"*\" element={<Navigate to=\"/\" replace />} />\r\n      </Routes>\r\n    </BrowserRouter>\r\n  );\r\n}\r\n\r\nexport default App;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,kBAAkB;AACzE,OAAO,WAAW;AAClB,OAAO,sCAAsC;AAE7C,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,eAAe,MAAM,0BAA0B;AACtD,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,aAAa,MAAM,yBAAyB;AACnD,OAAOC,sBAAsB,MAAM,gCAAgC;AACnE,OAAOC,qBAAqB,MAAM,8CAA8C;AAChF,OAAOC,qBAAqB,MAAM,+BAA+B;AACjE,OAAOC,oBAAoB,MAAM,iDAAiD;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnF,SAASC,GAAGA,CAAA,EAAG;EAEb,oBACED,OAAA,CAACd,aAAa;IAAAgB,QAAA,eACZF,OAAA,CAACZ,MAAM;MAAAc,QAAA,gBACLF,OAAA,CAACb,KAAK;QAACgB,IAAI,EAAC,GAAG;QAACC,OAAO,eAAEJ,OAAA,CAACV,UAAU;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3CR,OAAA,CAACb,KAAK;QAACgB,IAAI,EAAC,QAAQ;QAACC,OAAO,eAAEJ,OAAA,CAACT,WAAW;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACjDR,OAAA,CAACb,KAAK;QAACgB,IAAI,EAAC,OAAO;QAACC,OAAO,eAAEJ,OAAA,CAACR,eAAe;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpDR,OAAA,CAACb,KAAK;QAACgB,IAAI,EAAC,aAAa;QAACC,OAAO,eAAEJ,OAAA,CAACP,YAAY;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvDR,OAAA,CAACb,KAAK;QAACgB,IAAI,EAAC,YAAY;QAACC,OAAO,eAAEJ,OAAA,CAACN,aAAa;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvDR,OAAA,CAACb,KAAK;QAACgB,IAAI,EAAC,sBAAsB;QAACC,OAAO,eAAEJ,OAAA,CAACL,sBAAsB;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1ER,OAAA,CAACb,KAAK;QAACgB,IAAI,EAAC,qBAAqB;QAACC,OAAO,eAAEJ,OAAA,CAACJ,qBAAqB;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxER,OAAA,CAACb,KAAK;QAACgB,IAAI,EAAC,qBAAqB;QAACC,OAAO,eAAEJ,OAAA,CAACH,qBAAqB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxER,OAAA,CAACb,KAAK;QAACgB,IAAI,EAAC,wBAAwB;QAACC,OAAO,eAAEJ,OAAA,CAACF,oBAAoB;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAG1ER,OAAA,CAACb,KAAK;QAACgB,IAAI,EAAC,GAAG;QAACC,OAAO,eAAEJ,OAAA,CAACX,QAAQ;UAACoB,EAAE,EAAC,GAAG;UAACC,OAAO;QAAA;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEpB;AAACG,EAAA,GApBQV,GAAG;AAsBZ,eAAeA,GAAG;AAAC,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}