{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\interview-ai-app\\\\interviewAI\\\\client\\\\src\\\\components\\\\SimpleTranscriber.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport './SimpleTranscriber.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction SimpleTranscriber({\n  onTranscriptChange\n}) {\n  _s();\n  const [transcript, setTranscript] = useState('');\n  const [isListening, setIsListening] = useState(false);\n  const recognitionRef = useRef(null);\n  const finalTranscriptRef = useRef('');\n\n  // Initialize speech recognition\n  useEffect(() => {\n    // Browser compatibility\n    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n    if (!SpeechRecognition) {\n      console.error(\"Speech recognition not supported in this browser\");\n      return;\n    }\n\n    // Create recognition instance\n    recognitionRef.current = new SpeechRecognition();\n\n    // Configure\n    recognitionRef.current.continuous = true;\n    recognitionRef.current.interimResults = true;\n    recognitionRef.current.lang = 'en-US';\n\n    // Set up event handlers\n    recognitionRef.current.onresult = event => {\n      let interimTranscript = '';\n      for (let i = event.resultIndex; i < event.results.length; i++) {\n        const result = event.results[i];\n        const text = result[0].transcript;\n        if (result.isFinal) {\n          finalTranscriptRef.current += text + ' ';\n        } else {\n          interimTranscript += text;\n        }\n      }\n      const fullTranscript = finalTranscriptRef.current + interimTranscript;\n      setTranscript(fullTranscript);\n\n      // Notify parent component\n      if (onTranscriptChange) {\n        onTranscriptChange(fullTranscript);\n      }\n    };\n    recognitionRef.current.onerror = event => {\n      console.error('Speech recognition error:', event.error);\n\n      // Handle different error types\n      switch (event.error) {\n        case 'no-speech':\n          // This is a common error that doesn't need to stop the recognition\n          console.log('No speech detected, continuing to listen');\n          break;\n        case 'aborted':\n          // When aborted, we should try to restart after a short delay\n          console.log('Recognition aborted, attempting to restart');\n          setTimeout(() => {\n            if (isListening) {\n              try {\n                recognitionRef.current.start();\n                console.log('Recognition restarted after abort');\n              } catch (error) {\n                console.error('Failed to restart after abort:', error);\n                setIsListening(false);\n              }\n            }\n          }, 300);\n          break;\n        default:\n          // For other errors, we should stop listening\n          console.error('Critical recognition error:', event.error);\n          setIsListening(false);\n      }\n    };\n    recognitionRef.current.onend = () => {\n      console.log('Speech recognition ended');\n\n      // Restart if still supposed to be listening\n      if (isListening) {\n        console.log('Still listening, attempting to restart recognition');\n\n        // Add a small delay before restarting to avoid rapid restart loops\n        setTimeout(() => {\n          if (isListening && recognitionRef.current) {\n            try {\n              recognitionRef.current.start();\n              console.log('Recognition restarted successfully');\n            } catch (error) {\n              console.error('Failed to restart recognition:', error);\n\n              // If we get an \"already started\" error, stop and try again\n              if (error.message && error.message.includes('already started')) {\n                try {\n                  recognitionRef.current.stop();\n                  setTimeout(() => {\n                    if (isListening && recognitionRef.current) {\n                      recognitionRef.current.start();\n                      console.log('Recognition restarted after stopping');\n                    }\n                  }, 300);\n                } catch (stopError) {\n                  console.error('Error stopping recognition:', stopError);\n                  setIsListening(false);\n                }\n              } else {\n                setIsListening(false);\n              }\n            }\n          }\n        }, 300);\n      } else {\n        console.log('Not listening anymore, not restarting recognition');\n      }\n    };\n\n    // Clean up\n    return () => {\n      if (recognitionRef.current) {\n        try {\n          recognitionRef.current.stop();\n        } catch (error) {\n          console.error('Error stopping recognition:', error);\n        }\n      }\n    };\n  }, [isListening, onTranscriptChange]);\n\n  // Start listening with improved error handling\n  const startListening = () => {\n    console.log('Starting speech recognition...');\n    if (!recognitionRef.current) {\n      console.error('Speech recognition not initialized');\n      alert('Speech recognition is not available in your browser. Please try using Chrome or Edge.');\n      return;\n    }\n    if (isListening) {\n      console.log('Already listening, no need to start again');\n      return;\n    }\n\n    // Reset transcript\n    finalTranscriptRef.current = '';\n    setTranscript('');\n    try {\n      console.log('Calling recognition.start()');\n      recognitionRef.current.start();\n      console.log('Recognition started successfully');\n      setIsListening(true);\n\n      // Add visual feedback\n      const transcriptDisplay = document.querySelector('.transcript-display');\n      if (transcriptDisplay) {\n        transcriptDisplay.classList.add('listening');\n      }\n    } catch (error) {\n      console.error('Error starting recognition:', error);\n\n      // Handle \"already started\" error\n      if (error.message && error.message.includes('already started')) {\n        console.log('Recognition already started, stopping and restarting');\n        try {\n          recognitionRef.current.stop();\n\n          // Add a small delay before restarting\n          setTimeout(() => {\n            try {\n              console.log('Restarting recognition after stop');\n              recognitionRef.current.start();\n              setIsListening(true);\n\n              // Add visual feedback\n              const transcriptDisplay = document.querySelector('.transcript-display');\n              if (transcriptDisplay) {\n                transcriptDisplay.classList.add('listening');\n              }\n            } catch (restartError) {\n              console.error('Failed to restart recognition:', restartError);\n              alert('Failed to start speech recognition. Please refresh the page and try again.');\n            }\n          }, 300);\n        } catch (stopError) {\n          console.error('Error stopping recognition:', stopError);\n        }\n      } else {\n        // For other errors, show a user-friendly message\n        alert('Failed to start speech recognition: ' + error.message);\n      }\n    }\n  };\n\n  // Stop listening with improved error handling\n  const stopListening = () => {\n    console.log('Stopping speech recognition...');\n    if (!recognitionRef.current) {\n      console.log('No recognition object to stop');\n      setIsListening(false);\n      return;\n    }\n    if (!isListening) {\n      console.log('Not listening, no need to stop');\n      return;\n    }\n    try {\n      console.log('Calling recognition.stop()');\n      recognitionRef.current.stop();\n      console.log('Recognition stopped successfully');\n      setIsListening(false);\n\n      // Remove visual feedback\n      const transcriptDisplay = document.querySelector('.transcript-display');\n      if (transcriptDisplay) {\n        transcriptDisplay.classList.remove('listening');\n      }\n    } catch (error) {\n      console.error('Error stopping recognition:', error);\n\n      // Force the state to be updated even if there was an error\n      setIsListening(false);\n\n      // If we can't stop normally, try to recreate the recognition object\n      try {\n        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n        recognitionRef.current = new SpeechRecognition();\n        recognitionRef.current.continuous = true;\n        recognitionRef.current.interimResults = true;\n        recognitionRef.current.lang = 'en-US';\n        console.log('Recognition object recreated after stop error');\n      } catch (recreateError) {\n        console.error('Failed to recreate recognition object:', recreateError);\n      }\n    }\n  };\n\n  // Check if speech recognition is supported\n  const isSpeechRecognitionSupported = !!(window.SpeechRecognition || window.webkitSpeechRecognition);\n\n  // State for tab audio capture\n  const [isCapturingTabAudio, setIsCapturingTabAudio] = useState(false);\n\n  // Function to start capturing tab audio\n  const startTabAudioCapture = async () => {\n    try {\n      console.log(\"Starting tab audio capture...\");\n\n      // Request screen sharing with audio\n      const stream = await navigator.mediaDevices.getDisplayMedia({\n        video: true,\n        audio: true // This is critical - we need to capture audio\n      });\n\n      // Check if audio is included\n      const audioTracks = stream.getAudioTracks();\n      if (audioTracks.length === 0) {\n        alert(\"No audio track found. Please make sure to select 'Share audio' when sharing the tab.\");\n        stream.getTracks().forEach(track => track.stop());\n        return;\n      }\n      console.log(\"Tab audio capture started with tracks:\", stream.getTracks().map(t => `${t.kind} (${t.label})`));\n\n      // Stop any existing recognition\n      if (recognitionRef.current && isListening) {\n        try {\n          recognitionRef.current.stop();\n        } catch (error) {\n          console.error(\"Error stopping existing recognition:\", error);\n        }\n      }\n\n      // Create a new recognition instance for the tab audio\n      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n      recognitionRef.current = new SpeechRecognition();\n      recognitionRef.current.continuous = true;\n      recognitionRef.current.interimResults = true;\n      recognitionRef.current.lang = 'en-US';\n\n      // Reset transcript\n      finalTranscriptRef.current = '';\n      setTranscript('');\n\n      // Set up event handlers\n      recognitionRef.current.onresult = event => {\n        let interimTranscript = '';\n        for (let i = event.resultIndex; i < event.results.length; i++) {\n          const result = event.results[i];\n          const text = result[0].transcript;\n          if (result.isFinal) {\n            finalTranscriptRef.current += text + ' ';\n          } else {\n            interimTranscript += text;\n          }\n        }\n        const fullTranscript = finalTranscriptRef.current + interimTranscript;\n        setTranscript(fullTranscript);\n\n        // Notify parent component\n        if (onTranscriptChange) {\n          onTranscriptChange(fullTranscript);\n        }\n      };\n\n      // Start recognition with the tab audio stream\n      recognitionRef.current.start();\n      setIsListening(true);\n      setIsCapturingTabAudio(true);\n\n      // Handle stream ending (user stops sharing)\n      stream.getVideoTracks()[0].onended = () => {\n        console.log(\"Tab sharing ended\");\n        stopTabAudioCapture();\n      };\n\n      // Store the stream for cleanup\n      window.tabAudioStream = stream;\n    } catch (error) {\n      console.error(\"Error capturing tab audio:\", error);\n      alert(\"Failed to capture tab audio: \" + error.message);\n    }\n  };\n\n  // Function to stop capturing tab audio\n  const stopTabAudioCapture = () => {\n    console.log(\"Stopping tab audio capture...\");\n\n    // Stop the stream\n    if (window.tabAudioStream) {\n      window.tabAudioStream.getTracks().forEach(track => {\n        track.stop();\n        console.log(`Stopped ${track.kind} track: ${track.label}`);\n      });\n      window.tabAudioStream = null;\n    }\n\n    // Stop recognition\n    if (recognitionRef.current && isListening) {\n      try {\n        recognitionRef.current.stop();\n      } catch (error) {\n        console.error(\"Error stopping recognition:\", error);\n      }\n    }\n    setIsListening(false);\n    setIsCapturingTabAudio(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"simple-transcriber\",\n    children: [!isSpeechRecognitionSupported && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"browser-warning\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Warning:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 13\n        }, this), \" Your browser doesn't support speech recognition. Please use Chrome, Edge, or another Chromium-based browser for this feature to work.\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 375,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 374,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"transcript-display\",\n      children: transcript ? /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"transcript-text\",\n        children: transcript\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 384,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"placeholder\",\n        children: \"Waiting for speech or audio...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 386,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 382,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"controls\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: `control-button ${isListening && !isCapturingTabAudio ? 'listening' : ''}`,\n        onClick: isListening ? stopListening : startListening,\n        disabled: !isSpeechRecognitionSupported || isCapturingTabAudio,\n        title: \"Capture microphone audio\",\n        children: [isListening && !isCapturingTabAudio ? 'Stop' : 'Start', \" Mic Listening\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 391,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: `control-button tab-audio ${isCapturingTabAudio ? 'capturing' : ''}`,\n        onClick: isCapturingTabAudio ? stopTabAudioCapture : startTabAudioCapture,\n        disabled: !isSpeechRecognitionSupported,\n        title: \"Capture audio from another tab (e.g., YouTube)\",\n        children: [isCapturingTabAudio ? 'Stop' : 'Start', \" Tab Audio Capture\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 400,\n        columnNumber: 9\n      }, this), isListening && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"status\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"listening-indicator\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 411,\n          columnNumber: 13\n        }, this), isCapturingTabAudio ? 'Capturing Tab Audio...' : 'Listening to Microphone...']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 410,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 390,\n      columnNumber: 7\n    }, this), process.env.NODE_ENV === 'development' && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"debug-info\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Speech Recognition Supported: \", isSpeechRecognitionSupported ? 'Yes' : 'No']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 420,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Is Listening: \", isListening ? 'Yes' : 'No']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 421,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Transcript Length: \", transcript.length]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 422,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 419,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 372,\n    columnNumber: 5\n  }, this);\n}\n_s(SimpleTranscriber, \"l6oo/IRdOd/kSOLe9HN1OpcN698=\");\n_c = SimpleTranscriber;\nexport default SimpleTranscriber;\nvar _c;\n$RefreshReg$(_c, \"SimpleTranscriber\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "SimpleTranscriber", "onTranscriptChange", "_s", "transcript", "setTranscript", "isListening", "setIsListening", "recognitionRef", "finalTranscriptRef", "SpeechRecognition", "window", "webkitSpeechRecognition", "console", "error", "current", "continuous", "interimResults", "lang", "on<PERSON>ult", "event", "interimTranscript", "i", "resultIndex", "results", "length", "result", "text", "isFinal", "fullTranscript", "onerror", "log", "setTimeout", "start", "onend", "message", "includes", "stop", "stopError", "startListening", "alert", "transcriptDisplay", "document", "querySelector", "classList", "add", "restartError", "stopListening", "remove", "recreateError", "isSpeechRecognitionSupported", "isCapturingTabAudio", "setIsCapturingTabAudio", "startTabAudioCapture", "stream", "navigator", "mediaDevices", "getDisplayMedia", "video", "audio", "audioTracks", "getAudioTracks", "getTracks", "for<PERSON>ach", "track", "map", "t", "kind", "label", "getVideoTracks", "onended", "stopTabAudioCapture", "tabAudioStream", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "title", "process", "env", "NODE_ENV", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/interview-ai-app/interviewAI/client/src/components/SimpleTranscriber.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport './SimpleTranscriber.css';\n\nfunction SimpleTranscriber({ onTranscriptChange }) {\n  const [transcript, setTranscript] = useState('');\n  const [isListening, setIsListening] = useState(false);\n  const recognitionRef = useRef(null);\n  const finalTranscriptRef = useRef('');\n\n  // Initialize speech recognition\n  useEffect(() => {\n    // Browser compatibility\n    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n\n    if (!SpeechRecognition) {\n      console.error(\"Speech recognition not supported in this browser\");\n      return;\n    }\n\n    // Create recognition instance\n    recognitionRef.current = new SpeechRecognition();\n\n    // Configure\n    recognitionRef.current.continuous = true;\n    recognitionRef.current.interimResults = true;\n    recognitionRef.current.lang = 'en-US';\n\n    // Set up event handlers\n    recognitionRef.current.onresult = (event) => {\n      let interimTranscript = '';\n\n      for (let i = event.resultIndex; i < event.results.length; i++) {\n        const result = event.results[i];\n        const text = result[0].transcript;\n\n        if (result.isFinal) {\n          finalTranscriptRef.current += text + ' ';\n        } else {\n          interimTranscript += text;\n        }\n      }\n\n      const fullTranscript = finalTranscriptRef.current + interimTranscript;\n      setTranscript(fullTranscript);\n\n      // Notify parent component\n      if (onTranscriptChange) {\n        onTranscriptChange(fullTranscript);\n      }\n    };\n\n    recognitionRef.current.onerror = (event) => {\n      console.error('Speech recognition error:', event.error);\n\n      // Handle different error types\n      switch (event.error) {\n        case 'no-speech':\n          // This is a common error that doesn't need to stop the recognition\n          console.log('No speech detected, continuing to listen');\n          break;\n\n        case 'aborted':\n          // When aborted, we should try to restart after a short delay\n          console.log('Recognition aborted, attempting to restart');\n          setTimeout(() => {\n            if (isListening) {\n              try {\n                recognitionRef.current.start();\n                console.log('Recognition restarted after abort');\n              } catch (error) {\n                console.error('Failed to restart after abort:', error);\n                setIsListening(false);\n              }\n            }\n          }, 300);\n          break;\n\n        default:\n          // For other errors, we should stop listening\n          console.error('Critical recognition error:', event.error);\n          setIsListening(false);\n      }\n    };\n\n    recognitionRef.current.onend = () => {\n      console.log('Speech recognition ended');\n\n      // Restart if still supposed to be listening\n      if (isListening) {\n        console.log('Still listening, attempting to restart recognition');\n\n        // Add a small delay before restarting to avoid rapid restart loops\n        setTimeout(() => {\n          if (isListening && recognitionRef.current) {\n            try {\n              recognitionRef.current.start();\n              console.log('Recognition restarted successfully');\n            } catch (error) {\n              console.error('Failed to restart recognition:', error);\n\n              // If we get an \"already started\" error, stop and try again\n              if (error.message && error.message.includes('already started')) {\n                try {\n                  recognitionRef.current.stop();\n                  setTimeout(() => {\n                    if (isListening && recognitionRef.current) {\n                      recognitionRef.current.start();\n                      console.log('Recognition restarted after stopping');\n                    }\n                  }, 300);\n                } catch (stopError) {\n                  console.error('Error stopping recognition:', stopError);\n                  setIsListening(false);\n                }\n              } else {\n                setIsListening(false);\n              }\n            }\n          }\n        }, 300);\n      } else {\n        console.log('Not listening anymore, not restarting recognition');\n      }\n    };\n\n    // Clean up\n    return () => {\n      if (recognitionRef.current) {\n        try {\n          recognitionRef.current.stop();\n        } catch (error) {\n          console.error('Error stopping recognition:', error);\n        }\n      }\n    };\n  }, [isListening, onTranscriptChange]);\n\n  // Start listening with improved error handling\n  const startListening = () => {\n    console.log('Starting speech recognition...');\n\n    if (!recognitionRef.current) {\n      console.error('Speech recognition not initialized');\n      alert('Speech recognition is not available in your browser. Please try using Chrome or Edge.');\n      return;\n    }\n\n    if (isListening) {\n      console.log('Already listening, no need to start again');\n      return;\n    }\n\n    // Reset transcript\n    finalTranscriptRef.current = '';\n    setTranscript('');\n\n    try {\n      console.log('Calling recognition.start()');\n      recognitionRef.current.start();\n      console.log('Recognition started successfully');\n      setIsListening(true);\n\n      // Add visual feedback\n      const transcriptDisplay = document.querySelector('.transcript-display');\n      if (transcriptDisplay) {\n        transcriptDisplay.classList.add('listening');\n      }\n\n    } catch (error) {\n      console.error('Error starting recognition:', error);\n\n      // Handle \"already started\" error\n      if (error.message && error.message.includes('already started')) {\n        console.log('Recognition already started, stopping and restarting');\n\n        try {\n          recognitionRef.current.stop();\n\n          // Add a small delay before restarting\n          setTimeout(() => {\n            try {\n              console.log('Restarting recognition after stop');\n              recognitionRef.current.start();\n              setIsListening(true);\n\n              // Add visual feedback\n              const transcriptDisplay = document.querySelector('.transcript-display');\n              if (transcriptDisplay) {\n                transcriptDisplay.classList.add('listening');\n              }\n            } catch (restartError) {\n              console.error('Failed to restart recognition:', restartError);\n              alert('Failed to start speech recognition. Please refresh the page and try again.');\n            }\n          }, 300);\n        } catch (stopError) {\n          console.error('Error stopping recognition:', stopError);\n        }\n      } else {\n        // For other errors, show a user-friendly message\n        alert('Failed to start speech recognition: ' + error.message);\n      }\n    }\n  };\n\n  // Stop listening with improved error handling\n  const stopListening = () => {\n    console.log('Stopping speech recognition...');\n\n    if (!recognitionRef.current) {\n      console.log('No recognition object to stop');\n      setIsListening(false);\n      return;\n    }\n\n    if (!isListening) {\n      console.log('Not listening, no need to stop');\n      return;\n    }\n\n    try {\n      console.log('Calling recognition.stop()');\n      recognitionRef.current.stop();\n      console.log('Recognition stopped successfully');\n      setIsListening(false);\n\n      // Remove visual feedback\n      const transcriptDisplay = document.querySelector('.transcript-display');\n      if (transcriptDisplay) {\n        transcriptDisplay.classList.remove('listening');\n      }\n\n    } catch (error) {\n      console.error('Error stopping recognition:', error);\n\n      // Force the state to be updated even if there was an error\n      setIsListening(false);\n\n      // If we can't stop normally, try to recreate the recognition object\n      try {\n        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n        recognitionRef.current = new SpeechRecognition();\n        recognitionRef.current.continuous = true;\n        recognitionRef.current.interimResults = true;\n        recognitionRef.current.lang = 'en-US';\n        console.log('Recognition object recreated after stop error');\n      } catch (recreateError) {\n        console.error('Failed to recreate recognition object:', recreateError);\n      }\n    }\n  };\n\n  // Check if speech recognition is supported\n  const isSpeechRecognitionSupported = !!(window.SpeechRecognition || window.webkitSpeechRecognition);\n\n  // State for tab audio capture\n  const [isCapturingTabAudio, setIsCapturingTabAudio] = useState(false);\n\n  // Function to start capturing tab audio\n  const startTabAudioCapture = async () => {\n    try {\n      console.log(\"Starting tab audio capture...\");\n\n      // Request screen sharing with audio\n      const stream = await navigator.mediaDevices.getDisplayMedia({\n        video: true,\n        audio: true // This is critical - we need to capture audio\n      });\n\n      // Check if audio is included\n      const audioTracks = stream.getAudioTracks();\n      if (audioTracks.length === 0) {\n        alert(\"No audio track found. Please make sure to select 'Share audio' when sharing the tab.\");\n        stream.getTracks().forEach(track => track.stop());\n        return;\n      }\n\n      console.log(\"Tab audio capture started with tracks:\",\n        stream.getTracks().map(t => `${t.kind} (${t.label})`));\n\n      // Stop any existing recognition\n      if (recognitionRef.current && isListening) {\n        try {\n          recognitionRef.current.stop();\n        } catch (error) {\n          console.error(\"Error stopping existing recognition:\", error);\n        }\n      }\n\n      // Create a new recognition instance for the tab audio\n      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n      recognitionRef.current = new SpeechRecognition();\n      recognitionRef.current.continuous = true;\n      recognitionRef.current.interimResults = true;\n      recognitionRef.current.lang = 'en-US';\n\n      // Reset transcript\n      finalTranscriptRef.current = '';\n      setTranscript('');\n\n      // Set up event handlers\n      recognitionRef.current.onresult = (event) => {\n        let interimTranscript = '';\n\n        for (let i = event.resultIndex; i < event.results.length; i++) {\n          const result = event.results[i];\n          const text = result[0].transcript;\n\n          if (result.isFinal) {\n            finalTranscriptRef.current += text + ' ';\n          } else {\n            interimTranscript += text;\n          }\n        }\n\n        const fullTranscript = finalTranscriptRef.current + interimTranscript;\n        setTranscript(fullTranscript);\n\n        // Notify parent component\n        if (onTranscriptChange) {\n          onTranscriptChange(fullTranscript);\n        }\n      };\n\n      // Start recognition with the tab audio stream\n      recognitionRef.current.start();\n      setIsListening(true);\n      setIsCapturingTabAudio(true);\n\n      // Handle stream ending (user stops sharing)\n      stream.getVideoTracks()[0].onended = () => {\n        console.log(\"Tab sharing ended\");\n        stopTabAudioCapture();\n      };\n\n      // Store the stream for cleanup\n      window.tabAudioStream = stream;\n\n    } catch (error) {\n      console.error(\"Error capturing tab audio:\", error);\n      alert(\"Failed to capture tab audio: \" + error.message);\n    }\n  };\n\n  // Function to stop capturing tab audio\n  const stopTabAudioCapture = () => {\n    console.log(\"Stopping tab audio capture...\");\n\n    // Stop the stream\n    if (window.tabAudioStream) {\n      window.tabAudioStream.getTracks().forEach(track => {\n        track.stop();\n        console.log(`Stopped ${track.kind} track: ${track.label}`);\n      });\n      window.tabAudioStream = null;\n    }\n\n    // Stop recognition\n    if (recognitionRef.current && isListening) {\n      try {\n        recognitionRef.current.stop();\n      } catch (error) {\n        console.error(\"Error stopping recognition:\", error);\n      }\n    }\n\n    setIsListening(false);\n    setIsCapturingTabAudio(false);\n  };\n\n  return (\n    <div className=\"simple-transcriber\">\n      {!isSpeechRecognitionSupported && (\n        <div className=\"browser-warning\">\n          <p>\n            <strong>Warning:</strong> Your browser doesn't support speech recognition.\n            Please use Chrome, Edge, or another Chromium-based browser for this feature to work.\n          </p>\n        </div>\n      )}\n\n      <div className=\"transcript-display\">\n        {transcript ? (\n          <p className=\"transcript-text\">{transcript}</p>\n        ) : (\n          <p className=\"placeholder\">Waiting for speech or audio...</p>\n        )}\n      </div>\n\n      <div className=\"controls\">\n        <button\n          className={`control-button ${isListening && !isCapturingTabAudio ? 'listening' : ''}`}\n          onClick={isListening ? stopListening : startListening}\n          disabled={!isSpeechRecognitionSupported || isCapturingTabAudio}\n          title=\"Capture microphone audio\"\n        >\n          {isListening && !isCapturingTabAudio ? 'Stop' : 'Start'} Mic Listening\n        </button>\n\n        <button\n          className={`control-button tab-audio ${isCapturingTabAudio ? 'capturing' : ''}`}\n          onClick={isCapturingTabAudio ? stopTabAudioCapture : startTabAudioCapture}\n          disabled={!isSpeechRecognitionSupported}\n          title=\"Capture audio from another tab (e.g., YouTube)\"\n        >\n          {isCapturingTabAudio ? 'Stop' : 'Start'} Tab Audio Capture\n        </button>\n\n        {isListening && (\n          <div className=\"status\">\n            <span className=\"listening-indicator\"></span>\n            {isCapturingTabAudio ? 'Capturing Tab Audio...' : 'Listening to Microphone...'}\n          </div>\n        )}\n      </div>\n\n      {/* Add debug info */}\n      {process.env.NODE_ENV === 'development' && (\n        <div className=\"debug-info\">\n          <p>Speech Recognition Supported: {isSpeechRecognitionSupported ? 'Yes' : 'No'}</p>\n          <p>Is Listening: {isListening ? 'Yes' : 'No'}</p>\n          <p>Transcript Length: {transcript.length}</p>\n        </div>\n      )}\n    </div>\n  );\n}\n\nexport default SimpleTranscriber;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjC,SAASC,iBAAiBA,CAAC;EAAEC;AAAmB,CAAC,EAAE;EAAAC,EAAA;EACjD,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACU,WAAW,EAAEC,cAAc,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAMY,cAAc,GAAGV,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMW,kBAAkB,GAAGX,MAAM,CAAC,EAAE,CAAC;;EAErC;EACAD,SAAS,CAAC,MAAM;IACd;IACA,MAAMa,iBAAiB,GAAGC,MAAM,CAACD,iBAAiB,IAAIC,MAAM,CAACC,uBAAuB;IAEpF,IAAI,CAACF,iBAAiB,EAAE;MACtBG,OAAO,CAACC,KAAK,CAAC,kDAAkD,CAAC;MACjE;IACF;;IAEA;IACAN,cAAc,CAACO,OAAO,GAAG,IAAIL,iBAAiB,CAAC,CAAC;;IAEhD;IACAF,cAAc,CAACO,OAAO,CAACC,UAAU,GAAG,IAAI;IACxCR,cAAc,CAACO,OAAO,CAACE,cAAc,GAAG,IAAI;IAC5CT,cAAc,CAACO,OAAO,CAACG,IAAI,GAAG,OAAO;;IAErC;IACAV,cAAc,CAACO,OAAO,CAACI,QAAQ,GAAIC,KAAK,IAAK;MAC3C,IAAIC,iBAAiB,GAAG,EAAE;MAE1B,KAAK,IAAIC,CAAC,GAAGF,KAAK,CAACG,WAAW,EAAED,CAAC,GAAGF,KAAK,CAACI,OAAO,CAACC,MAAM,EAAEH,CAAC,EAAE,EAAE;QAC7D,MAAMI,MAAM,GAAGN,KAAK,CAACI,OAAO,CAACF,CAAC,CAAC;QAC/B,MAAMK,IAAI,GAAGD,MAAM,CAAC,CAAC,CAAC,CAACtB,UAAU;QAEjC,IAAIsB,MAAM,CAACE,OAAO,EAAE;UAClBnB,kBAAkB,CAACM,OAAO,IAAIY,IAAI,GAAG,GAAG;QAC1C,CAAC,MAAM;UACLN,iBAAiB,IAAIM,IAAI;QAC3B;MACF;MAEA,MAAME,cAAc,GAAGpB,kBAAkB,CAACM,OAAO,GAAGM,iBAAiB;MACrEhB,aAAa,CAACwB,cAAc,CAAC;;MAE7B;MACA,IAAI3B,kBAAkB,EAAE;QACtBA,kBAAkB,CAAC2B,cAAc,CAAC;MACpC;IACF,CAAC;IAEDrB,cAAc,CAACO,OAAO,CAACe,OAAO,GAAIV,KAAK,IAAK;MAC1CP,OAAO,CAACC,KAAK,CAAC,2BAA2B,EAAEM,KAAK,CAACN,KAAK,CAAC;;MAEvD;MACA,QAAQM,KAAK,CAACN,KAAK;QACjB,KAAK,WAAW;UACd;UACAD,OAAO,CAACkB,GAAG,CAAC,0CAA0C,CAAC;UACvD;QAEF,KAAK,SAAS;UACZ;UACAlB,OAAO,CAACkB,GAAG,CAAC,4CAA4C,CAAC;UACzDC,UAAU,CAAC,MAAM;YACf,IAAI1B,WAAW,EAAE;cACf,IAAI;gBACFE,cAAc,CAACO,OAAO,CAACkB,KAAK,CAAC,CAAC;gBAC9BpB,OAAO,CAACkB,GAAG,CAAC,mCAAmC,CAAC;cAClD,CAAC,CAAC,OAAOjB,KAAK,EAAE;gBACdD,OAAO,CAACC,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;gBACtDP,cAAc,CAAC,KAAK,CAAC;cACvB;YACF;UACF,CAAC,EAAE,GAAG,CAAC;UACP;QAEF;UACE;UACAM,OAAO,CAACC,KAAK,CAAC,6BAA6B,EAAEM,KAAK,CAACN,KAAK,CAAC;UACzDP,cAAc,CAAC,KAAK,CAAC;MACzB;IACF,CAAC;IAEDC,cAAc,CAACO,OAAO,CAACmB,KAAK,GAAG,MAAM;MACnCrB,OAAO,CAACkB,GAAG,CAAC,0BAA0B,CAAC;;MAEvC;MACA,IAAIzB,WAAW,EAAE;QACfO,OAAO,CAACkB,GAAG,CAAC,oDAAoD,CAAC;;QAEjE;QACAC,UAAU,CAAC,MAAM;UACf,IAAI1B,WAAW,IAAIE,cAAc,CAACO,OAAO,EAAE;YACzC,IAAI;cACFP,cAAc,CAACO,OAAO,CAACkB,KAAK,CAAC,CAAC;cAC9BpB,OAAO,CAACkB,GAAG,CAAC,oCAAoC,CAAC;YACnD,CAAC,CAAC,OAAOjB,KAAK,EAAE;cACdD,OAAO,CAACC,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;;cAEtD;cACA,IAAIA,KAAK,CAACqB,OAAO,IAAIrB,KAAK,CAACqB,OAAO,CAACC,QAAQ,CAAC,iBAAiB,CAAC,EAAE;gBAC9D,IAAI;kBACF5B,cAAc,CAACO,OAAO,CAACsB,IAAI,CAAC,CAAC;kBAC7BL,UAAU,CAAC,MAAM;oBACf,IAAI1B,WAAW,IAAIE,cAAc,CAACO,OAAO,EAAE;sBACzCP,cAAc,CAACO,OAAO,CAACkB,KAAK,CAAC,CAAC;sBAC9BpB,OAAO,CAACkB,GAAG,CAAC,sCAAsC,CAAC;oBACrD;kBACF,CAAC,EAAE,GAAG,CAAC;gBACT,CAAC,CAAC,OAAOO,SAAS,EAAE;kBAClBzB,OAAO,CAACC,KAAK,CAAC,6BAA6B,EAAEwB,SAAS,CAAC;kBACvD/B,cAAc,CAAC,KAAK,CAAC;gBACvB;cACF,CAAC,MAAM;gBACLA,cAAc,CAAC,KAAK,CAAC;cACvB;YACF;UACF;QACF,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,MAAM;QACLM,OAAO,CAACkB,GAAG,CAAC,mDAAmD,CAAC;MAClE;IACF,CAAC;;IAED;IACA,OAAO,MAAM;MACX,IAAIvB,cAAc,CAACO,OAAO,EAAE;QAC1B,IAAI;UACFP,cAAc,CAACO,OAAO,CAACsB,IAAI,CAAC,CAAC;QAC/B,CAAC,CAAC,OAAOvB,KAAK,EAAE;UACdD,OAAO,CAACC,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACrD;MACF;IACF,CAAC;EACH,CAAC,EAAE,CAACR,WAAW,EAAEJ,kBAAkB,CAAC,CAAC;;EAErC;EACA,MAAMqC,cAAc,GAAGA,CAAA,KAAM;IAC3B1B,OAAO,CAACkB,GAAG,CAAC,gCAAgC,CAAC;IAE7C,IAAI,CAACvB,cAAc,CAACO,OAAO,EAAE;MAC3BF,OAAO,CAACC,KAAK,CAAC,oCAAoC,CAAC;MACnD0B,KAAK,CAAC,uFAAuF,CAAC;MAC9F;IACF;IAEA,IAAIlC,WAAW,EAAE;MACfO,OAAO,CAACkB,GAAG,CAAC,2CAA2C,CAAC;MACxD;IACF;;IAEA;IACAtB,kBAAkB,CAACM,OAAO,GAAG,EAAE;IAC/BV,aAAa,CAAC,EAAE,CAAC;IAEjB,IAAI;MACFQ,OAAO,CAACkB,GAAG,CAAC,6BAA6B,CAAC;MAC1CvB,cAAc,CAACO,OAAO,CAACkB,KAAK,CAAC,CAAC;MAC9BpB,OAAO,CAACkB,GAAG,CAAC,kCAAkC,CAAC;MAC/CxB,cAAc,CAAC,IAAI,CAAC;;MAEpB;MACA,MAAMkC,iBAAiB,GAAGC,QAAQ,CAACC,aAAa,CAAC,qBAAqB,CAAC;MACvE,IAAIF,iBAAiB,EAAE;QACrBA,iBAAiB,CAACG,SAAS,CAACC,GAAG,CAAC,WAAW,CAAC;MAC9C;IAEF,CAAC,CAAC,OAAO/B,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;;MAEnD;MACA,IAAIA,KAAK,CAACqB,OAAO,IAAIrB,KAAK,CAACqB,OAAO,CAACC,QAAQ,CAAC,iBAAiB,CAAC,EAAE;QAC9DvB,OAAO,CAACkB,GAAG,CAAC,sDAAsD,CAAC;QAEnE,IAAI;UACFvB,cAAc,CAACO,OAAO,CAACsB,IAAI,CAAC,CAAC;;UAE7B;UACAL,UAAU,CAAC,MAAM;YACf,IAAI;cACFnB,OAAO,CAACkB,GAAG,CAAC,mCAAmC,CAAC;cAChDvB,cAAc,CAACO,OAAO,CAACkB,KAAK,CAAC,CAAC;cAC9B1B,cAAc,CAAC,IAAI,CAAC;;cAEpB;cACA,MAAMkC,iBAAiB,GAAGC,QAAQ,CAACC,aAAa,CAAC,qBAAqB,CAAC;cACvE,IAAIF,iBAAiB,EAAE;gBACrBA,iBAAiB,CAACG,SAAS,CAACC,GAAG,CAAC,WAAW,CAAC;cAC9C;YACF,CAAC,CAAC,OAAOC,YAAY,EAAE;cACrBjC,OAAO,CAACC,KAAK,CAAC,gCAAgC,EAAEgC,YAAY,CAAC;cAC7DN,KAAK,CAAC,4EAA4E,CAAC;YACrF;UACF,CAAC,EAAE,GAAG,CAAC;QACT,CAAC,CAAC,OAAOF,SAAS,EAAE;UAClBzB,OAAO,CAACC,KAAK,CAAC,6BAA6B,EAAEwB,SAAS,CAAC;QACzD;MACF,CAAC,MAAM;QACL;QACAE,KAAK,CAAC,sCAAsC,GAAG1B,KAAK,CAACqB,OAAO,CAAC;MAC/D;IACF;EACF,CAAC;;EAED;EACA,MAAMY,aAAa,GAAGA,CAAA,KAAM;IAC1BlC,OAAO,CAACkB,GAAG,CAAC,gCAAgC,CAAC;IAE7C,IAAI,CAACvB,cAAc,CAACO,OAAO,EAAE;MAC3BF,OAAO,CAACkB,GAAG,CAAC,+BAA+B,CAAC;MAC5CxB,cAAc,CAAC,KAAK,CAAC;MACrB;IACF;IAEA,IAAI,CAACD,WAAW,EAAE;MAChBO,OAAO,CAACkB,GAAG,CAAC,gCAAgC,CAAC;MAC7C;IACF;IAEA,IAAI;MACFlB,OAAO,CAACkB,GAAG,CAAC,4BAA4B,CAAC;MACzCvB,cAAc,CAACO,OAAO,CAACsB,IAAI,CAAC,CAAC;MAC7BxB,OAAO,CAACkB,GAAG,CAAC,kCAAkC,CAAC;MAC/CxB,cAAc,CAAC,KAAK,CAAC;;MAErB;MACA,MAAMkC,iBAAiB,GAAGC,QAAQ,CAACC,aAAa,CAAC,qBAAqB,CAAC;MACvE,IAAIF,iBAAiB,EAAE;QACrBA,iBAAiB,CAACG,SAAS,CAACI,MAAM,CAAC,WAAW,CAAC;MACjD;IAEF,CAAC,CAAC,OAAOlC,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;;MAEnD;MACAP,cAAc,CAAC,KAAK,CAAC;;MAErB;MACA,IAAI;QACF,MAAMG,iBAAiB,GAAGC,MAAM,CAACD,iBAAiB,IAAIC,MAAM,CAACC,uBAAuB;QACpFJ,cAAc,CAACO,OAAO,GAAG,IAAIL,iBAAiB,CAAC,CAAC;QAChDF,cAAc,CAACO,OAAO,CAACC,UAAU,GAAG,IAAI;QACxCR,cAAc,CAACO,OAAO,CAACE,cAAc,GAAG,IAAI;QAC5CT,cAAc,CAACO,OAAO,CAACG,IAAI,GAAG,OAAO;QACrCL,OAAO,CAACkB,GAAG,CAAC,+CAA+C,CAAC;MAC9D,CAAC,CAAC,OAAOkB,aAAa,EAAE;QACtBpC,OAAO,CAACC,KAAK,CAAC,wCAAwC,EAAEmC,aAAa,CAAC;MACxE;IACF;EACF,CAAC;;EAED;EACA,MAAMC,4BAA4B,GAAG,CAAC,EAAEvC,MAAM,CAACD,iBAAiB,IAAIC,MAAM,CAACC,uBAAuB,CAAC;;EAEnG;EACA,MAAM,CAACuC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;;EAErE;EACA,MAAMyD,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACFxC,OAAO,CAACkB,GAAG,CAAC,+BAA+B,CAAC;;MAE5C;MACA,MAAMuB,MAAM,GAAG,MAAMC,SAAS,CAACC,YAAY,CAACC,eAAe,CAAC;QAC1DC,KAAK,EAAE,IAAI;QACXC,KAAK,EAAE,IAAI,CAAC;MACd,CAAC,CAAC;;MAEF;MACA,MAAMC,WAAW,GAAGN,MAAM,CAACO,cAAc,CAAC,CAAC;MAC3C,IAAID,WAAW,CAACnC,MAAM,KAAK,CAAC,EAAE;QAC5Be,KAAK,CAAC,sFAAsF,CAAC;QAC7Fc,MAAM,CAACQ,SAAS,CAAC,CAAC,CAACC,OAAO,CAACC,KAAK,IAAIA,KAAK,CAAC3B,IAAI,CAAC,CAAC,CAAC;QACjD;MACF;MAEAxB,OAAO,CAACkB,GAAG,CAAC,wCAAwC,EAClDuB,MAAM,CAACQ,SAAS,CAAC,CAAC,CAACG,GAAG,CAACC,CAAC,IAAI,GAAGA,CAAC,CAACC,IAAI,KAAKD,CAAC,CAACE,KAAK,GAAG,CAAC,CAAC;;MAExD;MACA,IAAI5D,cAAc,CAACO,OAAO,IAAIT,WAAW,EAAE;QACzC,IAAI;UACFE,cAAc,CAACO,OAAO,CAACsB,IAAI,CAAC,CAAC;QAC/B,CAAC,CAAC,OAAOvB,KAAK,EAAE;UACdD,OAAO,CAACC,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;QAC9D;MACF;;MAEA;MACA,MAAMJ,iBAAiB,GAAGC,MAAM,CAACD,iBAAiB,IAAIC,MAAM,CAACC,uBAAuB;MACpFJ,cAAc,CAACO,OAAO,GAAG,IAAIL,iBAAiB,CAAC,CAAC;MAChDF,cAAc,CAACO,OAAO,CAACC,UAAU,GAAG,IAAI;MACxCR,cAAc,CAACO,OAAO,CAACE,cAAc,GAAG,IAAI;MAC5CT,cAAc,CAACO,OAAO,CAACG,IAAI,GAAG,OAAO;;MAErC;MACAT,kBAAkB,CAACM,OAAO,GAAG,EAAE;MAC/BV,aAAa,CAAC,EAAE,CAAC;;MAEjB;MACAG,cAAc,CAACO,OAAO,CAACI,QAAQ,GAAIC,KAAK,IAAK;QAC3C,IAAIC,iBAAiB,GAAG,EAAE;QAE1B,KAAK,IAAIC,CAAC,GAAGF,KAAK,CAACG,WAAW,EAAED,CAAC,GAAGF,KAAK,CAACI,OAAO,CAACC,MAAM,EAAEH,CAAC,EAAE,EAAE;UAC7D,MAAMI,MAAM,GAAGN,KAAK,CAACI,OAAO,CAACF,CAAC,CAAC;UAC/B,MAAMK,IAAI,GAAGD,MAAM,CAAC,CAAC,CAAC,CAACtB,UAAU;UAEjC,IAAIsB,MAAM,CAACE,OAAO,EAAE;YAClBnB,kBAAkB,CAACM,OAAO,IAAIY,IAAI,GAAG,GAAG;UAC1C,CAAC,MAAM;YACLN,iBAAiB,IAAIM,IAAI;UAC3B;QACF;QAEA,MAAME,cAAc,GAAGpB,kBAAkB,CAACM,OAAO,GAAGM,iBAAiB;QACrEhB,aAAa,CAACwB,cAAc,CAAC;;QAE7B;QACA,IAAI3B,kBAAkB,EAAE;UACtBA,kBAAkB,CAAC2B,cAAc,CAAC;QACpC;MACF,CAAC;;MAED;MACArB,cAAc,CAACO,OAAO,CAACkB,KAAK,CAAC,CAAC;MAC9B1B,cAAc,CAAC,IAAI,CAAC;MACpB6C,sBAAsB,CAAC,IAAI,CAAC;;MAE5B;MACAE,MAAM,CAACe,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,OAAO,GAAG,MAAM;QACzCzD,OAAO,CAACkB,GAAG,CAAC,mBAAmB,CAAC;QAChCwC,mBAAmB,CAAC,CAAC;MACvB,CAAC;;MAED;MACA5D,MAAM,CAAC6D,cAAc,GAAGlB,MAAM;IAEhC,CAAC,CAAC,OAAOxC,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD0B,KAAK,CAAC,+BAA+B,GAAG1B,KAAK,CAACqB,OAAO,CAAC;IACxD;EACF,CAAC;;EAED;EACA,MAAMoC,mBAAmB,GAAGA,CAAA,KAAM;IAChC1D,OAAO,CAACkB,GAAG,CAAC,+BAA+B,CAAC;;IAE5C;IACA,IAAIpB,MAAM,CAAC6D,cAAc,EAAE;MACzB7D,MAAM,CAAC6D,cAAc,CAACV,SAAS,CAAC,CAAC,CAACC,OAAO,CAACC,KAAK,IAAI;QACjDA,KAAK,CAAC3B,IAAI,CAAC,CAAC;QACZxB,OAAO,CAACkB,GAAG,CAAC,WAAWiC,KAAK,CAACG,IAAI,WAAWH,KAAK,CAACI,KAAK,EAAE,CAAC;MAC5D,CAAC,CAAC;MACFzD,MAAM,CAAC6D,cAAc,GAAG,IAAI;IAC9B;;IAEA;IACA,IAAIhE,cAAc,CAACO,OAAO,IAAIT,WAAW,EAAE;MACzC,IAAI;QACFE,cAAc,CAACO,OAAO,CAACsB,IAAI,CAAC,CAAC;MAC/B,CAAC,CAAC,OAAOvB,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACrD;IACF;IAEAP,cAAc,CAAC,KAAK,CAAC;IACrB6C,sBAAsB,CAAC,KAAK,CAAC;EAC/B,CAAC;EAED,oBACEpD,OAAA;IAAKyE,SAAS,EAAC,oBAAoB;IAAAC,QAAA,GAChC,CAACxB,4BAA4B,iBAC5BlD,OAAA;MAAKyE,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAC9B1E,OAAA;QAAA0E,QAAA,gBACE1E,OAAA;UAAA0E,QAAA,EAAQ;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,0IAE3B;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACN,eAED9E,OAAA;MAAKyE,SAAS,EAAC,oBAAoB;MAAAC,QAAA,EAChCtE,UAAU,gBACTJ,OAAA;QAAGyE,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAEtE;MAAU;QAAAuE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,gBAE/C9E,OAAA;QAAGyE,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAA8B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAC7D;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEN9E,OAAA;MAAKyE,SAAS,EAAC,UAAU;MAAAC,QAAA,gBACvB1E,OAAA;QACEyE,SAAS,EAAE,kBAAkBnE,WAAW,IAAI,CAAC6C,mBAAmB,GAAG,WAAW,GAAG,EAAE,EAAG;QACtF4B,OAAO,EAAEzE,WAAW,GAAGyC,aAAa,GAAGR,cAAe;QACtDyC,QAAQ,EAAE,CAAC9B,4BAA4B,IAAIC,mBAAoB;QAC/D8B,KAAK,EAAC,0BAA0B;QAAAP,QAAA,GAE/BpE,WAAW,IAAI,CAAC6C,mBAAmB,GAAG,MAAM,GAAG,OAAO,EAAC,gBAC1D;MAAA;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAET9E,OAAA;QACEyE,SAAS,EAAE,4BAA4BtB,mBAAmB,GAAG,WAAW,GAAG,EAAE,EAAG;QAChF4B,OAAO,EAAE5B,mBAAmB,GAAGoB,mBAAmB,GAAGlB,oBAAqB;QAC1E2B,QAAQ,EAAE,CAAC9B,4BAA6B;QACxC+B,KAAK,EAAC,gDAAgD;QAAAP,QAAA,GAErDvB,mBAAmB,GAAG,MAAM,GAAG,OAAO,EAAC,oBAC1C;MAAA;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EAERxE,WAAW,iBACVN,OAAA;QAAKyE,SAAS,EAAC,QAAQ;QAAAC,QAAA,gBACrB1E,OAAA;UAAMyE,SAAS,EAAC;QAAqB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EAC5C3B,mBAAmB,GAAG,wBAAwB,GAAG,4BAA4B;MAAA;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLI,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,iBACrCpF,OAAA;MAAKyE,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzB1E,OAAA;QAAA0E,QAAA,GAAG,gCAA8B,EAACxB,4BAA4B,GAAG,KAAK,GAAG,IAAI;MAAA;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAClF9E,OAAA;QAAA0E,QAAA,GAAG,gBAAc,EAACpE,WAAW,GAAG,KAAK,GAAG,IAAI;MAAA;QAAAqE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACjD9E,OAAA;QAAA0E,QAAA,GAAG,qBAAmB,EAACtE,UAAU,CAACqB,MAAM;MAAA;QAAAkD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAAC3E,EAAA,CAvaQF,iBAAiB;AAAAoF,EAAA,GAAjBpF,iBAAiB;AAya1B,eAAeA,iBAAiB;AAAC,IAAAoF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}