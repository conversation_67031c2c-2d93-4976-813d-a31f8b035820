import React, { useState, useEffect, useRef } from 'react';
import './InterviewSession.css';
import AccessTimeIcon from '@mui/icons-material/AccessTime';

function InterviewSession({ config, onEndSession }) {
  const [questions, setQuestions] = useState([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [userAnswer, setUserAnswer] = useState('');
  const [feedback, setFeedback] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [resumeInfo, setResumeInfo] = useState({
    hasResume: false,
    fileName: ''
  });

  // Session timer state
  const [sessionTimeRemaining, setSessionTimeRemaining] = useState(config.sessionTimeRemaining || 5);
  const [sessionTimeDisplay, setSessionTimeDisplay] = useState('05:00');
  const sessionTimerRef = useRef(null);

  // Format time as MM:SS
  const formatTime = (minutes) => {
    const mins = Math.floor(minutes);
    const secs = Math.round((minutes - mins) * 60);
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Start session timer
  useEffect(() => {
    // Initialize timer display
    setSessionTimeDisplay(formatTime(sessionTimeRemaining));

    // Start countdown timer
    sessionTimerRef.current = setInterval(() => {
      setSessionTimeRemaining(prev => {
        const newTime = prev - (1/60); // Decrease by 1 second (1/60 of a minute)

        // Update display
        setSessionTimeDisplay(formatTime(newTime));

        // Check if time is up
        if (newTime <= 0) {
          clearInterval(sessionTimerRef.current);
          return 0;
        }

        return newTime;
      });
    }, 1000);

    // Clean up timer on unmount
    return () => {
      if (sessionTimerRef.current) {
        clearInterval(sessionTimerRef.current);
      }
    };
  }, []);

  // Save remaining time to localStorage when component unmounts
  useEffect(() => {
    return () => {
      try {
        const userDetails = localStorage.getItem('userDetails');
        let userData = userDetails ? JSON.parse(userDetails) : {};

        userData.timeRemaining = sessionTimeRemaining;
        localStorage.setItem('userDetails', JSON.stringify(userData));
      } catch (error) {
        console.error('Error saving session time to localStorage:', error);
      }
    };
  }, [sessionTimeRemaining]);

  useEffect(() => {
    // Check if resume was provided
    if (config.resumeFile) {
      setResumeInfo({
        hasResume: true,
        fileName: config.resumeFile.name
      });
    }

    // Generate questions based on job title and skillset
    setTimeout(() => {
      let dummyQuestions = [
        `Tell me about your experience as a ${config.jobTitle}.`,
        "Describe a challenging project you worked on recently.",
        "How do you handle tight deadlines?",
        "What are your strengths and weaknesses?",
        "Where do you see yourself in 5 years?"
      ];

      // Add skillset-specific questions if provided
      if (config.skillset) {
        const skills = config.skillset.split(',').map(skill => skill.trim());
        skills.forEach(skill => {
          if (skill) {
            dummyQuestions.push(`Tell me about your experience with ${skill}.`);
          }
        });
      }

      // Add resume-specific question if resume was provided
      if (config.resumeFile) {
        dummyQuestions.push("Based on your resume, can you elaborate on your most relevant experience for this role?");
        dummyQuestions.push("I see from your resume that you have experience with [skill]. How have you applied this in your work?");
      }

      // Add job description-specific questions if provided
      if (config.jobDescription) {
        dummyQuestions.push("Based on the job description, what makes you a good fit for this role?");
        dummyQuestions.push("How do your skills align with the requirements mentioned in the job description?");
      }

      setQuestions(dummyQuestions);
      setIsLoading(false);
    }, 1500);
  }, [config]);

  const handleSubmitAnswer = () => {
    // In a real app, you would send the answer to an API for feedback
    setFeedback({
      score: 4,
      comments: "Good answer! Consider adding more specific examples to illustrate your points."
    });
  };

  const handleNextQuestion = () => {
    setCurrentQuestionIndex(currentQuestionIndex + 1);
    setUserAnswer('');
    setFeedback(null);
  };

  if (isLoading) {
    return (
      <div className="loading">
        <div className="spinner"></div>
        <p>Preparing your interview questions{resumeInfo.hasResume ? ' based on your resume' : ''}...</p>
        {resumeInfo.hasResume && (
          <p className="resume-info">Using resume: {resumeInfo.fileName}</p>
        )}
      </div>
    );
  }

  if (currentQuestionIndex >= questions.length) {
    return (
      <div className="interview-complete">
        <h2>Interview Complete!</h2>
        <p>You've completed all the questions. Great job!</p>
        <button onClick={onEndSession}>Start New Interview</button>
      </div>
    );
  }

  return (
    <div className="interview-session">
      <div className="session-header">
        <div className="question-counter">
          Question {currentQuestionIndex + 1} of {questions.length}
        </div>

        <div className="session-timer">
          <AccessTimeIcon className="timer-icon" />
          <span className="timer-display">Time Remaining: {sessionTimeDisplay}</span>
        </div>
      </div>

      <div className="question-card">
        <h3>{questions[currentQuestionIndex]}</h3>

        <textarea
          value={userAnswer}
          onChange={(e) => setUserAnswer(e.target.value)}
          placeholder="Type your answer here..."
          disabled={!!feedback || sessionTimeRemaining <= 0}
        />

        {sessionTimeRemaining <= 0 ? (
          <div className="time-expired">
            <h4>Session Time Expired</h4>
            <p>Your session time has ended. Please start a new session or make a payment to continue.</p>
            <button onClick={onEndSession}>
              End Session
            </button>
          </div>
        ) : !feedback ? (
          <button
            onClick={handleSubmitAnswer}
            disabled={!userAnswer.trim()}
          >
            Submit Answer
          </button>
        ) : (
          <div className="feedback">
            <h4>Feedback</h4>
            <div className="score">Score: {feedback.score}/5</div>
            <p>{feedback.comments}</p>
            <button onClick={handleNextQuestion}>
              {currentQuestionIndex < questions.length - 1 ? 'Next Question' : 'Finish Interview'}
            </button>
          </div>
        )}
      </div>

      <button className="end-session" onClick={onEndSession}>End Session</button>
    </div>
  );
}

export default InterviewSession;