{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\interview-ai-app\\\\interviewAI\\\\client\\\\src\\\\pages\\\\home\\\\index.jsx\",\n  _s = $RefreshSig$();\nimport React, { useRef } from 'react';\nimport { Link } from 'react-router-dom';\nimport Header from '../../components/Header';\nimport Footer from '../../components/Footer';\nimport './home.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction HomeScreen() {\n  _s();\n  const pricingRef = useRef(null);\n  const scrollToPricing = e => {\n    var _pricingRef$current;\n    e.preventDefault();\n    (_pricingRef$current = pricingRef.current) === null || _pricingRef$current === void 0 ? void 0 : _pricingRef$current.scrollIntoView({\n      behavior: 'smooth'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"home-page\",\n    children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"home-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"hero-section\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"container\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            children: \"Prepare for Interviews with AI\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"subtitle\",\n            children: \"Practice, improve, and land your dream job with our AI-powered interview coach\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"cta-buttons\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/automated-interview\",\n              className: \"cta-button primary\",\n              children: \"Try Automated Interview\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/login\",\n              className: \"cta-button secondary\",\n              children: \"Try Manual Interview\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#pricing\",\n              className: \"cta-button secondary\",\n              onClick: scrollToPricing,\n              children: \"View Plans\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/live-transcription\",\n              className: \"cta-button secondary\",\n              children: \"Live Transcription Demo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/diarized-interview\",\n              className: \"cta-button highlight\",\n              children: \"Enhanced Interview with Speaker Diarization\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 29,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/automatic-diarization\",\n              className: \"cta-button highlight-new\",\n              children: \"Automatic Speaker Detection\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 30,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"features-section\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"container\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Features\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"features-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"feature-icon\",\n                children: \"\\uD83C\\uDF99\\uFE0F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 40,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Real-time Voice Analysis\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 41,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Practice your answers with our AI that provides instant feedback on your delivery\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 42,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 39,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"feature-icon\",\n                children: \"\\uD83D\\uDCBC\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 45,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Industry-specific Questions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 46,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Get questions tailored to your industry and role\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 47,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"feature-icon\",\n                children: \"\\uD83D\\uDCDD\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 50,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Personalized Feedback\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 51,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Receive detailed feedback to improve your interview skills\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 52,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-card highlight\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"feature-icon\",\n                children: \"\\uD83D\\uDD04\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 55,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Automated Interview Simulation\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 56,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Experience our new automated interview feature with screen sharing and automatic voice transcription that responds to your answers in real-time\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 57,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        id: \"pricing\",\n        className: \"pricing-section\",\n        ref: pricingRef,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"container\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Choose Your Plan\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"pricing-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"pricing-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"pricing-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: \"FREE\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 69,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"price\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"currency\",\n                    children: \"$\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 71,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"amount\",\n                    children: \"0\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 72,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"period\",\n                    children: \"/ 15Min Free\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 73,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 70,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 68,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"pricing-features\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item available\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"check\",\n                    children: \"\\u2713\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 78,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"15-min Interview Sessions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 79,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 77,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item available\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"check\",\n                    children: \"\\u2713\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 82,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"AI Resume Builder\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 83,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 81,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item available\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"check\",\n                    children: \"\\u2713\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 86,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"AI Story Editor\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 87,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 85,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item unavailable\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"cross\",\n                    children: \"\\u2715\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 90,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Full Customization Suite\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 91,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 89,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item unavailable\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"cross\",\n                    children: \"\\u2715\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 94,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Industry Knowledge Base Add-Ons\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 95,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 93,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item unavailable\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"cross\",\n                    children: \"\\u2715\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 98,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Coding Interview Practice\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 99,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 97,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item unavailable\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"cross\",\n                    children: \"\\u2715\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 102,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"AI Playground\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 103,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 101,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 76,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/signup/free\",\n                className: \"pricing-cta\",\n                children: \"Try for free\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"pricing-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"pricing-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: \"PRO\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 111,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"price\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"currency\",\n                    children: \"$\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 113,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"amount\",\n                    children: \"4.99\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 114,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"period\",\n                    children: \"/ hourly\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 115,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 112,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"pricing-features\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item available\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"check\",\n                    children: \"\\u2713\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 120,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Unlimited Interview Sessions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 121,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 119,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item available\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"check\",\n                    children: \"\\u2713\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 124,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"AI Resume Builder\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 125,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 123,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item available\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"check\",\n                    children: \"\\u2713\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 128,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"AI Story Editor\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 129,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 127,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item available\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"check\",\n                    children: \"\\u2713\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 132,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Full Customization Suite\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 133,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 131,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item available\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"check\",\n                    children: \"\\u2713\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 136,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Industry Knowledge Base Add-Ons\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 137,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 135,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item available\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"check\",\n                    children: \"\\u2713\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 140,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Coding Interview Practice\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 141,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 139,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item available\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"check\",\n                    children: \"\\u2713\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 144,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"AI Playground\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 145,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 143,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/signup/pro\",\n                className: \"pricing-cta\",\n                children: \"Get the plan\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"faq-section\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"container\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Frequently Asked Questions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"faq-list\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"faq-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"How does the AI interview coach work?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Our AI interview coach uses advanced natural language processing to analyze your responses, provide feedback on your delivery, and suggest improvements to help you ace your interviews.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"faq-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Can I use this for technical interviews?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Yes! Our PRO plan includes specialized modules for technical and coding interviews across various programming languages and frameworks.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"faq-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Is my data secure?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"We take data privacy seriously. All your interview sessions and personal information are encrypted and never shared with third parties.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"faq-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Can I cancel my subscription anytime?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Absolutely. You can cancel your subscription at any time with no questions asked.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"faq-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Do you offer support?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Yes, we provide 24/7 customer support via email and live chat for all our users.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 242,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 5\n  }, this);\n}\n_s(HomeScreen, \"laDXekn2mJuIrVIUszvgabpzV48=\");\n_c = HomeScreen;\nexport default HomeScreen;\nvar _c;\n$RefreshReg$(_c, \"HomeScreen\");", "map": {"version": 3, "names": ["React", "useRef", "Link", "Header", "Footer", "jsxDEV", "_jsxDEV", "HomeScreen", "_s", "pricingRef", "scrollToPricing", "e", "_pricingRef$current", "preventDefault", "current", "scrollIntoView", "behavior", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "href", "onClick", "id", "ref", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/interview-ai-app/interviewAI/client/src/pages/home/<USER>"], "sourcesContent": ["import React, { useRef } from 'react';\nimport { Link } from 'react-router-dom';\nimport Header from '../../components/Header';\nimport Footer from '../../components/Footer';\nimport './home.css';\n\nfunction HomeScreen() {\n  const pricingRef = useRef(null);\n\n  const scrollToPricing = (e) => {\n    e.preventDefault();\n    pricingRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  return (\n    <div className=\"home-page\">\n      <Header />\n\n      <main className=\"home-content\">\n        <section className=\"hero-section\">\n          <div className=\"container\">\n            <h1>Prepare for Interviews with AI</h1>\n            <p className=\"subtitle\">Practice, improve, and land your dream job with our AI-powered interview coach</p>\n            <div className=\"cta-buttons\">\n              <Link to=\"/automated-interview\" className=\"cta-button primary\">Try Automated Interview</Link>\n              <Link to=\"/login\" className=\"cta-button secondary\">Try Manual Interview</Link>\n              <a href=\"#pricing\" className=\"cta-button secondary\" onClick={scrollToPricing}>View Plans</a>\n              <Link to=\"/live-transcription\" className=\"cta-button secondary\">Live Transcription Demo</Link>\n              <Link to=\"/diarized-interview\" className=\"cta-button highlight\">Enhanced Interview with Speaker Diarization</Link>\n              <Link to=\"/automatic-diarization\" className=\"cta-button highlight-new\">Automatic Speaker Detection</Link>\n            </div>\n          </div>\n        </section>\n\n        <section className=\"features-section\">\n          <div className=\"container\">\n            <h2>Features</h2>\n            <div className=\"features-grid\">\n              <div className=\"feature-card\">\n                <div className=\"feature-icon\">🎙️</div>\n                <h3>Real-time Voice Analysis</h3>\n                <p>Practice your answers with our AI that provides instant feedback on your delivery</p>\n              </div>\n              <div className=\"feature-card\">\n                <div className=\"feature-icon\">💼</div>\n                <h3>Industry-specific Questions</h3>\n                <p>Get questions tailored to your industry and role</p>\n              </div>\n              <div className=\"feature-card\">\n                <div className=\"feature-icon\">📝</div>\n                <h3>Personalized Feedback</h3>\n                <p>Receive detailed feedback to improve your interview skills</p>\n              </div>\n              <div className=\"feature-card highlight\">\n                <div className=\"feature-icon\">🔄</div>\n                <h3>Automated Interview Simulation</h3>\n                <p>Experience our new automated interview feature with screen sharing and automatic voice transcription that responds to your answers in real-time</p>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        <section id=\"pricing\" className=\"pricing-section\" ref={pricingRef}>\n          <div className=\"container\">\n            <h2>Choose Your Plan</h2>\n            <div className=\"pricing-grid\">\n              <div className=\"pricing-card\">\n                <div className=\"pricing-header\">\n                  <h3>FREE</h3>\n                  <div className=\"price\">\n                    <span className=\"currency\">$</span>\n                    <span className=\"amount\">0</span>\n                    <span className=\"period\">/ 15Min Free</span>\n                  </div>\n                </div>\n                <div className=\"pricing-features\">\n                  <div className=\"feature-item available\">\n                    <span className=\"check\">✓</span>\n                    <span>15-min Interview Sessions</span>\n                  </div>\n                  <div className=\"feature-item available\">\n                    <span className=\"check\">✓</span>\n                    <span>AI Resume Builder</span>\n                  </div>\n                  <div className=\"feature-item available\">\n                    <span className=\"check\">✓</span>\n                    <span>AI Story Editor</span>\n                  </div>\n                  <div className=\"feature-item unavailable\">\n                    <span className=\"cross\">✕</span>\n                    <span>Full Customization Suite</span>\n                  </div>\n                  <div className=\"feature-item unavailable\">\n                    <span className=\"cross\">✕</span>\n                    <span>Industry Knowledge Base Add-Ons</span>\n                  </div>\n                  <div className=\"feature-item unavailable\">\n                    <span className=\"cross\">✕</span>\n                    <span>Coding Interview Practice</span>\n                  </div>\n                  <div className=\"feature-item unavailable\">\n                    <span className=\"cross\">✕</span>\n                    <span>AI Playground</span>\n                  </div>\n                </div>\n                <Link to=\"/signup/free\" className=\"pricing-cta\">Try for free</Link>\n              </div>\n\n              <div className=\"pricing-card\">\n                <div className=\"pricing-header\">\n                  <h3>PRO</h3>\n                  <div className=\"price\">\n                    <span className=\"currency\">$</span>\n                    <span className=\"amount\">4.99</span>\n                    <span className=\"period\">/ hourly</span>\n                  </div>\n                </div>\n                <div className=\"pricing-features\">\n                  <div className=\"feature-item available\">\n                    <span className=\"check\">✓</span>\n                    <span>Unlimited Interview Sessions</span>\n                  </div>\n                  <div className=\"feature-item available\">\n                    <span className=\"check\">✓</span>\n                    <span>AI Resume Builder</span>\n                  </div>\n                  <div className=\"feature-item available\">\n                    <span className=\"check\">✓</span>\n                    <span>AI Story Editor</span>\n                  </div>\n                  <div className=\"feature-item available\">\n                    <span className=\"check\">✓</span>\n                    <span>Full Customization Suite</span>\n                  </div>\n                  <div className=\"feature-item available\">\n                    <span className=\"check\">✓</span>\n                    <span>Industry Knowledge Base Add-Ons</span>\n                  </div>\n                  <div className=\"feature-item available\">\n                    <span className=\"check\">✓</span>\n                    <span>Coding Interview Practice</span>\n                  </div>\n                  <div className=\"feature-item available\">\n                    <span className=\"check\">✓</span>\n                    <span>AI Playground</span>\n                  </div>\n                </div>\n                <Link to=\"/signup/pro\" className=\"pricing-cta\">Get the plan</Link>\n              </div>\n\n              {/* <div className=\"pricing-card premium\">\n                <div className=\"pricing-badge\">Most Popular</div>\n                <div className=\"pricing-header\">\n                  <h3>PRO PLUS</h3>\n                  <div className=\"price\">\n                    <span className=\"currency\">$</span>\n                    <span className=\"amount\">29.99</span>\n                    <span className=\"period\">/ month billed annually</span>\n                  </div>\n                </div>\n                <div className=\"pricing-features\">\n                  <div className=\"feature-item available\">\n                    <span className=\"check\">✓</span>\n                    <span>Everything in PRO plan</span>\n                  </div>\n                  <div className=\"feature-item available\">\n                    <span className=\"check\">✓</span>\n                    <span>Multi-device support (answer from any device)</span>\n                  </div>\n                  <div className=\"feature-item available\">\n                    <span className=\"check\">✓</span>\n                    <span>No eye movement detection</span>\n                  </div>\n                  <div className=\"feature-item available\">\n                    <span className=\"check\">✓</span>\n                    <span>All meeting platform integration</span>\n                  </div>\n                  <div className=\"feature-item available\">\n                    <span className=\"check\">✓</span>\n                    <span>Pre-trained on your technical skills</span>\n                  </div>\n                  <div className=\"feature-item available\">\n                    <span className=\"check\">✓</span>\n                    <span>Undetectable assistance</span>\n                  </div>\n                  <div className=\"feature-item available\">\n                    <span className=\"check\">✓</span>\n                    <span>Support for all industries & technologies</span>\n                  </div>\n                  <div className=\"feature-item available\">\n                    <span className=\"check\">✓</span>\n                    <span>Multiple language support</span>\n                  </div>\n                  <div className=\"feature-item available\">\n                    <span className=\"check\">✓</span>\n                    <span>Create designs & architecture diagrams</span>\n                  </div>\n                  <div className=\"feature-item available\">\n                    <span className=\"check\">✓</span>\n                    <span>Zero latency (custom optimized model)</span>\n                  </div>\n                  <div className=\"feature-item available\">\n                    <span className=\"check\">✓</span>\n                    <span>Multiple answers before time limit</span>\n                  </div>\n                </div>\n                <Link to=\"/signup/premium\" className=\"pricing-cta\">Get Premium</Link>\n              </div> */}\n            </div>\n          </div>\n        </section>\n\n        <section className=\"faq-section\">\n          <div className=\"container\">\n            <h2>Frequently Asked Questions</h2>\n            <div className=\"faq-list\">\n              <div className=\"faq-item\">\n                <h3>How does the AI interview coach work?</h3>\n                <p>Our AI interview coach uses advanced natural language processing to analyze your responses, provide feedback on your delivery, and suggest improvements to help you ace your interviews.</p>\n              </div>\n              <div className=\"faq-item\">\n                <h3>Can I use this for technical interviews?</h3>\n                <p>Yes! Our PRO plan includes specialized modules for technical and coding interviews across various programming languages and frameworks.</p>\n              </div>\n              <div className=\"faq-item\">\n                <h3>Is my data secure?</h3>\n                <p>We take data privacy seriously. All your interview sessions and personal information are encrypted and never shared with third parties.</p>\n              </div>\n              <div className=\"faq-item\">\n                <h3>Can I cancel my subscription anytime?</h3>\n                <p>Absolutely. You can cancel your subscription at any time with no questions asked.</p>\n              </div>\n              <div className=\"faq-item\">\n                <h3>Do you offer support?</h3>\n                <p>Yes, we provide 24/7 customer support via email and live chat for all our users.</p>\n              </div>\n            </div>\n          </div>\n        </section>\n      </main>\n\n      <Footer />\n    </div>\n  );\n}\n\nexport default HomeScreen;\n\n\n\n\n\n\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,MAAM,QAAQ,OAAO;AACrC,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAO,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpB,SAASC,UAAUA,CAAA,EAAG;EAAAC,EAAA;EACpB,MAAMC,UAAU,GAAGR,MAAM,CAAC,IAAI,CAAC;EAE/B,MAAMS,eAAe,GAAIC,CAAC,IAAK;IAAA,IAAAC,mBAAA;IAC7BD,CAAC,CAACE,cAAc,CAAC,CAAC;IAClB,CAAAD,mBAAA,GAAAH,UAAU,CAACK,OAAO,cAAAF,mBAAA,uBAAlBA,mBAAA,CAAoBG,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAC5D,CAAC;EAED,oBACEV,OAAA;IAAKW,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBZ,OAAA,CAACH,MAAM;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEVhB,OAAA;MAAMW,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC5BZ,OAAA;QAASW,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC/BZ,OAAA;UAAKW,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBZ,OAAA;YAAAY,QAAA,EAAI;UAA8B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvChB,OAAA;YAAGW,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAC;UAA8E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC1GhB,OAAA;YAAKW,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BZ,OAAA,CAACJ,IAAI;cAACqB,EAAE,EAAC,sBAAsB;cAACN,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAuB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7FhB,OAAA,CAACJ,IAAI;cAACqB,EAAE,EAAC,QAAQ;cAACN,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC9EhB,OAAA;cAAGkB,IAAI,EAAC,UAAU;cAACP,SAAS,EAAC,sBAAsB;cAACQ,OAAO,EAAEf,eAAgB;cAAAQ,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC5FhB,OAAA,CAACJ,IAAI;cAACqB,EAAE,EAAC,qBAAqB;cAACN,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAAC;YAAuB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC9FhB,OAAA,CAACJ,IAAI;cAACqB,EAAE,EAAC,qBAAqB;cAACN,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAAC;YAA2C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClHhB,OAAA,CAACJ,IAAI;cAACqB,EAAE,EAAC,wBAAwB;cAACN,SAAS,EAAC,0BAA0B;cAAAC,QAAA,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEVhB,OAAA;QAASW,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eACnCZ,OAAA;UAAKW,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBZ,OAAA;YAAAY,QAAA,EAAI;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjBhB,OAAA;YAAKW,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BZ,OAAA;cAAKW,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BZ,OAAA;gBAAKW,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvChB,OAAA;gBAAAY,QAAA,EAAI;cAAwB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjChB,OAAA;gBAAAY,QAAA,EAAG;cAAiF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrF,CAAC,eACNhB,OAAA;cAAKW,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BZ,OAAA;gBAAKW,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtChB,OAAA;gBAAAY,QAAA,EAAI;cAA2B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpChB,OAAA;gBAAAY,QAAA,EAAG;cAAgD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eACNhB,OAAA;cAAKW,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BZ,OAAA;gBAAKW,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtChB,OAAA;gBAAAY,QAAA,EAAI;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9BhB,OAAA;gBAAAY,QAAA,EAAG;cAA0D;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9D,CAAC,eACNhB,OAAA;cAAKW,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrCZ,OAAA;gBAAKW,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtChB,OAAA;gBAAAY,QAAA,EAAI;cAA8B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvChB,OAAA;gBAAAY,QAAA,EAAG;cAA+I;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEVhB,OAAA;QAASoB,EAAE,EAAC,SAAS;QAACT,SAAS,EAAC,iBAAiB;QAACU,GAAG,EAAElB,UAAW;QAAAS,QAAA,eAChEZ,OAAA;UAAKW,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBZ,OAAA;YAAAY,QAAA,EAAI;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzBhB,OAAA;YAAKW,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BZ,OAAA;cAAKW,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BZ,OAAA;gBAAKW,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BZ,OAAA;kBAAAY,QAAA,EAAI;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACbhB,OAAA;kBAAKW,SAAS,EAAC,OAAO;kBAAAC,QAAA,gBACpBZ,OAAA;oBAAMW,SAAS,EAAC,UAAU;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACnChB,OAAA;oBAAMW,SAAS,EAAC,QAAQ;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACjChB,OAAA;oBAAMW,SAAS,EAAC,QAAQ;oBAAAC,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNhB,OAAA;gBAAKW,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BZ,OAAA;kBAAKW,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrCZ,OAAA;oBAAMW,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChChB,OAAA;oBAAAY,QAAA,EAAM;kBAAyB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,eACNhB,OAAA;kBAAKW,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrCZ,OAAA;oBAAMW,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChChB,OAAA;oBAAAY,QAAA,EAAM;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,eACNhB,OAAA;kBAAKW,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrCZ,OAAA;oBAAMW,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChChB,OAAA;oBAAAY,QAAA,EAAM;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eACNhB,OAAA;kBAAKW,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,gBACvCZ,OAAA;oBAAMW,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChChB,OAAA;oBAAAY,QAAA,EAAM;kBAAwB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC,eACNhB,OAAA;kBAAKW,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,gBACvCZ,OAAA;oBAAMW,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChChB,OAAA;oBAAAY,QAAA,EAAM;kBAA+B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CAAC,eACNhB,OAAA;kBAAKW,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,gBACvCZ,OAAA;oBAAMW,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChChB,OAAA;oBAAAY,QAAA,EAAM;kBAAyB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,eACNhB,OAAA;kBAAKW,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,gBACvCZ,OAAA;oBAAMW,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChChB,OAAA;oBAAAY,QAAA,EAAM;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNhB,OAAA,CAACJ,IAAI;gBAACqB,EAAE,EAAC,cAAc;gBAACN,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC,eAENhB,OAAA;cAAKW,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BZ,OAAA;gBAAKW,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BZ,OAAA;kBAAAY,QAAA,EAAI;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACZhB,OAAA;kBAAKW,SAAS,EAAC,OAAO;kBAAAC,QAAA,gBACpBZ,OAAA;oBAAMW,SAAS,EAAC,UAAU;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACnChB,OAAA;oBAAMW,SAAS,EAAC,QAAQ;oBAAAC,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACpChB,OAAA;oBAAMW,SAAS,EAAC,QAAQ;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNhB,OAAA;gBAAKW,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BZ,OAAA;kBAAKW,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrCZ,OAAA;oBAAMW,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChChB,OAAA;oBAAAY,QAAA,EAAM;kBAA4B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC,eACNhB,OAAA;kBAAKW,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrCZ,OAAA;oBAAMW,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChChB,OAAA;oBAAAY,QAAA,EAAM;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,eACNhB,OAAA;kBAAKW,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrCZ,OAAA;oBAAMW,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChChB,OAAA;oBAAAY,QAAA,EAAM;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eACNhB,OAAA;kBAAKW,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrCZ,OAAA;oBAAMW,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChChB,OAAA;oBAAAY,QAAA,EAAM;kBAAwB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC,eACNhB,OAAA;kBAAKW,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrCZ,OAAA;oBAAMW,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChChB,OAAA;oBAAAY,QAAA,EAAM;kBAA+B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CAAC,eACNhB,OAAA;kBAAKW,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrCZ,OAAA;oBAAMW,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChChB,OAAA;oBAAAY,QAAA,EAAM;kBAAyB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,eACNhB,OAAA;kBAAKW,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrCZ,OAAA;oBAAMW,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChChB,OAAA;oBAAAY,QAAA,EAAM;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNhB,OAAA,CAACJ,IAAI;gBAACqB,EAAE,EAAC,aAAa;gBAACN,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA4DH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEVhB,OAAA;QAASW,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC9BZ,OAAA;UAAKW,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBZ,OAAA;YAAAY,QAAA,EAAI;UAA0B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnChB,OAAA;YAAKW,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBZ,OAAA;cAAKW,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBZ,OAAA;gBAAAY,QAAA,EAAI;cAAqC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9ChB,OAAA;gBAAAY,QAAA,EAAG;cAAwL;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5L,CAAC,eACNhB,OAAA;cAAKW,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBZ,OAAA;gBAAAY,QAAA,EAAI;cAAwC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjDhB,OAAA;gBAAAY,QAAA,EAAG;cAAuI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3I,CAAC,eACNhB,OAAA;cAAKW,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBZ,OAAA;gBAAAY,QAAA,EAAI;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3BhB,OAAA;gBAAAY,QAAA,EAAG;cAAuI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3I,CAAC,eACNhB,OAAA;cAAKW,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBZ,OAAA;gBAAAY,QAAA,EAAI;cAAqC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9ChB,OAAA;gBAAAY,QAAA,EAAG;cAAiF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrF,CAAC,eACNhB,OAAA;cAAKW,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBZ,OAAA;gBAAAY,QAAA,EAAI;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9BhB,OAAA;gBAAAY,QAAA,EAAG;cAAgF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAEPhB,OAAA,CAACF,MAAM;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV;AAACd,EAAA,CA9OQD,UAAU;AAAAqB,EAAA,GAAVrB,UAAU;AAgPnB,eAAeA,UAAU;AAAC,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}