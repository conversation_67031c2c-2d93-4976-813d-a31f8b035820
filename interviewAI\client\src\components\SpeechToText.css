.speech-to-text {
  max-width: 900px;
  margin: 0 auto;
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 12px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

/* Timer at the top */
.timer-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
  padding: 8px;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.timer-icon {
  color: #4285f4;
  margin-right: 5px;
}

.timer-display {
  display: inline-block;
  font-family: monospace;
  font-size: 18px;
  font-weight: bold;
  color: #333;
  padding: 2px 8px;
}

.listening-indicator {
  display: inline-block;
  color: #e74c3c;
  margin-left: 10px;
  font-weight: 500;
  animation: pulse 1.5s infinite;
}

/* Container layout - vertical stack instead of horizontal */
.flex-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* Section headers */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.section-header h3 {
  margin: 0;
  font-size: 18px;
  color: #2c3e50;
  font-weight: 600;
}

/* Answer container - larger and at the top */
.answer-container {
  width: 100%;
}

.response-area {
  width: 100%;
  height: 250px;
  padding: 18px;
  border: 1px solid #ddd;
  background: rgba(255, 255, 255, 0.9);
  overflow-y: auto;
  resize: vertical;
  font-size: 17px;
  line-height: 1.5;
  border-radius: 8px;
  font-family: 'Segoe UI', Arial, sans-serif;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: border 0.3s, box-shadow 0.3s;
}

/* Question container - smaller and at the bottom */
.question-container {
  width: 100%;
}

.transcript-area {
  width: 100%;
  height: 120px;
  padding: 15px;
  border: 1px solid #ddd;
  background: rgba(255, 255, 255, 0.9);
  overflow-y: auto;
  resize: vertical;
  font-size: 16px;
  border-radius: 8px;
  font-family: 'Segoe UI', Arial, sans-serif;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: border 0.3s, box-shadow 0.3s;
}

.transcript-area:focus,
.response-area:focus {
  outline: none;
  border-color: #4285f4;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05), 0 0 0 3px rgba(66, 133, 244, 0.2);
}

/* Button rows */
.button-row {
  display: flex;
  justify-content: flex-end;
  margin-top: 8px;
  gap: 10px;
}

.button-row.compact {
  margin-top: 0;
}

/* Clear buttons */
.clear-button {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  background-color: #f0f0f0;
  color: #555;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s;
}

.clear-button:hover:not(:disabled) {
  background-color: #e0e0e0;
}

.clear-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Mic and Send buttons */
.mic-button, .send-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.mic-button {
  background-color: #4285f4;
}

.mic-button.listening {
  background-color: #ea4335;
  animation: pulse 1.5s infinite;
}

.send-button {
  background-color: #34a853;
}

.mic-button:hover:not(:disabled),
.send-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.mic-button:active:not(:disabled),
.send-button:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
}

.mic-button:disabled,
.send-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
  opacity: 0.7;
  box-shadow: none;
}

/* Microphone animation when listening */
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .speech-to-text {
    padding: 15px;
  }

  .response-area {
    height: 200px;
    font-size: 16px;
  }

  .transcript-area {
    height: 100px;
    font-size: 15px;
  }
}
