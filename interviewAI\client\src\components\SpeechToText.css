.speech-to-text {
  max-width: 900px;
  margin: 0 auto;
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 12px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  position: relative;
}

/* Exit button in top right corner */
.exit-button {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.8);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.exit-button:hover {
  background-color: #f44336;
  color: white;
  transform: scale(1.1);
}

/* Timer at the top right corner */
.timer-container {
  position: absolute;
  top: 10px;
  right: 60px; /* Position to the left of the exit button */
  display: flex;
  align-items: center;
  padding: 5px 10px;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  z-index: 10;
}

.timer-icon {
  color: #4285f4;
  margin-right: 5px;
}

.timer-display {
  display: inline-block;
  font-family: monospace;
  font-size: 18px;
  font-weight: bold;
  color: #333;
  padding: 2px 8px;
}

.session-timer {
  color: #2c3e50;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 4px;
  padding: 4px 8px;
}

.listening-indicator {
  display: inline-block;
  color: #e74c3c;
  margin-left: 10px;
  font-weight: 500;
  animation: pulse 1.5s infinite;
}

/* Container layout - vertical stack instead of horizontal */
.flex-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* Section headers */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding: 0 5px;
}

.section-header h3 {
  margin: 0;
  font-size: 18px;
  color: #2c3e50;
  font-weight: 600;
}

/* Control group for buttons */
.control-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* Answer container - larger and at the top */
.answer-container {
  width: 100%;
}

.response-area {
  width: 100%;
  height: 350px; /* Increased height for more space */
  padding: 18px;
  border: 1px solid #ddd;
  background: rgba(255, 255, 255, 0.9);
  overflow-y: auto;
  resize: vertical;
  font-size: 17px;
  line-height: 1.5;
  border-radius: 8px;
  font-family: 'Segoe UI', Arial, sans-serif;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: border 0.3s, box-shadow 0.3s;
  margin-top: 5px;
}

/* Question container - smaller and at the bottom */
.question-container {
  width: 100%;
  margin-top: 10px;
}

.transcript-area {
  width: 100%;
  height: 80px; /* Reduced height for smaller question area */
  padding: 15px;
  border: 1px solid #ddd;
  background: rgba(255, 255, 255, 0.9);
  overflow-y: auto;
  resize: vertical;
  font-size: 16px;
  border-radius: 8px;
  font-family: 'Segoe UI', Arial, sans-serif;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: border 0.3s, box-shadow 0.3s;
}

.transcript-area:focus,
.response-area:focus {
  outline: none;
  border-color: #4285f4;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05), 0 0 0 3px rgba(66, 133, 244, 0.2);
}

/* Button rows */
.button-row {
  display: flex;
  justify-content: flex-end;
  margin-top: 8px;
  gap: 10px;
}

.button-row.compact {
  margin-top: 0;
}

/* Clear buttons */
.clear-button {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  background-color: #f0f0f0;
  color: #555;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s;
}

/* Side button style for clear buttons */
.side-button {
  width: 36px;
  height: 36px;
  padding: 0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.clear-button:hover:not(:disabled) {
  background-color: #e0e0e0;
}

.side-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.15);
}

.clear-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Mic and Send buttons */
.mic-button, .send-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.mic-button {
  background-color: #4285f4;
}

.mic-button.listening {
  background-color: #ea4335;
  animation: pulse 1.5s infinite;
}

.send-button {
  background-color: #34a853;
}

.mic-button:hover:not(:disabled),
.send-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.mic-button:active:not(:disabled),
.send-button:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
}

.mic-button:disabled,
.send-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
  opacity: 0.7;
  box-shadow: none;
}

/* Microphone animation when listening */
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .speech-to-text {
    padding: 15px;
    padding-top: 50px; /* Add space for the timer at the top */
  }

  .timer-container {
    top: 10px;
    right: 10px;
    left: 10px; /* Stretch across the top on mobile */
    width: calc(100% - 20px);
    justify-content: center;
  }

  .exit-button {
    top: 60px; /* Move below the timer */
  }

  .response-area {
    height: 250px;
    font-size: 16px;
  }

  .transcript-area {
    height: 70px;
    font-size: 15px;
  }

  /* Make buttons more touch-friendly on mobile */
  .mic-button, .send-button, .side-button {
    width: 44px;
    height: 44px;
  }
}
