.transcription-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.transcription-intro {
  max-width: 900px;
  margin: 0 auto;
  padding: 40px 20px;
}

.transcription-intro h1 {
  font-size: 2.2rem;
  color: #2c3e50;
  margin-bottom: 20px;
  text-align: center;
}

.feature-badges {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: center;
  margin-bottom: 30px;
}

.feature-badge {
  background-color: #f0f0f0;
  color: #333;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
}

.feature-badge.highlight {
  background-color: #1976d2;
  color: white;
}

.intro-description {
  background: white;
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.intro-description p {
  font-size: 16px;
  line-height: 1.6;
  color: #444;
  margin-bottom: 20px;
}

.intro-description h3 {
  font-size: 18px;
  margin-bottom: 15px;
  color: #2c3e50;
}

.intro-description ul {
  padding-left: 20px;
  margin-bottom: 20px;
}

.intro-description li {
  margin-bottom: 10px;
  line-height: 1.5;
}

.api-status {
  margin-top: 20px;
  padding: 15px;
  border-radius: 8px;
}

.api-status.checking {
  background-color: #fff8e1;
  border: 1px solid #ffd54f;
}

.api-status.invalid {
  background-color: #ffebee;
  border: 1px solid #ef5350;
}

.action-buttons {
  display: flex;
  gap: 15px;
  justify-content: center;
}

.start-button, .back-button {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
}

.start-button {
  background-color: #1976d2;
  color: white;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.start-button:hover:not(:disabled) {
  background-color: #1565c0;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.start-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
  opacity: 0.7;
  box-shadow: none;
}

.back-button {
  background-color: #f5f5f5;
  color: #333;
}

.back-button:hover {
  background-color: #e0e0e0;
}

@media (max-width: 768px) {
  .transcription-intro {
    padding: 20px 15px;
  }
  
  .transcription-intro h1 {
    font-size: 1.8rem;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .feature-badges {
    flex-direction: column;
    align-items: center;
  }
}
