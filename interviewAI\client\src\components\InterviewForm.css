.interview-form {
  max-width: 700px;
  margin: 0 auto;
  padding: 30px;
  background-color: #f9f9f9;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.interview-form h2 {
  text-align: center;
  margin-bottom: 25px;
  color: #333;
  font-size: 24px;
}

.form-group {
  margin-bottom: 25px;
}

label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #444;
}

input[type="text"],
select,
textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 16px;
  transition: border-color 0.3s;
}

input[type="text"]:focus,
select:focus,
textarea:focus {
  border-color: #4CAF50;
  outline: none;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

.job-description-textarea {
  min-height: 120px;
  resize: vertical;
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.checkbox-group label {
  display: flex;
  align-items: center;
  font-weight: normal;
}

.checkbox-group input {
  margin-right: 10px;
}

.file-upload-container {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 5px;
}

.file-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  flex-grow: 1;
}

.file-input {
  position: absolute;
  left: 0;
  top: 0;
  opacity: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.file-input-button {
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  white-space: nowrap;
}

.file-name {
  margin-left: 10px;
  font-size: 14px;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.clear-file {
  background-color: #f8d7da;
  color: #721c24;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  white-space: nowrap;
}

.help-text {
  font-size: 14px;
  color: #666;
  margin-top: 5px;
}

.error-text {
  color: #dc3545;
  font-size: 14px;
  margin-top: 5px;
  margin-bottom: 0;
}

/* Voice recorder styles */
.voice-recorder-container {
  background-color: #f0f0f0;
  border-radius: 8px;
  padding: 15px;
  margin-top: 10px;
}

.recording-controls, .playback-controls {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.record-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  background-color: #4285f4;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 30px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s;
}

.record-button:hover {
  background-color: #3367d6;
  transform: translateY(-2px);
}

.record-button.recording {
  background-color: #ea4335;
  animation: pulse 1.5s infinite;
}

.recording-indicator {
  color: #ea4335;
  font-weight: 500;
  margin: 5px 0;
  animation: pulse 1.5s infinite;
}

.audio-info {
  font-size: 14px;
  color: #555;
  margin-bottom: 10px;
}

.audio-buttons {
  display: flex;
  gap: 10px;
}

.play-button, .delete-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.play-button {
  background-color: #4285f4;
  color: white;
}

.play-button:hover {
  background-color: #3367d6;
}

.delete-button {
  background-color: #f8d7da;
  color: #721c24;
}

.delete-button:hover {
  background-color: #f5c6cb;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

/* Start button styles */
.start-button {
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 14px 24px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 18px;
  width: 100%;
  margin-top: 10px;
  transition: all 0.3s;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.start-button:hover {
  background-color: #45a049;
  transform: translateY(-2px);
  box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
}

.start-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}