import React, { useState } from 'react';
// import { useNavigate } from 'react-router-dom';
import Header from '../../components/Header';
import InterviewForm from '../../components/InterviewForm';
import InterviewSession from '../../components/InterviewSession';
import SpeechToText from '../../components/SpeechToText';
import VoiceTranscriber from '../../components/VoiceTranscriber';
import MeetingTranscriber from '../../components/MeetingTranscriber';
import './admin-home.css';

function AdminHomeScreen() {
  // const navigate = useNavigate();
  const [sessionActive, setSessionActive] = useState(false);
  const [sessionConfig, setSessionConfig] = useState(null);
  const [showSpeechToText, setShowSpeechToText] = useState(false);
  const [showVoiceTranscriber, setShowVoiceTranscriber] = useState(false);
  const [showMeetingTranscriber, setShowMeetingTranscriber] = useState(false);

  React.useEffect(() => {
    const userDetails = localStorage.getItem("userDetails");
    if (!userDetails) {
      window.location.href = "/login";
    }
  }, []);

  const startSession = (config) => {
    // Store session config
    setSessionConfig(config);
    setSessionActive(true);
    setShowSpeechToText(false);
    setShowVoiceTranscriber(false);
    setShowMeetingTranscriber(false);

    // Store session time in localStorage if provided
    if (config && config.sessionTimeRemaining) {
      try {
        const userDetails = localStorage.getItem('userDetails');
        let userData = userDetails ? JSON.parse(userDetails) : {};

        userData.timeRemaining = config.sessionTimeRemaining;
        localStorage.setItem('userDetails', JSON.stringify(userData));
      } catch (error) {
        console.error('Error saving session time to localStorage:', error);
      }
    }
  };

  const endSession = () => {
    setSessionActive(false);
    setSessionConfig(null);
  };

  // const toggleSpeechToText = () => {
  //   setShowSpeechToText(!showSpeechToText);
  //   if (!showSpeechToText) {
  //     setSessionActive(false);
  //     setShowVoiceTranscriber(false);
  //     setShowMeetingTranscriber(false);
  //   }
  // };

  // const toggleVoiceTranscriber = () => {
  //   setShowVoiceTranscriber(!showVoiceTranscriber);
  //   if (!showVoiceTranscriber) {
  //     setSessionActive(false);
  //     setShowSpeechToText(false);
  //     setShowMeetingTranscriber(false);
  //   }
  // };

  // const toggleMeetingTranscriber = () => {
  //   setShowMeetingTranscriber(!showMeetingTranscriber);
  //   if (!showMeetingTranscriber) {
  //     setSessionActive(false);
  //     setShowSpeechToText(false);
  //     setShowVoiceTranscriber(false);
  //   }
  // };

  const handleBackFromVoiceTranscriber = () => {
    setShowVoiceTranscriber(false);
  };

  const handleBackFromMeetingTranscriber = () => {
    setShowMeetingTranscriber(false);
  };

  // const navigateToDeepgramTranscription = () => {
  //   navigate('/deepgram-transcription');
  // };

  return (
    <div className="App">
      {!showVoiceTranscriber && !showMeetingTranscriber && !showSpeechToText && <Header />}

      {/* {!showVoiceTranscriber && !showMeetingTranscriber && !showSpeechToText && (
        <div className="nav-buttons">
          <button
            onClick={() => {
              setShowSpeechToText(false);
              setShowVoiceTranscriber(false);
              setShowMeetingTranscriber(false);
              setSessionActive(false);
            }}
            className={!sessionActive && !showSpeechToText && !showVoiceTranscriber && !showMeetingTranscriber ? 'active' : ''}
          >
            Home
          </button>
          <button
            onClick={toggleSpeechToText}
            className={showSpeechToText ? 'active' : ''}
          >
            Interview AI Tool
          </button>
          <button
            onClick={toggleVoiceTranscriber}
            className={showVoiceTranscriber ? 'active' : ''}
          >
            Voice Transcriber
          </button>
          <button
            onClick={toggleMeetingTranscriber}
            className={showMeetingTranscriber ? 'active' : ''}
          >
            Meeting Transcriber
          </button>
          <button
            onClick={navigateToDeepgramTranscription}
            className="highlight"
          >
            Deepgram Transcription
          </button>
        </div>
      )} */}

      <main className={showVoiceTranscriber || showMeetingTranscriber || showSpeechToText ? 'fullscreen' : ''}>
        {showSpeechToText ? (
          <SpeechToText />
        ) : showVoiceTranscriber ? (
          <VoiceTranscriber onBack={handleBackFromVoiceTranscriber} />
        ) : showMeetingTranscriber ? (
          <MeetingTranscriber onBack={handleBackFromMeetingTranscriber} />
        ) : !sessionActive ? (
          <InterviewForm onStartSession={startSession} />
        ) : (
          <InterviewSession config={sessionConfig} onEndSession={endSession} />
        )}
      </main>
    </div>
  );
}
export default AdminHomeScreen;
