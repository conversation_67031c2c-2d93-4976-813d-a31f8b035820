{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\interview-ai-app\\\\interviewAI\\\\client\\\\src\\\\pages\\\\live-transcription\\\\GoogleSpeechDiarization.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport './GoogleSpeechDiarization.css';\nimport env from '../../utils/env';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction GoogleSpeechDiarization() {\n  _s();\n  // State for recording and transcription\n  const [isRecording, setIsRecording] = useState(false);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [transcriptSegments, setTranscriptSegments] = useState([]);\n  const [errorMessage, setErrorMessage] = useState('');\n  const [audioSource, setAudioSource] = useState('microphone'); // 'microphone' or 'tab'\n\n  // Refs for audio handling\n  const mediaRecorderRef = useRef(null);\n  const audioChunksRef = useRef([]);\n  const mediaStreamRef = useRef(null);\n\n  // Clean up function for audio resources\n  const cleanupAudio = () => {\n    if (mediaRecorderRef.current) {\n      if (mediaRecorderRef.current.state !== 'inactive') {\n        mediaRecorderRef.current.stop();\n      }\n      mediaRecorderRef.current = null;\n    }\n    if (mediaStreamRef.current) {\n      mediaStreamRef.current.getTracks().forEach(track => track.stop());\n      mediaStreamRef.current = null;\n    }\n    audioChunksRef.current = [];\n  };\n\n  // Clean up on component unmount\n  useEffect(() => {\n    return () => {\n      cleanupAudio();\n    };\n  }, []);\n\n  // Start recording from microphone\n  const startMicrophoneRecording = async () => {\n    try {\n      cleanupAudio();\n      setErrorMessage('');\n      setAudioSource('microphone');\n\n      // Get microphone stream\n      const stream = await navigator.mediaDevices.getUserMedia({\n        audio: true\n      });\n      mediaStreamRef.current = stream;\n\n      // Create media recorder\n      const mediaRecorder = new MediaRecorder(stream);\n      mediaRecorderRef.current = mediaRecorder;\n\n      // Set up data handling\n      mediaRecorder.ondataavailable = event => {\n        if (event.data.size > 0) {\n          audioChunksRef.current.push(event.data);\n        }\n      };\n\n      // Handle recording stop\n      mediaRecorder.onstop = () => {\n        processAudioForTranscription();\n      };\n\n      // Start recording\n      mediaRecorder.start(1000); // Collect data every second\n      setIsRecording(true);\n      console.log(\"Microphone recording started\");\n    } catch (error) {\n      console.error(\"Error starting microphone recording:\", error);\n      setErrorMessage(`Failed to start microphone: ${error.message}`);\n    }\n  };\n\n  // Start recording from tab audio\n  const startTabAudioRecording = async () => {\n    try {\n      cleanupAudio();\n      setErrorMessage('');\n      setAudioSource('tab');\n\n      // Request screen sharing with audio\n      const stream = await navigator.mediaDevices.getDisplayMedia({\n        video: true,\n        audio: true\n      });\n\n      // Check if audio is included\n      const audioTracks = stream.getAudioTracks();\n      if (audioTracks.length === 0) {\n        throw new Error(\"No audio track found. Please make sure to select 'Share audio' when sharing the tab.\");\n      }\n      mediaStreamRef.current = stream;\n\n      // Create media recorder\n      const mediaRecorder = new MediaRecorder(stream);\n      mediaRecorderRef.current = mediaRecorder;\n\n      // Set up data handling\n      mediaRecorder.ondataavailable = event => {\n        if (event.data.size > 0) {\n          audioChunksRef.current.push(event.data);\n        }\n      };\n\n      // Handle recording stop\n      mediaRecorder.onstop = () => {\n        processAudioForTranscription();\n      };\n\n      // Handle stream ending (user stops sharing)\n      stream.getVideoTracks()[0].onended = () => {\n        stopRecording();\n      };\n\n      // Start recording\n      mediaRecorder.start(1000); // Collect data every second\n      setIsRecording(true);\n      console.log(\"Tab audio recording started\");\n    } catch (error) {\n      console.error(\"Error starting tab audio recording:\", error);\n      setErrorMessage(`Failed to capture tab audio: ${error.message}`);\n    }\n  };\n\n  // Stop recording\n  const stopRecording = () => {\n    if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {\n      mediaRecorderRef.current.stop();\n    }\n    setIsRecording(false);\n  };\n\n  // Process recorded audio and send to Google Speech-to-Text API\n  const processAudioForTranscription = async () => {\n    if (audioChunksRef.current.length === 0) {\n      console.log(\"No audio data to process\");\n      return;\n    }\n    try {\n      setIsProcessing(true);\n\n      // Create audio blob from chunks\n      const audioBlob = new Blob(audioChunksRef.current, {\n        type: 'audio/webm'\n      });\n\n      // Convert blob to base64\n      const reader = new FileReader();\n      reader.readAsDataURL(audioBlob);\n      reader.onloadend = async () => {\n        // Get base64 data (remove the data URL prefix)\n        const base64Audio = reader.result.split(',')[1];\n\n        // Get API key from environment variables\n        const apiKey = env.REACT_APP_GOOGLE_SPEECH_API_KEY;\n        if (!apiKey) {\n          throw new Error(\"Google Speech API key not found. Please add it to your .env file as REACT_APP_GOOGLE_SPEECH_API_KEY\");\n        }\n        console.log(\"Using Google Speech API key:\", apiKey);\n\n        // Prepare request to Google Speech-to-Text API\n        const response = await fetch(`https://speech.googleapis.com/v1p1beta1/speech:recognize?key=${apiKey}`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            config: {\n              encoding: 'WEBM_OPUS',\n              sampleRateHertz: 48000,\n              languageCode: 'en-US',\n              enableAutomaticPunctuation: true,\n              enableSpeakerDiarization: true,\n              diarizationSpeakerCount: 2,\n              model: 'default'\n            },\n            audio: {\n              content: base64Audio\n            }\n          })\n        });\n        if (!response.ok) {\n          var _errorData$error;\n          const errorData = await response.json();\n          throw new Error(`Google API error: ${((_errorData$error = errorData.error) === null || _errorData$error === void 0 ? void 0 : _errorData$error.message) || response.statusText}`);\n        }\n        const data = await response.json();\n        processTranscriptionResponse(data);\n\n        // Clear audio chunks for next recording\n        audioChunksRef.current = [];\n        setIsProcessing(false);\n      };\n      reader.onerror = error => {\n        throw new Error(`Error reading audio data: ${error}`);\n      };\n    } catch (error) {\n      console.error(\"Error processing audio:\", error);\n      setErrorMessage(`Error processing audio: ${error.message}`);\n      setIsProcessing(false);\n    }\n  };\n\n  // Process the response from Google Speech-to-Text API\n  const processTranscriptionResponse = data => {\n    if (!data.results || data.results.length === 0) {\n      console.log(\"No transcription results returned\");\n      return;\n    }\n    try {\n      // Get the result with diarization\n      const result = data.results[data.results.length - 1];\n      if (!result.alternatives || !result.alternatives[0]) {\n        console.log(\"No alternatives in result\");\n        return;\n      }\n      const alternative = result.alternatives[0];\n      const transcript = alternative.transcript;\n\n      // Process speaker diarization if available\n      if (alternative.words && alternative.words.length > 0) {\n        let currentSpeaker = null;\n        let currentText = '';\n        let segments = [];\n\n        // Group words by speaker\n        alternative.words.forEach(word => {\n          const speakerTag = word.speakerTag || 0;\n\n          // If speaker changed or this is the first word\n          if (speakerTag !== currentSpeaker) {\n            // Save previous segment if it exists\n            if (currentText) {\n              segments.push({\n                speaker: currentSpeaker === 1 ? 'Speaker 1' : 'Speaker 2',\n                text: currentText.trim(),\n                timestamp: new Date()\n              });\n            }\n\n            // Start new segment\n            currentSpeaker = speakerTag;\n            currentText = word.word + ' ';\n          } else {\n            // Continue current segment\n            currentText += word.word + ' ';\n          }\n        });\n\n        // Add the last segment\n        if (currentText) {\n          segments.push({\n            speaker: currentSpeaker === 1 ? 'Speaker 1' : 'Speaker 2',\n            text: currentText.trim(),\n            timestamp: new Date()\n          });\n        }\n\n        // Update transcript segments\n        setTranscriptSegments(prev => [...prev, ...segments]);\n      } else {\n        // No speaker diarization, add as single segment\n        setTranscriptSegments(prev => [...prev, {\n          speaker: audioSource === 'microphone' ? 'Candidate' : 'Interviewer',\n          text: transcript,\n          timestamp: new Date()\n        }]);\n      }\n    } catch (error) {\n      console.error(\"Error processing transcription response:\", error);\n      setErrorMessage(`Error processing transcription: ${error.message}`);\n    }\n  };\n\n  // Download transcript as text file\n  const downloadTranscript = () => {\n    if (transcriptSegments.length === 0) return;\n    let content = \"Conversation Transcript:\\n\\n\";\n    transcriptSegments.forEach(segment => {\n      content += `[${segment.timestamp.toLocaleTimeString()}] ${segment.speaker}: ${segment.text}\\n`;\n    });\n    const blob = new Blob([content], {\n      type: 'text/plain'\n    });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = 'google_transcript.txt';\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"google-speech-diarization\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Google Speech API with Speaker Diarization\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 315,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"description\",\n        children: \"This page uses Google's Speech-to-Text API to transcribe audio with speaker diarization.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 316,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 314,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"controls-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"recording-controls\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: `control-button ${isRecording && audioSource === 'microphone' ? 'recording' : ''}`,\n          onClick: isRecording ? stopRecording : startMicrophoneRecording,\n          disabled: isProcessing,\n          children: isRecording && audioSource === 'microphone' ? 'Stop Recording' : 'Start Microphone Recording'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `control-button tab-audio ${isRecording && audioSource === 'tab' ? 'recording' : ''}`,\n          onClick: isRecording ? stopRecording : startTabAudioRecording,\n          disabled: isProcessing,\n          children: isRecording && audioSource === 'tab' ? 'Stop Tab Recording' : 'Start Tab Audio Recording'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 322,\n        columnNumber: 9\n      }, this), isProcessing && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"processing-indicator\",\n        children: \"Processing audio... This may take a moment.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 345,\n        columnNumber: 11\n      }, this), errorMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-message\",\n        children: errorMessage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 351,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 321,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"transcript-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"transcript-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Transcript with Speaker Diarization\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 11\n        }, this), transcriptSegments.length > 0 && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"download-button\",\n          onClick: downloadTranscript,\n          children: \"Download Transcript\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 358,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"transcript-content\",\n        children: transcriptSegments.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"empty-state\",\n          children: \"No transcription yet. Start recording to see results.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 372,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"transcript-segments\",\n          children: transcriptSegments.map((segment, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `transcript-segment ${segment.speaker.toLowerCase().replace(' ', '-')}`,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"segment-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"timestamp\",\n                children: segment.timestamp.toLocaleTimeString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 383,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"speaker-label\",\n                children: segment.speaker\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 384,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"segment-text\",\n              children: segment.text\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 386,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 370,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 357,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 313,\n    columnNumber: 5\n  }, this);\n}\n_s(GoogleSpeechDiarization, \"Ch2U/UQUwSymmkrDSCURPJMNP7A=\");\n_c = GoogleSpeechDiarization;\nexport default GoogleSpeechDiarization;\nvar _c;\n$RefreshReg$(_c, \"GoogleSpeechDiarization\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "env", "jsxDEV", "_jsxDEV", "GoogleSpeechDiarization", "_s", "isRecording", "setIsRecording", "isProcessing", "setIsProcessing", "transcriptSegments", "setTranscriptSegments", "errorMessage", "setErrorMessage", "audioSource", "setAudioSource", "mediaRecorderRef", "audioChunksRef", "mediaStreamRef", "cleanupAudio", "current", "state", "stop", "getTracks", "for<PERSON>ach", "track", "startMicrophoneRecording", "stream", "navigator", "mediaDevices", "getUserMedia", "audio", "mediaRecorder", "MediaRecorder", "ondataavailable", "event", "data", "size", "push", "onstop", "processAudioForTranscription", "start", "console", "log", "error", "message", "startTabAudioRecording", "getDisplayMedia", "video", "audioTracks", "getAudioTracks", "length", "Error", "getVideoTracks", "onended", "stopRecording", "audioBlob", "Blob", "type", "reader", "FileReader", "readAsDataURL", "onloadend", "base64Audio", "result", "split", "<PERSON><PERSON><PERSON><PERSON>", "REACT_APP_GOOGLE_SPEECH_API_KEY", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "config", "encoding", "sampleRateHertz", "languageCode", "enableAutomaticPunctuation", "enableSpeakerDiarization", "diarizationSpeakerCount", "model", "content", "ok", "_errorData$error", "errorData", "json", "statusText", "processTranscriptionResponse", "onerror", "results", "alternatives", "alternative", "transcript", "words", "currentSpeaker", "currentText", "segments", "word", "speakerTag", "speaker", "text", "trim", "timestamp", "Date", "prev", "downloadTranscript", "segment", "toLocaleTimeString", "blob", "url", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "map", "index", "toLowerCase", "replace", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/interview-ai-app/interviewAI/client/src/pages/live-transcription/GoogleSpeechDiarization.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport './GoogleSpeechDiarization.css';\nimport env from '../../utils/env';\n\nfunction GoogleSpeechDiarization() {\n  // State for recording and transcription\n  const [isRecording, setIsRecording] = useState(false);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [transcriptSegments, setTranscriptSegments] = useState([]);\n  const [errorMessage, setErrorMessage] = useState('');\n  const [audioSource, setAudioSource] = useState('microphone'); // 'microphone' or 'tab'\n\n  // Refs for audio handling\n  const mediaRecorderRef = useRef(null);\n  const audioChunksRef = useRef([]);\n  const mediaStreamRef = useRef(null);\n\n  // Clean up function for audio resources\n  const cleanupAudio = () => {\n    if (mediaRecorderRef.current) {\n      if (mediaRecorderRef.current.state !== 'inactive') {\n        mediaRecorderRef.current.stop();\n      }\n      mediaRecorderRef.current = null;\n    }\n\n    if (mediaStreamRef.current) {\n      mediaStreamRef.current.getTracks().forEach(track => track.stop());\n      mediaStreamRef.current = null;\n    }\n\n    audioChunksRef.current = [];\n  };\n\n  // Clean up on component unmount\n  useEffect(() => {\n    return () => {\n      cleanupAudio();\n    };\n  }, []);\n\n  // Start recording from microphone\n  const startMicrophoneRecording = async () => {\n    try {\n      cleanupAudio();\n      setErrorMessage('');\n      setAudioSource('microphone');\n\n      // Get microphone stream\n      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });\n      mediaStreamRef.current = stream;\n\n      // Create media recorder\n      const mediaRecorder = new MediaRecorder(stream);\n      mediaRecorderRef.current = mediaRecorder;\n\n      // Set up data handling\n      mediaRecorder.ondataavailable = (event) => {\n        if (event.data.size > 0) {\n          audioChunksRef.current.push(event.data);\n        }\n      };\n\n      // Handle recording stop\n      mediaRecorder.onstop = () => {\n        processAudioForTranscription();\n      };\n\n      // Start recording\n      mediaRecorder.start(1000); // Collect data every second\n      setIsRecording(true);\n\n      console.log(\"Microphone recording started\");\n    } catch (error) {\n      console.error(\"Error starting microphone recording:\", error);\n      setErrorMessage(`Failed to start microphone: ${error.message}`);\n    }\n  };\n\n  // Start recording from tab audio\n  const startTabAudioRecording = async () => {\n    try {\n      cleanupAudio();\n      setErrorMessage('');\n      setAudioSource('tab');\n\n      // Request screen sharing with audio\n      const stream = await navigator.mediaDevices.getDisplayMedia({\n        video: true,\n        audio: true\n      });\n\n      // Check if audio is included\n      const audioTracks = stream.getAudioTracks();\n      if (audioTracks.length === 0) {\n        throw new Error(\"No audio track found. Please make sure to select 'Share audio' when sharing the tab.\");\n      }\n\n      mediaStreamRef.current = stream;\n\n      // Create media recorder\n      const mediaRecorder = new MediaRecorder(stream);\n      mediaRecorderRef.current = mediaRecorder;\n\n      // Set up data handling\n      mediaRecorder.ondataavailable = (event) => {\n        if (event.data.size > 0) {\n          audioChunksRef.current.push(event.data);\n        }\n      };\n\n      // Handle recording stop\n      mediaRecorder.onstop = () => {\n        processAudioForTranscription();\n      };\n\n      // Handle stream ending (user stops sharing)\n      stream.getVideoTracks()[0].onended = () => {\n        stopRecording();\n      };\n\n      // Start recording\n      mediaRecorder.start(1000); // Collect data every second\n      setIsRecording(true);\n\n      console.log(\"Tab audio recording started\");\n    } catch (error) {\n      console.error(\"Error starting tab audio recording:\", error);\n      setErrorMessage(`Failed to capture tab audio: ${error.message}`);\n    }\n  };\n\n  // Stop recording\n  const stopRecording = () => {\n    if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {\n      mediaRecorderRef.current.stop();\n    }\n    setIsRecording(false);\n  };\n\n  // Process recorded audio and send to Google Speech-to-Text API\n  const processAudioForTranscription = async () => {\n    if (audioChunksRef.current.length === 0) {\n      console.log(\"No audio data to process\");\n      return;\n    }\n\n    try {\n      setIsProcessing(true);\n\n      // Create audio blob from chunks\n      const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });\n\n      // Convert blob to base64\n      const reader = new FileReader();\n      reader.readAsDataURL(audioBlob);\n\n      reader.onloadend = async () => {\n        // Get base64 data (remove the data URL prefix)\n        const base64Audio = reader.result.split(',')[1];\n\n        // Get API key from environment variables\n        const apiKey = env.REACT_APP_GOOGLE_SPEECH_API_KEY;\n        if (!apiKey) {\n          throw new Error(\"Google Speech API key not found. Please add it to your .env file as REACT_APP_GOOGLE_SPEECH_API_KEY\");\n        }\n        console.log(\"Using Google Speech API key:\", apiKey);\n\n        // Prepare request to Google Speech-to-Text API\n        const response = await fetch(`https://speech.googleapis.com/v1p1beta1/speech:recognize?key=${apiKey}`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            config: {\n              encoding: 'WEBM_OPUS',\n              sampleRateHertz: 48000,\n              languageCode: 'en-US',\n              enableAutomaticPunctuation: true,\n              enableSpeakerDiarization: true,\n              diarizationSpeakerCount: 2,\n              model: 'default'\n            },\n            audio: {\n              content: base64Audio\n            }\n          })\n        });\n\n        if (!response.ok) {\n          const errorData = await response.json();\n          throw new Error(`Google API error: ${errorData.error?.message || response.statusText}`);\n        }\n\n        const data = await response.json();\n        processTranscriptionResponse(data);\n\n        // Clear audio chunks for next recording\n        audioChunksRef.current = [];\n        setIsProcessing(false);\n      };\n\n      reader.onerror = (error) => {\n        throw new Error(`Error reading audio data: ${error}`);\n      };\n\n    } catch (error) {\n      console.error(\"Error processing audio:\", error);\n      setErrorMessage(`Error processing audio: ${error.message}`);\n      setIsProcessing(false);\n    }\n  };\n\n  // Process the response from Google Speech-to-Text API\n  const processTranscriptionResponse = (data) => {\n    if (!data.results || data.results.length === 0) {\n      console.log(\"No transcription results returned\");\n      return;\n    }\n\n    try {\n      // Get the result with diarization\n      const result = data.results[data.results.length - 1];\n\n      if (!result.alternatives || !result.alternatives[0]) {\n        console.log(\"No alternatives in result\");\n        return;\n      }\n\n      const alternative = result.alternatives[0];\n      const transcript = alternative.transcript;\n\n      // Process speaker diarization if available\n      if (alternative.words && alternative.words.length > 0) {\n        let currentSpeaker = null;\n        let currentText = '';\n        let segments = [];\n\n        // Group words by speaker\n        alternative.words.forEach(word => {\n          const speakerTag = word.speakerTag || 0;\n\n          // If speaker changed or this is the first word\n          if (speakerTag !== currentSpeaker) {\n            // Save previous segment if it exists\n            if (currentText) {\n              segments.push({\n                speaker: currentSpeaker === 1 ? 'Speaker 1' : 'Speaker 2',\n                text: currentText.trim(),\n                timestamp: new Date()\n              });\n            }\n\n            // Start new segment\n            currentSpeaker = speakerTag;\n            currentText = word.word + ' ';\n          } else {\n            // Continue current segment\n            currentText += word.word + ' ';\n          }\n        });\n\n        // Add the last segment\n        if (currentText) {\n          segments.push({\n            speaker: currentSpeaker === 1 ? 'Speaker 1' : 'Speaker 2',\n            text: currentText.trim(),\n            timestamp: new Date()\n          });\n        }\n\n        // Update transcript segments\n        setTranscriptSegments(prev => [...prev, ...segments]);\n      } else {\n        // No speaker diarization, add as single segment\n        setTranscriptSegments(prev => [\n          ...prev,\n          {\n            speaker: audioSource === 'microphone' ? 'Candidate' : 'Interviewer',\n            text: transcript,\n            timestamp: new Date()\n          }\n        ]);\n      }\n    } catch (error) {\n      console.error(\"Error processing transcription response:\", error);\n      setErrorMessage(`Error processing transcription: ${error.message}`);\n    }\n  };\n\n  // Download transcript as text file\n  const downloadTranscript = () => {\n    if (transcriptSegments.length === 0) return;\n\n    let content = \"Conversation Transcript:\\n\\n\";\n    transcriptSegments.forEach(segment => {\n      content += `[${segment.timestamp.toLocaleTimeString()}] ${segment.speaker}: ${segment.text}\\n`;\n    });\n\n    const blob = new Blob([content], { type: 'text/plain' });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = 'google_transcript.txt';\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n\n  return (\n    <div className=\"google-speech-diarization\">\n      <div className=\"page-header\">\n        <h1>Google Speech API with Speaker Diarization</h1>\n        <p className=\"description\">\n          This page uses Google's Speech-to-Text API to transcribe audio with speaker diarization.\n        </p>\n      </div>\n\n      <div className=\"controls-container\">\n        <div className=\"recording-controls\">\n          <button\n            className={`control-button ${isRecording && audioSource === 'microphone' ? 'recording' : ''}`}\n            onClick={isRecording ? stopRecording : startMicrophoneRecording}\n            disabled={isProcessing}\n          >\n            {isRecording && audioSource === 'microphone'\n              ? 'Stop Recording'\n              : 'Start Microphone Recording'}\n          </button>\n\n          <button\n            className={`control-button tab-audio ${isRecording && audioSource === 'tab' ? 'recording' : ''}`}\n            onClick={isRecording ? stopRecording : startTabAudioRecording}\n            disabled={isProcessing}\n          >\n            {isRecording && audioSource === 'tab'\n              ? 'Stop Tab Recording'\n              : 'Start Tab Audio Recording'}\n          </button>\n        </div>\n\n        {isProcessing && (\n          <div className=\"processing-indicator\">\n            Processing audio... This may take a moment.\n          </div>\n        )}\n\n        {errorMessage && (\n          <div className=\"error-message\">\n            {errorMessage}\n          </div>\n        )}\n      </div>\n\n      <div className=\"transcript-container\">\n        <div className=\"transcript-header\">\n          <h2>Transcript with Speaker Diarization</h2>\n          {transcriptSegments.length > 0 && (\n            <button\n              className=\"download-button\"\n              onClick={downloadTranscript}\n            >\n              Download Transcript\n            </button>\n          )}\n        </div>\n\n        <div className=\"transcript-content\">\n          {transcriptSegments.length === 0 ? (\n            <div className=\"empty-state\">\n              No transcription yet. Start recording to see results.\n            </div>\n          ) : (\n            <div className=\"transcript-segments\">\n              {transcriptSegments.map((segment, index) => (\n                <div\n                  key={index}\n                  className={`transcript-segment ${segment.speaker.toLowerCase().replace(' ', '-')}`}\n                >\n                  <div className=\"segment-header\">\n                    <span className=\"timestamp\">{segment.timestamp.toLocaleTimeString()}</span>\n                    <span className=\"speaker-label\">{segment.speaker}</span>\n                  </div>\n                  <div className=\"segment-text\">{segment.text}</div>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default GoogleSpeechDiarization;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,+BAA+B;AACtC,OAAOC,GAAG,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,SAASC,uBAAuBA,CAAA,EAAG;EAAAC,EAAA;EACjC;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGT,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACU,YAAY,EAAEC,eAAe,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACY,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAACc,YAAY,EAAEC,eAAe,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACgB,WAAW,EAAEC,cAAc,CAAC,GAAGjB,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC;;EAE9D;EACA,MAAMkB,gBAAgB,GAAGhB,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMiB,cAAc,GAAGjB,MAAM,CAAC,EAAE,CAAC;EACjC,MAAMkB,cAAc,GAAGlB,MAAM,CAAC,IAAI,CAAC;;EAEnC;EACA,MAAMmB,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIH,gBAAgB,CAACI,OAAO,EAAE;MAC5B,IAAIJ,gBAAgB,CAACI,OAAO,CAACC,KAAK,KAAK,UAAU,EAAE;QACjDL,gBAAgB,CAACI,OAAO,CAACE,IAAI,CAAC,CAAC;MACjC;MACAN,gBAAgB,CAACI,OAAO,GAAG,IAAI;IACjC;IAEA,IAAIF,cAAc,CAACE,OAAO,EAAE;MAC1BF,cAAc,CAACE,OAAO,CAACG,SAAS,CAAC,CAAC,CAACC,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACH,IAAI,CAAC,CAAC,CAAC;MACjEJ,cAAc,CAACE,OAAO,GAAG,IAAI;IAC/B;IAEAH,cAAc,CAACG,OAAO,GAAG,EAAE;EAC7B,CAAC;;EAED;EACArB,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACXoB,YAAY,CAAC,CAAC;IAChB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMO,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3C,IAAI;MACFP,YAAY,CAAC,CAAC;MACdN,eAAe,CAAC,EAAE,CAAC;MACnBE,cAAc,CAAC,YAAY,CAAC;;MAE5B;MACA,MAAMY,MAAM,GAAG,MAAMC,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;QAAEC,KAAK,EAAE;MAAK,CAAC,CAAC;MACzEb,cAAc,CAACE,OAAO,GAAGO,MAAM;;MAE/B;MACA,MAAMK,aAAa,GAAG,IAAIC,aAAa,CAACN,MAAM,CAAC;MAC/CX,gBAAgB,CAACI,OAAO,GAAGY,aAAa;;MAExC;MACAA,aAAa,CAACE,eAAe,GAAIC,KAAK,IAAK;QACzC,IAAIA,KAAK,CAACC,IAAI,CAACC,IAAI,GAAG,CAAC,EAAE;UACvBpB,cAAc,CAACG,OAAO,CAACkB,IAAI,CAACH,KAAK,CAACC,IAAI,CAAC;QACzC;MACF,CAAC;;MAED;MACAJ,aAAa,CAACO,MAAM,GAAG,MAAM;QAC3BC,4BAA4B,CAAC,CAAC;MAChC,CAAC;;MAED;MACAR,aAAa,CAACS,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;MAC3BlC,cAAc,CAAC,IAAI,CAAC;MAEpBmC,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;IAC7C,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D/B,eAAe,CAAC,+BAA+B+B,KAAK,CAACC,OAAO,EAAE,CAAC;IACjE;EACF,CAAC;;EAED;EACA,MAAMC,sBAAsB,GAAG,MAAAA,CAAA,KAAY;IACzC,IAAI;MACF3B,YAAY,CAAC,CAAC;MACdN,eAAe,CAAC,EAAE,CAAC;MACnBE,cAAc,CAAC,KAAK,CAAC;;MAErB;MACA,MAAMY,MAAM,GAAG,MAAMC,SAAS,CAACC,YAAY,CAACkB,eAAe,CAAC;QAC1DC,KAAK,EAAE,IAAI;QACXjB,KAAK,EAAE;MACT,CAAC,CAAC;;MAEF;MACA,MAAMkB,WAAW,GAAGtB,MAAM,CAACuB,cAAc,CAAC,CAAC;MAC3C,IAAID,WAAW,CAACE,MAAM,KAAK,CAAC,EAAE;QAC5B,MAAM,IAAIC,KAAK,CAAC,sFAAsF,CAAC;MACzG;MAEAlC,cAAc,CAACE,OAAO,GAAGO,MAAM;;MAE/B;MACA,MAAMK,aAAa,GAAG,IAAIC,aAAa,CAACN,MAAM,CAAC;MAC/CX,gBAAgB,CAACI,OAAO,GAAGY,aAAa;;MAExC;MACAA,aAAa,CAACE,eAAe,GAAIC,KAAK,IAAK;QACzC,IAAIA,KAAK,CAACC,IAAI,CAACC,IAAI,GAAG,CAAC,EAAE;UACvBpB,cAAc,CAACG,OAAO,CAACkB,IAAI,CAACH,KAAK,CAACC,IAAI,CAAC;QACzC;MACF,CAAC;;MAED;MACAJ,aAAa,CAACO,MAAM,GAAG,MAAM;QAC3BC,4BAA4B,CAAC,CAAC;MAChC,CAAC;;MAED;MACAb,MAAM,CAAC0B,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,OAAO,GAAG,MAAM;QACzCC,aAAa,CAAC,CAAC;MACjB,CAAC;;MAED;MACAvB,aAAa,CAACS,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;MAC3BlC,cAAc,CAAC,IAAI,CAAC;MAEpBmC,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;IAC5C,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D/B,eAAe,CAAC,gCAAgC+B,KAAK,CAACC,OAAO,EAAE,CAAC;IAClE;EACF,CAAC;;EAED;EACA,MAAMU,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIvC,gBAAgB,CAACI,OAAO,IAAIJ,gBAAgB,CAACI,OAAO,CAACC,KAAK,KAAK,UAAU,EAAE;MAC7EL,gBAAgB,CAACI,OAAO,CAACE,IAAI,CAAC,CAAC;IACjC;IACAf,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC;;EAED;EACA,MAAMiC,4BAA4B,GAAG,MAAAA,CAAA,KAAY;IAC/C,IAAIvB,cAAc,CAACG,OAAO,CAAC+B,MAAM,KAAK,CAAC,EAAE;MACvCT,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MACvC;IACF;IAEA,IAAI;MACFlC,eAAe,CAAC,IAAI,CAAC;;MAErB;MACA,MAAM+C,SAAS,GAAG,IAAIC,IAAI,CAACxC,cAAc,CAACG,OAAO,EAAE;QAAEsC,IAAI,EAAE;MAAa,CAAC,CAAC;;MAE1E;MACA,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,aAAa,CAACL,SAAS,CAAC;MAE/BG,MAAM,CAACG,SAAS,GAAG,YAAY;QAC7B;QACA,MAAMC,WAAW,GAAGJ,MAAM,CAACK,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;QAE/C;QACA,MAAMC,MAAM,GAAGjE,GAAG,CAACkE,+BAA+B;QAClD,IAAI,CAACD,MAAM,EAAE;UACX,MAAM,IAAId,KAAK,CAAC,qGAAqG,CAAC;QACxH;QACAV,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEuB,MAAM,CAAC;;QAEnD;QACA,MAAME,QAAQ,GAAG,MAAMC,KAAK,CAAC,gEAAgEH,MAAM,EAAE,EAAE;UACrGI,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YACP,cAAc,EAAE;UAClB,CAAC;UACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YACnBC,MAAM,EAAE;cACNC,QAAQ,EAAE,WAAW;cACrBC,eAAe,EAAE,KAAK;cACtBC,YAAY,EAAE,OAAO;cACrBC,0BAA0B,EAAE,IAAI;cAChCC,wBAAwB,EAAE,IAAI;cAC9BC,uBAAuB,EAAE,CAAC;cAC1BC,KAAK,EAAE;YACT,CAAC;YACDnD,KAAK,EAAE;cACLoD,OAAO,EAAEpB;YACX;UACF,CAAC;QACH,CAAC,CAAC;QAEF,IAAI,CAACK,QAAQ,CAACgB,EAAE,EAAE;UAAA,IAAAC,gBAAA;UAChB,MAAMC,SAAS,GAAG,MAAMlB,QAAQ,CAACmB,IAAI,CAAC,CAAC;UACvC,MAAM,IAAInC,KAAK,CAAC,qBAAqB,EAAAiC,gBAAA,GAAAC,SAAS,CAAC1C,KAAK,cAAAyC,gBAAA,uBAAfA,gBAAA,CAAiBxC,OAAO,KAAIuB,QAAQ,CAACoB,UAAU,EAAE,CAAC;QACzF;QAEA,MAAMpD,IAAI,GAAG,MAAMgC,QAAQ,CAACmB,IAAI,CAAC,CAAC;QAClCE,4BAA4B,CAACrD,IAAI,CAAC;;QAElC;QACAnB,cAAc,CAACG,OAAO,GAAG,EAAE;QAC3BX,eAAe,CAAC,KAAK,CAAC;MACxB,CAAC;MAEDkD,MAAM,CAAC+B,OAAO,GAAI9C,KAAK,IAAK;QAC1B,MAAM,IAAIQ,KAAK,CAAC,6BAA6BR,KAAK,EAAE,CAAC;MACvD,CAAC;IAEH,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C/B,eAAe,CAAC,2BAA2B+B,KAAK,CAACC,OAAO,EAAE,CAAC;MAC3DpC,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAMgF,4BAA4B,GAAIrD,IAAI,IAAK;IAC7C,IAAI,CAACA,IAAI,CAACuD,OAAO,IAAIvD,IAAI,CAACuD,OAAO,CAACxC,MAAM,KAAK,CAAC,EAAE;MAC9CT,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;MAChD;IACF;IAEA,IAAI;MACF;MACA,MAAMqB,MAAM,GAAG5B,IAAI,CAACuD,OAAO,CAACvD,IAAI,CAACuD,OAAO,CAACxC,MAAM,GAAG,CAAC,CAAC;MAEpD,IAAI,CAACa,MAAM,CAAC4B,YAAY,IAAI,CAAC5B,MAAM,CAAC4B,YAAY,CAAC,CAAC,CAAC,EAAE;QACnDlD,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;QACxC;MACF;MAEA,MAAMkD,WAAW,GAAG7B,MAAM,CAAC4B,YAAY,CAAC,CAAC,CAAC;MAC1C,MAAME,UAAU,GAAGD,WAAW,CAACC,UAAU;;MAEzC;MACA,IAAID,WAAW,CAACE,KAAK,IAAIF,WAAW,CAACE,KAAK,CAAC5C,MAAM,GAAG,CAAC,EAAE;QACrD,IAAI6C,cAAc,GAAG,IAAI;QACzB,IAAIC,WAAW,GAAG,EAAE;QACpB,IAAIC,QAAQ,GAAG,EAAE;;QAEjB;QACAL,WAAW,CAACE,KAAK,CAACvE,OAAO,CAAC2E,IAAI,IAAI;UAChC,MAAMC,UAAU,GAAGD,IAAI,CAACC,UAAU,IAAI,CAAC;;UAEvC;UACA,IAAIA,UAAU,KAAKJ,cAAc,EAAE;YACjC;YACA,IAAIC,WAAW,EAAE;cACfC,QAAQ,CAAC5D,IAAI,CAAC;gBACZ+D,OAAO,EAAEL,cAAc,KAAK,CAAC,GAAG,WAAW,GAAG,WAAW;gBACzDM,IAAI,EAAEL,WAAW,CAACM,IAAI,CAAC,CAAC;gBACxBC,SAAS,EAAE,IAAIC,IAAI,CAAC;cACtB,CAAC,CAAC;YACJ;;YAEA;YACAT,cAAc,GAAGI,UAAU;YAC3BH,WAAW,GAAGE,IAAI,CAACA,IAAI,GAAG,GAAG;UAC/B,CAAC,MAAM;YACL;YACAF,WAAW,IAAIE,IAAI,CAACA,IAAI,GAAG,GAAG;UAChC;QACF,CAAC,CAAC;;QAEF;QACA,IAAIF,WAAW,EAAE;UACfC,QAAQ,CAAC5D,IAAI,CAAC;YACZ+D,OAAO,EAAEL,cAAc,KAAK,CAAC,GAAG,WAAW,GAAG,WAAW;YACzDM,IAAI,EAAEL,WAAW,CAACM,IAAI,CAAC,CAAC;YACxBC,SAAS,EAAE,IAAIC,IAAI,CAAC;UACtB,CAAC,CAAC;QACJ;;QAEA;QACA9F,qBAAqB,CAAC+F,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAGR,QAAQ,CAAC,CAAC;MACvD,CAAC,MAAM;QACL;QACAvF,qBAAqB,CAAC+F,IAAI,IAAI,CAC5B,GAAGA,IAAI,EACP;UACEL,OAAO,EAAEvF,WAAW,KAAK,YAAY,GAAG,WAAW,GAAG,aAAa;UACnEwF,IAAI,EAAER,UAAU;UAChBU,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC,CACF,CAAC;MACJ;IACF,CAAC,CAAC,OAAO7D,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChE/B,eAAe,CAAC,mCAAmC+B,KAAK,CAACC,OAAO,EAAE,CAAC;IACrE;EACF,CAAC;;EAED;EACA,MAAM8D,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAIjG,kBAAkB,CAACyC,MAAM,KAAK,CAAC,EAAE;IAErC,IAAIgC,OAAO,GAAG,8BAA8B;IAC5CzE,kBAAkB,CAACc,OAAO,CAACoF,OAAO,IAAI;MACpCzB,OAAO,IAAI,IAAIyB,OAAO,CAACJ,SAAS,CAACK,kBAAkB,CAAC,CAAC,KAAKD,OAAO,CAACP,OAAO,KAAKO,OAAO,CAACN,IAAI,IAAI;IAChG,CAAC,CAAC;IAEF,MAAMQ,IAAI,GAAG,IAAIrD,IAAI,CAAC,CAAC0B,OAAO,CAAC,EAAE;MAAEzB,IAAI,EAAE;IAAa,CAAC,CAAC;IACxD,MAAMqD,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACH,IAAI,CAAC;IACrC,MAAMI,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACrCF,CAAC,CAACG,IAAI,GAAGN,GAAG;IACZG,CAAC,CAACI,QAAQ,GAAG,uBAAuB;IACpCH,QAAQ,CAAC3C,IAAI,CAAC+C,WAAW,CAACL,CAAC,CAAC;IAC5BA,CAAC,CAACM,KAAK,CAAC,CAAC;IACTL,QAAQ,CAAC3C,IAAI,CAACiD,WAAW,CAACP,CAAC,CAAC;IAC5BF,GAAG,CAACU,eAAe,CAACX,GAAG,CAAC;EAC1B,CAAC;EAED,oBACE5G,OAAA;IAAKwH,SAAS,EAAC,2BAA2B;IAAAC,QAAA,gBACxCzH,OAAA;MAAKwH,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BzH,OAAA;QAAAyH,QAAA,EAAI;MAA0C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnD7H,OAAA;QAAGwH,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAE3B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAEN7H,OAAA;MAAKwH,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBACjCzH,OAAA;QAAKwH,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjCzH,OAAA;UACEwH,SAAS,EAAE,kBAAkBrH,WAAW,IAAIQ,WAAW,KAAK,YAAY,GAAG,WAAW,GAAG,EAAE,EAAG;UAC9FmH,OAAO,EAAE3H,WAAW,GAAGiD,aAAa,GAAG7B,wBAAyB;UAChEwG,QAAQ,EAAE1H,YAAa;UAAAoH,QAAA,EAEtBtH,WAAW,IAAIQ,WAAW,KAAK,YAAY,GACxC,gBAAgB,GAChB;QAA4B;UAAA+G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eAET7H,OAAA;UACEwH,SAAS,EAAE,4BAA4BrH,WAAW,IAAIQ,WAAW,KAAK,KAAK,GAAG,WAAW,GAAG,EAAE,EAAG;UACjGmH,OAAO,EAAE3H,WAAW,GAAGiD,aAAa,GAAGT,sBAAuB;UAC9DoF,QAAQ,EAAE1H,YAAa;UAAAoH,QAAA,EAEtBtH,WAAW,IAAIQ,WAAW,KAAK,KAAK,GACjC,oBAAoB,GACpB;QAA2B;UAAA+G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAELxH,YAAY,iBACXL,OAAA;QAAKwH,SAAS,EAAC,sBAAsB;QAAAC,QAAA,EAAC;MAEtC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CACN,EAEApH,YAAY,iBACXT,OAAA;QAAKwH,SAAS,EAAC,eAAe;QAAAC,QAAA,EAC3BhH;MAAY;QAAAiH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEN7H,OAAA;MAAKwH,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBACnCzH,OAAA;QAAKwH,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCzH,OAAA;UAAAyH,QAAA,EAAI;QAAmC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAC3CtH,kBAAkB,CAACyC,MAAM,GAAG,CAAC,iBAC5BhD,OAAA;UACEwH,SAAS,EAAC,iBAAiB;UAC3BM,OAAO,EAAEtB,kBAAmB;UAAAiB,QAAA,EAC7B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEN7H,OAAA;QAAKwH,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAChClH,kBAAkB,CAACyC,MAAM,KAAK,CAAC,gBAC9BhD,OAAA;UAAKwH,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAE7B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,gBAEN7H,OAAA;UAAKwH,SAAS,EAAC,qBAAqB;UAAAC,QAAA,EACjClH,kBAAkB,CAACyH,GAAG,CAAC,CAACvB,OAAO,EAAEwB,KAAK,kBACrCjI,OAAA;YAEEwH,SAAS,EAAE,sBAAsBf,OAAO,CAACP,OAAO,CAACgC,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,EAAG;YAAAV,QAAA,gBAEnFzH,OAAA;cAAKwH,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BzH,OAAA;gBAAMwH,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAEhB,OAAO,CAACJ,SAAS,CAACK,kBAAkB,CAAC;cAAC;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3E7H,OAAA;gBAAMwH,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAEhB,OAAO,CAACP;cAAO;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC,eACN7H,OAAA;cAAKwH,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAEhB,OAAO,CAACN;YAAI;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,GAP7CI,KAAK;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAQP,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC3H,EAAA,CAtYQD,uBAAuB;AAAAmI,EAAA,GAAvBnI,uBAAuB;AAwYhC,eAAeA,uBAAuB;AAAC,IAAAmI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}