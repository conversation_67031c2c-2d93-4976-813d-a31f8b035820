.interview-session {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.session-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 10px 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.question-counter {
  font-size: 16px;
  color: #666;
  font-weight: 500;
}

.session-timer {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #4285f4;
  font-weight: 500;
}

.timer-icon {
  font-size: 20px;
}

.timer-display {
  font-family: monospace;
  font-size: 16px;
}

.question-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 30px;
  margin-bottom: 30px;
}

.question-card h3 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #333;
}

textarea {
  width: 100%;
  min-height: 150px;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
  margin-bottom: 20px;
  resize: vertical;
}

button {
  background-color: #4285f4;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
}

button:hover {
  background-color: #3367d6;
}

button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.feedback {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
}

.feedback h4 {
  margin-top: 0;
  color: #333;
}

.score {
  font-weight: bold;
  margin-bottom: 10px;
  color: #4285f4;
}

.time-expired {
  background-color: #ffebee;
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
  text-align: center;
  border: 1px solid #ffcdd2;
}

.time-expired h4 {
  margin-top: 0;
  color: #d32f2f;
  font-size: 18px;
}

.time-expired p {
  margin-bottom: 15px;
  color: #555;
}

.time-expired button {
  background-color: #d32f2f;
}

.time-expired button:hover {
  background-color: #b71c1c;
}

.end-session {
  background-color: #f44336;
  margin-top: 20px;
}

.end-session:hover {
  background-color: #d32f2f;
}

.loading {
  text-align: center;
  padding: 40px;
  font-size: 18px;
  color: #666;
}

.interview-complete {
  text-align: center;
  padding: 40px;
  background-color: #e8f5e9;
  border-radius: 8px;
}

.resume-info {
  font-size: 14px;
  color: #4285f4;
  margin-top: 5px;
  background-color: rgba(66, 133, 244, 0.1);
  padding: 8px 12px;
  border-radius: 4px;
  display: inline-block;
}