{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\interview-ai-app\\\\interviewAI\\\\client\\\\src\\\\pages\\\\live-transcription\\\\GoogleSpeechDiarization.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport './GoogleSpeechDiarization.css';\nimport env from '../../utils/env';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction GoogleSpeechDiarization() {\n  _s();\n  // State for recording and transcription\n  const [isRecording, setIsRecording] = useState(false);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [transcriptSegments, setTranscriptSegments] = useState([]);\n  const [errorMessage, setErrorMessage] = useState('');\n  const [audioSource, setAudioSource] = useState('microphone'); // 'microphone' or 'tab'\n  const [apiKeyStatus, setApiKeyStatus] = useState('unknown'); // 'unknown', 'valid', 'invalid'\n  const [isTestingApiKey, setIsTestingApiKey] = useState(false);\n\n  // Refs for audio handling\n  const mediaRecorderRef = useRef(null);\n  const audioChunksRef = useRef([]);\n  const mediaStreamRef = useRef(null);\n\n  // Clean up function for audio resources\n  const cleanupAudio = () => {\n    if (mediaRecorderRef.current) {\n      if (mediaRecorderRef.current.state !== 'inactive') {\n        mediaRecorderRef.current.stop();\n      }\n      mediaRecorderRef.current = null;\n    }\n    if (mediaStreamRef.current) {\n      mediaStreamRef.current.getTracks().forEach(track => track.stop());\n      mediaStreamRef.current = null;\n    }\n    audioChunksRef.current = [];\n  };\n\n  // Clean up on component unmount\n  useEffect(() => {\n    return () => {\n      cleanupAudio();\n    };\n  }, []);\n\n  // Test the Google Speech API key on component mount\n  useEffect(() => {\n    testApiKey();\n  }, []);\n\n  // Function to test if the API key is valid\n  const testApiKey = async () => {\n    try {\n      setIsTestingApiKey(true);\n      setErrorMessage('');\n\n      // Get API key from environment variables\n      const apiKey = env.GOOGLE_SPEECH_API_KEY;\n      if (!apiKey) {\n        throw new Error(\"Google Speech API key not found. Please add it to your .env file as REACT_APP_GOOGLE_SPEECH_API_KEY\");\n      }\n\n      // Create a minimal audio sample for testing\n      // This is a very short, empty audio buffer encoded as base64\n      const testAudioContent = \"UklGRiQAAABXQVZFZm10IBAAAAABAAEARKwAAIhYAQACABAAZGF0YQAAAAA=\";\n\n      // Make a simple request to the Google Speech API with minimal audio data\n      const response = await fetch(`https://speech.googleapis.com/v1p1beta1/speech:recognize?key=${apiKey}`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          config: {\n            encoding: 'LINEAR16',\n            sampleRateHertz: 44100,\n            // Updated to match WAV header\n            languageCode: 'en-US'\n          },\n          audio: {\n            content: testAudioContent\n          }\n        })\n      });\n      const data = await response.json();\n\n      // Check if the response contains an error\n      if (data.error) {\n        // If the error is about invalid audio, that means the API key is valid\n        // but our test audio is too small (which is expected)\n        if (data.error.message && data.error.message.includes('audio')) {\n          console.log(\"Google Speech API key is valid (audio error is expected)\");\n          setApiKeyStatus('valid');\n        } else {\n          // Other errors likely mean the API key is invalid\n          console.error(\"Google Speech API key validation failed:\", data.error);\n          setApiKeyStatus('invalid');\n          setErrorMessage(`API Key Error: ${data.error.message || 'Invalid API key'}`);\n        }\n      } else {\n        // If no error, the key is definitely valid\n        console.log(\"Google Speech API key is valid\");\n        setApiKeyStatus('valid');\n      }\n    } catch (error) {\n      console.error(\"Error testing API key:\", error);\n\n      // Check for specific network errors\n      if (error.message === 'Failed to fetch') {\n        setErrorMessage(`Network error: Could not connect to Google API. This could be due to:\n          1. No internet connection\n          2. CORS restrictions in your browser\n          3. A firewall blocking the request\n\n          Try using the API key directly in your application instead.`);\n      } else {\n        setErrorMessage(`Error testing API key: ${error.message}`);\n      }\n      setApiKeyStatus('unknown'); // Set to unknown instead of invalid for network errors\n    } finally {\n      setIsTestingApiKey(false);\n    }\n  };\n\n  // Start recording from microphone with live transcription\n  const startMicrophoneRecording = async () => {\n    try {\n      cleanupAudio();\n      setErrorMessage('');\n      setAudioSource('microphone');\n\n      // Get microphone stream\n      const stream = await navigator.mediaDevices.getUserMedia({\n        audio: true\n      });\n      mediaStreamRef.current = stream;\n\n      // Create media recorder\n      const mediaRecorder = new MediaRecorder(stream);\n      mediaRecorderRef.current = mediaRecorder;\n\n      // Set up data handling for live transcription\n      mediaRecorder.ondataavailable = event => {\n        if (event.data.size > 0) {\n          audioChunksRef.current.push(event.data);\n\n          // Process the audio chunk immediately for live transcription\n          processAudioChunkForLiveTranscription(event.data);\n        }\n      };\n\n      // Handle recording stop\n      mediaRecorder.onstop = () => {\n        // Final processing with all collected audio for better diarization\n        processAudioForTranscription();\n      };\n\n      // Start recording with shorter intervals for more responsive live transcription\n      mediaRecorder.start(500); // Collect data every 500ms for more responsive updates\n      setIsRecording(true);\n      console.log(\"Microphone recording started with live transcription\");\n    } catch (error) {\n      console.error(\"Error starting microphone recording:\", error);\n      setErrorMessage(`Failed to start microphone: ${error.message}`);\n    }\n  };\n\n  // Process a single audio chunk for live transcription\n  const processAudioChunkForLiveTranscription = async audioChunk => {\n    try {\n      // Create audio blob from the single chunk\n      const audioBlob = new Blob([audioChunk], {\n        type: 'audio/webm'\n      });\n\n      // Skip very small audio chunks (likely silence)\n      if (audioBlob.size < 1000) {\n        return;\n      }\n\n      // Convert blob to base64\n      const reader = new FileReader();\n      reader.readAsDataURL(audioBlob);\n      reader.onloadend = async () => {\n        // Get base64 data (remove the data URL prefix)\n        const base64Audio = reader.result.split(',')[1];\n\n        // Get API key from environment variables\n        const apiKey = env.GOOGLE_SPEECH_API_KEY;\n        if (!apiKey) {\n          throw new Error(\"Google Speech API key not found\");\n        }\n\n        // Send to Google Speech API for live transcription\n        try {\n          const response = await fetch(`https://speech.googleapis.com/v1p1beta1/speech:recognize?key=${apiKey}`, {\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n              config: {\n                encoding: 'WEBM_OPUS',\n                languageCode: 'en-US',\n                enableAutomaticPunctuation: true,\n                // For live transcription, we use simpler settings for speed\n                model: 'latest_short',\n                useEnhanced: true\n              },\n              audio: {\n                content: base64Audio\n              }\n            })\n          });\n          if (!response.ok) {\n            return; // Silently fail for live updates to avoid flooding with errors\n          }\n          const data = await response.json();\n\n          // Process live transcription results\n          if (data.results && data.results.length > 0) {\n            // Get the transcript text\n            const transcript = data.results[0].alternatives[0].transcript;\n            if (transcript.trim()) {\n              // Add to transcript with current audio source as speaker\n              setTranscriptSegments(prev => {\n                // Check if we already have a \"live\" segment\n                const hasLiveSegment = prev.length > 0 && prev[prev.length - 1].isLive;\n                if (hasLiveSegment) {\n                  // Update the existing live segment\n                  const updatedSegments = [...prev];\n                  updatedSegments[updatedSegments.length - 1] = {\n                    ...updatedSegments[updatedSegments.length - 1],\n                    text: transcript,\n                    timestamp: new Date()\n                  };\n                  return updatedSegments;\n                } else {\n                  // Add a new live segment\n                  return [...prev, {\n                    speaker: audioSource === 'microphone' ? 'Candidate' : 'Interviewer',\n                    text: transcript,\n                    timestamp: new Date(),\n                    isLive: true // Mark as a live segment\n                  }];\n                }\n              });\n            }\n          }\n        } catch (error) {\n          // Silently fail for live updates\n          console.error(\"Live transcription error:\", error);\n        }\n      };\n    } catch (error) {\n      console.error(\"Error processing audio chunk:\", error);\n    }\n  };\n\n  // Start recording from tab audio\n  const startTabAudioRecording = async () => {\n    try {\n      cleanupAudio();\n      setErrorMessage('');\n      setAudioSource('tab');\n\n      // Request screen sharing with audio\n      const stream = await navigator.mediaDevices.getDisplayMedia({\n        video: true,\n        audio: true\n      });\n\n      // Check if audio is included\n      const audioTracks = stream.getAudioTracks();\n      if (audioTracks.length === 0) {\n        throw new Error(\"No audio track found. Please make sure to select 'Share audio' when sharing the tab.\");\n      }\n      mediaStreamRef.current = stream;\n\n      // Create media recorder\n      const mediaRecorder = new MediaRecorder(stream);\n      mediaRecorderRef.current = mediaRecorder;\n\n      // Set up data handling for live transcription\n      mediaRecorder.ondataavailable = event => {\n        if (event.data.size > 0) {\n          audioChunksRef.current.push(event.data);\n\n          // Process the audio chunk immediately for live transcription\n          processAudioChunkForLiveTranscription(event.data);\n        }\n      };\n\n      // Handle recording stop\n      mediaRecorder.onstop = () => {\n        // Final processing with all collected audio for better diarization\n        processAudioForTranscription();\n      };\n\n      // Handle stream ending (user stops sharing)\n      stream.getVideoTracks()[0].onended = () => {\n        stopRecording();\n      };\n\n      // Start recording with shorter intervals for more responsive live transcription\n      mediaRecorder.start(500); // Collect data every 500ms\n      setIsRecording(true);\n      console.log(\"Tab audio recording started with live transcription\");\n    } catch (error) {\n      console.error(\"Error starting tab audio recording:\", error);\n      setErrorMessage(`Failed to capture tab audio: ${error.message}`);\n    }\n  };\n\n  // Stop recording\n  const stopRecording = () => {\n    if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {\n      mediaRecorderRef.current.stop();\n    }\n    setIsRecording(false);\n  };\n\n  // Process recorded audio and send to Google Speech-to-Text API\n  const processAudioForTranscription = async () => {\n    if (audioChunksRef.current.length === 0) {\n      console.log(\"No audio data to process\");\n      return;\n    }\n\n    // Remove any live segments before final processing\n    setTranscriptSegments(prev => prev.filter(segment => !segment.isLive));\n    try {\n      setIsProcessing(true);\n\n      // Create audio blob from chunks\n      const audioBlob = new Blob(audioChunksRef.current, {\n        type: 'audio/webm'\n      });\n      console.log(\"Audio blob created:\", audioBlob.size, \"bytes\");\n\n      // Log audio details for debugging\n      const audioUrl = URL.createObjectURL(audioBlob);\n      console.log(\"Audio URL for debugging:\", audioUrl);\n\n      // Convert blob to base64\n      const reader = new FileReader();\n      reader.readAsDataURL(audioBlob);\n      reader.onloadend = async () => {\n        // Get base64 data (remove the data URL prefix)\n        const base64Audio = reader.result.split(',')[1];\n        console.log(\"Base64 audio length:\", base64Audio.length);\n\n        // Get API key from environment variables\n        const apiKey = env.GOOGLE_SPEECH_API_KEY;\n        if (!apiKey) {\n          throw new Error(\"Google Speech API key not found. Please add it to your .env file as REACT_APP_GOOGLE_SPEECH_API_KEY\");\n        }\n        console.log(\"Using Google Speech API key:\", apiKey);\n\n        // Prepare request to Google Speech-to-Text API\n        console.log(\"Sending request to Google Speech API...\");\n        const response = await fetch(`https://speech.googleapis.com/v1p1beta1/speech:recognize?key=${apiKey}`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            config: {\n              encoding: 'WEBM_OPUS',\n              // Don't specify sampleRateHertz to let Google detect it automatically\n              languageCode: 'en-US',\n              enableAutomaticPunctuation: true,\n              enableSpeakerDiarization: true,\n              diarizationSpeakerCount: 2,\n              model: 'default',\n              // Add additional settings for better results\n              useEnhanced: true,\n              metadata: {\n                // Valid values from Google's documentation\n                interactionType: 'DISCUSSION',\n                microphoneDistance: 'NEARFIELD',\n                recordingDeviceType: audioSource === 'microphone' ? 'PC' : 'OTHER_INDOOR_DEVICE',\n                originalMediaType: 'AUDIO'\n              }\n            },\n            audio: {\n              content: base64Audio\n            }\n          })\n        });\n        console.log(\"Response status:\", response.status);\n        if (!response.ok) {\n          var _errorData$error, _errorData$error2, _errorData$error3;\n          const errorData = await response.json();\n          console.error(\"API Error details:\", errorData);\n\n          // Extract detailed error information\n          const errorMessage = ((_errorData$error = errorData.error) === null || _errorData$error === void 0 ? void 0 : _errorData$error.message) || response.statusText;\n          const errorCode = ((_errorData$error2 = errorData.error) === null || _errorData$error2 === void 0 ? void 0 : _errorData$error2.code) || response.status;\n          const errorDetails = ((_errorData$error3 = errorData.error) === null || _errorData$error3 === void 0 ? void 0 : _errorData$error3.details) || [];\n\n          // Log detailed error information for debugging\n          console.error(\"Error code:\", errorCode);\n          console.error(\"Error message:\", errorMessage);\n          console.error(\"Error details:\", errorDetails);\n\n          // Add error to transcript for user visibility with more helpful message\n          setTranscriptSegments(prev => [...prev, {\n            speaker: 'System',\n            text: `Error from Google Speech API (${errorCode}): ${errorMessage}. Please check the console for more details.`,\n            timestamp: new Date()\n          }]);\n          throw new Error(`Google API error: ${errorMessage}`);\n        }\n        const data = await response.json();\n        console.log(\"Received transcription data:\", data);\n\n        // Add a default transcript if no speaker diarization is available\n        if (!data.results || data.results.length === 0) {\n          setTranscriptSegments(prev => [...prev, {\n            speaker: audioSource === 'microphone' ? 'Candidate' : 'Interviewer',\n            text: \"No speech detected. Please try speaking louder or check your microphone.\",\n            timestamp: new Date()\n          }]);\n        } else {\n          processTranscriptionResponse(data);\n        }\n\n        // Clear audio chunks for next recording\n        audioChunksRef.current = [];\n        setIsProcessing(false);\n      };\n      reader.onerror = error => {\n        console.error(\"FileReader error:\", error);\n        throw new Error(`Error reading audio data: ${error}`);\n      };\n    } catch (error) {\n      console.error(\"Error processing audio:\", error);\n      setErrorMessage(`Error processing audio: ${error.message}`);\n\n      // Add error to transcript for user visibility\n      setTranscriptSegments(prev => [...prev, {\n        speaker: 'System',\n        text: `Error: ${error.message}. Please try again.`,\n        timestamp: new Date()\n      }]);\n      setIsProcessing(false);\n    }\n  };\n\n  // Process the response from Google Speech-to-Text API\n  const processTranscriptionResponse = data => {\n    if (!data.results || data.results.length === 0) {\n      console.log(\"No transcription results returned\");\n      return;\n    }\n    try {\n      console.log(\"Processing transcription results:\", data.results);\n\n      // First, try to get a complete transcript\n      let fullTranscript = '';\n      data.results.forEach(result => {\n        if (result.alternatives && result.alternatives[0]) {\n          fullTranscript += result.alternatives[0].transcript + ' ';\n        }\n      });\n      fullTranscript = fullTranscript.trim();\n      console.log(\"Full transcript:\", fullTranscript);\n\n      // Check if we have any words with speaker tags\n      let hasWordLevelDiarization = false;\n      let diarizedWords = [];\n\n      // Look through all results for word-level speaker diarization\n      data.results.forEach(result => {\n        if (result.alternatives && result.alternatives[0] && result.alternatives[0].words) {\n          const words = result.alternatives[0].words;\n          if (words.length > 0 && words[0].speakerTag) {\n            hasWordLevelDiarization = true;\n            diarizedWords = diarizedWords.concat(words);\n          }\n        }\n      });\n      if (hasWordLevelDiarization && diarizedWords.length > 0) {\n        console.log(\"Found word-level diarization with\", diarizedWords.length, \"words\");\n\n        // Group words by speaker\n        let currentSpeaker = null;\n        let currentText = '';\n        let segments = [];\n        diarizedWords.forEach(word => {\n          const speakerTag = word.speakerTag || 0;\n\n          // If speaker changed or this is the first word\n          if (speakerTag !== currentSpeaker) {\n            // Save previous segment if it exists\n            if (currentText) {\n              // Map speaker tags to meaningful names based on audio source\n              let speakerName;\n              if (audioSource === 'tab') {\n                // For tab audio, speaker 1 is usually the interviewer (from YouTube/video)\n                speakerName = currentSpeaker === 1 ? 'Interviewer' : 'Candidate';\n              } else {\n                // For microphone, speaker 1 is usually the candidate (local user)\n                speakerName = currentSpeaker === 1 ? 'Candidate' : 'Interviewer';\n              }\n              segments.push({\n                speaker: speakerName,\n                text: currentText.trim(),\n                timestamp: new Date()\n              });\n            }\n\n            // Start new segment\n            currentSpeaker = speakerTag;\n            currentText = word.word + ' ';\n          } else {\n            // Continue current segment\n            currentText += word.word + ' ';\n          }\n        });\n\n        // Add the last segment\n        if (currentText) {\n          // Map speaker tags to meaningful names based on audio source\n          let speakerName;\n          if (audioSource === 'tab') {\n            // For tab audio, speaker 1 is usually the interviewer (from YouTube/video)\n            speakerName = currentSpeaker === 1 ? 'Interviewer' : 'Candidate';\n          } else {\n            // For microphone, speaker 1 is usually the candidate (local user)\n            speakerName = currentSpeaker === 1 ? 'Candidate' : 'Interviewer';\n          }\n          segments.push({\n            speaker: speakerName,\n            text: currentText.trim(),\n            timestamp: new Date()\n          });\n        }\n        console.log(\"Created segments:\", segments);\n\n        // Update transcript segments\n        setTranscriptSegments(prev => [...prev, ...segments]);\n      } else {\n        // No speaker diarization, add as single segment based on audio source\n        console.log(\"No word-level diarization found, using audio source for speaker identification\");\n        if (fullTranscript) {\n          setTranscriptSegments(prev => [...prev, {\n            speaker: audioSource === 'microphone' ? 'Candidate' : 'Interviewer',\n            text: fullTranscript,\n            timestamp: new Date()\n          }]);\n        } else {\n          console.log(\"No transcript text found in the response\");\n        }\n      }\n    } catch (error) {\n      console.error(\"Error processing transcription response:\", error);\n      setErrorMessage(`Error processing transcription: ${error.message}`);\n\n      // Add error to transcript for user visibility\n      setTranscriptSegments(prev => [...prev, {\n        speaker: 'System',\n        text: `Error processing speech: ${error.message}`,\n        timestamp: new Date()\n      }]);\n    }\n  };\n\n  // Download transcript as text file\n  const downloadTranscript = () => {\n    if (transcriptSegments.length === 0) return;\n    let content = \"Conversation Transcript:\\n\\n\";\n    transcriptSegments.forEach(segment => {\n      content += `[${segment.timestamp.toLocaleTimeString()}] ${segment.speaker}: ${segment.text}\\n`;\n    });\n    const blob = new Blob([content], {\n      type: 'text/plain'\n    });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = 'google_transcript.txt';\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"google-speech-diarization\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Google Meet-Style Transcription\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 628,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"description\",\n        children: \"This page uses Google's Speech-to-Text API to automatically identify speakers and transcribe conversations, similar to how Google Meet provides live captions with speaker identification.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 629,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"feature-badges\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"feature-badge\",\n          children: \"Automatic Speaker Detection\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 634,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"feature-badge highlight\",\n          children: \"Real-time Live Transcription\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 635,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"feature-badge\",\n          children: \"Voice-Based Identification\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 636,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"feature-badge\",\n          children: \"Google Meet-Style Captions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 637,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 633,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 627,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"api-status\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: `api-indicator ${apiKeyStatus}`,\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"status-dot\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 643,\n          columnNumber: 11\n        }, this), isTestingApiKey ? 'Testing API key...' : apiKeyStatus === 'valid' ? 'API Key: Valid' : apiKeyStatus === 'invalid' ? 'API Key: Invalid' : 'API Key: Not verified']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 642,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"api-buttons\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"test-api-button\",\n          onClick: testApiKey,\n          disabled: isTestingApiKey,\n          children: \"Test API Key\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 650,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: `https://console.cloud.google.com/apis/credentials`,\n          target: \"_blank\",\n          rel: \"noopener noreferrer\",\n          className: \"verify-link\",\n          children: \"Verify in Google Console\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 657,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 649,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 641,\n      columnNumber: 7\n    }, this), (apiKeyStatus === 'unknown' || errorMessage) && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"api-instructions\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"How to Verify Your API Key Manually:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 670,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"ol\", {\n        children: [/*#__PURE__*/_jsxDEV(\"li\", {\n          children: [\"Go to the \", /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"https://console.cloud.google.com/apis/credentials\",\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            children: \"Google Cloud Console\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 672,\n            columnNumber: 27\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 672,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"Select your project\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 673,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"Go to \\\"APIs & Services\\\" \\u2192 \\\"Credentials\\\"\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 674,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"Check that your API key exists and is not restricted in a way that prevents browser usage\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 675,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"Make sure the Speech-to-Text API is enabled for your project\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 676,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 671,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Your current API key is: \", /*#__PURE__*/_jsxDEV(\"code\", {\n          children: env.GOOGLE_SPEECH_API_KEY\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 678,\n          columnNumber: 39\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 678,\n        columnNumber: 11\n      }, this), errorMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"troubleshooting-tips\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          children: \"Troubleshooting Tips:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 682,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Check that your Google Cloud project has billing enabled\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 684,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Verify that you have enabled the Speech-to-Text API in your Google Cloud project\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 685,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Make sure your API key has access to the Speech-to-Text API\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 686,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Try recording shorter audio clips (15-30 seconds) for testing\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 687,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Speak clearly and ensure there's minimal background noise\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 688,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 683,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 681,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 669,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"controls-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"recording-controls\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: `control-button ${isRecording && audioSource === 'microphone' ? 'recording' : ''}`,\n          onClick: isRecording ? stopRecording : startMicrophoneRecording,\n          disabled: isProcessing || isTestingApiKey,\n          children: isRecording && audioSource === 'microphone' ? 'Stop Recording' : 'Start Live Microphone Transcription'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 697,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `control-button tab-audio ${isRecording && audioSource === 'tab' ? 'recording' : ''}`,\n          onClick: isRecording ? stopRecording : startTabAudioRecording,\n          disabled: isProcessing || isTestingApiKey,\n          children: isRecording && audioSource === 'tab' ? 'Stop Tab Recording' : 'Start Tab Audio Recording'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 707,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 696,\n        columnNumber: 9\n      }, this), isProcessing && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"processing-indicator\",\n        children: \"Processing audio... This may take a moment.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 719,\n        columnNumber: 11\n      }, this), errorMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-message\",\n        children: errorMessage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 725,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 695,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"transcript-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"transcript-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Transcript with Speaker Diarization\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 733,\n          columnNumber: 11\n        }, this), transcriptSegments.length > 0 && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"download-button\",\n          onClick: downloadTranscript,\n          children: \"Download Transcript\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 735,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 732,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"transcript-content\",\n        children: transcriptSegments.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"empty-state\",\n          children: \"No transcription yet. Start recording to see results.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 746,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"transcript-segments\",\n          children: transcriptSegments.map((segment, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `transcript-segment ${segment.speaker.toLowerCase().replace(' ', '-')} ${segment.isLive ? 'is-live' : ''}`,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"segment-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"timestamp\",\n                children: segment.timestamp.toLocaleTimeString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 757,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"speaker-label\",\n                children: segment.speaker === 'Interviewer' ? '👤 Interviewer' : segment.speaker === 'Candidate' ? '🎙️ Candidate' : segment.speaker === 'System' ? '⚙️ System' : segment.speaker\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 758,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 756,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"segment-text\",\n              children: segment.text\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 764,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 752,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 750,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 744,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 731,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 626,\n    columnNumber: 5\n  }, this);\n}\n_s(GoogleSpeechDiarization, \"PuHNEyT1NEel8gEMHGM3OTsp48A=\");\n_c = GoogleSpeechDiarization;\nexport default GoogleSpeechDiarization;\nvar _c;\n$RefreshReg$(_c, \"GoogleSpeechDiarization\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "env", "jsxDEV", "_jsxDEV", "GoogleSpeechDiarization", "_s", "isRecording", "setIsRecording", "isProcessing", "setIsProcessing", "transcriptSegments", "setTranscriptSegments", "errorMessage", "setErrorMessage", "audioSource", "setAudioSource", "apiKeyStatus", "setApiKeyStatus", "isTestingApiKey", "setIsTestingApiKey", "mediaRecorderRef", "audioChunksRef", "mediaStreamRef", "cleanupAudio", "current", "state", "stop", "getTracks", "for<PERSON>ach", "track", "test<PERSON>pi<PERSON>ey", "<PERSON><PERSON><PERSON><PERSON>", "GOOGLE_SPEECH_API_KEY", "Error", "test<PERSON><PERSON>o<PERSON><PERSON><PERSON>", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "config", "encoding", "sampleRateHertz", "languageCode", "audio", "content", "data", "json", "error", "message", "includes", "console", "log", "startMicrophoneRecording", "stream", "navigator", "mediaDevices", "getUserMedia", "mediaRecorder", "MediaRecorder", "ondataavailable", "event", "size", "push", "processAudioChunkForLiveTranscription", "onstop", "processAudioForTranscription", "start", "audioChunk", "audioBlob", "Blob", "type", "reader", "FileReader", "readAsDataURL", "onloadend", "base64Audio", "result", "split", "enableAutomaticPunctuation", "model", "useEnhanced", "ok", "results", "length", "transcript", "alternatives", "trim", "prev", "hasLiveSegment", "isLive", "updatedSegments", "text", "timestamp", "Date", "speaker", "startTabAudioRecording", "getDisplayMedia", "video", "audioTracks", "getAudioTracks", "getVideoTracks", "onended", "stopRecording", "filter", "segment", "audioUrl", "URL", "createObjectURL", "enableSpeakerDiarization", "diarizationSpeakerCount", "metadata", "interactionType", "microphoneDistance", "recordingDeviceType", "originalMediaType", "status", "_errorData$error", "_errorData$error2", "_errorData$error3", "errorData", "statusText", "errorCode", "code", "errorDetails", "details", "processTranscriptionResponse", "onerror", "fullTranscript", "hasWordLevelDiarization", "diarizedWords", "words", "speakerTag", "concat", "currentSpeaker", "currentText", "segments", "word", "<PERSON><PERSON><PERSON>", "downloadTranscript", "toLocaleTimeString", "blob", "url", "a", "document", "createElement", "href", "download", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "target", "rel", "map", "index", "toLowerCase", "replace", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/interview-ai-app/interviewAI/client/src/pages/live-transcription/GoogleSpeechDiarization.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport './GoogleSpeechDiarization.css';\nimport env from '../../utils/env';\n\nfunction GoogleSpeechDiarization() {\n  // State for recording and transcription\n  const [isRecording, setIsRecording] = useState(false);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [transcriptSegments, setTranscriptSegments] = useState([]);\n  const [errorMessage, setErrorMessage] = useState('');\n  const [audioSource, setAudioSource] = useState('microphone'); // 'microphone' or 'tab'\n  const [apiKeyStatus, setApiKeyStatus] = useState('unknown'); // 'unknown', 'valid', 'invalid'\n  const [isTestingApiKey, setIsTestingApiKey] = useState(false);\n\n  // Refs for audio handling\n  const mediaRecorderRef = useRef(null);\n  const audioChunksRef = useRef([]);\n  const mediaStreamRef = useRef(null);\n\n  // Clean up function for audio resources\n  const cleanupAudio = () => {\n    if (mediaRecorderRef.current) {\n      if (mediaRecorderRef.current.state !== 'inactive') {\n        mediaRecorderRef.current.stop();\n      }\n      mediaRecorderRef.current = null;\n    }\n\n    if (mediaStreamRef.current) {\n      mediaStreamRef.current.getTracks().forEach(track => track.stop());\n      mediaStreamRef.current = null;\n    }\n\n    audioChunksRef.current = [];\n  };\n\n  // Clean up on component unmount\n  useEffect(() => {\n    return () => {\n      cleanupAudio();\n    };\n  }, []);\n\n  // Test the Google Speech API key on component mount\n  useEffect(() => {\n    testApiKey();\n  }, []);\n\n  // Function to test if the API key is valid\n  const testApiKey = async () => {\n    try {\n      setIsTestingApiKey(true);\n      setErrorMessage('');\n\n      // Get API key from environment variables\n      const apiKey = env.GOOGLE_SPEECH_API_KEY;\n      if (!apiKey) {\n        throw new Error(\"Google Speech API key not found. Please add it to your .env file as REACT_APP_GOOGLE_SPEECH_API_KEY\");\n      }\n\n      // Create a minimal audio sample for testing\n      // This is a very short, empty audio buffer encoded as base64\n      const testAudioContent = \"UklGRiQAAABXQVZFZm10IBAAAAABAAEARKwAAIhYAQACABAAZGF0YQAAAAA=\";\n\n      // Make a simple request to the Google Speech API with minimal audio data\n      const response = await fetch(`https://speech.googleapis.com/v1p1beta1/speech:recognize?key=${apiKey}`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          config: {\n            encoding: 'LINEAR16',\n            sampleRateHertz: 44100, // Updated to match WAV header\n            languageCode: 'en-US',\n          },\n          audio: {\n            content: testAudioContent\n          }\n        })\n      });\n\n      const data = await response.json();\n\n      // Check if the response contains an error\n      if (data.error) {\n        // If the error is about invalid audio, that means the API key is valid\n        // but our test audio is too small (which is expected)\n        if (data.error.message && data.error.message.includes('audio')) {\n          console.log(\"Google Speech API key is valid (audio error is expected)\");\n          setApiKeyStatus('valid');\n        } else {\n          // Other errors likely mean the API key is invalid\n          console.error(\"Google Speech API key validation failed:\", data.error);\n          setApiKeyStatus('invalid');\n          setErrorMessage(`API Key Error: ${data.error.message || 'Invalid API key'}`);\n        }\n      } else {\n        // If no error, the key is definitely valid\n        console.log(\"Google Speech API key is valid\");\n        setApiKeyStatus('valid');\n      }\n    } catch (error) {\n      console.error(\"Error testing API key:\", error);\n\n      // Check for specific network errors\n      if (error.message === 'Failed to fetch') {\n        setErrorMessage(`Network error: Could not connect to Google API. This could be due to:\n          1. No internet connection\n          2. CORS restrictions in your browser\n          3. A firewall blocking the request\n\n          Try using the API key directly in your application instead.`);\n      } else {\n        setErrorMessage(`Error testing API key: ${error.message}`);\n      }\n\n      setApiKeyStatus('unknown'); // Set to unknown instead of invalid for network errors\n    } finally {\n      setIsTestingApiKey(false);\n    }\n  };\n\n  // Start recording from microphone with live transcription\n  const startMicrophoneRecording = async () => {\n    try {\n      cleanupAudio();\n      setErrorMessage('');\n      setAudioSource('microphone');\n\n      // Get microphone stream\n      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });\n      mediaStreamRef.current = stream;\n\n      // Create media recorder\n      const mediaRecorder = new MediaRecorder(stream);\n      mediaRecorderRef.current = mediaRecorder;\n\n      // Set up data handling for live transcription\n      mediaRecorder.ondataavailable = (event) => {\n        if (event.data.size > 0) {\n          audioChunksRef.current.push(event.data);\n\n          // Process the audio chunk immediately for live transcription\n          processAudioChunkForLiveTranscription(event.data);\n        }\n      };\n\n      // Handle recording stop\n      mediaRecorder.onstop = () => {\n        // Final processing with all collected audio for better diarization\n        processAudioForTranscription();\n      };\n\n      // Start recording with shorter intervals for more responsive live transcription\n      mediaRecorder.start(500); // Collect data every 500ms for more responsive updates\n      setIsRecording(true);\n\n      console.log(\"Microphone recording started with live transcription\");\n    } catch (error) {\n      console.error(\"Error starting microphone recording:\", error);\n      setErrorMessage(`Failed to start microphone: ${error.message}`);\n    }\n  };\n\n  // Process a single audio chunk for live transcription\n  const processAudioChunkForLiveTranscription = async (audioChunk) => {\n    try {\n      // Create audio blob from the single chunk\n      const audioBlob = new Blob([audioChunk], { type: 'audio/webm' });\n\n      // Skip very small audio chunks (likely silence)\n      if (audioBlob.size < 1000) {\n        return;\n      }\n\n      // Convert blob to base64\n      const reader = new FileReader();\n      reader.readAsDataURL(audioBlob);\n\n      reader.onloadend = async () => {\n        // Get base64 data (remove the data URL prefix)\n        const base64Audio = reader.result.split(',')[1];\n\n        // Get API key from environment variables\n        const apiKey = env.GOOGLE_SPEECH_API_KEY;\n        if (!apiKey) {\n          throw new Error(\"Google Speech API key not found\");\n        }\n\n        // Send to Google Speech API for live transcription\n        try {\n          const response = await fetch(`https://speech.googleapis.com/v1p1beta1/speech:recognize?key=${apiKey}`, {\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n              config: {\n                encoding: 'WEBM_OPUS',\n                languageCode: 'en-US',\n                enableAutomaticPunctuation: true,\n                // For live transcription, we use simpler settings for speed\n                model: 'latest_short',\n                useEnhanced: true\n              },\n              audio: {\n                content: base64Audio\n              }\n            })\n          });\n\n          if (!response.ok) {\n            return; // Silently fail for live updates to avoid flooding with errors\n          }\n\n          const data = await response.json();\n\n          // Process live transcription results\n          if (data.results && data.results.length > 0) {\n            // Get the transcript text\n            const transcript = data.results[0].alternatives[0].transcript;\n\n            if (transcript.trim()) {\n              // Add to transcript with current audio source as speaker\n              setTranscriptSegments(prev => {\n                // Check if we already have a \"live\" segment\n                const hasLiveSegment = prev.length > 0 && prev[prev.length - 1].isLive;\n\n                if (hasLiveSegment) {\n                  // Update the existing live segment\n                  const updatedSegments = [...prev];\n                  updatedSegments[updatedSegments.length - 1] = {\n                    ...updatedSegments[updatedSegments.length - 1],\n                    text: transcript,\n                    timestamp: new Date()\n                  };\n                  return updatedSegments;\n                } else {\n                  // Add a new live segment\n                  return [\n                    ...prev,\n                    {\n                      speaker: audioSource === 'microphone' ? 'Candidate' : 'Interviewer',\n                      text: transcript,\n                      timestamp: new Date(),\n                      isLive: true // Mark as a live segment\n                    }\n                  ];\n                }\n              });\n            }\n          }\n        } catch (error) {\n          // Silently fail for live updates\n          console.error(\"Live transcription error:\", error);\n        }\n      };\n    } catch (error) {\n      console.error(\"Error processing audio chunk:\", error);\n    }\n  };\n\n  // Start recording from tab audio\n  const startTabAudioRecording = async () => {\n    try {\n      cleanupAudio();\n      setErrorMessage('');\n      setAudioSource('tab');\n\n      // Request screen sharing with audio\n      const stream = await navigator.mediaDevices.getDisplayMedia({\n        video: true,\n        audio: true\n      });\n\n      // Check if audio is included\n      const audioTracks = stream.getAudioTracks();\n      if (audioTracks.length === 0) {\n        throw new Error(\"No audio track found. Please make sure to select 'Share audio' when sharing the tab.\");\n      }\n\n      mediaStreamRef.current = stream;\n\n      // Create media recorder\n      const mediaRecorder = new MediaRecorder(stream);\n      mediaRecorderRef.current = mediaRecorder;\n\n      // Set up data handling for live transcription\n      mediaRecorder.ondataavailable = (event) => {\n        if (event.data.size > 0) {\n          audioChunksRef.current.push(event.data);\n\n          // Process the audio chunk immediately for live transcription\n          processAudioChunkForLiveTranscription(event.data);\n        }\n      };\n\n      // Handle recording stop\n      mediaRecorder.onstop = () => {\n        // Final processing with all collected audio for better diarization\n        processAudioForTranscription();\n      };\n\n      // Handle stream ending (user stops sharing)\n      stream.getVideoTracks()[0].onended = () => {\n        stopRecording();\n      };\n\n      // Start recording with shorter intervals for more responsive live transcription\n      mediaRecorder.start(500); // Collect data every 500ms\n      setIsRecording(true);\n\n      console.log(\"Tab audio recording started with live transcription\");\n    } catch (error) {\n      console.error(\"Error starting tab audio recording:\", error);\n      setErrorMessage(`Failed to capture tab audio: ${error.message}`);\n    }\n  };\n\n  // Stop recording\n  const stopRecording = () => {\n    if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {\n      mediaRecorderRef.current.stop();\n    }\n    setIsRecording(false);\n  };\n\n  // Process recorded audio and send to Google Speech-to-Text API\n  const processAudioForTranscription = async () => {\n    if (audioChunksRef.current.length === 0) {\n      console.log(\"No audio data to process\");\n      return;\n    }\n\n    // Remove any live segments before final processing\n    setTranscriptSegments(prev => prev.filter(segment => !segment.isLive));\n\n    try {\n      setIsProcessing(true);\n\n      // Create audio blob from chunks\n      const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });\n      console.log(\"Audio blob created:\", audioBlob.size, \"bytes\");\n\n      // Log audio details for debugging\n      const audioUrl = URL.createObjectURL(audioBlob);\n      console.log(\"Audio URL for debugging:\", audioUrl);\n\n      // Convert blob to base64\n      const reader = new FileReader();\n      reader.readAsDataURL(audioBlob);\n\n      reader.onloadend = async () => {\n        // Get base64 data (remove the data URL prefix)\n        const base64Audio = reader.result.split(',')[1];\n        console.log(\"Base64 audio length:\", base64Audio.length);\n\n        // Get API key from environment variables\n        const apiKey = env.GOOGLE_SPEECH_API_KEY;\n        if (!apiKey) {\n          throw new Error(\"Google Speech API key not found. Please add it to your .env file as REACT_APP_GOOGLE_SPEECH_API_KEY\");\n        }\n        console.log(\"Using Google Speech API key:\", apiKey);\n\n        // Prepare request to Google Speech-to-Text API\n        console.log(\"Sending request to Google Speech API...\");\n        const response = await fetch(`https://speech.googleapis.com/v1p1beta1/speech:recognize?key=${apiKey}`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            config: {\n              encoding: 'WEBM_OPUS',\n              // Don't specify sampleRateHertz to let Google detect it automatically\n              languageCode: 'en-US',\n              enableAutomaticPunctuation: true,\n              enableSpeakerDiarization: true,\n              diarizationSpeakerCount: 2,\n              model: 'default',\n              // Add additional settings for better results\n              useEnhanced: true,\n              metadata: {\n                // Valid values from Google's documentation\n                interactionType: 'DISCUSSION',\n                microphoneDistance: 'NEARFIELD',\n                recordingDeviceType: audioSource === 'microphone' ? 'PC' : 'OTHER_INDOOR_DEVICE',\n                originalMediaType: 'AUDIO'\n              }\n            },\n            audio: {\n              content: base64Audio\n            }\n          })\n        });\n\n        console.log(\"Response status:\", response.status);\n\n        if (!response.ok) {\n          const errorData = await response.json();\n          console.error(\"API Error details:\", errorData);\n\n          // Extract detailed error information\n          const errorMessage = errorData.error?.message || response.statusText;\n          const errorCode = errorData.error?.code || response.status;\n          const errorDetails = errorData.error?.details || [];\n\n          // Log detailed error information for debugging\n          console.error(\"Error code:\", errorCode);\n          console.error(\"Error message:\", errorMessage);\n          console.error(\"Error details:\", errorDetails);\n\n          // Add error to transcript for user visibility with more helpful message\n          setTranscriptSegments(prev => [\n            ...prev,\n            {\n              speaker: 'System',\n              text: `Error from Google Speech API (${errorCode}): ${errorMessage}. Please check the console for more details.`,\n              timestamp: new Date()\n            }\n          ]);\n\n          throw new Error(`Google API error: ${errorMessage}`);\n        }\n\n        const data = await response.json();\n        console.log(\"Received transcription data:\", data);\n\n        // Add a default transcript if no speaker diarization is available\n        if (!data.results || data.results.length === 0) {\n          setTranscriptSegments(prev => [\n            ...prev,\n            {\n              speaker: audioSource === 'microphone' ? 'Candidate' : 'Interviewer',\n              text: \"No speech detected. Please try speaking louder or check your microphone.\",\n              timestamp: new Date()\n            }\n          ]);\n        } else {\n          processTranscriptionResponse(data);\n        }\n\n        // Clear audio chunks for next recording\n        audioChunksRef.current = [];\n        setIsProcessing(false);\n      };\n\n      reader.onerror = (error) => {\n        console.error(\"FileReader error:\", error);\n        throw new Error(`Error reading audio data: ${error}`);\n      };\n\n    } catch (error) {\n      console.error(\"Error processing audio:\", error);\n      setErrorMessage(`Error processing audio: ${error.message}`);\n\n      // Add error to transcript for user visibility\n      setTranscriptSegments(prev => [\n        ...prev,\n        {\n          speaker: 'System',\n          text: `Error: ${error.message}. Please try again.`,\n          timestamp: new Date()\n        }\n      ]);\n\n      setIsProcessing(false);\n    }\n  };\n\n  // Process the response from Google Speech-to-Text API\n  const processTranscriptionResponse = (data) => {\n    if (!data.results || data.results.length === 0) {\n      console.log(\"No transcription results returned\");\n      return;\n    }\n\n    try {\n      console.log(\"Processing transcription results:\", data.results);\n\n      // First, try to get a complete transcript\n      let fullTranscript = '';\n      data.results.forEach(result => {\n        if (result.alternatives && result.alternatives[0]) {\n          fullTranscript += result.alternatives[0].transcript + ' ';\n        }\n      });\n\n      fullTranscript = fullTranscript.trim();\n      console.log(\"Full transcript:\", fullTranscript);\n\n      // Check if we have any words with speaker tags\n      let hasWordLevelDiarization = false;\n      let diarizedWords = [];\n\n      // Look through all results for word-level speaker diarization\n      data.results.forEach(result => {\n        if (result.alternatives && result.alternatives[0] && result.alternatives[0].words) {\n          const words = result.alternatives[0].words;\n          if (words.length > 0 && words[0].speakerTag) {\n            hasWordLevelDiarization = true;\n            diarizedWords = diarizedWords.concat(words);\n          }\n        }\n      });\n\n      if (hasWordLevelDiarization && diarizedWords.length > 0) {\n        console.log(\"Found word-level diarization with\", diarizedWords.length, \"words\");\n\n        // Group words by speaker\n        let currentSpeaker = null;\n        let currentText = '';\n        let segments = [];\n\n        diarizedWords.forEach(word => {\n          const speakerTag = word.speakerTag || 0;\n\n          // If speaker changed or this is the first word\n          if (speakerTag !== currentSpeaker) {\n            // Save previous segment if it exists\n            if (currentText) {\n              // Map speaker tags to meaningful names based on audio source\n              let speakerName;\n              if (audioSource === 'tab') {\n                // For tab audio, speaker 1 is usually the interviewer (from YouTube/video)\n                speakerName = currentSpeaker === 1 ? 'Interviewer' : 'Candidate';\n              } else {\n                // For microphone, speaker 1 is usually the candidate (local user)\n                speakerName = currentSpeaker === 1 ? 'Candidate' : 'Interviewer';\n              }\n\n              segments.push({\n                speaker: speakerName,\n                text: currentText.trim(),\n                timestamp: new Date()\n              });\n            }\n\n            // Start new segment\n            currentSpeaker = speakerTag;\n            currentText = word.word + ' ';\n          } else {\n            // Continue current segment\n            currentText += word.word + ' ';\n          }\n        });\n\n        // Add the last segment\n        if (currentText) {\n          // Map speaker tags to meaningful names based on audio source\n          let speakerName;\n          if (audioSource === 'tab') {\n            // For tab audio, speaker 1 is usually the interviewer (from YouTube/video)\n            speakerName = currentSpeaker === 1 ? 'Interviewer' : 'Candidate';\n          } else {\n            // For microphone, speaker 1 is usually the candidate (local user)\n            speakerName = currentSpeaker === 1 ? 'Candidate' : 'Interviewer';\n          }\n\n          segments.push({\n            speaker: speakerName,\n            text: currentText.trim(),\n            timestamp: new Date()\n          });\n        }\n\n        console.log(\"Created segments:\", segments);\n\n        // Update transcript segments\n        setTranscriptSegments(prev => [...prev, ...segments]);\n      } else {\n        // No speaker diarization, add as single segment based on audio source\n        console.log(\"No word-level diarization found, using audio source for speaker identification\");\n\n        if (fullTranscript) {\n          setTranscriptSegments(prev => [\n            ...prev,\n            {\n              speaker: audioSource === 'microphone' ? 'Candidate' : 'Interviewer',\n              text: fullTranscript,\n              timestamp: new Date()\n            }\n          ]);\n        } else {\n          console.log(\"No transcript text found in the response\");\n        }\n      }\n    } catch (error) {\n      console.error(\"Error processing transcription response:\", error);\n      setErrorMessage(`Error processing transcription: ${error.message}`);\n\n      // Add error to transcript for user visibility\n      setTranscriptSegments(prev => [\n        ...prev,\n        {\n          speaker: 'System',\n          text: `Error processing speech: ${error.message}`,\n          timestamp: new Date()\n        }\n      ]);\n    }\n  };\n\n  // Download transcript as text file\n  const downloadTranscript = () => {\n    if (transcriptSegments.length === 0) return;\n\n    let content = \"Conversation Transcript:\\n\\n\";\n    transcriptSegments.forEach(segment => {\n      content += `[${segment.timestamp.toLocaleTimeString()}] ${segment.speaker}: ${segment.text}\\n`;\n    });\n\n    const blob = new Blob([content], { type: 'text/plain' });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = 'google_transcript.txt';\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n\n  return (\n    <div className=\"google-speech-diarization\">\n      <div className=\"page-header\">\n        <h1>Google Meet-Style Transcription</h1>\n        <p className=\"description\">\n          This page uses Google's Speech-to-Text API to automatically identify speakers and transcribe conversations,\n          similar to how Google Meet provides live captions with speaker identification.\n        </p>\n        <div className=\"feature-badges\">\n          <span className=\"feature-badge\">Automatic Speaker Detection</span>\n          <span className=\"feature-badge highlight\">Real-time Live Transcription</span>\n          <span className=\"feature-badge\">Voice-Based Identification</span>\n          <span className=\"feature-badge\">Google Meet-Style Captions</span>\n        </div>\n      </div>\n\n      <div className=\"api-status\">\n        <div className={`api-indicator ${apiKeyStatus}`}>\n          <span className=\"status-dot\"></span>\n          {isTestingApiKey ? 'Testing API key...' :\n            apiKeyStatus === 'valid' ? 'API Key: Valid' :\n            apiKeyStatus === 'invalid' ? 'API Key: Invalid' :\n            'API Key: Not verified'}\n        </div>\n        <div className=\"api-buttons\">\n          <button\n            className=\"test-api-button\"\n            onClick={testApiKey}\n            disabled={isTestingApiKey}\n          >\n            Test API Key\n          </button>\n          <a\n            href={`https://console.cloud.google.com/apis/credentials`}\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n            className=\"verify-link\"\n          >\n            Verify in Google Console\n          </a>\n        </div>\n      </div>\n\n      {(apiKeyStatus === 'unknown' || errorMessage) && (\n        <div className=\"api-instructions\">\n          <h3>How to Verify Your API Key Manually:</h3>\n          <ol>\n            <li>Go to the <a href=\"https://console.cloud.google.com/apis/credentials\" target=\"_blank\" rel=\"noopener noreferrer\">Google Cloud Console</a></li>\n            <li>Select your project</li>\n            <li>Go to \"APIs & Services\" → \"Credentials\"</li>\n            <li>Check that your API key exists and is not restricted in a way that prevents browser usage</li>\n            <li>Make sure the Speech-to-Text API is enabled for your project</li>\n          </ol>\n          <p>Your current API key is: <code>{env.GOOGLE_SPEECH_API_KEY}</code></p>\n\n          {errorMessage && (\n            <div className=\"troubleshooting-tips\">\n              <h4>Troubleshooting Tips:</h4>\n              <ul>\n                <li>Check that your Google Cloud project has billing enabled</li>\n                <li>Verify that you have enabled the Speech-to-Text API in your Google Cloud project</li>\n                <li>Make sure your API key has access to the Speech-to-Text API</li>\n                <li>Try recording shorter audio clips (15-30 seconds) for testing</li>\n                <li>Speak clearly and ensure there's minimal background noise</li>\n              </ul>\n            </div>\n          )}\n        </div>\n      )}\n\n      <div className=\"controls-container\">\n        <div className=\"recording-controls\">\n          <button\n            className={`control-button ${isRecording && audioSource === 'microphone' ? 'recording' : ''}`}\n            onClick={isRecording ? stopRecording : startMicrophoneRecording}\n            disabled={isProcessing || isTestingApiKey}\n          >\n            {isRecording && audioSource === 'microphone'\n              ? 'Stop Recording'\n              : 'Start Live Microphone Transcription'}\n          </button>\n\n          <button\n            className={`control-button tab-audio ${isRecording && audioSource === 'tab' ? 'recording' : ''}`}\n            onClick={isRecording ? stopRecording : startTabAudioRecording}\n            disabled={isProcessing || isTestingApiKey}\n          >\n            {isRecording && audioSource === 'tab'\n              ? 'Stop Tab Recording'\n              : 'Start Tab Audio Recording'}\n          </button>\n        </div>\n\n        {isProcessing && (\n          <div className=\"processing-indicator\">\n            Processing audio... This may take a moment.\n          </div>\n        )}\n\n        {errorMessage && (\n          <div className=\"error-message\">\n            {errorMessage}\n          </div>\n        )}\n      </div>\n\n      <div className=\"transcript-container\">\n        <div className=\"transcript-header\">\n          <h2>Transcript with Speaker Diarization</h2>\n          {transcriptSegments.length > 0 && (\n            <button\n              className=\"download-button\"\n              onClick={downloadTranscript}\n            >\n              Download Transcript\n            </button>\n          )}\n        </div>\n\n        <div className=\"transcript-content\">\n          {transcriptSegments.length === 0 ? (\n            <div className=\"empty-state\">\n              No transcription yet. Start recording to see results.\n            </div>\n          ) : (\n            <div className=\"transcript-segments\">\n              {transcriptSegments.map((segment, index) => (\n                <div\n                  key={index}\n                  className={`transcript-segment ${segment.speaker.toLowerCase().replace(' ', '-')} ${segment.isLive ? 'is-live' : ''}`}\n                >\n                  <div className=\"segment-header\">\n                    <span className=\"timestamp\">{segment.timestamp.toLocaleTimeString()}</span>\n                    <span className=\"speaker-label\">\n                      {segment.speaker === 'Interviewer' ? '👤 Interviewer' :\n                       segment.speaker === 'Candidate' ? '🎙️ Candidate' :\n                       segment.speaker === 'System' ? '⚙️ System' : segment.speaker}\n                    </span>\n                  </div>\n                  <div className=\"segment-text\">{segment.text}</div>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default GoogleSpeechDiarization;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,+BAA+B;AACtC,OAAOC,GAAG,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,SAASC,uBAAuBA,CAAA,EAAG;EAAAC,EAAA;EACjC;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGT,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACU,YAAY,EAAEC,eAAe,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACY,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAACc,YAAY,EAAEC,eAAe,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACgB,WAAW,EAAEC,cAAc,CAAC,GAAGjB,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC;EAC9D,MAAM,CAACkB,YAAY,EAAEC,eAAe,CAAC,GAAGnB,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;EAC7D,MAAM,CAACoB,eAAe,EAAEC,kBAAkB,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACA,MAAMsB,gBAAgB,GAAGpB,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMqB,cAAc,GAAGrB,MAAM,CAAC,EAAE,CAAC;EACjC,MAAMsB,cAAc,GAAGtB,MAAM,CAAC,IAAI,CAAC;;EAEnC;EACA,MAAMuB,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIH,gBAAgB,CAACI,OAAO,EAAE;MAC5B,IAAIJ,gBAAgB,CAACI,OAAO,CAACC,KAAK,KAAK,UAAU,EAAE;QACjDL,gBAAgB,CAACI,OAAO,CAACE,IAAI,CAAC,CAAC;MACjC;MACAN,gBAAgB,CAACI,OAAO,GAAG,IAAI;IACjC;IAEA,IAAIF,cAAc,CAACE,OAAO,EAAE;MAC1BF,cAAc,CAACE,OAAO,CAACG,SAAS,CAAC,CAAC,CAACC,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACH,IAAI,CAAC,CAAC,CAAC;MACjEJ,cAAc,CAACE,OAAO,GAAG,IAAI;IAC/B;IAEAH,cAAc,CAACG,OAAO,GAAG,EAAE;EAC7B,CAAC;;EAED;EACAzB,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACXwB,YAAY,CAAC,CAAC;IAChB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAxB,SAAS,CAAC,MAAM;IACd+B,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMA,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFX,kBAAkB,CAAC,IAAI,CAAC;MACxBN,eAAe,CAAC,EAAE,CAAC;;MAEnB;MACA,MAAMkB,MAAM,GAAG9B,GAAG,CAAC+B,qBAAqB;MACxC,IAAI,CAACD,MAAM,EAAE;QACX,MAAM,IAAIE,KAAK,CAAC,qGAAqG,CAAC;MACxH;;MAEA;MACA;MACA,MAAMC,gBAAgB,GAAG,8DAA8D;;MAEvF;MACA,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,gEAAgEL,MAAM,EAAE,EAAE;QACrGM,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBC,MAAM,EAAE;YACNC,QAAQ,EAAE,UAAU;YACpBC,eAAe,EAAE,KAAK;YAAE;YACxBC,YAAY,EAAE;UAChB,CAAC;UACDC,KAAK,EAAE;YACLC,OAAO,EAAEb;UACX;QACF,CAAC;MACH,CAAC,CAAC;MAEF,MAAMc,IAAI,GAAG,MAAMb,QAAQ,CAACc,IAAI,CAAC,CAAC;;MAElC;MACA,IAAID,IAAI,CAACE,KAAK,EAAE;QACd;QACA;QACA,IAAIF,IAAI,CAACE,KAAK,CAACC,OAAO,IAAIH,IAAI,CAACE,KAAK,CAACC,OAAO,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;UAC9DC,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;UACvErC,eAAe,CAAC,OAAO,CAAC;QAC1B,CAAC,MAAM;UACL;UACAoC,OAAO,CAACH,KAAK,CAAC,0CAA0C,EAAEF,IAAI,CAACE,KAAK,CAAC;UACrEjC,eAAe,CAAC,SAAS,CAAC;UAC1BJ,eAAe,CAAC,kBAAkBmC,IAAI,CAACE,KAAK,CAACC,OAAO,IAAI,iBAAiB,EAAE,CAAC;QAC9E;MACF,CAAC,MAAM;QACL;QACAE,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;QAC7CrC,eAAe,CAAC,OAAO,CAAC;MAC1B;IACF,CAAC,CAAC,OAAOiC,KAAK,EAAE;MACdG,OAAO,CAACH,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;;MAE9C;MACA,IAAIA,KAAK,CAACC,OAAO,KAAK,iBAAiB,EAAE;QACvCtC,eAAe,CAAC;AACxB;AACA;AACA;AACA;AACA,sEAAsE,CAAC;MACjE,CAAC,MAAM;QACLA,eAAe,CAAC,0BAA0BqC,KAAK,CAACC,OAAO,EAAE,CAAC;MAC5D;MAEAlC,eAAe,CAAC,SAAS,CAAC,CAAC,CAAC;IAC9B,CAAC,SAAS;MACRE,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;;EAED;EACA,MAAMoC,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3C,IAAI;MACFhC,YAAY,CAAC,CAAC;MACdV,eAAe,CAAC,EAAE,CAAC;MACnBE,cAAc,CAAC,YAAY,CAAC;;MAE5B;MACA,MAAMyC,MAAM,GAAG,MAAMC,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;QAAEb,KAAK,EAAE;MAAK,CAAC,CAAC;MACzExB,cAAc,CAACE,OAAO,GAAGgC,MAAM;;MAE/B;MACA,MAAMI,aAAa,GAAG,IAAIC,aAAa,CAACL,MAAM,CAAC;MAC/CpC,gBAAgB,CAACI,OAAO,GAAGoC,aAAa;;MAExC;MACAA,aAAa,CAACE,eAAe,GAAIC,KAAK,IAAK;QACzC,IAAIA,KAAK,CAACf,IAAI,CAACgB,IAAI,GAAG,CAAC,EAAE;UACvB3C,cAAc,CAACG,OAAO,CAACyC,IAAI,CAACF,KAAK,CAACf,IAAI,CAAC;;UAEvC;UACAkB,qCAAqC,CAACH,KAAK,CAACf,IAAI,CAAC;QACnD;MACF,CAAC;;MAED;MACAY,aAAa,CAACO,MAAM,GAAG,MAAM;QAC3B;QACAC,4BAA4B,CAAC,CAAC;MAChC,CAAC;;MAED;MACAR,aAAa,CAACS,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;MAC1B9D,cAAc,CAAC,IAAI,CAAC;MAEpB8C,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;IACrE,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACdG,OAAO,CAACH,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5DrC,eAAe,CAAC,+BAA+BqC,KAAK,CAACC,OAAO,EAAE,CAAC;IACjE;EACF,CAAC;;EAED;EACA,MAAMe,qCAAqC,GAAG,MAAOI,UAAU,IAAK;IAClE,IAAI;MACF;MACA,MAAMC,SAAS,GAAG,IAAIC,IAAI,CAAC,CAACF,UAAU,CAAC,EAAE;QAAEG,IAAI,EAAE;MAAa,CAAC,CAAC;;MAEhE;MACA,IAAIF,SAAS,CAACP,IAAI,GAAG,IAAI,EAAE;QACzB;MACF;;MAEA;MACA,MAAMU,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,aAAa,CAACL,SAAS,CAAC;MAE/BG,MAAM,CAACG,SAAS,GAAG,YAAY;QAC7B;QACA,MAAMC,WAAW,GAAGJ,MAAM,CAACK,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;QAE/C;QACA,MAAMjD,MAAM,GAAG9B,GAAG,CAAC+B,qBAAqB;QACxC,IAAI,CAACD,MAAM,EAAE;UACX,MAAM,IAAIE,KAAK,CAAC,iCAAiC,CAAC;QACpD;;QAEA;QACA,IAAI;UACF,MAAME,QAAQ,GAAG,MAAMC,KAAK,CAAC,gEAAgEL,MAAM,EAAE,EAAE;YACrGM,MAAM,EAAE,MAAM;YACdC,OAAO,EAAE;cACP,cAAc,EAAE;YAClB,CAAC;YACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;cACnBC,MAAM,EAAE;gBACNC,QAAQ,EAAE,WAAW;gBACrBE,YAAY,EAAE,OAAO;gBACrBoC,0BAA0B,EAAE,IAAI;gBAChC;gBACAC,KAAK,EAAE,cAAc;gBACrBC,WAAW,EAAE;cACf,CAAC;cACDrC,KAAK,EAAE;gBACLC,OAAO,EAAE+B;cACX;YACF,CAAC;UACH,CAAC,CAAC;UAEF,IAAI,CAAC3C,QAAQ,CAACiD,EAAE,EAAE;YAChB,OAAO,CAAC;UACV;UAEA,MAAMpC,IAAI,GAAG,MAAMb,QAAQ,CAACc,IAAI,CAAC,CAAC;;UAElC;UACA,IAAID,IAAI,CAACqC,OAAO,IAAIrC,IAAI,CAACqC,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;YAC3C;YACA,MAAMC,UAAU,GAAGvC,IAAI,CAACqC,OAAO,CAAC,CAAC,CAAC,CAACG,YAAY,CAAC,CAAC,CAAC,CAACD,UAAU;YAE7D,IAAIA,UAAU,CAACE,IAAI,CAAC,CAAC,EAAE;cACrB;cACA9E,qBAAqB,CAAC+E,IAAI,IAAI;gBAC5B;gBACA,MAAMC,cAAc,GAAGD,IAAI,CAACJ,MAAM,GAAG,CAAC,IAAII,IAAI,CAACA,IAAI,CAACJ,MAAM,GAAG,CAAC,CAAC,CAACM,MAAM;gBAEtE,IAAID,cAAc,EAAE;kBAClB;kBACA,MAAME,eAAe,GAAG,CAAC,GAAGH,IAAI,CAAC;kBACjCG,eAAe,CAACA,eAAe,CAACP,MAAM,GAAG,CAAC,CAAC,GAAG;oBAC5C,GAAGO,eAAe,CAACA,eAAe,CAACP,MAAM,GAAG,CAAC,CAAC;oBAC9CQ,IAAI,EAAEP,UAAU;oBAChBQ,SAAS,EAAE,IAAIC,IAAI,CAAC;kBACtB,CAAC;kBACD,OAAOH,eAAe;gBACxB,CAAC,MAAM;kBACL;kBACA,OAAO,CACL,GAAGH,IAAI,EACP;oBACEO,OAAO,EAAEnF,WAAW,KAAK,YAAY,GAAG,WAAW,GAAG,aAAa;oBACnEgF,IAAI,EAAEP,UAAU;oBAChBQ,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;oBACrBJ,MAAM,EAAE,IAAI,CAAC;kBACf,CAAC,CACF;gBACH;cACF,CAAC,CAAC;YACJ;UACF;QACF,CAAC,CAAC,OAAO1C,KAAK,EAAE;UACd;UACAG,OAAO,CAACH,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACnD;MACF,CAAC;IACH,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdG,OAAO,CAACH,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD;EACF,CAAC;;EAED;EACA,MAAMgD,sBAAsB,GAAG,MAAAA,CAAA,KAAY;IACzC,IAAI;MACF3E,YAAY,CAAC,CAAC;MACdV,eAAe,CAAC,EAAE,CAAC;MACnBE,cAAc,CAAC,KAAK,CAAC;;MAErB;MACA,MAAMyC,MAAM,GAAG,MAAMC,SAAS,CAACC,YAAY,CAACyC,eAAe,CAAC;QAC1DC,KAAK,EAAE,IAAI;QACXtD,KAAK,EAAE;MACT,CAAC,CAAC;;MAEF;MACA,MAAMuD,WAAW,GAAG7C,MAAM,CAAC8C,cAAc,CAAC,CAAC;MAC3C,IAAID,WAAW,CAACf,MAAM,KAAK,CAAC,EAAE;QAC5B,MAAM,IAAIrD,KAAK,CAAC,sFAAsF,CAAC;MACzG;MAEAX,cAAc,CAACE,OAAO,GAAGgC,MAAM;;MAE/B;MACA,MAAMI,aAAa,GAAG,IAAIC,aAAa,CAACL,MAAM,CAAC;MAC/CpC,gBAAgB,CAACI,OAAO,GAAGoC,aAAa;;MAExC;MACAA,aAAa,CAACE,eAAe,GAAIC,KAAK,IAAK;QACzC,IAAIA,KAAK,CAACf,IAAI,CAACgB,IAAI,GAAG,CAAC,EAAE;UACvB3C,cAAc,CAACG,OAAO,CAACyC,IAAI,CAACF,KAAK,CAACf,IAAI,CAAC;;UAEvC;UACAkB,qCAAqC,CAACH,KAAK,CAACf,IAAI,CAAC;QACnD;MACF,CAAC;;MAED;MACAY,aAAa,CAACO,MAAM,GAAG,MAAM;QAC3B;QACAC,4BAA4B,CAAC,CAAC;MAChC,CAAC;;MAED;MACAZ,MAAM,CAAC+C,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,OAAO,GAAG,MAAM;QACzCC,aAAa,CAAC,CAAC;MACjB,CAAC;;MAED;MACA7C,aAAa,CAACS,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;MAC1B9D,cAAc,CAAC,IAAI,CAAC;MAEpB8C,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;IACpE,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACdG,OAAO,CAACH,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3DrC,eAAe,CAAC,gCAAgCqC,KAAK,CAACC,OAAO,EAAE,CAAC;IAClE;EACF,CAAC;;EAED;EACA,MAAMsD,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIrF,gBAAgB,CAACI,OAAO,IAAIJ,gBAAgB,CAACI,OAAO,CAACC,KAAK,KAAK,UAAU,EAAE;MAC7EL,gBAAgB,CAACI,OAAO,CAACE,IAAI,CAAC,CAAC;IACjC;IACAnB,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC;;EAED;EACA,MAAM6D,4BAA4B,GAAG,MAAAA,CAAA,KAAY;IAC/C,IAAI/C,cAAc,CAACG,OAAO,CAAC8D,MAAM,KAAK,CAAC,EAAE;MACvCjC,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MACvC;IACF;;IAEA;IACA3C,qBAAqB,CAAC+E,IAAI,IAAIA,IAAI,CAACgB,MAAM,CAACC,OAAO,IAAI,CAACA,OAAO,CAACf,MAAM,CAAC,CAAC;IAEtE,IAAI;MACFnF,eAAe,CAAC,IAAI,CAAC;;MAErB;MACA,MAAM8D,SAAS,GAAG,IAAIC,IAAI,CAACnD,cAAc,CAACG,OAAO,EAAE;QAAEiD,IAAI,EAAE;MAAa,CAAC,CAAC;MAC1EpB,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEiB,SAAS,CAACP,IAAI,EAAE,OAAO,CAAC;;MAE3D;MACA,MAAM4C,QAAQ,GAAGC,GAAG,CAACC,eAAe,CAACvC,SAAS,CAAC;MAC/ClB,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEsD,QAAQ,CAAC;;MAEjD;MACA,MAAMlC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,aAAa,CAACL,SAAS,CAAC;MAE/BG,MAAM,CAACG,SAAS,GAAG,YAAY;QAC7B;QACA,MAAMC,WAAW,GAAGJ,MAAM,CAACK,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC/C3B,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEwB,WAAW,CAACQ,MAAM,CAAC;;QAEvD;QACA,MAAMvD,MAAM,GAAG9B,GAAG,CAAC+B,qBAAqB;QACxC,IAAI,CAACD,MAAM,EAAE;UACX,MAAM,IAAIE,KAAK,CAAC,qGAAqG,CAAC;QACxH;QACAoB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEvB,MAAM,CAAC;;QAEnD;QACAsB,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;QACtD,MAAMnB,QAAQ,GAAG,MAAMC,KAAK,CAAC,gEAAgEL,MAAM,EAAE,EAAE;UACrGM,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YACP,cAAc,EAAE;UAClB,CAAC;UACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YACnBC,MAAM,EAAE;cACNC,QAAQ,EAAE,WAAW;cACrB;cACAE,YAAY,EAAE,OAAO;cACrBoC,0BAA0B,EAAE,IAAI;cAChC8B,wBAAwB,EAAE,IAAI;cAC9BC,uBAAuB,EAAE,CAAC;cAC1B9B,KAAK,EAAE,SAAS;cAChB;cACAC,WAAW,EAAE,IAAI;cACjB8B,QAAQ,EAAE;gBACR;gBACAC,eAAe,EAAE,YAAY;gBAC7BC,kBAAkB,EAAE,WAAW;gBAC/BC,mBAAmB,EAAEtG,WAAW,KAAK,YAAY,GAAG,IAAI,GAAG,qBAAqB;gBAChFuG,iBAAiB,EAAE;cACrB;YACF,CAAC;YACDvE,KAAK,EAAE;cACLC,OAAO,EAAE+B;YACX;UACF,CAAC;QACH,CAAC,CAAC;QAEFzB,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEnB,QAAQ,CAACmF,MAAM,CAAC;QAEhD,IAAI,CAACnF,QAAQ,CAACiD,EAAE,EAAE;UAAA,IAAAmC,gBAAA,EAAAC,iBAAA,EAAAC,iBAAA;UAChB,MAAMC,SAAS,GAAG,MAAMvF,QAAQ,CAACc,IAAI,CAAC,CAAC;UACvCI,OAAO,CAACH,KAAK,CAAC,oBAAoB,EAAEwE,SAAS,CAAC;;UAE9C;UACA,MAAM9G,YAAY,GAAG,EAAA2G,gBAAA,GAAAG,SAAS,CAACxE,KAAK,cAAAqE,gBAAA,uBAAfA,gBAAA,CAAiBpE,OAAO,KAAIhB,QAAQ,CAACwF,UAAU;UACpE,MAAMC,SAAS,GAAG,EAAAJ,iBAAA,GAAAE,SAAS,CAACxE,KAAK,cAAAsE,iBAAA,uBAAfA,iBAAA,CAAiBK,IAAI,KAAI1F,QAAQ,CAACmF,MAAM;UAC1D,MAAMQ,YAAY,GAAG,EAAAL,iBAAA,GAAAC,SAAS,CAACxE,KAAK,cAAAuE,iBAAA,uBAAfA,iBAAA,CAAiBM,OAAO,KAAI,EAAE;;UAEnD;UACA1E,OAAO,CAACH,KAAK,CAAC,aAAa,EAAE0E,SAAS,CAAC;UACvCvE,OAAO,CAACH,KAAK,CAAC,gBAAgB,EAAEtC,YAAY,CAAC;UAC7CyC,OAAO,CAACH,KAAK,CAAC,gBAAgB,EAAE4E,YAAY,CAAC;;UAE7C;UACAnH,qBAAqB,CAAC+E,IAAI,IAAI,CAC5B,GAAGA,IAAI,EACP;YACEO,OAAO,EAAE,QAAQ;YACjBH,IAAI,EAAE,iCAAiC8B,SAAS,MAAMhH,YAAY,8CAA8C;YAChHmF,SAAS,EAAE,IAAIC,IAAI,CAAC;UACtB,CAAC,CACF,CAAC;UAEF,MAAM,IAAI/D,KAAK,CAAC,qBAAqBrB,YAAY,EAAE,CAAC;QACtD;QAEA,MAAMoC,IAAI,GAAG,MAAMb,QAAQ,CAACc,IAAI,CAAC,CAAC;QAClCI,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEN,IAAI,CAAC;;QAEjD;QACA,IAAI,CAACA,IAAI,CAACqC,OAAO,IAAIrC,IAAI,CAACqC,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;UAC9C3E,qBAAqB,CAAC+E,IAAI,IAAI,CAC5B,GAAGA,IAAI,EACP;YACEO,OAAO,EAAEnF,WAAW,KAAK,YAAY,GAAG,WAAW,GAAG,aAAa;YACnEgF,IAAI,EAAE,0EAA0E;YAChFC,SAAS,EAAE,IAAIC,IAAI,CAAC;UACtB,CAAC,CACF,CAAC;QACJ,CAAC,MAAM;UACLgC,4BAA4B,CAAChF,IAAI,CAAC;QACpC;;QAEA;QACA3B,cAAc,CAACG,OAAO,GAAG,EAAE;QAC3Bf,eAAe,CAAC,KAAK,CAAC;MACxB,CAAC;MAEDiE,MAAM,CAACuD,OAAO,GAAI/E,KAAK,IAAK;QAC1BG,OAAO,CAACH,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;QACzC,MAAM,IAAIjB,KAAK,CAAC,6BAA6BiB,KAAK,EAAE,CAAC;MACvD,CAAC;IAEH,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdG,OAAO,CAACH,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CrC,eAAe,CAAC,2BAA2BqC,KAAK,CAACC,OAAO,EAAE,CAAC;;MAE3D;MACAxC,qBAAqB,CAAC+E,IAAI,IAAI,CAC5B,GAAGA,IAAI,EACP;QACEO,OAAO,EAAE,QAAQ;QACjBH,IAAI,EAAE,UAAU5C,KAAK,CAACC,OAAO,qBAAqB;QAClD4C,SAAS,EAAE,IAAIC,IAAI,CAAC;MACtB,CAAC,CACF,CAAC;MAEFvF,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAMuH,4BAA4B,GAAIhF,IAAI,IAAK;IAC7C,IAAI,CAACA,IAAI,CAACqC,OAAO,IAAIrC,IAAI,CAACqC,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;MAC9CjC,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;MAChD;IACF;IAEA,IAAI;MACFD,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEN,IAAI,CAACqC,OAAO,CAAC;;MAE9D;MACA,IAAI6C,cAAc,GAAG,EAAE;MACvBlF,IAAI,CAACqC,OAAO,CAACzD,OAAO,CAACmD,MAAM,IAAI;QAC7B,IAAIA,MAAM,CAACS,YAAY,IAAIT,MAAM,CAACS,YAAY,CAAC,CAAC,CAAC,EAAE;UACjD0C,cAAc,IAAInD,MAAM,CAACS,YAAY,CAAC,CAAC,CAAC,CAACD,UAAU,GAAG,GAAG;QAC3D;MACF,CAAC,CAAC;MAEF2C,cAAc,GAAGA,cAAc,CAACzC,IAAI,CAAC,CAAC;MACtCpC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE4E,cAAc,CAAC;;MAE/C;MACA,IAAIC,uBAAuB,GAAG,KAAK;MACnC,IAAIC,aAAa,GAAG,EAAE;;MAEtB;MACApF,IAAI,CAACqC,OAAO,CAACzD,OAAO,CAACmD,MAAM,IAAI;QAC7B,IAAIA,MAAM,CAACS,YAAY,IAAIT,MAAM,CAACS,YAAY,CAAC,CAAC,CAAC,IAAIT,MAAM,CAACS,YAAY,CAAC,CAAC,CAAC,CAAC6C,KAAK,EAAE;UACjF,MAAMA,KAAK,GAAGtD,MAAM,CAACS,YAAY,CAAC,CAAC,CAAC,CAAC6C,KAAK;UAC1C,IAAIA,KAAK,CAAC/C,MAAM,GAAG,CAAC,IAAI+C,KAAK,CAAC,CAAC,CAAC,CAACC,UAAU,EAAE;YAC3CH,uBAAuB,GAAG,IAAI;YAC9BC,aAAa,GAAGA,aAAa,CAACG,MAAM,CAACF,KAAK,CAAC;UAC7C;QACF;MACF,CAAC,CAAC;MAEF,IAAIF,uBAAuB,IAAIC,aAAa,CAAC9C,MAAM,GAAG,CAAC,EAAE;QACvDjC,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE8E,aAAa,CAAC9C,MAAM,EAAE,OAAO,CAAC;;QAE/E;QACA,IAAIkD,cAAc,GAAG,IAAI;QACzB,IAAIC,WAAW,GAAG,EAAE;QACpB,IAAIC,QAAQ,GAAG,EAAE;QAEjBN,aAAa,CAACxG,OAAO,CAAC+G,IAAI,IAAI;UAC5B,MAAML,UAAU,GAAGK,IAAI,CAACL,UAAU,IAAI,CAAC;;UAEvC;UACA,IAAIA,UAAU,KAAKE,cAAc,EAAE;YACjC;YACA,IAAIC,WAAW,EAAE;cACf;cACA,IAAIG,WAAW;cACf,IAAI9H,WAAW,KAAK,KAAK,EAAE;gBACzB;gBACA8H,WAAW,GAAGJ,cAAc,KAAK,CAAC,GAAG,aAAa,GAAG,WAAW;cAClE,CAAC,MAAM;gBACL;gBACAI,WAAW,GAAGJ,cAAc,KAAK,CAAC,GAAG,WAAW,GAAG,aAAa;cAClE;cAEAE,QAAQ,CAACzE,IAAI,CAAC;gBACZgC,OAAO,EAAE2C,WAAW;gBACpB9C,IAAI,EAAE2C,WAAW,CAAChD,IAAI,CAAC,CAAC;gBACxBM,SAAS,EAAE,IAAIC,IAAI,CAAC;cACtB,CAAC,CAAC;YACJ;;YAEA;YACAwC,cAAc,GAAGF,UAAU;YAC3BG,WAAW,GAAGE,IAAI,CAACA,IAAI,GAAG,GAAG;UAC/B,CAAC,MAAM;YACL;YACAF,WAAW,IAAIE,IAAI,CAACA,IAAI,GAAG,GAAG;UAChC;QACF,CAAC,CAAC;;QAEF;QACA,IAAIF,WAAW,EAAE;UACf;UACA,IAAIG,WAAW;UACf,IAAI9H,WAAW,KAAK,KAAK,EAAE;YACzB;YACA8H,WAAW,GAAGJ,cAAc,KAAK,CAAC,GAAG,aAAa,GAAG,WAAW;UAClE,CAAC,MAAM;YACL;YACAI,WAAW,GAAGJ,cAAc,KAAK,CAAC,GAAG,WAAW,GAAG,aAAa;UAClE;UAEAE,QAAQ,CAACzE,IAAI,CAAC;YACZgC,OAAO,EAAE2C,WAAW;YACpB9C,IAAI,EAAE2C,WAAW,CAAChD,IAAI,CAAC,CAAC;YACxBM,SAAS,EAAE,IAAIC,IAAI,CAAC;UACtB,CAAC,CAAC;QACJ;QAEA3C,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEoF,QAAQ,CAAC;;QAE1C;QACA/H,qBAAqB,CAAC+E,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAGgD,QAAQ,CAAC,CAAC;MACvD,CAAC,MAAM;QACL;QACArF,OAAO,CAACC,GAAG,CAAC,gFAAgF,CAAC;QAE7F,IAAI4E,cAAc,EAAE;UAClBvH,qBAAqB,CAAC+E,IAAI,IAAI,CAC5B,GAAGA,IAAI,EACP;YACEO,OAAO,EAAEnF,WAAW,KAAK,YAAY,GAAG,WAAW,GAAG,aAAa;YACnEgF,IAAI,EAAEoC,cAAc;YACpBnC,SAAS,EAAE,IAAIC,IAAI,CAAC;UACtB,CAAC,CACF,CAAC;QACJ,CAAC,MAAM;UACL3C,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;QACzD;MACF;IACF,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACdG,OAAO,CAACH,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChErC,eAAe,CAAC,mCAAmCqC,KAAK,CAACC,OAAO,EAAE,CAAC;;MAEnE;MACAxC,qBAAqB,CAAC+E,IAAI,IAAI,CAC5B,GAAGA,IAAI,EACP;QACEO,OAAO,EAAE,QAAQ;QACjBH,IAAI,EAAE,4BAA4B5C,KAAK,CAACC,OAAO,EAAE;QACjD4C,SAAS,EAAE,IAAIC,IAAI,CAAC;MACtB,CAAC,CACF,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAM6C,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAInI,kBAAkB,CAAC4E,MAAM,KAAK,CAAC,EAAE;IAErC,IAAIvC,OAAO,GAAG,8BAA8B;IAC5CrC,kBAAkB,CAACkB,OAAO,CAAC+E,OAAO,IAAI;MACpC5D,OAAO,IAAI,IAAI4D,OAAO,CAACZ,SAAS,CAAC+C,kBAAkB,CAAC,CAAC,KAAKnC,OAAO,CAACV,OAAO,KAAKU,OAAO,CAACb,IAAI,IAAI;IAChG,CAAC,CAAC;IAEF,MAAMiD,IAAI,GAAG,IAAIvE,IAAI,CAAC,CAACzB,OAAO,CAAC,EAAE;MAAE0B,IAAI,EAAE;IAAa,CAAC,CAAC;IACxD,MAAMuE,GAAG,GAAGnC,GAAG,CAACC,eAAe,CAACiC,IAAI,CAAC;IACrC,MAAME,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACrCF,CAAC,CAACG,IAAI,GAAGJ,GAAG;IACZC,CAAC,CAACI,QAAQ,GAAG,uBAAuB;IACpCH,QAAQ,CAAC3G,IAAI,CAAC+G,WAAW,CAACL,CAAC,CAAC;IAC5BA,CAAC,CAACM,KAAK,CAAC,CAAC;IACTL,QAAQ,CAAC3G,IAAI,CAACiH,WAAW,CAACP,CAAC,CAAC;IAC5BpC,GAAG,CAAC4C,eAAe,CAACT,GAAG,CAAC;EAC1B,CAAC;EAED,oBACE7I,OAAA;IAAKuJ,SAAS,EAAC,2BAA2B;IAAAC,QAAA,gBACxCxJ,OAAA;MAAKuJ,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BxJ,OAAA;QAAAwJ,QAAA,EAAI;MAA+B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxC5J,OAAA;QAAGuJ,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAG3B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJ5J,OAAA;QAAKuJ,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BxJ,OAAA;UAAMuJ,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAA2B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAClE5J,OAAA;UAAMuJ,SAAS,EAAC,yBAAyB;UAAAC,QAAA,EAAC;QAA4B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC7E5J,OAAA;UAAMuJ,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAA0B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjE5J,OAAA;UAAMuJ,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAA0B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN5J,OAAA;MAAKuJ,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzBxJ,OAAA;QAAKuJ,SAAS,EAAE,iBAAiB1I,YAAY,EAAG;QAAA2I,QAAA,gBAC9CxJ,OAAA;UAAMuJ,SAAS,EAAC;QAAY;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACnC7I,eAAe,GAAG,oBAAoB,GACrCF,YAAY,KAAK,OAAO,GAAG,gBAAgB,GAC3CA,YAAY,KAAK,SAAS,GAAG,kBAAkB,GAC/C,uBAAuB;MAAA;QAAA4I,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC,eACN5J,OAAA;QAAKuJ,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BxJ,OAAA;UACEuJ,SAAS,EAAC,iBAAiB;UAC3BM,OAAO,EAAElI,UAAW;UACpBmI,QAAQ,EAAE/I,eAAgB;UAAAyI,QAAA,EAC3B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT5J,OAAA;UACEiJ,IAAI,EAAE,mDAAoD;UAC1Dc,MAAM,EAAC,QAAQ;UACfC,GAAG,EAAC,qBAAqB;UACzBT,SAAS,EAAC,aAAa;UAAAC,QAAA,EACxB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAEL,CAAC/I,YAAY,KAAK,SAAS,IAAIJ,YAAY,kBAC1CT,OAAA;MAAKuJ,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BxJ,OAAA;QAAAwJ,QAAA,EAAI;MAAoC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7C5J,OAAA;QAAAwJ,QAAA,gBACExJ,OAAA;UAAAwJ,QAAA,GAAI,YAAU,eAAAxJ,OAAA;YAAGiJ,IAAI,EAAC,mDAAmD;YAACc,MAAM,EAAC,QAAQ;YAACC,GAAG,EAAC,qBAAqB;YAAAR,QAAA,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjJ5J,OAAA;UAAAwJ,QAAA,EAAI;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5B5J,OAAA;UAAAwJ,QAAA,EAAI;QAAuC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChD5J,OAAA;UAAAwJ,QAAA,EAAI;QAAyF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClG5J,OAAA;UAAAwJ,QAAA,EAAI;QAA4D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE,CAAC,eACL5J,OAAA;QAAAwJ,QAAA,GAAG,2BAAyB,eAAAxJ,OAAA;UAAAwJ,QAAA,EAAO1J,GAAG,CAAC+B;QAAqB;UAAA4H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,EAEvEnJ,YAAY,iBACXT,OAAA;QAAKuJ,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACnCxJ,OAAA;UAAAwJ,QAAA,EAAI;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9B5J,OAAA;UAAAwJ,QAAA,gBACExJ,OAAA;YAAAwJ,QAAA,EAAI;UAAwD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjE5J,OAAA;YAAAwJ,QAAA,EAAI;UAAgF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzF5J,OAAA;YAAAwJ,QAAA,EAAI;UAA2D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpE5J,OAAA;YAAAwJ,QAAA,EAAI;UAA6D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtE5J,OAAA;YAAAwJ,QAAA,EAAI;UAAyD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,eAED5J,OAAA;MAAKuJ,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBACjCxJ,OAAA;QAAKuJ,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjCxJ,OAAA;UACEuJ,SAAS,EAAE,kBAAkBpJ,WAAW,IAAIQ,WAAW,KAAK,YAAY,GAAG,WAAW,GAAG,EAAE,EAAG;UAC9FkJ,OAAO,EAAE1J,WAAW,GAAGmG,aAAa,GAAGlD,wBAAyB;UAChE0G,QAAQ,EAAEzJ,YAAY,IAAIU,eAAgB;UAAAyI,QAAA,EAEzCrJ,WAAW,IAAIQ,WAAW,KAAK,YAAY,GACxC,gBAAgB,GAChB;QAAqC;UAAA8I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eAET5J,OAAA;UACEuJ,SAAS,EAAE,4BAA4BpJ,WAAW,IAAIQ,WAAW,KAAK,KAAK,GAAG,WAAW,GAAG,EAAE,EAAG;UACjGkJ,OAAO,EAAE1J,WAAW,GAAGmG,aAAa,GAAGP,sBAAuB;UAC9D+D,QAAQ,EAAEzJ,YAAY,IAAIU,eAAgB;UAAAyI,QAAA,EAEzCrJ,WAAW,IAAIQ,WAAW,KAAK,KAAK,GACjC,oBAAoB,GACpB;QAA2B;UAAA8I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAELvJ,YAAY,iBACXL,OAAA;QAAKuJ,SAAS,EAAC,sBAAsB;QAAAC,QAAA,EAAC;MAEtC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CACN,EAEAnJ,YAAY,iBACXT,OAAA;QAAKuJ,SAAS,EAAC,eAAe;QAAAC,QAAA,EAC3B/I;MAAY;QAAAgJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEN5J,OAAA;MAAKuJ,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBACnCxJ,OAAA;QAAKuJ,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCxJ,OAAA;UAAAwJ,QAAA,EAAI;QAAmC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAC3CrJ,kBAAkB,CAAC4E,MAAM,GAAG,CAAC,iBAC5BnF,OAAA;UACEuJ,SAAS,EAAC,iBAAiB;UAC3BM,OAAO,EAAEnB,kBAAmB;UAAAc,QAAA,EAC7B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEN5J,OAAA;QAAKuJ,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAChCjJ,kBAAkB,CAAC4E,MAAM,KAAK,CAAC,gBAC9BnF,OAAA;UAAKuJ,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAE7B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,gBAEN5J,OAAA;UAAKuJ,SAAS,EAAC,qBAAqB;UAAAC,QAAA,EACjCjJ,kBAAkB,CAAC0J,GAAG,CAAC,CAACzD,OAAO,EAAE0D,KAAK,kBACrClK,OAAA;YAEEuJ,SAAS,EAAE,sBAAsB/C,OAAO,CAACV,OAAO,CAACqE,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI5D,OAAO,CAACf,MAAM,GAAG,SAAS,GAAG,EAAE,EAAG;YAAA+D,QAAA,gBAEtHxJ,OAAA;cAAKuJ,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BxJ,OAAA;gBAAMuJ,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAEhD,OAAO,CAACZ,SAAS,CAAC+C,kBAAkB,CAAC;cAAC;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3E5J,OAAA;gBAAMuJ,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAC5BhD,OAAO,CAACV,OAAO,KAAK,aAAa,GAAG,gBAAgB,GACpDU,OAAO,CAACV,OAAO,KAAK,WAAW,GAAG,eAAe,GACjDU,OAAO,CAACV,OAAO,KAAK,QAAQ,GAAG,WAAW,GAAGU,OAAO,CAACV;cAAO;gBAAA2D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACN5J,OAAA;cAAKuJ,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAEhD,OAAO,CAACb;YAAI;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,GAX7CM,KAAK;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAYP,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC1J,EAAA,CAhwBQD,uBAAuB;AAAAoK,EAAA,GAAvBpK,uBAAuB;AAkwBhC,eAAeA,uBAAuB;AAAC,IAAAoK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}