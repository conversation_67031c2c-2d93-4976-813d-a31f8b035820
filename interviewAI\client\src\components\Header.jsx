import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import HomeIcon from '@mui/icons-material/Home';
import LogoutIcon from '@mui/icons-material/Logout';
import './Header.css';

function Header() {
  const [menuOpen, setMenuOpen] = useState(false);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    // Check if user is logged in
    const userDetails = localStorage.getItem("userDetails");
    if (userDetails) {
      setIsLoggedIn(true);
    }
  }, []);

  const toggleMenu = () => {
    setMenuOpen(!menuOpen);
  };

  const handleLogout = () => {
    // Clear localStorage
    localStorage.removeItem("userDetails");
    localStorage.removeItem("token");
    setIsLoggedIn(false);
    // Navigate to home page
    navigate('/');
  };

  return (
    <header className="site-header">
      <div className="container">
        <div className="top-bar">
          <div className="contact-info">
            <a href="mailto:<EMAIL>" className="contact-item" onClick={(e) => {
              window.location.href = "mailto:<EMAIL>";
            }}>
              <i className="fas fa-envelope"></i> <EMAIL>
            </a>
            <a href="tel:+19092359247" className="contact-item">
              <i className="fas fa-phone"></i> ******-235-9247
            </a>
          </div>
          <div className="social-links">
            {/* <a href="https://youtube.com" target="_blank" rel="noopener noreferrer" className="social-icon youtube">
              <i className="fab fa-youtube"></i>
            </a>
            <a href="https://twitter.com" target="_blank" rel="noopener noreferrer" className="social-icon twitter">
              <i className="fab fa-twitter"></i>
            </a>
            <a href="https://instagram.com" target="_blank" rel="noopener noreferrer" className="social-icon instagram">
              <i className="fab fa-instagram"></i>
            </a> */}
            <a href="https://www.linkedin.com/company/interviewsupport-ai/" target="_blank" rel="noopener noreferrer" className="social-icon linkedin">
              <i className="fab fa-linkedin"></i>
            </a>
          </div>
        </div>

        <nav className="main-nav">
          <div className="logo">
            <Link to="/" className="logo-link">
              <HomeIcon className="home-icon" />
              <span>Interview AI</span>
            </Link>
          </div>

          <button className="menu-toggle" onClick={toggleMenu}>
            <i className={`fas ${menuOpen ? 'fa-times' : 'fa-bars'}`}></i>
          </button>

          <ul className={`nav-links ${menuOpen ? 'active' : ''}`}>
            <li><Link to="/">Home</Link></li>
            <li><Link to="/features">Features</Link></li>
            <li><Link to="/pricing">Pricing</Link></li>
            <li><Link to="/faq">FAQ</Link></li>
            <li><Link to="/contact">Contact</Link></li>
            {/* <li><Link to="/automated-interview" className="highlight">Try Automated Interview</Link></li> */}
            {isLoggedIn ? (
              <li className="nav-button logout-button">
                <button onClick={handleLogout} className="logout-btn">
                  <LogoutIcon className="logout-icon" />
                  <span>Logout</span>
                </button>
              </li>
            ) : (
              <li className="nav-button"><Link to="/login">Login</Link></li>
            )}
          </ul>
        </nav>
      </div>

      {/* <div className="header-banner">
        <h1>Interview AI Assistant</h1>
        <p>Practice your interview skills with AI-powered feedback</p>
      </div> */}
    </header>
  );
}

export default Header;





