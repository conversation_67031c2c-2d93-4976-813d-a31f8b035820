import React from "react";
import { Browser<PERSON>outer, Route, Routes, Navigate } from "react-router-dom";
import "./App.css";
import 'bootstrap/dist/css/bootstrap.min.css';

import HomeScreen from "./pages/home/<USER>";
import LoginScreen from "./pages/login/index";
import AdminHomeScreen from "./pages/admin-home/index";
import SignUpScreen from "./pages/signup/index";
import InterviewPage from "./pages/interview/index";
import AutomatedInterviewPage from "./pages/AutomatedInterviewPage";
import LiveTranscriptionPage from "./pages/live-transcription/LiveTranscription";
import DiarizedInterviewPage from "./pages/DiarizedInterviewPage";

function App() {

  return (
    <BrowserRouter>
      <Routes>
        <Route path="/" element={<HomeScreen />} />
        <Route path="/login" element={<LoginScreen />} />
        <Route path="/home" element={<AdminHomeScreen />} />
        <Route path="/signup/:id" element={<SignUpScreen />} />
        <Route path="/interview" element={<InterviewPage />} />
        <Route path="/automated-interview" element={<AutomatedInterviewPage />} />
        <Route path="/live-transcription" element={<LiveTranscriptionPage />} />
        <Route path="/diarized-interview" element={<DiarizedInterviewPage />} />
        {/* <Route path="/launch" element={<LaunchScreen />} /> */}

        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
    </BrowserRouter>
  );
}

export default App;
