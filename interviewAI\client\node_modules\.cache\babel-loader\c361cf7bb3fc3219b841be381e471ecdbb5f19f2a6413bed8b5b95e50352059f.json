{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\interview-ai-app\\\\interviewAI\\\\client\\\\src\\\\components\\\\InterviewSession.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport './InterviewSession.css';\nimport AccessTimeIcon from '@mui/icons-material/AccessTime';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction InterviewSession({\n  config,\n  onEndSession\n}) {\n  _s();\n  const [questions, setQuestions] = useState([]);\n  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);\n  const [userAnswer, setUserAnswer] = useState('');\n  const [feedback, setFeedback] = useState(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [resumeInfo, setResumeInfo] = useState({\n    hasResume: false,\n    fileName: ''\n  });\n\n  // Session timer state\n  const [sessionTimeRemaining, setSessionTimeRemaining] = useState(config.sessionTimeRemaining || 5);\n  const [sessionTimeDisplay, setSessionTimeDisplay] = useState('05:00');\n  const sessionTimerRef = useRef(null);\n\n  // Format time as MM:SS\n  const formatTime = minutes => {\n    const mins = Math.floor(minutes);\n    const secs = Math.round((minutes - mins) * 60);\n    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  // Start session timer\n  useEffect(() => {\n    // Initialize timer display\n    setSessionTimeDisplay(formatTime(sessionTimeRemaining));\n\n    // Start countdown timer\n    sessionTimerRef.current = setInterval(() => {\n      setSessionTimeRemaining(prev => {\n        const newTime = prev - 1 / 60; // Decrease by 1 second (1/60 of a minute)\n\n        // Update display\n        setSessionTimeDisplay(formatTime(newTime));\n\n        // Check if time is up\n        if (newTime <= 0) {\n          clearInterval(sessionTimerRef.current);\n          return 0;\n        }\n        return newTime;\n      });\n    }, 1000);\n\n    // Clean up timer on unmount\n    return () => {\n      if (sessionTimerRef.current) {\n        clearInterval(sessionTimerRef.current);\n      }\n    };\n  }, []);\n\n  // Save remaining time to localStorage when component unmounts\n  useEffect(() => {\n    return () => {\n      try {\n        const userDetails = localStorage.getItem('userDetails');\n        let userData = userDetails ? JSON.parse(userDetails) : {};\n        userData.timeRemaining = sessionTimeRemaining;\n        localStorage.setItem('userDetails', JSON.stringify(userData));\n      } catch (error) {\n        console.error('Error saving session time to localStorage:', error);\n      }\n    };\n  }, [sessionTimeRemaining]);\n  useEffect(() => {\n    // Check if resume was provided\n    if (config.resumeFile) {\n      setResumeInfo({\n        hasResume: true,\n        fileName: config.resumeFile.name\n      });\n    }\n\n    // Generate questions based on job title and skillset\n    setTimeout(() => {\n      let dummyQuestions = [`Tell me about your experience as a ${config.jobTitle}.`, \"Describe a challenging project you worked on recently.\", \"How do you handle tight deadlines?\", \"What are your strengths and weaknesses?\", \"Where do you see yourself in 5 years?\"];\n\n      // Add skillset-specific questions if provided\n      if (config.skillset) {\n        const skills = config.skillset.split(',').map(skill => skill.trim());\n        skills.forEach(skill => {\n          if (skill) {\n            dummyQuestions.push(`Tell me about your experience with ${skill}.`);\n          }\n        });\n      }\n\n      // Add resume-specific question if resume was provided\n      if (config.resumeFile) {\n        dummyQuestions.push(\"Based on your resume, can you elaborate on your most relevant experience for this role?\");\n        dummyQuestions.push(\"I see from your resume that you have experience with [skill]. How have you applied this in your work?\");\n      }\n\n      // Add job description-specific questions if provided\n      if (config.jobDescription) {\n        dummyQuestions.push(\"Based on the job description, what makes you a good fit for this role?\");\n        dummyQuestions.push(\"How do your skills align with the requirements mentioned in the job description?\");\n      }\n      setQuestions(dummyQuestions);\n      setIsLoading(false);\n    }, 1500);\n  }, [config]);\n  const handleSubmitAnswer = () => {\n    // In a real app, you would send the answer to an API for feedback\n    setFeedback({\n      score: 4,\n      comments: \"Good answer! Consider adding more specific examples to illustrate your points.\"\n    });\n  };\n  const handleNextQuestion = () => {\n    setCurrentQuestionIndex(currentQuestionIndex + 1);\n    setUserAnswer('');\n    setFeedback(null);\n  };\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Preparing your interview questions\", resumeInfo.hasResume ? ' based on your resume' : '', \"...\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this), resumeInfo.hasResume && /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"resume-info\",\n        children: [\"Using resume: \", resumeInfo.fileName]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 7\n    }, this);\n  }\n  if (currentQuestionIndex >= questions.length) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"interview-complete\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Interview Complete!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"You've completed all the questions. Great job!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onEndSession,\n        children: \"Start New Interview\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"interview-session\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"session-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"question-counter\",\n        children: [\"Question \", currentQuestionIndex + 1, \" of \", questions.length]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"session-timer\",\n        children: [/*#__PURE__*/_jsxDEV(AccessTimeIcon, {\n          className: \"timer-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"timer-display\",\n          children: [\"Time Remaining: \", sessionTimeDisplay]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 158,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"question-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: questions[currentQuestionIndex]\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n        value: userAnswer,\n        onChange: e => setUserAnswer(e.target.value),\n        placeholder: \"Type your answer here...\",\n        disabled: !!feedback || sessionTimeRemaining <= 0\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 9\n      }, this), sessionTimeRemaining <= 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"time-expired\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          children: \"Session Time Expired\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Your session time has ended. Please start a new session or make a payment to continue.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onEndSession,\n          children: \"End Session\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 11\n      }, this) : !feedback ? /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleSubmitAnswer,\n        disabled: !userAnswer.trim(),\n        children: \"Submit Answer\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"feedback\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          children: \"Feedback\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"score\",\n          children: [\"Score: \", feedback.score, \"/5\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: feedback.comments\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleNextQuestion,\n          children: currentQuestionIndex < questions.length - 1 ? 'Next Question' : 'Finish Interview'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"end-session\",\n      onClick: onEndSession,\n      children: \"End Session\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 206,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 157,\n    columnNumber: 5\n  }, this);\n}\n_s(InterviewSession, \"bmwkpc12odfZ3hJAWoTuXe90O8M=\");\n_c = InterviewSession;\nexport default InterviewSession;\nvar _c;\n$RefreshReg$(_c, \"InterviewSession\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "AccessTimeIcon", "jsxDEV", "_jsxDEV", "InterviewSession", "config", "onEndSession", "_s", "questions", "setQuestions", "currentQuestionIndex", "setCurrentQuestionIndex", "userAnswer", "setUserAnswer", "feedback", "setFeedback", "isLoading", "setIsLoading", "resumeInfo", "setResumeInfo", "hasResume", "fileName", "sessionTimeRemaining", "setSessionTimeRemaining", "sessionTimeDisplay", "setSessionTimeDisplay", "sessionTimerRef", "formatTime", "minutes", "mins", "Math", "floor", "secs", "round", "toString", "padStart", "current", "setInterval", "prev", "newTime", "clearInterval", "userDetails", "localStorage", "getItem", "userData", "JSON", "parse", "timeRemaining", "setItem", "stringify", "error", "console", "resumeFile", "name", "setTimeout", "dummyQuestions", "jobTitle", "skillset", "skills", "split", "map", "skill", "trim", "for<PERSON>ach", "push", "jobDescription", "handleSubmitAnswer", "score", "comments", "handleNextQuestion", "className", "children", "_jsxFileName", "lineNumber", "columnNumber", "length", "onClick", "value", "onChange", "e", "target", "placeholder", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/interview-ai-app/interviewAI/client/src/components/InterviewSession.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport './InterviewSession.css';\nimport AccessTimeIcon from '@mui/icons-material/AccessTime';\n\nfunction InterviewSession({ config, onEndSession }) {\n  const [questions, setQuestions] = useState([]);\n  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);\n  const [userAnswer, setUserAnswer] = useState('');\n  const [feedback, setFeedback] = useState(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [resumeInfo, setResumeInfo] = useState({\n    hasResume: false,\n    fileName: ''\n  });\n\n  // Session timer state\n  const [sessionTimeRemaining, setSessionTimeRemaining] = useState(config.sessionTimeRemaining || 5);\n  const [sessionTimeDisplay, setSessionTimeDisplay] = useState('05:00');\n  const sessionTimerRef = useRef(null);\n\n  // Format time as MM:SS\n  const formatTime = (minutes) => {\n    const mins = Math.floor(minutes);\n    const secs = Math.round((minutes - mins) * 60);\n    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  // Start session timer\n  useEffect(() => {\n    // Initialize timer display\n    setSessionTimeDisplay(formatTime(sessionTimeRemaining));\n\n    // Start countdown timer\n    sessionTimerRef.current = setInterval(() => {\n      setSessionTimeRemaining(prev => {\n        const newTime = prev - (1/60); // Decrease by 1 second (1/60 of a minute)\n\n        // Update display\n        setSessionTimeDisplay(formatTime(newTime));\n\n        // Check if time is up\n        if (newTime <= 0) {\n          clearInterval(sessionTimerRef.current);\n          return 0;\n        }\n\n        return newTime;\n      });\n    }, 1000);\n\n    // Clean up timer on unmount\n    return () => {\n      if (sessionTimerRef.current) {\n        clearInterval(sessionTimerRef.current);\n      }\n    };\n  }, []);\n\n  // Save remaining time to localStorage when component unmounts\n  useEffect(() => {\n    return () => {\n      try {\n        const userDetails = localStorage.getItem('userDetails');\n        let userData = userDetails ? JSON.parse(userDetails) : {};\n\n        userData.timeRemaining = sessionTimeRemaining;\n        localStorage.setItem('userDetails', JSON.stringify(userData));\n      } catch (error) {\n        console.error('Error saving session time to localStorage:', error);\n      }\n    };\n  }, [sessionTimeRemaining]);\n\n  useEffect(() => {\n    // Check if resume was provided\n    if (config.resumeFile) {\n      setResumeInfo({\n        hasResume: true,\n        fileName: config.resumeFile.name\n      });\n    }\n\n    // Generate questions based on job title and skillset\n    setTimeout(() => {\n      let dummyQuestions = [\n        `Tell me about your experience as a ${config.jobTitle}.`,\n        \"Describe a challenging project you worked on recently.\",\n        \"How do you handle tight deadlines?\",\n        \"What are your strengths and weaknesses?\",\n        \"Where do you see yourself in 5 years?\"\n      ];\n\n      // Add skillset-specific questions if provided\n      if (config.skillset) {\n        const skills = config.skillset.split(',').map(skill => skill.trim());\n        skills.forEach(skill => {\n          if (skill) {\n            dummyQuestions.push(`Tell me about your experience with ${skill}.`);\n          }\n        });\n      }\n\n      // Add resume-specific question if resume was provided\n      if (config.resumeFile) {\n        dummyQuestions.push(\"Based on your resume, can you elaborate on your most relevant experience for this role?\");\n        dummyQuestions.push(\"I see from your resume that you have experience with [skill]. How have you applied this in your work?\");\n      }\n\n      // Add job description-specific questions if provided\n      if (config.jobDescription) {\n        dummyQuestions.push(\"Based on the job description, what makes you a good fit for this role?\");\n        dummyQuestions.push(\"How do your skills align with the requirements mentioned in the job description?\");\n      }\n\n      setQuestions(dummyQuestions);\n      setIsLoading(false);\n    }, 1500);\n  }, [config]);\n\n  const handleSubmitAnswer = () => {\n    // In a real app, you would send the answer to an API for feedback\n    setFeedback({\n      score: 4,\n      comments: \"Good answer! Consider adding more specific examples to illustrate your points.\"\n    });\n  };\n\n  const handleNextQuestion = () => {\n    setCurrentQuestionIndex(currentQuestionIndex + 1);\n    setUserAnswer('');\n    setFeedback(null);\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"loading\">\n        <div className=\"spinner\"></div>\n        <p>Preparing your interview questions{resumeInfo.hasResume ? ' based on your resume' : ''}...</p>\n        {resumeInfo.hasResume && (\n          <p className=\"resume-info\">Using resume: {resumeInfo.fileName}</p>\n        )}\n      </div>\n    );\n  }\n\n  if (currentQuestionIndex >= questions.length) {\n    return (\n      <div className=\"interview-complete\">\n        <h2>Interview Complete!</h2>\n        <p>You've completed all the questions. Great job!</p>\n        <button onClick={onEndSession}>Start New Interview</button>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"interview-session\">\n      <div className=\"session-header\">\n        <div className=\"question-counter\">\n          Question {currentQuestionIndex + 1} of {questions.length}\n        </div>\n\n        <div className=\"session-timer\">\n          <AccessTimeIcon className=\"timer-icon\" />\n          <span className=\"timer-display\">Time Remaining: {sessionTimeDisplay}</span>\n        </div>\n      </div>\n\n      <div className=\"question-card\">\n        <h3>{questions[currentQuestionIndex]}</h3>\n\n        <textarea\n          value={userAnswer}\n          onChange={(e) => setUserAnswer(e.target.value)}\n          placeholder=\"Type your answer here...\"\n          disabled={!!feedback || sessionTimeRemaining <= 0}\n        />\n\n        {sessionTimeRemaining <= 0 ? (\n          <div className=\"time-expired\">\n            <h4>Session Time Expired</h4>\n            <p>Your session time has ended. Please start a new session or make a payment to continue.</p>\n            <button onClick={onEndSession}>\n              End Session\n            </button>\n          </div>\n        ) : !feedback ? (\n          <button\n            onClick={handleSubmitAnswer}\n            disabled={!userAnswer.trim()}\n          >\n            Submit Answer\n          </button>\n        ) : (\n          <div className=\"feedback\">\n            <h4>Feedback</h4>\n            <div className=\"score\">Score: {feedback.score}/5</div>\n            <p>{feedback.comments}</p>\n            <button onClick={handleNextQuestion}>\n              {currentQuestionIndex < questions.length - 1 ? 'Next Question' : 'Finish Interview'}\n            </button>\n          </div>\n        )}\n      </div>\n\n      <button className=\"end-session\" onClick={onEndSession}>End Session</button>\n    </div>\n  );\n}\n\nexport default InterviewSession;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,wBAAwB;AAC/B,OAAOC,cAAc,MAAM,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,SAASC,gBAAgBA,CAAC;EAAEC,MAAM;EAAEC;AAAa,CAAC,EAAE;EAAAC,EAAA;EAClD,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACY,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGb,QAAQ,CAAC,CAAC,CAAC;EACnE,MAAM,CAACc,UAAU,EAAEC,aAAa,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACgB,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACkB,SAAS,EAAEC,YAAY,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACoB,UAAU,EAAEC,aAAa,CAAC,GAAGrB,QAAQ,CAAC;IAC3CsB,SAAS,EAAE,KAAK;IAChBC,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGzB,QAAQ,CAACO,MAAM,CAACiB,oBAAoB,IAAI,CAAC,CAAC;EAClG,MAAM,CAACE,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG3B,QAAQ,CAAC,OAAO,CAAC;EACrE,MAAM4B,eAAe,GAAG1B,MAAM,CAAC,IAAI,CAAC;;EAEpC;EACA,MAAM2B,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,CAAC;IAChC,MAAMI,IAAI,GAAGF,IAAI,CAACG,KAAK,CAAC,CAACL,OAAO,GAAGC,IAAI,IAAI,EAAE,CAAC;IAC9C,OAAO,GAAGA,IAAI,CAACK,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIH,IAAI,CAACE,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAClF,CAAC;;EAED;EACApC,SAAS,CAAC,MAAM;IACd;IACA0B,qBAAqB,CAACE,UAAU,CAACL,oBAAoB,CAAC,CAAC;;IAEvD;IACAI,eAAe,CAACU,OAAO,GAAGC,WAAW,CAAC,MAAM;MAC1Cd,uBAAuB,CAACe,IAAI,IAAI;QAC9B,MAAMC,OAAO,GAAGD,IAAI,GAAI,CAAC,GAAC,EAAG,CAAC,CAAC;;QAE/B;QACAb,qBAAqB,CAACE,UAAU,CAACY,OAAO,CAAC,CAAC;;QAE1C;QACA,IAAIA,OAAO,IAAI,CAAC,EAAE;UAChBC,aAAa,CAACd,eAAe,CAACU,OAAO,CAAC;UACtC,OAAO,CAAC;QACV;QAEA,OAAOG,OAAO;MAChB,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC;;IAER;IACA,OAAO,MAAM;MACX,IAAIb,eAAe,CAACU,OAAO,EAAE;QAC3BI,aAAa,CAACd,eAAe,CAACU,OAAO,CAAC;MACxC;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACArC,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX,IAAI;QACF,MAAM0C,WAAW,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;QACvD,IAAIC,QAAQ,GAAGH,WAAW,GAAGI,IAAI,CAACC,KAAK,CAACL,WAAW,CAAC,GAAG,CAAC,CAAC;QAEzDG,QAAQ,CAACG,aAAa,GAAGzB,oBAAoB;QAC7CoB,YAAY,CAACM,OAAO,CAAC,aAAa,EAAEH,IAAI,CAACI,SAAS,CAACL,QAAQ,CAAC,CAAC;MAC/D,CAAC,CAAC,OAAOM,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;MACpE;IACF,CAAC;EACH,CAAC,EAAE,CAAC5B,oBAAoB,CAAC,CAAC;EAE1BvB,SAAS,CAAC,MAAM;IACd;IACA,IAAIM,MAAM,CAAC+C,UAAU,EAAE;MACrBjC,aAAa,CAAC;QACZC,SAAS,EAAE,IAAI;QACfC,QAAQ,EAAEhB,MAAM,CAAC+C,UAAU,CAACC;MAC9B,CAAC,CAAC;IACJ;;IAEA;IACAC,UAAU,CAAC,MAAM;MACf,IAAIC,cAAc,GAAG,CACnB,sCAAsClD,MAAM,CAACmD,QAAQ,GAAG,EACxD,wDAAwD,EACxD,oCAAoC,EACpC,yCAAyC,EACzC,uCAAuC,CACxC;;MAED;MACA,IAAInD,MAAM,CAACoD,QAAQ,EAAE;QACnB,MAAMC,MAAM,GAAGrD,MAAM,CAACoD,QAAQ,CAACE,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,KAAK,IAAIA,KAAK,CAACC,IAAI,CAAC,CAAC,CAAC;QACpEJ,MAAM,CAACK,OAAO,CAACF,KAAK,IAAI;UACtB,IAAIA,KAAK,EAAE;YACTN,cAAc,CAACS,IAAI,CAAC,sCAAsCH,KAAK,GAAG,CAAC;UACrE;QACF,CAAC,CAAC;MACJ;;MAEA;MACA,IAAIxD,MAAM,CAAC+C,UAAU,EAAE;QACrBG,cAAc,CAACS,IAAI,CAAC,yFAAyF,CAAC;QAC9GT,cAAc,CAACS,IAAI,CAAC,uGAAuG,CAAC;MAC9H;;MAEA;MACA,IAAI3D,MAAM,CAAC4D,cAAc,EAAE;QACzBV,cAAc,CAACS,IAAI,CAAC,wEAAwE,CAAC;QAC7FT,cAAc,CAACS,IAAI,CAAC,kFAAkF,CAAC;MACzG;MAEAvD,YAAY,CAAC8C,cAAc,CAAC;MAC5BtC,YAAY,CAAC,KAAK,CAAC;IACrB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC,EAAE,CAACZ,MAAM,CAAC,CAAC;EAEZ,MAAM6D,kBAAkB,GAAGA,CAAA,KAAM;IAC/B;IACAnD,WAAW,CAAC;MACVoD,KAAK,EAAE,CAAC;MACRC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B1D,uBAAuB,CAACD,oBAAoB,GAAG,CAAC,CAAC;IACjDG,aAAa,CAAC,EAAE,CAAC;IACjBE,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;EAED,IAAIC,SAAS,EAAE;IACb,oBACEb,OAAA;MAAKmE,SAAS,EAAC,SAAS;MAAAC,QAAA,gBACtBpE,OAAA;QAAKmE,SAAS,EAAC;MAAS;QAAAjD,QAAA,EAAAmD,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC/BvE,OAAA;QAAAoE,QAAA,GAAG,oCAAkC,EAACrD,UAAU,CAACE,SAAS,GAAG,uBAAuB,GAAG,EAAE,EAAC,KAAG;MAAA;QAAAC,QAAA,EAAAmD,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,EAChGxD,UAAU,CAACE,SAAS,iBACnBjB,OAAA;QAAGmE,SAAS,EAAC,aAAa;QAAAC,QAAA,GAAC,gBAAc,EAACrD,UAAU,CAACG,QAAQ;MAAA;QAAAA,QAAA,EAAAmD,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAClE;IAAA;MAAArD,QAAA,EAAAmD,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEV;EAEA,IAAIhE,oBAAoB,IAAIF,SAAS,CAACmE,MAAM,EAAE;IAC5C,oBACExE,OAAA;MAAKmE,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBACjCpE,OAAA;QAAAoE,QAAA,EAAI;MAAmB;QAAAlD,QAAA,EAAAmD,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5BvE,OAAA;QAAAoE,QAAA,EAAG;MAA8C;QAAAlD,QAAA,EAAAmD,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACrDvE,OAAA;QAAQyE,OAAO,EAAEtE,YAAa;QAAAiE,QAAA,EAAC;MAAmB;QAAAlD,QAAA,EAAAmD,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAArD,QAAA,EAAAmD,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxD,CAAC;EAEV;EAEA,oBACEvE,OAAA;IAAKmE,SAAS,EAAC,mBAAmB;IAAAC,QAAA,gBAChCpE,OAAA;MAAKmE,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BpE,OAAA;QAAKmE,SAAS,EAAC,kBAAkB;QAAAC,QAAA,GAAC,WACvB,EAAC7D,oBAAoB,GAAG,CAAC,EAAC,MAAI,EAACF,SAAS,CAACmE,MAAM;MAAA;QAAAtD,QAAA,EAAAmD,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD,CAAC,eAENvE,OAAA;QAAKmE,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BpE,OAAA,CAACF,cAAc;UAACqE,SAAS,EAAC;QAAY;UAAAjD,QAAA,EAAAmD,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzCvE,OAAA;UAAMmE,SAAS,EAAC,eAAe;UAAAC,QAAA,GAAC,kBAAgB,EAAC/C,kBAAkB;QAAA;UAAAH,QAAA,EAAAmD,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAArD,QAAA,EAAAmD,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxE,CAAC;IAAA;MAAArD,QAAA,EAAAmD,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENvE,OAAA;MAAKmE,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BpE,OAAA;QAAAoE,QAAA,EAAK/D,SAAS,CAACE,oBAAoB;MAAC;QAAAW,QAAA,EAAAmD,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAE1CvE,OAAA;QACE0E,KAAK,EAAEjE,UAAW;QAClBkE,QAAQ,EAAGC,CAAC,IAAKlE,aAAa,CAACkE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;QAC/CI,WAAW,EAAC,0BAA0B;QACtCC,QAAQ,EAAE,CAAC,CAACpE,QAAQ,IAAIQ,oBAAoB,IAAI;MAAE;QAAAD,QAAA,EAAAmD,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC,EAEDpD,oBAAoB,IAAI,CAAC,gBACxBnB,OAAA;QAAKmE,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BpE,OAAA;UAAAoE,QAAA,EAAI;QAAoB;UAAAlD,QAAA,EAAAmD,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7BvE,OAAA;UAAAoE,QAAA,EAAG;QAAsF;UAAAlD,QAAA,EAAAmD,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC7FvE,OAAA;UAAQyE,OAAO,EAAEtE,YAAa;UAAAiE,QAAA,EAAC;QAE/B;UAAAlD,QAAA,EAAAmD,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAArD,QAAA,EAAAmD,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,GACJ,CAAC5D,QAAQ,gBACXX,OAAA;QACEyE,OAAO,EAAEV,kBAAmB;QAC5BgB,QAAQ,EAAE,CAACtE,UAAU,CAACkD,IAAI,CAAC,CAAE;QAAAS,QAAA,EAC9B;MAED;QAAAlD,QAAA,EAAAmD,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,gBAETvE,OAAA;QAAKmE,SAAS,EAAC,UAAU;QAAAC,QAAA,gBACvBpE,OAAA;UAAAoE,QAAA,EAAI;QAAQ;UAAAlD,QAAA,EAAAmD,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjBvE,OAAA;UAAKmE,SAAS,EAAC,OAAO;UAAAC,QAAA,GAAC,SAAO,EAACzD,QAAQ,CAACqD,KAAK,EAAC,IAAE;QAAA;UAAA9C,QAAA,EAAAmD,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACtDvE,OAAA;UAAAoE,QAAA,EAAIzD,QAAQ,CAACsD;QAAQ;UAAA/C,QAAA,EAAAmD,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1BvE,OAAA;UAAQyE,OAAO,EAAEP,kBAAmB;UAAAE,QAAA,EACjC7D,oBAAoB,GAAGF,SAAS,CAACmE,MAAM,GAAG,CAAC,GAAG,eAAe,GAAG;QAAkB;UAAAtD,QAAA,EAAAmD,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7E,CAAC;MAAA;QAAArD,QAAA,EAAAmD,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN;IAAA;MAAArD,QAAA,EAAAmD,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAENvE,OAAA;MAAQmE,SAAS,EAAC,aAAa;MAACM,OAAO,EAAEtE,YAAa;MAAAiE,QAAA,EAAC;IAAW;MAAAlD,QAAA,EAAAmD,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAArD,QAAA,EAAAmD,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACxE,CAAC;AAEV;AAACnE,EAAA,CA5MQH,gBAAgB;AAAA+E,EAAA,GAAhB/E,gBAAgB;AA8MzB,eAAeA,gBAAgB;AAAC,IAAA+E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}