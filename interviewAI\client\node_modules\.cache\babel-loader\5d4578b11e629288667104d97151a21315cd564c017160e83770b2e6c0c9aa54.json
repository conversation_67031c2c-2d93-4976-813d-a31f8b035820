{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\interview-ai-app\\\\interviewAI\\\\client\\\\src\\\\pages\\\\home\\\\index.jsx\",\n  _s = $RefreshSig$();\nimport React, { useRef, useState, useEffect } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport Header from '../../components/Header';\nimport Footer from '../../components/Footer';\nimport LogoutIcon from '@mui/icons-material/Logout';\nimport './home.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction HomeScreen() {\n  _s();\n  const pricingRef = useRef(null);\n  const [isLoggedIn, setIsLoggedIn] = useState(false);\n  // eslint-disable-next-line\n  const [userData, setUserData] = useState(null);\n  // eslint-disable-next-line\n  const navigate = useNavigate();\n  useEffect(() => {\n    // Check if user is logged in\n    const userDetails = localStorage.getItem(\"userDetails\");\n    if (userDetails) {\n      setIsLoggedIn(true);\n      try {\n        const parsedData = JSON.parse(userDetails);\n        setUserData(parsedData);\n      } catch (error) {\n        console.error(\"Error parsing user data:\", error);\n      }\n    }\n  }, []);\n  const scrollToPricing = e => {\n    var _pricingRef$current;\n    e.preventDefault();\n    (_pricingRef$current = pricingRef.current) === null || _pricingRef$current === void 0 ? void 0 : _pricingRef$current.scrollIntoView({\n      behavior: 'smooth'\n    });\n  };\n  const handleLogout = () => {\n    // Clear localStorage\n    localStorage.removeItem(\"userDetails\");\n    localStorage.removeItem(\"token\");\n    setIsLoggedIn(false);\n    setUserData(null);\n    // Refresh the page to update UI\n    window.location.reload();\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"home-page\",\n    children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"home-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"hero-section\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"container\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            children: \"Prepare for Interviews with AI\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"subtitle\",\n            children: \"Practice, improve, and land your dream job with our AI-powered interview coach\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"cta-buttons\",\n            children: isLoggedIn ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/home\",\n                className: \"cta-button primary\",\n                children: \"Start Interview\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 57,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: handleLogout,\n                className: \"cta-button logout\",\n                children: [/*#__PURE__*/_jsxDEV(LogoutIcon, {\n                  className: \"logout-icon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 59,\n                  columnNumber: 21\n                }, this), \"Logout\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 58,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/login\",\n                className: \"cta-button primary\",\n                children: \"Try now\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 65,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#pricing\",\n                className: \"cta-button secondary\",\n                onClick: scrollToPricing,\n                children: \"View Plans\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 66,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"features-section\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"container\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Features\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"features-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"feature-icon\",\n                children: \"\\uD83C\\uDF99\\uFE0F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 82,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Real-time Voice Analysis\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 83,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Practice your answers with our AI that provides instant feedback on your delivery\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 84,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"feature-icon\",\n                children: \"\\uD83D\\uDCBC\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 87,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Industry-specific Questions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 88,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Get questions tailored to your industry and role\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 89,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"feature-icon\",\n                children: \"\\uD83D\\uDCDD\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 92,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Personalized Feedback\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 93,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Receive detailed feedback to improve your interview skills\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 94,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-card highlight\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"feature-icon\",\n                children: \"\\uD83D\\uDD04\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 97,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Automated Interview Simulation\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 98,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Experience our new automated interview feature with screen sharing and automatic voice transcription that responds to your answers in real-time\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 99,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        id: \"pricing\",\n        className: \"pricing-section\",\n        ref: pricingRef,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"container\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Choose Your Plan\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"pricing-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"pricing-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"pricing-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: \"FREE\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 111,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"price\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"currency\",\n                    children: \"$\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 113,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"amount\",\n                    children: \"0\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 114,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"period\",\n                    children: \"/ 15Min Free\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 115,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 112,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"pricing-features\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item available\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"check\",\n                    children: \"\\u2713\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 120,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"15-min Interview Sessions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 121,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 119,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item available\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"check\",\n                    children: \"\\u2713\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 124,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"AI Resume Builder\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 125,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 123,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item available\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"check\",\n                    children: \"\\u2713\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 128,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"AI Story Editor\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 129,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 127,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item unavailable\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"cross\",\n                    children: \"\\u2715\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 132,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Full Customization Suite\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 133,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 131,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item unavailable\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"cross\",\n                    children: \"\\u2715\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 136,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Industry Knowledge Base Add-Ons\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 137,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 135,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item unavailable\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"cross\",\n                    children: \"\\u2715\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 140,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Coding Interview Practice\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 141,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 139,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item unavailable\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"cross\",\n                    children: \"\\u2715\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 144,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"AI Playground\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 145,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 143,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/signup/free\",\n                className: \"pricing-cta\",\n                children: \"Try for free\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"pricing-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"pricing-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: \"PRO\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 153,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"price\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"currency\",\n                    children: \"$\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 155,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"amount\",\n                    children: \"4.99\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 156,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"period\",\n                    children: \"/ hourly\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 157,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 154,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"pricing-features\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item available\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"check\",\n                    children: \"\\u2713\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 162,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Unlimited Interview Sessions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 163,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 161,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item available\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"check\",\n                    children: \"\\u2713\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 166,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"AI Resume Builder\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 167,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 165,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item available\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"check\",\n                    children: \"\\u2713\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 170,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"AI Story Editor\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 171,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 169,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item available\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"check\",\n                    children: \"\\u2713\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 174,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Full Customization Suite\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 175,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 173,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item available\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"check\",\n                    children: \"\\u2713\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 178,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Industry Knowledge Base Add-Ons\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 179,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item available\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"check\",\n                    children: \"\\u2713\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 182,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Coding Interview Practice\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 183,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item available\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"check\",\n                    children: \"\\u2713\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 186,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"AI Playground\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 187,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/signup/pro\",\n                className: \"pricing-cta\",\n                children: \"Get the plan\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"faq-section\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"container\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Frequently Asked Questions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"faq-list\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"faq-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"How does the AI interview coach work?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Our AI interview coach uses advanced natural language processing to analyze your responses, provide feedback on your delivery, and suggest improvements to help you ace your interviews.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"faq-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Can I use this for technical interviews?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Yes! Our PRO plan includes specialized modules for technical and coding interviews across various programming languages and frameworks.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"faq-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Is my data secure?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"We take data privacy seriously. All your interview sessions and personal information are encrypted and never shared with third parties.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"faq-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Can I cancel my subscription anytime?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Absolutely. You can cancel your subscription at any time with no questions asked.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"faq-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Do you offer support?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Yes, we provide 24/7 customer support via email and live chat for all our users.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 284,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 46,\n    columnNumber: 5\n  }, this);\n}\n_s(HomeScreen, \"Cc7qYJA1yOqG0tH+LUVMPenH/pw=\", false, function () {\n  return [useNavigate];\n});\n_c = HomeScreen;\nexport default HomeScreen;\nvar _c;\n$RefreshReg$(_c, \"HomeScreen\");", "map": {"version": 3, "names": ["React", "useRef", "useState", "useEffect", "Link", "useNavigate", "Header", "Footer", "LogoutIcon", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "HomeScreen", "_s", "pricingRef", "isLoggedIn", "setIsLoggedIn", "userData", "setUserData", "navigate", "userDetails", "localStorage", "getItem", "parsedData", "JSON", "parse", "error", "console", "scrollToPricing", "e", "_pricingRef$current", "preventDefault", "current", "scrollIntoView", "behavior", "handleLogout", "removeItem", "window", "location", "reload", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "onClick", "href", "id", "ref", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/interview-ai-app/interviewAI/client/src/pages/home/<USER>"], "sourcesContent": ["import React, { useRef, useState, useEffect } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport Header from '../../components/Header';\nimport Footer from '../../components/Footer';\nimport LogoutIcon from '@mui/icons-material/Logout';\nimport './home.css';\n\nfunction HomeScreen() {\n  const pricingRef = useRef(null);\n  const [isLoggedIn, setIsLoggedIn] = useState(false);\n  // eslint-disable-next-line\n  const [userData, setUserData] = useState(null);\n  // eslint-disable-next-line\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    // Check if user is logged in\n    const userDetails = localStorage.getItem(\"userDetails\");\n    if (userDetails) {\n      setIsLoggedIn(true);\n      try {\n        const parsedData = JSON.parse(userDetails);\n        setUserData(parsedData);\n      } catch (error) {\n        console.error(\"Error parsing user data:\", error);\n      }\n    }\n  }, []);\n\n  const scrollToPricing = (e) => {\n    e.preventDefault();\n    pricingRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  const handleLogout = () => {\n    // Clear localStorage\n    localStorage.removeItem(\"userDetails\");\n    localStorage.removeItem(\"token\");\n    setIsLoggedIn(false);\n    setUserData(null);\n    // Refresh the page to update UI\n    window.location.reload();\n  };\n\n  return (\n    <div className=\"home-page\">\n      <Header />\n\n      <main className=\"home-content\">\n        <section className=\"hero-section\">\n          <div className=\"container\">\n            <h1>Prepare for Interviews with AI</h1>\n            <p className=\"subtitle\">Practice, improve, and land your dream job with our AI-powered interview coach</p>\n            <div className=\"cta-buttons\">\n              {isLoggedIn ? (\n                <>\n                  <Link to=\"/home\" className=\"cta-button primary\">Start Interview</Link>\n                  <button onClick={handleLogout} className=\"cta-button logout\">\n                    <LogoutIcon className=\"logout-icon\" />\n                    Logout\n                  </button>\n                </>\n              ) : (\n                <>\n                  <Link to=\"/login\" className=\"cta-button primary\">Try now</Link>\n                  <a href=\"#pricing\" className=\"cta-button secondary\" onClick={scrollToPricing}>View Plans</a>\n                </>\n              )}\n              {/* <Link to=\"/live-transcription\" className=\"cta-button secondary\">Live Transcription Demo</Link>\n              <Link to=\"/diarized-interview\" className=\"cta-button highlight\">Enhanced Interview with Speaker Diarization</Link>\n              <Link to=\"/automatic-diarization\" className=\"cta-button highlight-new\">Automatic Speaker Detection</Link>\n              <Link to=\"/google-speech\" className=\"cta-button highlight-google\">Google Speech API Diarization</Link> */}\n            </div>\n          </div>\n        </section>\n\n        <section className=\"features-section\">\n          <div className=\"container\">\n            <h2>Features</h2>\n            <div className=\"features-grid\">\n              <div className=\"feature-card\">\n                <div className=\"feature-icon\">🎙️</div>\n                <h3>Real-time Voice Analysis</h3>\n                <p>Practice your answers with our AI that provides instant feedback on your delivery</p>\n              </div>\n              <div className=\"feature-card\">\n                <div className=\"feature-icon\">💼</div>\n                <h3>Industry-specific Questions</h3>\n                <p>Get questions tailored to your industry and role</p>\n              </div>\n              <div className=\"feature-card\">\n                <div className=\"feature-icon\">📝</div>\n                <h3>Personalized Feedback</h3>\n                <p>Receive detailed feedback to improve your interview skills</p>\n              </div>\n              <div className=\"feature-card highlight\">\n                <div className=\"feature-icon\">🔄</div>\n                <h3>Automated Interview Simulation</h3>\n                <p>Experience our new automated interview feature with screen sharing and automatic voice transcription that responds to your answers in real-time</p>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        <section id=\"pricing\" className=\"pricing-section\" ref={pricingRef}>\n          <div className=\"container\">\n            <h2>Choose Your Plan</h2>\n            <div className=\"pricing-grid\">\n              <div className=\"pricing-card\">\n                <div className=\"pricing-header\">\n                  <h3>FREE</h3>\n                  <div className=\"price\">\n                    <span className=\"currency\">$</span>\n                    <span className=\"amount\">0</span>\n                    <span className=\"period\">/ 15Min Free</span>\n                  </div>\n                </div>\n                <div className=\"pricing-features\">\n                  <div className=\"feature-item available\">\n                    <span className=\"check\">✓</span>\n                    <span>15-min Interview Sessions</span>\n                  </div>\n                  <div className=\"feature-item available\">\n                    <span className=\"check\">✓</span>\n                    <span>AI Resume Builder</span>\n                  </div>\n                  <div className=\"feature-item available\">\n                    <span className=\"check\">✓</span>\n                    <span>AI Story Editor</span>\n                  </div>\n                  <div className=\"feature-item unavailable\">\n                    <span className=\"cross\">✕</span>\n                    <span>Full Customization Suite</span>\n                  </div>\n                  <div className=\"feature-item unavailable\">\n                    <span className=\"cross\">✕</span>\n                    <span>Industry Knowledge Base Add-Ons</span>\n                  </div>\n                  <div className=\"feature-item unavailable\">\n                    <span className=\"cross\">✕</span>\n                    <span>Coding Interview Practice</span>\n                  </div>\n                  <div className=\"feature-item unavailable\">\n                    <span className=\"cross\">✕</span>\n                    <span>AI Playground</span>\n                  </div>\n                </div>\n                <Link to=\"/signup/free\" className=\"pricing-cta\">Try for free</Link>\n              </div>\n\n              <div className=\"pricing-card\">\n                <div className=\"pricing-header\">\n                  <h3>PRO</h3>\n                  <div className=\"price\">\n                    <span className=\"currency\">$</span>\n                    <span className=\"amount\">4.99</span>\n                    <span className=\"period\">/ hourly</span>\n                  </div>\n                </div>\n                <div className=\"pricing-features\">\n                  <div className=\"feature-item available\">\n                    <span className=\"check\">✓</span>\n                    <span>Unlimited Interview Sessions</span>\n                  </div>\n                  <div className=\"feature-item available\">\n                    <span className=\"check\">✓</span>\n                    <span>AI Resume Builder</span>\n                  </div>\n                  <div className=\"feature-item available\">\n                    <span className=\"check\">✓</span>\n                    <span>AI Story Editor</span>\n                  </div>\n                  <div className=\"feature-item available\">\n                    <span className=\"check\">✓</span>\n                    <span>Full Customization Suite</span>\n                  </div>\n                  <div className=\"feature-item available\">\n                    <span className=\"check\">✓</span>\n                    <span>Industry Knowledge Base Add-Ons</span>\n                  </div>\n                  <div className=\"feature-item available\">\n                    <span className=\"check\">✓</span>\n                    <span>Coding Interview Practice</span>\n                  </div>\n                  <div className=\"feature-item available\">\n                    <span className=\"check\">✓</span>\n                    <span>AI Playground</span>\n                  </div>\n                </div>\n                <Link to=\"/signup/pro\" className=\"pricing-cta\">Get the plan</Link>\n              </div>\n\n              {/* <div className=\"pricing-card premium\">\n                <div className=\"pricing-badge\">Most Popular</div>\n                <div className=\"pricing-header\">\n                  <h3>PRO PLUS</h3>\n                  <div className=\"price\">\n                    <span className=\"currency\">$</span>\n                    <span className=\"amount\">29.99</span>\n                    <span className=\"period\">/ month billed annually</span>\n                  </div>\n                </div>\n                <div className=\"pricing-features\">\n                  <div className=\"feature-item available\">\n                    <span className=\"check\">✓</span>\n                    <span>Everything in PRO plan</span>\n                  </div>\n                  <div className=\"feature-item available\">\n                    <span className=\"check\">✓</span>\n                    <span>Multi-device support (answer from any device)</span>\n                  </div>\n                  <div className=\"feature-item available\">\n                    <span className=\"check\">✓</span>\n                    <span>No eye movement detection</span>\n                  </div>\n                  <div className=\"feature-item available\">\n                    <span className=\"check\">✓</span>\n                    <span>All meeting platform integration</span>\n                  </div>\n                  <div className=\"feature-item available\">\n                    <span className=\"check\">✓</span>\n                    <span>Pre-trained on your technical skills</span>\n                  </div>\n                  <div className=\"feature-item available\">\n                    <span className=\"check\">✓</span>\n                    <span>Undetectable assistance</span>\n                  </div>\n                  <div className=\"feature-item available\">\n                    <span className=\"check\">✓</span>\n                    <span>Support for all industries & technologies</span>\n                  </div>\n                  <div className=\"feature-item available\">\n                    <span className=\"check\">✓</span>\n                    <span>Multiple language support</span>\n                  </div>\n                  <div className=\"feature-item available\">\n                    <span className=\"check\">✓</span>\n                    <span>Create designs & architecture diagrams</span>\n                  </div>\n                  <div className=\"feature-item available\">\n                    <span className=\"check\">✓</span>\n                    <span>Zero latency (custom optimized model)</span>\n                  </div>\n                  <div className=\"feature-item available\">\n                    <span className=\"check\">✓</span>\n                    <span>Multiple answers before time limit</span>\n                  </div>\n                </div>\n                <Link to=\"/signup/premium\" className=\"pricing-cta\">Get Premium</Link>\n              </div> */}\n            </div>\n          </div>\n        </section>\n\n        <section className=\"faq-section\">\n          <div className=\"container\">\n            <h2>Frequently Asked Questions</h2>\n            <div className=\"faq-list\">\n              <div className=\"faq-item\">\n                <h3>How does the AI interview coach work?</h3>\n                <p>Our AI interview coach uses advanced natural language processing to analyze your responses, provide feedback on your delivery, and suggest improvements to help you ace your interviews.</p>\n              </div>\n              <div className=\"faq-item\">\n                <h3>Can I use this for technical interviews?</h3>\n                <p>Yes! Our PRO plan includes specialized modules for technical and coding interviews across various programming languages and frameworks.</p>\n              </div>\n              <div className=\"faq-item\">\n                <h3>Is my data secure?</h3>\n                <p>We take data privacy seriously. All your interview sessions and personal information are encrypted and never shared with third parties.</p>\n              </div>\n              <div className=\"faq-item\">\n                <h3>Can I cancel my subscription anytime?</h3>\n                <p>Absolutely. You can cancel your subscription at any time with no questions asked.</p>\n              </div>\n              <div className=\"faq-item\">\n                <h3>Do you offer support?</h3>\n                <p>Yes, we provide 24/7 customer support via email and live chat for all our users.</p>\n              </div>\n            </div>\n          </div>\n        </section>\n      </main>\n\n      <Footer />\n    </div>\n  );\n}\n\nexport default HomeScreen;\n\n\n\n\n\n\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,MAAM,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAO,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpB,SAASC,UAAUA,CAAA,EAAG;EAAAC,EAAA;EACpB,MAAMC,UAAU,GAAGd,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAM,CAACe,UAAU,EAAEC,aAAa,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EACnD;EACA,MAAM,CAACgB,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAC9C;EACA,MAAMkB,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAE9BF,SAAS,CAAC,MAAM;IACd;IACA,MAAMkB,WAAW,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IACvD,IAAIF,WAAW,EAAE;MACfJ,aAAa,CAAC,IAAI,CAAC;MACnB,IAAI;QACF,MAAMO,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACL,WAAW,CAAC;QAC1CF,WAAW,CAACK,UAAU,CAAC;MACzB,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAClD;IACF;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAME,eAAe,GAAIC,CAAC,IAAK;IAAA,IAAAC,mBAAA;IAC7BD,CAAC,CAACE,cAAc,CAAC,CAAC;IAClB,CAAAD,mBAAA,GAAAhB,UAAU,CAACkB,OAAO,cAAAF,mBAAA,uBAAlBA,mBAAA,CAAoBG,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAC5D,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB;IACAd,YAAY,CAACe,UAAU,CAAC,aAAa,CAAC;IACtCf,YAAY,CAACe,UAAU,CAAC,OAAO,CAAC;IAChCpB,aAAa,CAAC,KAAK,CAAC;IACpBE,WAAW,CAAC,IAAI,CAAC;IACjB;IACAmB,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;EAC1B,CAAC;EAED,oBACE9B,OAAA;IAAK+B,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBhC,OAAA,CAACJ,MAAM;MAAAqC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEVpC,OAAA;MAAM+B,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC5BhC,OAAA;QAAS+B,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC/BhC,OAAA;UAAK+B,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBhC,OAAA;YAAAgC,QAAA,EAAI;UAA8B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvCpC,OAAA;YAAG+B,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAC;UAA8E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC1GpC,OAAA;YAAK+B,SAAS,EAAC,aAAa;YAAAC,QAAA,EACzB1B,UAAU,gBACTN,OAAA,CAAAE,SAAA;cAAA8B,QAAA,gBACEhC,OAAA,CAACN,IAAI;gBAAC2C,EAAE,EAAC,OAAO;gBAACN,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtEpC,OAAA;gBAAQsC,OAAO,EAAEZ,YAAa;gBAACK,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAC1DhC,OAAA,CAACF,UAAU;kBAACiC,SAAS,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,UAExC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA,eACT,CAAC,gBAEHpC,OAAA,CAAAE,SAAA;cAAA8B,QAAA,gBACEhC,OAAA,CAACN,IAAI;gBAAC2C,EAAE,EAAC,QAAQ;gBAACN,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/DpC,OAAA;gBAAGuC,IAAI,EAAC,UAAU;gBAACR,SAAS,EAAC,sBAAsB;gBAACO,OAAO,EAAEnB,eAAgB;gBAAAa,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA,eAC5F;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEVpC,OAAA;QAAS+B,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eACnChC,OAAA;UAAK+B,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBhC,OAAA;YAAAgC,QAAA,EAAI;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjBpC,OAAA;YAAK+B,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BhC,OAAA;cAAK+B,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BhC,OAAA;gBAAK+B,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvCpC,OAAA;gBAAAgC,QAAA,EAAI;cAAwB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjCpC,OAAA;gBAAAgC,QAAA,EAAG;cAAiF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrF,CAAC,eACNpC,OAAA;cAAK+B,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BhC,OAAA;gBAAK+B,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtCpC,OAAA;gBAAAgC,QAAA,EAAI;cAA2B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpCpC,OAAA;gBAAAgC,QAAA,EAAG;cAAgD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eACNpC,OAAA;cAAK+B,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BhC,OAAA;gBAAK+B,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtCpC,OAAA;gBAAAgC,QAAA,EAAI;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9BpC,OAAA;gBAAAgC,QAAA,EAAG;cAA0D;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9D,CAAC,eACNpC,OAAA;cAAK+B,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrChC,OAAA;gBAAK+B,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtCpC,OAAA;gBAAAgC,QAAA,EAAI;cAA8B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvCpC,OAAA;gBAAAgC,QAAA,EAAG;cAA+I;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEVpC,OAAA;QAASwC,EAAE,EAAC,SAAS;QAACT,SAAS,EAAC,iBAAiB;QAACU,GAAG,EAAEpC,UAAW;QAAA2B,QAAA,eAChEhC,OAAA;UAAK+B,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBhC,OAAA;YAAAgC,QAAA,EAAI;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzBpC,OAAA;YAAK+B,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BhC,OAAA;cAAK+B,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BhC,OAAA;gBAAK+B,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BhC,OAAA;kBAAAgC,QAAA,EAAI;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACbpC,OAAA;kBAAK+B,SAAS,EAAC,OAAO;kBAAAC,QAAA,gBACpBhC,OAAA;oBAAM+B,SAAS,EAAC,UAAU;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACnCpC,OAAA;oBAAM+B,SAAS,EAAC,QAAQ;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACjCpC,OAAA;oBAAM+B,SAAS,EAAC,QAAQ;oBAAAC,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNpC,OAAA;gBAAK+B,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BhC,OAAA;kBAAK+B,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrChC,OAAA;oBAAM+B,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChCpC,OAAA;oBAAAgC,QAAA,EAAM;kBAAyB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,eACNpC,OAAA;kBAAK+B,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrChC,OAAA;oBAAM+B,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChCpC,OAAA;oBAAAgC,QAAA,EAAM;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,eACNpC,OAAA;kBAAK+B,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrChC,OAAA;oBAAM+B,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChCpC,OAAA;oBAAAgC,QAAA,EAAM;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eACNpC,OAAA;kBAAK+B,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,gBACvChC,OAAA;oBAAM+B,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChCpC,OAAA;oBAAAgC,QAAA,EAAM;kBAAwB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC,eACNpC,OAAA;kBAAK+B,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,gBACvChC,OAAA;oBAAM+B,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChCpC,OAAA;oBAAAgC,QAAA,EAAM;kBAA+B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CAAC,eACNpC,OAAA;kBAAK+B,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,gBACvChC,OAAA;oBAAM+B,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChCpC,OAAA;oBAAAgC,QAAA,EAAM;kBAAyB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,eACNpC,OAAA;kBAAK+B,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,gBACvChC,OAAA;oBAAM+B,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChCpC,OAAA;oBAAAgC,QAAA,EAAM;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNpC,OAAA,CAACN,IAAI;gBAAC2C,EAAE,EAAC,cAAc;gBAACN,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC,eAENpC,OAAA;cAAK+B,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BhC,OAAA;gBAAK+B,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BhC,OAAA;kBAAAgC,QAAA,EAAI;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACZpC,OAAA;kBAAK+B,SAAS,EAAC,OAAO;kBAAAC,QAAA,gBACpBhC,OAAA;oBAAM+B,SAAS,EAAC,UAAU;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACnCpC,OAAA;oBAAM+B,SAAS,EAAC,QAAQ;oBAAAC,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACpCpC,OAAA;oBAAM+B,SAAS,EAAC,QAAQ;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNpC,OAAA;gBAAK+B,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BhC,OAAA;kBAAK+B,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrChC,OAAA;oBAAM+B,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChCpC,OAAA;oBAAAgC,QAAA,EAAM;kBAA4B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC,eACNpC,OAAA;kBAAK+B,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrChC,OAAA;oBAAM+B,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChCpC,OAAA;oBAAAgC,QAAA,EAAM;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,eACNpC,OAAA;kBAAK+B,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrChC,OAAA;oBAAM+B,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChCpC,OAAA;oBAAAgC,QAAA,EAAM;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eACNpC,OAAA;kBAAK+B,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrChC,OAAA;oBAAM+B,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChCpC,OAAA;oBAAAgC,QAAA,EAAM;kBAAwB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC,eACNpC,OAAA;kBAAK+B,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrChC,OAAA;oBAAM+B,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChCpC,OAAA;oBAAAgC,QAAA,EAAM;kBAA+B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CAAC,eACNpC,OAAA;kBAAK+B,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrChC,OAAA;oBAAM+B,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChCpC,OAAA;oBAAAgC,QAAA,EAAM;kBAAyB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,eACNpC,OAAA;kBAAK+B,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrChC,OAAA;oBAAM+B,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChCpC,OAAA;oBAAAgC,QAAA,EAAM;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNpC,OAAA,CAACN,IAAI;gBAAC2C,EAAE,EAAC,aAAa;gBAACN,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA4DH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEVpC,OAAA;QAAS+B,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC9BhC,OAAA;UAAK+B,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBhC,OAAA;YAAAgC,QAAA,EAAI;UAA0B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnCpC,OAAA;YAAK+B,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBhC,OAAA;cAAK+B,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBhC,OAAA;gBAAAgC,QAAA,EAAI;cAAqC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9CpC,OAAA;gBAAAgC,QAAA,EAAG;cAAwL;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5L,CAAC,eACNpC,OAAA;cAAK+B,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBhC,OAAA;gBAAAgC,QAAA,EAAI;cAAwC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjDpC,OAAA;gBAAAgC,QAAA,EAAG;cAAuI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3I,CAAC,eACNpC,OAAA;cAAK+B,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBhC,OAAA;gBAAAgC,QAAA,EAAI;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3BpC,OAAA;gBAAAgC,QAAA,EAAG;cAAuI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3I,CAAC,eACNpC,OAAA;cAAK+B,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBhC,OAAA;gBAAAgC,QAAA,EAAI;cAAqC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9CpC,OAAA;gBAAAgC,QAAA,EAAG;cAAiF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrF,CAAC,eACNpC,OAAA;cAAK+B,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBhC,OAAA;gBAAAgC,QAAA,EAAI;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9BpC,OAAA;gBAAAgC,QAAA,EAAG;cAAgF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAEPpC,OAAA,CAACH,MAAM;MAAAoC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV;AAAChC,EAAA,CAvRQD,UAAU;EAAA,QAMAR,WAAW;AAAA;AAAA+C,EAAA,GANrBvC,UAAU;AAyRnB,eAAeA,UAAU;AAAC,IAAAuC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}