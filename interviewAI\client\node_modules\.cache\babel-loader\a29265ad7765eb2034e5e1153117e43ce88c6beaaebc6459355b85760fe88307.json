{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\interview-ai-app\\\\interviewAI\\\\client\\\\src\\\\components\\\\DiarizedTranscription.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect, useRef } from 'react';\nimport './DiarizedTranscription.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction DiarizedTranscription({\n  onTranscriptChange\n}) {\n  _s();\n  // State for transcription\n  const [transcript, setTranscript] = useState('');\n  const [diarizedSegments, setDiarizedSegments] = useState([]);\n  const [isListening, setIsListening] = useState(false);\n  const [isCapturingTabAudio, setIsCapturingTabAudio] = useState(false);\n  // Default speaker is based on audio source - tab audio is speaker1 (interviewer), mic is speaker2 (candidate)\n  const [currentSpeaker, setCurrentSpeaker] = useState('speaker2');\n\n  // Refs\n  const recognitionRef = useRef(null);\n  const finalTranscriptRef = useRef('');\n  const audioContextRef = useRef(null);\n  const mediaStreamRef = useRef(null);\n\n  // Initialize speech recognition\n  useEffect(() => {\n    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n    if (!SpeechRecognition) {\n      console.error(\"Speech Recognition API not supported in this browser\");\n      return;\n    }\n\n    // Clean up function\n    return () => {\n      if (recognitionRef.current) {\n        try {\n          recognitionRef.current.stop();\n        } catch (error) {\n          console.error(\"Error stopping recognition:\", error);\n        }\n      }\n      if (audioContextRef.current) {\n        try {\n          audioContextRef.current.close();\n        } catch (error) {\n          console.error(\"Error closing audio context:\", error);\n        }\n      }\n      if (mediaStreamRef.current) {\n        mediaStreamRef.current.getTracks().forEach(track => track.stop());\n      }\n    };\n  }, []);\n\n  // Start listening with microphone\n  const startListening = async () => {\n    try {\n      if (isListening) return;\n      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n      recognitionRef.current = new SpeechRecognition();\n      recognitionRef.current.continuous = true;\n      recognitionRef.current.interimResults = true;\n      recognitionRef.current.lang = 'en-US';\n\n      // Reset transcript\n      finalTranscriptRef.current = '';\n      setTranscript('');\n      setDiarizedSegments([]);\n\n      // Set up audio context for voice analysis\n      if (!audioContextRef.current) {\n        // eslint-disable-next-line\n        audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();\n      }\n\n      // Get microphone stream\n      const stream = await navigator.mediaDevices.getUserMedia({\n        audio: true\n      });\n      mediaStreamRef.current = stream;\n\n      // Set speaker to candidate (speaker2) for microphone input\n      setCurrentSpeaker('speaker2');\n\n      // Set up recognition event handlers\n      setupRecognitionHandlers();\n\n      // Start recognition\n      recognitionRef.current.start();\n      setIsListening(true);\n      setIsCapturingTabAudio(false);\n      console.log(\"Microphone listening started - You are identified as the Candidate\");\n    } catch (error) {\n      console.error(\"Error starting microphone listening:\", error);\n      alert(\"Failed to start microphone: \" + error.message);\n    }\n  };\n\n  // Start capturing tab audio\n  const startTabAudioCapture = async () => {\n    try {\n      if (isListening) return;\n\n      // Request screen sharing with audio\n      const stream = await navigator.mediaDevices.getDisplayMedia({\n        video: true,\n        audio: true\n      });\n\n      // Check if audio is included\n      const audioTracks = stream.getAudioTracks();\n      if (audioTracks.length === 0) {\n        alert(\"No audio track found. Please make sure to select 'Share audio' when sharing the tab.\");\n        stream.getTracks().forEach(track => track.stop());\n        return;\n      }\n      mediaStreamRef.current = stream;\n\n      // Set up audio context for analysis\n      if (!audioContextRef.current) {\n        // eslint-disable-next-line\n        audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();\n      }\n\n      // Set speaker to interviewer (speaker1) for tab audio\n      setCurrentSpeaker('speaker1');\n\n      // Initialize speech recognition\n      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n      recognitionRef.current = new SpeechRecognition();\n      recognitionRef.current.continuous = true;\n      recognitionRef.current.interimResults = true;\n      recognitionRef.current.lang = 'en-US';\n\n      // Reset transcript\n      finalTranscriptRef.current = '';\n      setTranscript('');\n      setDiarizedSegments([]);\n\n      // Set up recognition event handlers\n      setupRecognitionHandlers();\n\n      // Start recognition\n      recognitionRef.current.start();\n      setIsListening(true);\n      setIsCapturingTabAudio(true);\n\n      // Handle stream ending\n      stream.getVideoTracks()[0].onended = () => {\n        stopTranscription();\n      };\n      console.log(\"Tab audio capture started - Tab audio is identified as the Interviewer\");\n    } catch (error) {\n      console.error(\"Error capturing tab audio:\", error);\n      alert(\"Failed to capture tab audio: \" + error.message);\n    }\n  };\n\n  // Stop transcription\n  const stopTranscription = () => {\n    if (recognitionRef.current) {\n      try {\n        recognitionRef.current.stop();\n      } catch (error) {\n        console.error(\"Error stopping recognition:\", error);\n      }\n    }\n    if (mediaStreamRef.current) {\n      mediaStreamRef.current.getTracks().forEach(track => track.stop());\n      mediaStreamRef.current = null;\n    }\n    setIsListening(false);\n    setIsCapturingTabAudio(false);\n    console.log(\"Transcription stopped\");\n  };\n\n  // Set up recognition event handlers\n  const setupRecognitionHandlers = () => {\n    if (!recognitionRef.current) return;\n    recognitionRef.current.onresult = event => {\n      let interimTranscript = '';\n      for (let i = event.resultIndex; i < event.results.length; i++) {\n        const result = event.results[i];\n        const text = result[0].transcript;\n        if (result.isFinal) {\n          // Add to final transcript with current speaker\n          finalTranscriptRef.current += text + ' ';\n\n          // Add as a new segment with the current speaker\n          setDiarizedSegments(prev => [...prev, {\n            text,\n            speaker: currentSpeaker,\n            timestamp: new Date(),\n            isFinal: true\n          }]);\n        } else {\n          interimTranscript += text;\n        }\n      }\n      const fullTranscript = finalTranscriptRef.current + interimTranscript;\n      setTranscript(fullTranscript);\n\n      // Notify parent component\n      if (onTranscriptChange) {\n        onTranscriptChange(fullTranscript, diarizedSegments);\n      }\n    };\n    recognitionRef.current.onerror = event => {\n      console.error(\"Recognition error:\", event.error);\n      if (event.error !== 'no-speech') {\n        stopTranscription();\n      }\n    };\n    recognitionRef.current.onend = () => {\n      console.log(\"Recognition ended\");\n      // Restart if we're still supposed to be listening\n      if (isListening) {\n        try {\n          recognitionRef.current.start();\n        } catch (error) {\n          console.error(\"Failed to restart recognition:\", error);\n          setIsListening(false);\n        }\n      }\n    };\n  };\n\n  // Note: We're not using audio analysis for speaker diarization anymore\n  // Instead, we're using the audio source to determine the speaker\n  // Tab audio = Interviewer (speaker1), Microphone = Candidate (speaker2)\n\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"diarized-transcription\",\n    children: [!window.SpeechRecognition && !window.webkitSpeechRecognition && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"browser-warning\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Warning:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 13\n        }, this), \" Your browser doesn't support speech recognition. Please use Chrome, Edge, or another Chromium-based browser for this feature to work.\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"transcript-display\",\n      children: diarizedSegments.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"diarized-segments\",\n        children: [diarizedSegments.map((segment, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `segment ${segment.speaker}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"speaker-label\",\n            children: segment.speaker === 'speaker1' ? 'Interviewer (Tab Audio)' : 'Candidate (Your Mic)'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"segment-text\",\n            children: segment.text\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 17\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 15\n        }, this)), transcript && finalTranscriptRef.current !== transcript && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `segment ${currentSpeaker} interim`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"speaker-label\",\n            children: currentSpeaker === 'speaker1' ? 'Interviewer (Tab Audio)' : 'Candidate (Your Mic)'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"segment-text\",\n            children: transcript.substring(finalTranscriptRef.current.length)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"placeholder\",\n        children: \"Waiting for speech...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 252,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"controls\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: `control-button ${isListening && !isCapturingTabAudio ? 'listening' : ''}`,\n        onClick: isListening ? stopTranscription : startListening,\n        disabled: !window.SpeechRecognition && !window.webkitSpeechRecognition,\n        title: \"Capture your microphone audio (you will be identified as the Candidate)\",\n        children: [isListening && !isCapturingTabAudio ? 'Stop' : 'Start', \" Your Mic (Candidate)\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: `control-button tab-audio ${isCapturingTabAudio ? 'capturing' : ''}`,\n        onClick: isCapturingTabAudio ? stopTranscription : startTabAudioCapture,\n        disabled: !window.SpeechRecognition && !window.webkitSpeechRecognition,\n        title: \"Capture audio from another tab like YouTube (will be identified as the Interviewer)\",\n        children: [isCapturingTabAudio ? 'Stop' : 'Start', \" Tab Audio (Interviewer)\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 9\n      }, this), isListening && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"status\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"listening-indicator\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 13\n        }, this), isCapturingTabAudio ? 'Capturing Tab Audio...' : 'Listening to Microphone...']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 282,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 242,\n    columnNumber: 5\n  }, this);\n}\n_s(DiarizedTranscription, \"6f3PSJgYBBBtWGwOK/kYm6Ih4A4=\");\n_c = DiarizedTranscription;\nexport default DiarizedTranscription;\nvar _c;\n$RefreshReg$(_c, \"DiarizedTranscription\");", "map": {"version": 3, "names": ["useState", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "DiarizedTranscription", "onTranscriptChange", "_s", "transcript", "setTranscript", "diarizedSegments", "setDiarizedSegments", "isListening", "setIsListening", "isCapturingTabAudio", "setIsCapturingTabAudio", "currentSpeaker", "setCurrentSpeaker", "recognitionRef", "finalTranscriptRef", "audioContextRef", "mediaStreamRef", "SpeechRecognition", "window", "webkitSpeechRecognition", "console", "error", "current", "stop", "close", "getTracks", "for<PERSON>ach", "track", "startListening", "continuous", "interimResults", "lang", "AudioContext", "webkitAudioContext", "stream", "navigator", "mediaDevices", "getUserMedia", "audio", "setupRecognitionHandlers", "start", "log", "alert", "message", "startTabAudioCapture", "getDisplayMedia", "video", "audioTracks", "getAudioTracks", "length", "getVideoTracks", "onended", "stopTranscription", "on<PERSON>ult", "event", "interimTranscript", "i", "resultIndex", "results", "result", "text", "isFinal", "prev", "speaker", "timestamp", "Date", "fullTranscript", "onerror", "onend", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "segment", "index", "substring", "onClick", "disabled", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/interview-ai-app/interviewAI/client/src/components/DiarizedTranscription.jsx"], "sourcesContent": ["import { useState, useEffect, useRef } from 'react';\nimport './DiarizedTranscription.css';\n\nfunction DiarizedTranscription({ onTranscriptChange }) {\n  // State for transcription\n  const [transcript, setTranscript] = useState('');\n  const [diarizedSegments, setDiarizedSegments] = useState([]);\n  const [isListening, setIsListening] = useState(false);\n  const [isCapturingTabAudio, setIsCapturingTabAudio] = useState(false);\n  // Default speaker is based on audio source - tab audio is speaker1 (interviewer), mic is speaker2 (candidate)\n  const [currentSpeaker, setCurrentSpeaker] = useState('speaker2');\n\n  // Refs\n  const recognitionRef = useRef(null);\n  const finalTranscriptRef = useRef('');\n  const audioContextRef = useRef(null);\n  const mediaStreamRef = useRef(null);\n\n  // Initialize speech recognition\n  useEffect(() => {\n    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n    if (!SpeechRecognition) {\n      console.error(\"Speech Recognition API not supported in this browser\");\n      return;\n    }\n\n    // Clean up function\n    return () => {\n      if (recognitionRef.current) {\n        try {\n          recognitionRef.current.stop();\n        } catch (error) {\n          console.error(\"Error stopping recognition:\", error);\n        }\n      }\n\n      if (audioContextRef.current) {\n        try {\n          audioContextRef.current.close();\n        } catch (error) {\n          console.error(\"Error closing audio context:\", error);\n        }\n      }\n\n      if (mediaStreamRef.current) {\n        mediaStreamRef.current.getTracks().forEach(track => track.stop());\n      }\n    };\n  }, []);\n\n  // Start listening with microphone\n  const startListening = async () => {\n    try {\n      if (isListening) return;\n\n      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n      recognitionRef.current = new SpeechRecognition();\n      recognitionRef.current.continuous = true;\n      recognitionRef.current.interimResults = true;\n      recognitionRef.current.lang = 'en-US';\n\n      // Reset transcript\n      finalTranscriptRef.current = '';\n      setTranscript('');\n      setDiarizedSegments([]);\n\n      // Set up audio context for voice analysis\n      if (!audioContextRef.current) {\n        // eslint-disable-next-line\n        audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();\n      }\n\n      // Get microphone stream\n      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });\n      mediaStreamRef.current = stream;\n\n      // Set speaker to candidate (speaker2) for microphone input\n      setCurrentSpeaker('speaker2');\n\n      // Set up recognition event handlers\n      setupRecognitionHandlers();\n\n      // Start recognition\n      recognitionRef.current.start();\n      setIsListening(true);\n      setIsCapturingTabAudio(false);\n\n      console.log(\"Microphone listening started - You are identified as the Candidate\");\n    } catch (error) {\n      console.error(\"Error starting microphone listening:\", error);\n      alert(\"Failed to start microphone: \" + error.message);\n    }\n  };\n\n  // Start capturing tab audio\n  const startTabAudioCapture = async () => {\n    try {\n      if (isListening) return;\n\n      // Request screen sharing with audio\n      const stream = await navigator.mediaDevices.getDisplayMedia({\n        video: true,\n        audio: true\n      });\n\n      // Check if audio is included\n      const audioTracks = stream.getAudioTracks();\n      if (audioTracks.length === 0) {\n        alert(\"No audio track found. Please make sure to select 'Share audio' when sharing the tab.\");\n        stream.getTracks().forEach(track => track.stop());\n        return;\n      }\n\n      mediaStreamRef.current = stream;\n\n      // Set up audio context for analysis\n      if (!audioContextRef.current) {\n        // eslint-disable-next-line\n        audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();\n      }\n\n      // Set speaker to interviewer (speaker1) for tab audio\n      setCurrentSpeaker('speaker1');\n\n      // Initialize speech recognition\n      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n      recognitionRef.current = new SpeechRecognition();\n      recognitionRef.current.continuous = true;\n      recognitionRef.current.interimResults = true;\n      recognitionRef.current.lang = 'en-US';\n\n      // Reset transcript\n      finalTranscriptRef.current = '';\n      setTranscript('');\n      setDiarizedSegments([]);\n\n      // Set up recognition event handlers\n      setupRecognitionHandlers();\n\n      // Start recognition\n      recognitionRef.current.start();\n      setIsListening(true);\n      setIsCapturingTabAudio(true);\n\n      // Handle stream ending\n      stream.getVideoTracks()[0].onended = () => {\n        stopTranscription();\n      };\n\n      console.log(\"Tab audio capture started - Tab audio is identified as the Interviewer\");\n    } catch (error) {\n      console.error(\"Error capturing tab audio:\", error);\n      alert(\"Failed to capture tab audio: \" + error.message);\n    }\n  };\n\n  // Stop transcription\n  const stopTranscription = () => {\n    if (recognitionRef.current) {\n      try {\n        recognitionRef.current.stop();\n      } catch (error) {\n        console.error(\"Error stopping recognition:\", error);\n      }\n    }\n\n    if (mediaStreamRef.current) {\n      mediaStreamRef.current.getTracks().forEach(track => track.stop());\n      mediaStreamRef.current = null;\n    }\n\n    setIsListening(false);\n    setIsCapturingTabAudio(false);\n    console.log(\"Transcription stopped\");\n  };\n\n  // Set up recognition event handlers\n  const setupRecognitionHandlers = () => {\n    if (!recognitionRef.current) return;\n\n    recognitionRef.current.onresult = (event) => {\n      let interimTranscript = '';\n\n      for (let i = event.resultIndex; i < event.results.length; i++) {\n        const result = event.results[i];\n        const text = result[0].transcript;\n\n        if (result.isFinal) {\n          // Add to final transcript with current speaker\n          finalTranscriptRef.current += text + ' ';\n\n          // Add as a new segment with the current speaker\n          setDiarizedSegments(prev => [\n            ...prev,\n            {\n              text,\n              speaker: currentSpeaker,\n              timestamp: new Date(),\n              isFinal: true\n            }\n          ]);\n        } else {\n          interimTranscript += text;\n        }\n      }\n\n      const fullTranscript = finalTranscriptRef.current + interimTranscript;\n      setTranscript(fullTranscript);\n\n      // Notify parent component\n      if (onTranscriptChange) {\n        onTranscriptChange(fullTranscript, diarizedSegments);\n      }\n    };\n\n    recognitionRef.current.onerror = (event) => {\n      console.error(\"Recognition error:\", event.error);\n      if (event.error !== 'no-speech') {\n        stopTranscription();\n      }\n    };\n\n    recognitionRef.current.onend = () => {\n      console.log(\"Recognition ended\");\n      // Restart if we're still supposed to be listening\n      if (isListening) {\n        try {\n          recognitionRef.current.start();\n        } catch (error) {\n          console.error(\"Failed to restart recognition:\", error);\n          setIsListening(false);\n        }\n      }\n    };\n  };\n\n  // Note: We're not using audio analysis for speaker diarization anymore\n  // Instead, we're using the audio source to determine the speaker\n  // Tab audio = Interviewer (speaker1), Microphone = Candidate (speaker2)\n\n  return (\n    <div className=\"diarized-transcription\">\n      {!window.SpeechRecognition && !window.webkitSpeechRecognition && (\n        <div className=\"browser-warning\">\n          <p>\n            <strong>Warning:</strong> Your browser doesn't support speech recognition.\n            Please use Chrome, Edge, or another Chromium-based browser for this feature to work.\n          </p>\n        </div>\n      )}\n\n      <div className=\"transcript-display\">\n        {diarizedSegments.length > 0 ? (\n          <div className=\"diarized-segments\">\n            {diarizedSegments.map((segment, index) => (\n              <div\n                key={index}\n                className={`segment ${segment.speaker}`}\n              >\n                <div className=\"speaker-label\">\n                  {segment.speaker === 'speaker1' ? 'Interviewer (Tab Audio)' : 'Candidate (Your Mic)'}\n                </div>\n                <div className=\"segment-text\">{segment.text}</div>\n              </div>\n            ))}\n\n            {/* Current interim transcript */}\n            {transcript && finalTranscriptRef.current !== transcript && (\n              <div className={`segment ${currentSpeaker} interim`}>\n                <div className=\"speaker-label\">\n                  {currentSpeaker === 'speaker1' ? 'Interviewer (Tab Audio)' : 'Candidate (Your Mic)'}\n                </div>\n                <div className=\"segment-text\">{transcript.substring(finalTranscriptRef.current.length)}</div>\n              </div>\n            )}\n          </div>\n        ) : (\n          <p className=\"placeholder\">Waiting for speech...</p>\n        )}\n      </div>\n\n      <div className=\"controls\">\n        <button\n          className={`control-button ${isListening && !isCapturingTabAudio ? 'listening' : ''}`}\n          onClick={isListening ? stopTranscription : startListening}\n          disabled={!window.SpeechRecognition && !window.webkitSpeechRecognition}\n          title=\"Capture your microphone audio (you will be identified as the Candidate)\"\n        >\n          {isListening && !isCapturingTabAudio ? 'Stop' : 'Start'} Your Mic (Candidate)\n        </button>\n\n        <button\n          className={`control-button tab-audio ${isCapturingTabAudio ? 'capturing' : ''}`}\n          onClick={isCapturingTabAudio ? stopTranscription : startTabAudioCapture}\n          disabled={!window.SpeechRecognition && !window.webkitSpeechRecognition}\n          title=\"Capture audio from another tab like YouTube (will be identified as the Interviewer)\"\n        >\n          {isCapturingTabAudio ? 'Stop' : 'Start'} Tab Audio (Interviewer)\n        </button>\n\n        {isListening && (\n          <div className=\"status\">\n            <span className=\"listening-indicator\"></span>\n            {isCapturingTabAudio ? 'Capturing Tab Audio...' : 'Listening to Microphone...'}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n\nexport default DiarizedTranscription;\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACnD,OAAO,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,SAASC,qBAAqBA,CAAC;EAAEC;AAAmB,CAAC,EAAE;EAAAC,EAAA;EACrD;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACU,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACY,WAAW,EAAEC,cAAc,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACc,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EACrE;EACA,MAAM,CAACgB,cAAc,EAAEC,iBAAiB,CAAC,GAAGjB,QAAQ,CAAC,UAAU,CAAC;;EAEhE;EACA,MAAMkB,cAAc,GAAGhB,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMiB,kBAAkB,GAAGjB,MAAM,CAAC,EAAE,CAAC;EACrC,MAAMkB,eAAe,GAAGlB,MAAM,CAAC,IAAI,CAAC;EACpC,MAAMmB,cAAc,GAAGnB,MAAM,CAAC,IAAI,CAAC;;EAEnC;EACAD,SAAS,CAAC,MAAM;IACd,MAAMqB,iBAAiB,GAAGC,MAAM,CAACD,iBAAiB,IAAIC,MAAM,CAACC,uBAAuB;IACpF,IAAI,CAACF,iBAAiB,EAAE;MACtBG,OAAO,CAACC,KAAK,CAAC,sDAAsD,CAAC;MACrE;IACF;;IAEA;IACA,OAAO,MAAM;MACX,IAAIR,cAAc,CAACS,OAAO,EAAE;QAC1B,IAAI;UACFT,cAAc,CAACS,OAAO,CAACC,IAAI,CAAC,CAAC;QAC/B,CAAC,CAAC,OAAOF,KAAK,EAAE;UACdD,OAAO,CAACC,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACrD;MACF;MAEA,IAAIN,eAAe,CAACO,OAAO,EAAE;QAC3B,IAAI;UACFP,eAAe,CAACO,OAAO,CAACE,KAAK,CAAC,CAAC;QACjC,CAAC,CAAC,OAAOH,KAAK,EAAE;UACdD,OAAO,CAACC,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACtD;MACF;MAEA,IAAIL,cAAc,CAACM,OAAO,EAAE;QAC1BN,cAAc,CAACM,OAAO,CAACG,SAAS,CAAC,CAAC,CAACC,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACJ,IAAI,CAAC,CAAC,CAAC;MACnE;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMK,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,IAAIrB,WAAW,EAAE;MAEjB,MAAMU,iBAAiB,GAAGC,MAAM,CAACD,iBAAiB,IAAIC,MAAM,CAACC,uBAAuB;MACpFN,cAAc,CAACS,OAAO,GAAG,IAAIL,iBAAiB,CAAC,CAAC;MAChDJ,cAAc,CAACS,OAAO,CAACO,UAAU,GAAG,IAAI;MACxChB,cAAc,CAACS,OAAO,CAACQ,cAAc,GAAG,IAAI;MAC5CjB,cAAc,CAACS,OAAO,CAACS,IAAI,GAAG,OAAO;;MAErC;MACAjB,kBAAkB,CAACQ,OAAO,GAAG,EAAE;MAC/BlB,aAAa,CAAC,EAAE,CAAC;MACjBE,mBAAmB,CAAC,EAAE,CAAC;;MAEvB;MACA,IAAI,CAACS,eAAe,CAACO,OAAO,EAAE;QAC5B;QACAP,eAAe,CAACO,OAAO,GAAG,KAAKJ,MAAM,CAACc,YAAY,IAAId,MAAM,CAACe,kBAAkB,EAAE,CAAC;MACpF;;MAEA;MACA,MAAMC,MAAM,GAAG,MAAMC,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;QAAEC,KAAK,EAAE;MAAK,CAAC,CAAC;MACzEtB,cAAc,CAACM,OAAO,GAAGY,MAAM;;MAE/B;MACAtB,iBAAiB,CAAC,UAAU,CAAC;;MAE7B;MACA2B,wBAAwB,CAAC,CAAC;;MAE1B;MACA1B,cAAc,CAACS,OAAO,CAACkB,KAAK,CAAC,CAAC;MAC9BhC,cAAc,CAAC,IAAI,CAAC;MACpBE,sBAAsB,CAAC,KAAK,CAAC;MAE7BU,OAAO,CAACqB,GAAG,CAAC,oEAAoE,CAAC;IACnF,CAAC,CAAC,OAAOpB,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5DqB,KAAK,CAAC,8BAA8B,GAAGrB,KAAK,CAACsB,OAAO,CAAC;IACvD;EACF,CAAC;;EAED;EACA,MAAMC,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACF,IAAIrC,WAAW,EAAE;;MAEjB;MACA,MAAM2B,MAAM,GAAG,MAAMC,SAAS,CAACC,YAAY,CAACS,eAAe,CAAC;QAC1DC,KAAK,EAAE,IAAI;QACXR,KAAK,EAAE;MACT,CAAC,CAAC;;MAEF;MACA,MAAMS,WAAW,GAAGb,MAAM,CAACc,cAAc,CAAC,CAAC;MAC3C,IAAID,WAAW,CAACE,MAAM,KAAK,CAAC,EAAE;QAC5BP,KAAK,CAAC,sFAAsF,CAAC;QAC7FR,MAAM,CAACT,SAAS,CAAC,CAAC,CAACC,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACJ,IAAI,CAAC,CAAC,CAAC;QACjD;MACF;MAEAP,cAAc,CAACM,OAAO,GAAGY,MAAM;;MAE/B;MACA,IAAI,CAACnB,eAAe,CAACO,OAAO,EAAE;QAC5B;QACAP,eAAe,CAACO,OAAO,GAAG,KAAKJ,MAAM,CAACc,YAAY,IAAId,MAAM,CAACe,kBAAkB,EAAE,CAAC;MACpF;;MAEA;MACArB,iBAAiB,CAAC,UAAU,CAAC;;MAE7B;MACA,MAAMK,iBAAiB,GAAGC,MAAM,CAACD,iBAAiB,IAAIC,MAAM,CAACC,uBAAuB;MACpFN,cAAc,CAACS,OAAO,GAAG,IAAIL,iBAAiB,CAAC,CAAC;MAChDJ,cAAc,CAACS,OAAO,CAACO,UAAU,GAAG,IAAI;MACxChB,cAAc,CAACS,OAAO,CAACQ,cAAc,GAAG,IAAI;MAC5CjB,cAAc,CAACS,OAAO,CAACS,IAAI,GAAG,OAAO;;MAErC;MACAjB,kBAAkB,CAACQ,OAAO,GAAG,EAAE;MAC/BlB,aAAa,CAAC,EAAE,CAAC;MACjBE,mBAAmB,CAAC,EAAE,CAAC;;MAEvB;MACAiC,wBAAwB,CAAC,CAAC;;MAE1B;MACA1B,cAAc,CAACS,OAAO,CAACkB,KAAK,CAAC,CAAC;MAC9BhC,cAAc,CAAC,IAAI,CAAC;MACpBE,sBAAsB,CAAC,IAAI,CAAC;;MAE5B;MACAwB,MAAM,CAACgB,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,OAAO,GAAG,MAAM;QACzCC,iBAAiB,CAAC,CAAC;MACrB,CAAC;MAEDhC,OAAO,CAACqB,GAAG,CAAC,wEAAwE,CAAC;IACvF,CAAC,CAAC,OAAOpB,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDqB,KAAK,CAAC,+BAA+B,GAAGrB,KAAK,CAACsB,OAAO,CAAC;IACxD;EACF,CAAC;;EAED;EACA,MAAMS,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAIvC,cAAc,CAACS,OAAO,EAAE;MAC1B,IAAI;QACFT,cAAc,CAACS,OAAO,CAACC,IAAI,CAAC,CAAC;MAC/B,CAAC,CAAC,OAAOF,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACrD;IACF;IAEA,IAAIL,cAAc,CAACM,OAAO,EAAE;MAC1BN,cAAc,CAACM,OAAO,CAACG,SAAS,CAAC,CAAC,CAACC,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACJ,IAAI,CAAC,CAAC,CAAC;MACjEP,cAAc,CAACM,OAAO,GAAG,IAAI;IAC/B;IAEAd,cAAc,CAAC,KAAK,CAAC;IACrBE,sBAAsB,CAAC,KAAK,CAAC;IAC7BU,OAAO,CAACqB,GAAG,CAAC,uBAAuB,CAAC;EACtC,CAAC;;EAED;EACA,MAAMF,wBAAwB,GAAGA,CAAA,KAAM;IACrC,IAAI,CAAC1B,cAAc,CAACS,OAAO,EAAE;IAE7BT,cAAc,CAACS,OAAO,CAAC+B,QAAQ,GAAIC,KAAK,IAAK;MAC3C,IAAIC,iBAAiB,GAAG,EAAE;MAE1B,KAAK,IAAIC,CAAC,GAAGF,KAAK,CAACG,WAAW,EAAED,CAAC,GAAGF,KAAK,CAACI,OAAO,CAACT,MAAM,EAAEO,CAAC,EAAE,EAAE;QAC7D,MAAMG,MAAM,GAAGL,KAAK,CAACI,OAAO,CAACF,CAAC,CAAC;QAC/B,MAAMI,IAAI,GAAGD,MAAM,CAAC,CAAC,CAAC,CAACxD,UAAU;QAEjC,IAAIwD,MAAM,CAACE,OAAO,EAAE;UAClB;UACA/C,kBAAkB,CAACQ,OAAO,IAAIsC,IAAI,GAAG,GAAG;;UAExC;UACAtD,mBAAmB,CAACwD,IAAI,IAAI,CAC1B,GAAGA,IAAI,EACP;YACEF,IAAI;YACJG,OAAO,EAAEpD,cAAc;YACvBqD,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;YACrBJ,OAAO,EAAE;UACX,CAAC,CACF,CAAC;QACJ,CAAC,MAAM;UACLN,iBAAiB,IAAIK,IAAI;QAC3B;MACF;MAEA,MAAMM,cAAc,GAAGpD,kBAAkB,CAACQ,OAAO,GAAGiC,iBAAiB;MACrEnD,aAAa,CAAC8D,cAAc,CAAC;;MAE7B;MACA,IAAIjE,kBAAkB,EAAE;QACtBA,kBAAkB,CAACiE,cAAc,EAAE7D,gBAAgB,CAAC;MACtD;IACF,CAAC;IAEDQ,cAAc,CAACS,OAAO,CAAC6C,OAAO,GAAIb,KAAK,IAAK;MAC1ClC,OAAO,CAACC,KAAK,CAAC,oBAAoB,EAAEiC,KAAK,CAACjC,KAAK,CAAC;MAChD,IAAIiC,KAAK,CAACjC,KAAK,KAAK,WAAW,EAAE;QAC/B+B,iBAAiB,CAAC,CAAC;MACrB;IACF,CAAC;IAEDvC,cAAc,CAACS,OAAO,CAAC8C,KAAK,GAAG,MAAM;MACnChD,OAAO,CAACqB,GAAG,CAAC,mBAAmB,CAAC;MAChC;MACA,IAAIlC,WAAW,EAAE;QACf,IAAI;UACFM,cAAc,CAACS,OAAO,CAACkB,KAAK,CAAC,CAAC;QAChC,CAAC,CAAC,OAAOnB,KAAK,EAAE;UACdD,OAAO,CAACC,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;UACtDb,cAAc,CAAC,KAAK,CAAC;QACvB;MACF;IACF,CAAC;EACH,CAAC;;EAED;EACA;EACA;;EAEA,oBACET,OAAA;IAAKsE,SAAS,EAAC,wBAAwB;IAAAC,QAAA,GACpC,CAACpD,MAAM,CAACD,iBAAiB,IAAI,CAACC,MAAM,CAACC,uBAAuB,iBAC3DpB,OAAA;MAAKsE,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAC9BvE,OAAA;QAAAuE,QAAA,gBACEvE,OAAA;UAAAuE,QAAA,EAAQ;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,0IAE3B;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACN,eAED3E,OAAA;MAAKsE,SAAS,EAAC,oBAAoB;MAAAC,QAAA,EAChCjE,gBAAgB,CAAC4C,MAAM,GAAG,CAAC,gBAC1BlD,OAAA;QAAKsE,SAAS,EAAC,mBAAmB;QAAAC,QAAA,GAC/BjE,gBAAgB,CAACsE,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBACnC9E,OAAA;UAEEsE,SAAS,EAAE,WAAWO,OAAO,CAACb,OAAO,EAAG;UAAAO,QAAA,gBAExCvE,OAAA;YAAKsE,SAAS,EAAC,eAAe;YAAAC,QAAA,EAC3BM,OAAO,CAACb,OAAO,KAAK,UAAU,GAAG,yBAAyB,GAAG;UAAsB;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjF,CAAC,eACN3E,OAAA;YAAKsE,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAEM,OAAO,CAAChB;UAAI;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA,GAN7CG,KAAK;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAOP,CACN,CAAC,EAGDvE,UAAU,IAAIW,kBAAkB,CAACQ,OAAO,KAAKnB,UAAU,iBACtDJ,OAAA;UAAKsE,SAAS,EAAE,WAAW1D,cAAc,UAAW;UAAA2D,QAAA,gBAClDvE,OAAA;YAAKsE,SAAS,EAAC,eAAe;YAAAC,QAAA,EAC3B3D,cAAc,KAAK,UAAU,GAAG,yBAAyB,GAAG;UAAsB;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChF,CAAC,eACN3E,OAAA;YAAKsE,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAEnE,UAAU,CAAC2E,SAAS,CAAChE,kBAAkB,CAACQ,OAAO,CAAC2B,MAAM;UAAC;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1F,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,gBAEN3E,OAAA;QAAGsE,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IACpD;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEN3E,OAAA;MAAKsE,SAAS,EAAC,UAAU;MAAAC,QAAA,gBACvBvE,OAAA;QACEsE,SAAS,EAAE,kBAAkB9D,WAAW,IAAI,CAACE,mBAAmB,GAAG,WAAW,GAAG,EAAE,EAAG;QACtFsE,OAAO,EAAExE,WAAW,GAAG6C,iBAAiB,GAAGxB,cAAe;QAC1DoD,QAAQ,EAAE,CAAC9D,MAAM,CAACD,iBAAiB,IAAI,CAACC,MAAM,CAACC,uBAAwB;QACvE8D,KAAK,EAAC,yEAAyE;QAAAX,QAAA,GAE9E/D,WAAW,IAAI,CAACE,mBAAmB,GAAG,MAAM,GAAG,OAAO,EAAC,uBAC1D;MAAA;QAAA8D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAET3E,OAAA;QACEsE,SAAS,EAAE,4BAA4B5D,mBAAmB,GAAG,WAAW,GAAG,EAAE,EAAG;QAChFsE,OAAO,EAAEtE,mBAAmB,GAAG2C,iBAAiB,GAAGR,oBAAqB;QACxEoC,QAAQ,EAAE,CAAC9D,MAAM,CAACD,iBAAiB,IAAI,CAACC,MAAM,CAACC,uBAAwB;QACvE8D,KAAK,EAAC,qFAAqF;QAAAX,QAAA,GAE1F7D,mBAAmB,GAAG,MAAM,GAAG,OAAO,EAAC,0BAC1C;MAAA;QAAA8D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EAERnE,WAAW,iBACVR,OAAA;QAAKsE,SAAS,EAAC,QAAQ;QAAAC,QAAA,gBACrBvE,OAAA;UAAMsE,SAAS,EAAC;QAAqB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EAC5CjE,mBAAmB,GAAG,wBAAwB,GAAG,4BAA4B;MAAA;QAAA8D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACxE,EAAA,CAlTQF,qBAAqB;AAAAkF,EAAA,GAArBlF,qBAAqB;AAoT9B,eAAeA,qBAAqB;AAAC,IAAAkF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}