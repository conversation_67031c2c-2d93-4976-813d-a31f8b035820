.automatic-diarization {
  max-width: 900px;
  margin: 0 auto;
  padding: 24px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.page-header {
  margin-bottom: 24px;
  text-align: center;
}

.page-header h1 {
  margin: 0 0 8px;
  color: #333;
  font-size: 28px;
}

.description {
  color: #666;
  font-size: 16px;
  margin: 0;
}

/* Controls */
.transcription-controls {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.control-button {
  padding: 10px 16px;
  border: none;
  border-radius: 4px;
  background-color: #4285f4;
  color: white;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.control-button:hover {
  background-color: #3367d6;
}

.control-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.control-button.recording {
  background-color: #ea4335;
  animation: pulse-bg 1.5s infinite;
}

.control-button.tab-audio {
  background-color: #34a853;
}

.control-button.tab-audio:hover {
  background-color: #2d9249;
}

@keyframes pulse-bg {
  0% { background-color: #ea4335; }
  50% { background-color: #c0392b; }
  100% { background-color: #ea4335; }
}

/* Status indicator */
.status-indicator {
  margin-bottom: 16px;
  min-height: 24px;
}

.recording-status {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #555;
}

.status-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 8px;
  animation: pulse 1.5s infinite;
}

.status-dot.interviewer {
  background-color: #2196f3;
}

.status-dot.candidate {
  background-color: #8bc34a;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

/* Live captions */
.live-captions {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 20px;
  background-color: #f9f9f9;
  position: relative;
  min-height: 30px;
}

.live-captions.interviewer {
  border-left: 4px solid #2196f3;
}

.live-captions.candidate {
  border-left: 4px solid #8bc34a;
}

.speaker-label {
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 4px;
  color: #555;
}

.live-captions.interviewer .speaker-label {
  color: #1976d2;
}

.live-captions.candidate .speaker-label {
  color: #689f38;
}

.caption-text {
  font-size: 16px;
  line-height: 1.5;
}

/* Transcript */
.transcript-container {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.transcript-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
}

.transcript-header h2 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.download-button {
  padding: 8px 12px;
  background-color: #4285f4;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 13px;
  cursor: pointer;
}

.download-button:hover {
  background-color: #3367d6;
}

.transcript-content {
  padding: 16px;
  max-height: 400px;
  overflow-y: auto;
}

.empty-state {
  color: #999;
  text-align: center;
  font-style: italic;
  padding: 20px 0;
}

.transcript-entries {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.transcript-entry {
  padding: 12px;
  border-radius: 8px;
  background-color: #f9f9f9;
  border-left: 3px solid #ddd;
}

.transcript-entry.interviewer {
  border-left-color: #2196f3;
  background-color: #e3f2fd;
}

.transcript-entry.candidate {
  border-left-color: #8bc34a;
  background-color: #f1f8e9;
}

.entry-header {
  display: flex;
  align-items: center;
  margin-bottom: 6px;
}

.timestamp {
  font-size: 12px;
  color: #777;
  margin-right: 8px;
}

.speaker {
  font-weight: 600;
  font-size: 14px;
  color: #555;
}

.transcript-entry.interviewer .speaker {
  color: #1976d2;
}

.transcript-entry.candidate .speaker {
  color: #689f38;
}

.entry-text {
  font-size: 15px;
  line-height: 1.5;
  color: #333;
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .automatic-diarization {
    padding: 16px;
  }
  
  .transcription-controls {
    flex-direction: column;
  }
  
  .transcript-content {
    max-height: 300px;
  }
}
