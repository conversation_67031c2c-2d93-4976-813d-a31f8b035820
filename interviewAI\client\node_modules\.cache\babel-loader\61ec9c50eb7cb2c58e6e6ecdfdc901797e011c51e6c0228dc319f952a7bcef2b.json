{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\interview-ai-app\\\\interviewAI\\\\client\\\\src\\\\components\\\\DeepgramLiveTranscript.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState, useCallback } from 'react';\nimport MicRecorder from 'mic-recorder-to-mp3';\nimport './DeepgramLiveTranscript.css';\nimport env from '../utils/env';\nimport CloseIcon from '@mui/icons-material/Close';\nimport MicIcon from '@mui/icons-material/Mic';\nimport StopIcon from '@mui/icons-material/Stop';\nimport SendIcon from '@mui/icons-material/Send';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport AccessTimeIcon from '@mui/icons-material/AccessTime';\nimport Dialog from '@mui/material/Dialog';\nimport DialogActions from '@mui/material/DialogActions';\nimport DialogContent from '@mui/material/DialogContent';\nimport DialogContentText from '@mui/material/DialogContentText';\nimport DialogTitle from '@mui/material/DialogTitle';\nimport Button from '@mui/material/Button';\nimport CircularProgress from '@mui/material/CircularProgress';\nimport Alert from '@mui/material/Alert';\nimport Snackbar from '@mui/material/Snackbar';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DeepgramLiveTranscript = ({\n  onBack\n}) => {\n  _s();\n  // State for transcription\n  const [transcripts, setTranscripts] = useState([]);\n  const [isRecording, setIsRecording] = useState(false);\n  const [apiKeyStatus, setApiKeyStatus] = useState('unknown'); // 'unknown', 'valid', 'invalid'\n  const [apiKeyError, setApiKeyError] = useState('');\n  const [showApiKeyError, setShowApiKeyError] = useState(false);\n\n  // State for GPT integration\n  const [gptResponse, setGptResponse] = useState('');\n  const [isLoadingGpt, setIsLoadingGpt] = useState(false);\n  const [selectedText, setSelectedText] = useState('');\n\n  // Session timer state (15 minutes = 900 seconds)\n  const SESSION_DURATION = 900; // 15 minutes in seconds\n  const WARNING_TIME = 60; // Show warning when 60 seconds remain\n  const [sessionTimeRemaining, setSessionTimeRemaining] = useState(SESSION_DURATION);\n  const [showPaymentDialog, setShowPaymentDialog] = useState(false);\n  const [sessionExpired, setSessionExpired] = useState(false);\n\n  // Refs\n  const wsRef = useRef(null);\n  const recorder = useRef(new MicRecorder({\n    bitRate: 128\n  }));\n  const intervalRef = useRef(null);\n  const sessionTimerRef = useRef(null);\n  const transcriptContainerRef = useRef(null);\n\n  // Format session time remaining (MM:SS)\n  const formatSessionTime = useCallback(totalSeconds => {\n    const minutes = Math.floor(totalSeconds / 60);\n    const seconds = totalSeconds % 60;\n    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\n  }, []);\n\n  // Start the session timer\n  const startSessionTimer = useCallback(() => {\n    // Reset session timer when starting\n    setSessionTimeRemaining(SESSION_DURATION);\n    setSessionExpired(false);\n\n    // Clear any existing interval\n    if (sessionTimerRef.current) {\n      clearInterval(sessionTimerRef.current);\n    }\n\n    // Start a new interval that counts down\n    sessionTimerRef.current = setInterval(() => {\n      setSessionTimeRemaining(prev => {\n        const newTime = prev - 1;\n\n        // Show payment dialog when 1 minute remains\n        if (newTime === WARNING_TIME) {\n          setShowPaymentDialog(true);\n        }\n\n        // Session expired\n        if (newTime <= 0) {\n          clearInterval(sessionTimerRef.current);\n          sessionTimerRef.current = null;\n          setSessionExpired(true);\n          stopRecording();\n          return 0;\n        }\n        return newTime;\n      });\n    }, 1000);\n  }, [SESSION_DURATION, WARNING_TIME]);\n\n  // Handle payment dialog close\n  const handlePaymentDialogClose = useCallback(() => {\n    setShowPaymentDialog(false);\n  }, []);\n\n  // Handle payment confirmation\n  const handlePaymentConfirm = useCallback(() => {\n    // Reset the session timer\n    setShowPaymentDialog(false);\n    setSessionTimeRemaining(SESSION_DURATION);\n    setSessionExpired(false);\n\n    // Restart the session timer\n    if (sessionTimerRef.current) {\n      clearInterval(sessionTimerRef.current);\n    }\n    sessionTimerRef.current = setInterval(() => {\n      setSessionTimeRemaining(prev => {\n        const newTime = prev - 1;\n\n        // Show payment dialog when 1 minute remains\n        if (newTime === WARNING_TIME) {\n          setShowPaymentDialog(true);\n        }\n\n        // Session expired\n        if (newTime <= 0) {\n          clearInterval(sessionTimerRef.current);\n          sessionTimerRef.current = null;\n          setSessionExpired(true);\n          stopRecording();\n          return 0;\n        }\n        return newTime;\n      });\n    }, 1000);\n  }, [SESSION_DURATION, WARNING_TIME]);\n\n  // Start session timer on component mount\n  useEffect(() => {\n    startSessionTimer();\n\n    // Clean up session timer on unmount\n    return () => {\n      if (sessionTimerRef.current) {\n        clearInterval(sessionTimerRef.current);\n      }\n    };\n  }, [startSessionTimer]);\n\n  // Verify Deepgram API key\n  const verifyDeepgramApiKey = useCallback(async () => {\n    try {\n      // Get API key from environment variables\n      const DEEPGRAM_API_KEY = env.DEEPGRAM_API_KEY;\n      if (!DEEPGRAM_API_KEY) {\n        setApiKeyStatus('invalid');\n        setApiKeyError('Deepgram API key is missing. Please add it to your environment variables as REACT_APP_DEEPGRAM_API_KEY.');\n        setShowApiKeyError(true);\n        return false;\n      }\n\n      // Test the API key with a simple request\n      const response = await fetch('https://api.deepgram.com/v1/projects', {\n        method: 'GET',\n        headers: {\n          'Authorization': `Token ${DEEPGRAM_API_KEY}`\n        }\n      });\n      if (response.ok) {\n        setApiKeyStatus('valid');\n        return true;\n      } else {\n        setApiKeyStatus('invalid');\n        setApiKeyError('Invalid Deepgram API key. Please check your API key and try again.');\n        setShowApiKeyError(true);\n        return false;\n      }\n    } catch (error) {\n      console.error('Error verifying Deepgram API key:', error);\n      setApiKeyStatus('invalid');\n      setApiKeyError('Error verifying Deepgram API key: ' + error.message);\n      setShowApiKeyError(true);\n      return false;\n    }\n  }, []);\n\n  // Start recording\n  const startRecording = useCallback(async () => {\n    if (sessionExpired) return;\n\n    // Verify API key first\n    const isApiKeyValid = await verifyDeepgramApiKey();\n    if (!isApiKeyValid) return;\n    try {\n      const DEEPGRAM_API_KEY = env.DEEPGRAM_API_KEY;\n\n      // Create WebSocket connection\n      const socket = new WebSocket(`wss://api.deepgram.com/v1/listen?punctuate=true&diarize=true`);\n\n      // Set up authorization header\n      socket.onopen = async () => {\n        console.log('WebSocket connected');\n\n        // Send authorization header\n        socket.send(JSON.stringify({\n          authorization: DEEPGRAM_API_KEY\n        }));\n\n        // Start recording\n        await recorder.current.start();\n        setIsRecording(true);\n\n        // Send audio data to Deepgram\n        intervalRef.current = setInterval(async () => {\n          try {\n            const [buffer, blob] = await recorder.current.getMp3();\n            const reader = new FileReader();\n            reader.readAsArrayBuffer(blob);\n            reader.onloadend = () => {\n              if (socket.readyState === WebSocket.OPEN) {\n                socket.send(reader.result);\n              }\n            };\n          } catch (error) {\n            console.error('Error getting MP3 data:', error);\n          }\n        }, 1000);\n        wsRef.current = socket;\n      };\n\n      // Handle incoming messages\n      socket.onmessage = message => {\n        try {\n          var _data$channel, _data$channel$alterna;\n          const data = JSON.parse(message.data);\n          const words = ((_data$channel = data.channel) === null || _data$channel === void 0 ? void 0 : (_data$channel$alterna = _data$channel.alternatives[0]) === null || _data$channel$alterna === void 0 ? void 0 : _data$channel$alterna.words) || [];\n          if (words.length === 0) return;\n          const groupedBySpeaker = words.reduce((acc, word) => {\n            if (!word.speaker) return acc;\n            const lastGroup = acc[acc.length - 1];\n            if (lastGroup && lastGroup.speaker === word.speaker) {\n              lastGroup.words.push(word.word);\n            } else {\n              acc.push({\n                speaker: word.speaker,\n                words: [word.word],\n                timestamp: new Date().toISOString()\n              });\n            }\n            return acc;\n          }, []);\n          if (groupedBySpeaker.length > 0) {\n            setTranscripts(prev => {\n              // Merge with existing transcripts if the speaker is the same\n              const newTranscripts = [...prev];\n              groupedBySpeaker.forEach(group => {\n                const lastGroup = newTranscripts[newTranscripts.length - 1];\n                if (lastGroup && lastGroup.speaker === group.speaker) {\n                  // Merge with the last group\n                  lastGroup.words = [...lastGroup.words, ...group.words];\n                } else {\n                  // Add as a new group\n                  newTranscripts.push(group);\n                }\n              });\n              return newTranscripts;\n            });\n\n            // Auto-scroll to the bottom\n            if (transcriptContainerRef.current) {\n              transcriptContainerRef.current.scrollTop = transcriptContainerRef.current.scrollHeight;\n            }\n          }\n        } catch (error) {\n          console.error('Error parsing message:', error);\n        }\n      };\n\n      // Handle errors\n      socket.onerror = error => {\n        console.error('WebSocket error:', error);\n        setApiKeyError('WebSocket error: ' + error.message);\n        setShowApiKeyError(true);\n      };\n\n      // Handle connection close\n      socket.onclose = () => {\n        console.log('WebSocket closed');\n        stopRecording();\n      };\n    } catch (error) {\n      console.error('Error starting recording:', error);\n      setApiKeyError('Error starting recording: ' + error.message);\n      setShowApiKeyError(true);\n    }\n  }, [sessionExpired, verifyDeepgramApiKey]);\n\n  // Stop recording\n  const stopRecording = useCallback(() => {\n    setIsRecording(false);\n\n    // Stop the recorder\n    if (recorder.current) {\n      recorder.current.stop();\n    }\n\n    // Clear the interval\n    if (intervalRef.current) {\n      clearInterval(intervalRef.current);\n      intervalRef.current = null;\n    }\n\n    // Close the WebSocket\n    if (wsRef.current) {\n      wsRef.current.close();\n      wsRef.current = null;\n    }\n  }, []);\n\n  // Clean up on unmount\n  useEffect(() => {\n    return () => {\n      stopRecording();\n      if (sessionTimerRef.current) {\n        clearInterval(sessionTimerRef.current);\n      }\n    };\n  }, [stopRecording]);\n\n  // Handle text selection for GPT\n  const handleTextSelection = useCallback(() => {\n    const selection = window.getSelection();\n    const selectedText = selection.toString().trim();\n    if (selectedText) {\n      setSelectedText(selectedText);\n    }\n  }, []);\n\n  // Send selected text to GPT\n  const sendToGPT = useCallback(async () => {\n    if (!selectedText || isLoadingGpt || sessionExpired) return;\n    const apiKey = env.OPENAI_API_KEY;\n    if (!apiKey) {\n      setApiKeyError('OpenAI API key is missing. Please add it to your environment variables as REACT_APP_OPENAI_API_KEY.');\n      setShowApiKeyError(true);\n      return;\n    }\n    setIsLoadingGpt(true);\n    try {\n      const response = await fetch(\"https://api.openai.com/v1/chat/completions\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          \"Authorization\": `Bearer ${apiKey}`\n        },\n        body: JSON.stringify({\n          model: \"gpt-4.1-nano\",\n          messages: [{\n            role: \"system\",\n            content: \"You are an AI assistant helping with interview transcripts. Provide concise, helpful responses.\"\n          }, {\n            role: \"user\",\n            content: `Please analyze this interview transcript and provide insights or suggestions: \"${selectedText}\"`\n          }],\n          stream: true\n        })\n      });\n      if (!response.ok) {\n        throw new Error(`API error: ${response.status} ${response.statusText}`);\n      }\n      const reader = response.body.getReader();\n      const decoder = new TextDecoder(\"utf-8\");\n      let result = \"\";\n      while (true) {\n        const {\n          value,\n          done\n        } = await reader.read();\n        if (done) break;\n        const chunk = decoder.decode(value, {\n          stream: true\n        });\n        const lines = chunk.split(\"\\n\").filter(line => line.trim().startsWith(\"data:\"));\n        for (const line of lines) {\n          const data = line.replace(/^data: /, '');\n          if (data === \"[DONE]\") break;\n          try {\n            var _json$choices, _json$choices$, _json$choices$$delta;\n            const json = JSON.parse(data);\n            const content = (_json$choices = json.choices) === null || _json$choices === void 0 ? void 0 : (_json$choices$ = _json$choices[0]) === null || _json$choices$ === void 0 ? void 0 : (_json$choices$$delta = _json$choices$.delta) === null || _json$choices$$delta === void 0 ? void 0 : _json$choices$$delta.content;\n            if (content) {\n              result += content;\n              setGptResponse(result);\n            }\n          } catch (e) {\n            console.error(\"Error parsing JSON:\", e);\n          }\n        }\n      }\n    } catch (error) {\n      console.error(\"GPT API Error:\", error);\n      setGptResponse(\"Error: \" + error.message);\n    } finally {\n      setIsLoadingGpt(false);\n    }\n  }, [selectedText, isLoadingGpt, sessionExpired]);\n\n  // Clear transcripts\n  const clearTranscripts = useCallback(() => {\n    setTranscripts([]);\n    setGptResponse('');\n    setSelectedText('');\n  }, []);\n\n  // Clear GPT response\n  const clearGptResponse = useCallback(() => {\n    setGptResponse('');\n    setSelectedText('');\n  }, []);\n\n  // Handle exit button click\n  const handleExit = useCallback(() => {\n    stopRecording();\n    if (onBack) onBack();\n  }, [stopRecording, onBack]);\n\n  // Handle API key error close\n  const handleApiKeyErrorClose = useCallback(() => {\n    setShowApiKeyError(false);\n  }, []);\n\n  // Get full transcript text\n  const getFullTranscript = useCallback(() => {\n    return transcripts.map(segment => `Speaker ${segment.speaker}: ${segment.words.join(' ')}`).join('\\n');\n  }, [transcripts]);\n\n  // Download transcript\n  const downloadTranscript = useCallback(() => {\n    const fullText = getFullTranscript();\n    if (!fullText) return;\n    const blob = new Blob([fullText], {\n      type: 'text/plain'\n    });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `transcript-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  }, [getFullTranscript]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"deepgram-transcript-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"exit-button\",\n      onClick: handleExit,\n      title: \"Exit\",\n      children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 471,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 470,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"timer-container\",\n      children: [/*#__PURE__*/_jsxDEV(AccessTimeIcon, {\n        className: \"timer-icon\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 476,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"timer-display session-timer\",\n        children: [\"Session: \", formatSessionTime(sessionTimeRemaining)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 477,\n        columnNumber: 9\n      }, this), isRecording && /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"listening-indicator\",\n        children: \"Recording...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 480,\n        columnNumber: 25\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 475,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"deepgram-transcript-title\",\n      children: \"Live Transcript with Speaker Diarization\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 483,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"deepgram-controls\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: `record-button ${isRecording ? 'recording' : ''}`,\n        onClick: isRecording ? stopRecording : startRecording,\n        disabled: sessionExpired || apiKeyStatus === 'invalid',\n        title: isRecording ? \"Stop Recording\" : \"Start Recording\",\n        children: [isRecording ? /*#__PURE__*/_jsxDEV(StopIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 492,\n          columnNumber: 26\n        }, this) : /*#__PURE__*/_jsxDEV(MicIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 492,\n          columnNumber: 41\n        }, this), isRecording ? \"Stop Recording\" : \"Start Recording\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 486,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"clear-button\",\n        onClick: clearTranscripts,\n        disabled: transcripts.length === 0 || sessionExpired,\n        title: \"Clear Transcripts\",\n        children: [/*#__PURE__*/_jsxDEV(DeleteIcon, {\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 502,\n          columnNumber: 11\n        }, this), \"Clear\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 496,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"download-button\",\n        onClick: downloadTranscript,\n        disabled: transcripts.length === 0 || sessionExpired,\n        title: \"Download Transcript\",\n        children: \"Download\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 506,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 485,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"deepgram-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"transcript-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"transcript-container\",\n          ref: transcriptContainerRef,\n          onMouseUp: handleTextSelection,\n          children: transcripts.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"empty-transcript\",\n            children: apiKeyStatus === 'invalid' ? /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"API key validation failed. Please check your Deepgram API key.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 526,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"No transcripts yet. Click \\\"Start Recording\\\" to begin.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 528,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 524,\n            columnNumber: 15\n          }, this) : transcripts.map((segment, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"deepgram-segment\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"deepgram-speaker\",\n              children: [\"Speaker \", segment.speaker, \":\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 534,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: segment.words.join(' ')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 535,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 533,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 518,\n          columnNumber: 11\n        }, this), selectedText && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"selected-text-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"selected-text-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Selected Text\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 544,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"send-to-gpt-button\",\n              onClick: sendToGPT,\n              disabled: !selectedText || isLoadingGpt || sessionExpired,\n              title: \"Analyze with GPT\",\n              children: [isLoadingGpt ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 551,\n                columnNumber: 35\n              }, this) : /*#__PURE__*/_jsxDEV(SendIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 551,\n                columnNumber: 68\n              }, this), \"Analyze with GPT\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 545,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 543,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"selected-text\",\n            children: selectedText\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 555,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 542,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 517,\n        columnNumber: 9\n      }, this), (gptResponse || isLoadingGpt) && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"gpt-response-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"gpt-response-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"AI Analysis\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 563,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"clear-button\",\n            onClick: clearGptResponse,\n            disabled: !gptResponse && !isLoadingGpt,\n            title: \"Clear Analysis\",\n            children: [/*#__PURE__*/_jsxDEV(DeleteIcon, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 570,\n              columnNumber: 17\n            }, this), \"Clear\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 564,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 562,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"gpt-response\",\n          children: isLoadingGpt && !gptResponse ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"loading-container\",\n            children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 30\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 577,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Analyzing transcript...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 578,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 576,\n            columnNumber: 17\n          }, this) : gptResponse\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 574,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 561,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 516,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showPaymentDialog,\n      onClose: handlePaymentDialogClose,\n      \"aria-labelledby\": \"payment-dialog-title\",\n      \"aria-describedby\": \"payment-dialog-description\",\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        id: \"payment-dialog-title\",\n        children: \"Session Expiring Soon\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 595,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(DialogContentText, {\n          id: \"payment-dialog-description\",\n          children: \"Your session will expire in one minute. Would you like to make a payment to extend your session?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 599,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 598,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handlePaymentDialogClose,\n          color: \"primary\",\n          children: \"Not Now\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 604,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handlePaymentConfirm,\n          color: \"primary\",\n          autoFocus: true,\n          children: \"Make Payment\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 607,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 603,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 589,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: sessionExpired,\n      \"aria-labelledby\": \"expired-dialog-title\",\n      \"aria-describedby\": \"expired-dialog-description\",\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        id: \"expired-dialog-title\",\n        children: \"Session Expired\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 619,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(DialogContentText, {\n          id: \"expired-dialog-description\",\n          children: \"Your session has expired. Please make a payment to continue using the service.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 623,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 622,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleExit,\n          color: \"primary\",\n          children: \"Exit\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 628,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handlePaymentConfirm,\n          color: \"primary\",\n          autoFocus: true,\n          children: \"Make Payment\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 631,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 627,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 614,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: showApiKeyError,\n      autoHideDuration: 6000,\n      onClose: handleApiKeyErrorClose,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleApiKeyErrorClose,\n        severity: \"error\",\n        sx: {\n          width: '100%'\n        },\n        children: apiKeyError\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 644,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 638,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 468,\n    columnNumber: 5\n  }, this);\n};\n_s(DeepgramLiveTranscript, \"J3C3JPwCYb+GH++E81QUH7Wjk9I=\");\n_c = DeepgramLiveTranscript;\nexport default DeepgramLiveTranscript;\nvar _c;\n$RefreshReg$(_c, \"DeepgramLiveTranscript\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "useCallback", "MicRecorder", "env", "CloseIcon", "MicIcon", "StopIcon", "SendIcon", "DeleteIcon", "AccessTimeIcon", "Dialog", "DialogActions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogContentText", "DialogTitle", "<PERSON><PERSON>", "CircularProgress", "<PERSON><PERSON>", "Snackbar", "jsxDEV", "_jsxDEV", "DeepgramLiveTranscript", "onBack", "_s", "transcripts", "setTranscripts", "isRecording", "setIsRecording", "apiKeyStatus", "setApiKeyStatus", "apiKeyError", "setApi<PERSON>eyError", "showApiKeyError", "setShowApiKeyError", "gptResponse", "setGptResponse", "isLoadingGpt", "setIsLoadingGpt", "selectedText", "setSelectedText", "SESSION_DURATION", "WARNING_TIME", "sessionTimeRemaining", "setSessionTimeRemaining", "showPaymentDialog", "setShowPaymentDialog", "sessionExpired", "setSessionExpired", "wsRef", "recorder", "bitRate", "intervalRef", "sessionTimerRef", "transcriptContainerRef", "formatSessionTime", "totalSeconds", "minutes", "Math", "floor", "seconds", "toString", "padStart", "startSessionTimer", "current", "clearInterval", "setInterval", "prev", "newTime", "stopRecording", "handlePaymentDialogClose", "handlePaymentConfirm", "verifyDeepgramApiKey", "DEEPGRAM_API_KEY", "response", "fetch", "method", "headers", "ok", "error", "console", "message", "startRecording", "isApiKey<PERSON>", "socket", "WebSocket", "onopen", "log", "send", "JSON", "stringify", "authorization", "start", "buffer", "blob", "getMp3", "reader", "FileReader", "readAsA<PERSON>y<PERSON><PERSON>er", "onloadend", "readyState", "OPEN", "result", "onmessage", "_data$channel", "_data$channel$alterna", "data", "parse", "words", "channel", "alternatives", "length", "groupedBySpeaker", "reduce", "acc", "word", "speaker", "lastGroup", "push", "timestamp", "Date", "toISOString", "newTranscripts", "for<PERSON>ach", "group", "scrollTop", "scrollHeight", "onerror", "onclose", "stop", "close", "handleTextSelection", "selection", "window", "getSelection", "trim", "sendToGPT", "<PERSON><PERSON><PERSON><PERSON>", "OPENAI_API_KEY", "body", "model", "messages", "role", "content", "stream", "Error", "status", "statusText", "<PERSON><PERSON><PERSON><PERSON>", "decoder", "TextDecoder", "value", "done", "read", "chunk", "decode", "lines", "split", "filter", "line", "startsWith", "replace", "_json$choices", "_json$choices$", "_json$choices$$delta", "json", "choices", "delta", "e", "clearTranscripts", "clearGptResponse", "handleExit", "handleApiKeyErrorClose", "getFullTranscript", "map", "segment", "join", "downloadTranscript", "fullText", "Blob", "type", "url", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "slice", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "className", "children", "onClick", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "disabled", "fontSize", "ref", "onMouseUp", "index", "size", "open", "onClose", "id", "color", "autoFocus", "autoHideDuration", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "severity", "sx", "width", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/interview-ai-app/interviewAI/client/src/components/DeepgramLiveTranscript.jsx"], "sourcesContent": ["import React, { useEffect, useRef, useState, useCallback } from 'react';\nimport MicRecorder from 'mic-recorder-to-mp3';\nimport './DeepgramLiveTranscript.css';\nimport env from '../utils/env';\nimport CloseIcon from '@mui/icons-material/Close';\nimport MicIcon from '@mui/icons-material/Mic';\nimport StopIcon from '@mui/icons-material/Stop';\nimport SendIcon from '@mui/icons-material/Send';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport AccessTimeIcon from '@mui/icons-material/AccessTime';\nimport Dialog from '@mui/material/Dialog';\nimport DialogActions from '@mui/material/DialogActions';\nimport DialogContent from '@mui/material/DialogContent';\nimport DialogContentText from '@mui/material/DialogContentText';\nimport DialogTitle from '@mui/material/DialogTitle';\nimport Button from '@mui/material/Button';\nimport CircularProgress from '@mui/material/CircularProgress';\nimport Alert from '@mui/material/Alert';\nimport Snackbar from '@mui/material/Snackbar';\n\nconst DeepgramLiveTranscript = ({ onBack }) => {\n  // State for transcription\n  const [transcripts, setTranscripts] = useState([]);\n  const [isRecording, setIsRecording] = useState(false);\n  const [apiKeyStatus, setApiKeyStatus] = useState('unknown'); // 'unknown', 'valid', 'invalid'\n  const [apiKeyError, setApiKeyError] = useState('');\n  const [showApiKeyError, setShowApiKeyError] = useState(false);\n\n  // State for GPT integration\n  const [gptResponse, setGptResponse] = useState('');\n  const [isLoadingGpt, setIsLoadingGpt] = useState(false);\n  const [selectedText, setSelectedText] = useState('');\n\n  // Session timer state (15 minutes = 900 seconds)\n  const SESSION_DURATION = 900; // 15 minutes in seconds\n  const WARNING_TIME = 60; // Show warning when 60 seconds remain\n  const [sessionTimeRemaining, setSessionTimeRemaining] = useState(SESSION_DURATION);\n  const [showPaymentDialog, setShowPaymentDialog] = useState(false);\n  const [sessionExpired, setSessionExpired] = useState(false);\n\n  // Refs\n  const wsRef = useRef(null);\n  const recorder = useRef(new MicRecorder({ bitRate: 128 }));\n  const intervalRef = useRef(null);\n  const sessionTimerRef = useRef(null);\n  const transcriptContainerRef = useRef(null);\n\n  // Format session time remaining (MM:SS)\n  const formatSessionTime = useCallback((totalSeconds) => {\n    const minutes = Math.floor(totalSeconds / 60);\n    const seconds = totalSeconds % 60;\n    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\n  }, []);\n\n  // Start the session timer\n  const startSessionTimer = useCallback(() => {\n    // Reset session timer when starting\n    setSessionTimeRemaining(SESSION_DURATION);\n    setSessionExpired(false);\n\n    // Clear any existing interval\n    if (sessionTimerRef.current) {\n      clearInterval(sessionTimerRef.current);\n    }\n\n    // Start a new interval that counts down\n    sessionTimerRef.current = setInterval(() => {\n      setSessionTimeRemaining(prev => {\n        const newTime = prev - 1;\n\n        // Show payment dialog when 1 minute remains\n        if (newTime === WARNING_TIME) {\n          setShowPaymentDialog(true);\n        }\n\n        // Session expired\n        if (newTime <= 0) {\n          clearInterval(sessionTimerRef.current);\n          sessionTimerRef.current = null;\n          setSessionExpired(true);\n          stopRecording();\n          return 0;\n        }\n\n        return newTime;\n      });\n    }, 1000);\n  }, [SESSION_DURATION, WARNING_TIME]);\n\n  // Handle payment dialog close\n  const handlePaymentDialogClose = useCallback(() => {\n    setShowPaymentDialog(false);\n  }, []);\n\n  // Handle payment confirmation\n  const handlePaymentConfirm = useCallback(() => {\n    // Reset the session timer\n    setShowPaymentDialog(false);\n    setSessionTimeRemaining(SESSION_DURATION);\n    setSessionExpired(false);\n\n    // Restart the session timer\n    if (sessionTimerRef.current) {\n      clearInterval(sessionTimerRef.current);\n    }\n\n    sessionTimerRef.current = setInterval(() => {\n      setSessionTimeRemaining(prev => {\n        const newTime = prev - 1;\n\n        // Show payment dialog when 1 minute remains\n        if (newTime === WARNING_TIME) {\n          setShowPaymentDialog(true);\n        }\n\n        // Session expired\n        if (newTime <= 0) {\n          clearInterval(sessionTimerRef.current);\n          sessionTimerRef.current = null;\n          setSessionExpired(true);\n          stopRecording();\n          return 0;\n        }\n\n        return newTime;\n      });\n    }, 1000);\n  }, [SESSION_DURATION, WARNING_TIME]);\n\n  // Start session timer on component mount\n  useEffect(() => {\n    startSessionTimer();\n\n    // Clean up session timer on unmount\n    return () => {\n      if (sessionTimerRef.current) {\n        clearInterval(sessionTimerRef.current);\n      }\n    };\n  }, [startSessionTimer]);\n\n  // Verify Deepgram API key\n  const verifyDeepgramApiKey = useCallback(async () => {\n    try {\n      // Get API key from environment variables\n      const DEEPGRAM_API_KEY = env.DEEPGRAM_API_KEY;\n\n      if (!DEEPGRAM_API_KEY) {\n        setApiKeyStatus('invalid');\n        setApiKeyError('Deepgram API key is missing. Please add it to your environment variables as REACT_APP_DEEPGRAM_API_KEY.');\n        setShowApiKeyError(true);\n        return false;\n      }\n\n      // Test the API key with a simple request\n      const response = await fetch('https://api.deepgram.com/v1/projects', {\n        method: 'GET',\n        headers: {\n          'Authorization': `Token ${DEEPGRAM_API_KEY}`\n        }\n      });\n\n      if (response.ok) {\n        setApiKeyStatus('valid');\n        return true;\n      } else {\n        setApiKeyStatus('invalid');\n        setApiKeyError('Invalid Deepgram API key. Please check your API key and try again.');\n        setShowApiKeyError(true);\n        return false;\n      }\n    } catch (error) {\n      console.error('Error verifying Deepgram API key:', error);\n      setApiKeyStatus('invalid');\n      setApiKeyError('Error verifying Deepgram API key: ' + error.message);\n      setShowApiKeyError(true);\n      return false;\n    }\n  }, []);\n\n  // Start recording\n  const startRecording = useCallback(async () => {\n    if (sessionExpired) return;\n\n    // Verify API key first\n    const isApiKeyValid = await verifyDeepgramApiKey();\n    if (!isApiKeyValid) return;\n\n    try {\n      const DEEPGRAM_API_KEY = env.DEEPGRAM_API_KEY;\n\n      // Create WebSocket connection\n      const socket = new WebSocket(`wss://api.deepgram.com/v1/listen?punctuate=true&diarize=true`);\n\n      // Set up authorization header\n      socket.onopen = async () => {\n        console.log('WebSocket connected');\n\n        // Send authorization header\n        socket.send(JSON.stringify({\n          authorization: DEEPGRAM_API_KEY\n        }));\n\n        // Start recording\n        await recorder.current.start();\n        setIsRecording(true);\n\n        // Send audio data to Deepgram\n        intervalRef.current = setInterval(async () => {\n          try {\n            const [buffer, blob] = await recorder.current.getMp3();\n            const reader = new FileReader();\n            reader.readAsArrayBuffer(blob);\n            reader.onloadend = () => {\n              if (socket.readyState === WebSocket.OPEN) {\n                socket.send(reader.result);\n              }\n            };\n          } catch (error) {\n            console.error('Error getting MP3 data:', error);\n          }\n        }, 1000);\n\n        wsRef.current = socket;\n      };\n\n      // Handle incoming messages\n      socket.onmessage = (message) => {\n        try {\n          const data = JSON.parse(message.data);\n          const words = data.channel?.alternatives[0]?.words || [];\n\n          if (words.length === 0) return;\n\n          const groupedBySpeaker = words.reduce((acc, word) => {\n            if (!word.speaker) return acc;\n            const lastGroup = acc[acc.length - 1];\n\n            if (lastGroup && lastGroup.speaker === word.speaker) {\n              lastGroup.words.push(word.word);\n            } else {\n              acc.push({\n                speaker: word.speaker,\n                words: [word.word],\n                timestamp: new Date().toISOString()\n              });\n            }\n\n            return acc;\n          }, []);\n\n          if (groupedBySpeaker.length > 0) {\n            setTranscripts(prev => {\n              // Merge with existing transcripts if the speaker is the same\n              const newTranscripts = [...prev];\n\n              groupedBySpeaker.forEach(group => {\n                const lastGroup = newTranscripts[newTranscripts.length - 1];\n\n                if (lastGroup && lastGroup.speaker === group.speaker) {\n                  // Merge with the last group\n                  lastGroup.words = [...lastGroup.words, ...group.words];\n                } else {\n                  // Add as a new group\n                  newTranscripts.push(group);\n                }\n              });\n\n              return newTranscripts;\n            });\n\n            // Auto-scroll to the bottom\n            if (transcriptContainerRef.current) {\n              transcriptContainerRef.current.scrollTop = transcriptContainerRef.current.scrollHeight;\n            }\n          }\n        } catch (error) {\n          console.error('Error parsing message:', error);\n        }\n      };\n\n      // Handle errors\n      socket.onerror = (error) => {\n        console.error('WebSocket error:', error);\n        setApiKeyError('WebSocket error: ' + error.message);\n        setShowApiKeyError(true);\n      };\n\n      // Handle connection close\n      socket.onclose = () => {\n        console.log('WebSocket closed');\n        stopRecording();\n      };\n    } catch (error) {\n      console.error('Error starting recording:', error);\n      setApiKeyError('Error starting recording: ' + error.message);\n      setShowApiKeyError(true);\n    }\n  }, [sessionExpired, verifyDeepgramApiKey]);\n\n  // Stop recording\n  const stopRecording = useCallback(() => {\n    setIsRecording(false);\n\n    // Stop the recorder\n    if (recorder.current) {\n      recorder.current.stop();\n    }\n\n    // Clear the interval\n    if (intervalRef.current) {\n      clearInterval(intervalRef.current);\n      intervalRef.current = null;\n    }\n\n    // Close the WebSocket\n    if (wsRef.current) {\n      wsRef.current.close();\n      wsRef.current = null;\n    }\n  }, []);\n\n  // Clean up on unmount\n  useEffect(() => {\n    return () => {\n      stopRecording();\n\n      if (sessionTimerRef.current) {\n        clearInterval(sessionTimerRef.current);\n      }\n    };\n  }, [stopRecording]);\n\n  // Handle text selection for GPT\n  const handleTextSelection = useCallback(() => {\n    const selection = window.getSelection();\n    const selectedText = selection.toString().trim();\n\n    if (selectedText) {\n      setSelectedText(selectedText);\n    }\n  }, []);\n\n  // Send selected text to GPT\n  const sendToGPT = useCallback(async () => {\n    if (!selectedText || isLoadingGpt || sessionExpired) return;\n\n    const apiKey = env.OPENAI_API_KEY;\n\n    if (!apiKey) {\n      setApiKeyError('OpenAI API key is missing. Please add it to your environment variables as REACT_APP_OPENAI_API_KEY.');\n      setShowApiKeyError(true);\n      return;\n    }\n\n    setIsLoadingGpt(true);\n\n    try {\n      const response = await fetch(\"https://api.openai.com/v1/chat/completions\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          \"Authorization\": `Bearer ${apiKey}`\n        },\n        body: JSON.stringify({\n          model: \"gpt-4.1-nano\",\n          messages: [\n            {\n              role: \"system\",\n              content: \"You are an AI assistant helping with interview transcripts. Provide concise, helpful responses.\"\n            },\n            {\n              role: \"user\",\n              content: `Please analyze this interview transcript and provide insights or suggestions: \"${selectedText}\"`\n            }\n          ],\n          stream: true\n        })\n      });\n\n      if (!response.ok) {\n        throw new Error(`API error: ${response.status} ${response.statusText}`);\n      }\n\n      const reader = response.body.getReader();\n      const decoder = new TextDecoder(\"utf-8\");\n      let result = \"\";\n\n      while (true) {\n        const { value, done } = await reader.read();\n        if (done) break;\n\n        const chunk = decoder.decode(value, { stream: true });\n        const lines = chunk.split(\"\\n\").filter(line => line.trim().startsWith(\"data:\"));\n\n        for (const line of lines) {\n          const data = line.replace(/^data: /, '');\n          if (data === \"[DONE]\") break;\n\n          try {\n            const json = JSON.parse(data);\n            const content = json.choices?.[0]?.delta?.content;\n            if (content) {\n              result += content;\n              setGptResponse(result);\n            }\n          } catch (e) {\n            console.error(\"Error parsing JSON:\", e);\n          }\n        }\n      }\n    } catch (error) {\n      console.error(\"GPT API Error:\", error);\n      setGptResponse(\"Error: \" + error.message);\n    } finally {\n      setIsLoadingGpt(false);\n    }\n  }, [selectedText, isLoadingGpt, sessionExpired]);\n\n  // Clear transcripts\n  const clearTranscripts = useCallback(() => {\n    setTranscripts([]);\n    setGptResponse('');\n    setSelectedText('');\n  }, []);\n\n  // Clear GPT response\n  const clearGptResponse = useCallback(() => {\n    setGptResponse('');\n    setSelectedText('');\n  }, []);\n\n  // Handle exit button click\n  const handleExit = useCallback(() => {\n    stopRecording();\n    if (onBack) onBack();\n  }, [stopRecording, onBack]);\n\n  // Handle API key error close\n  const handleApiKeyErrorClose = useCallback(() => {\n    setShowApiKeyError(false);\n  }, []);\n\n  // Get full transcript text\n  const getFullTranscript = useCallback(() => {\n    return transcripts.map(segment =>\n      `Speaker ${segment.speaker}: ${segment.words.join(' ')}`\n    ).join('\\n');\n  }, [transcripts]);\n\n  // Download transcript\n  const downloadTranscript = useCallback(() => {\n    const fullText = getFullTranscript();\n    if (!fullText) return;\n\n    const blob = new Blob([fullText], { type: 'text/plain' });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `transcript-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  }, [getFullTranscript]);\n\n  return (\n    <div className=\"deepgram-transcript-container\">\n      {/* Exit button in top right corner */}\n      <button className=\"exit-button\" onClick={handleExit} title=\"Exit\">\n        <CloseIcon />\n      </button>\n\n      {/* Session timer display at the top */}\n      <div className=\"timer-container\">\n        <AccessTimeIcon className=\"timer-icon\" />\n        <span className=\"timer-display session-timer\">\n          Session: {formatSessionTime(sessionTimeRemaining)}\n        </span>\n        {isRecording && <span className=\"listening-indicator\">Recording...</span>}\n      </div>\n\n      <h2 className=\"deepgram-transcript-title\">Live Transcript with Speaker Diarization</h2>\n\n      <div className=\"deepgram-controls\">\n        <button\n          className={`record-button ${isRecording ? 'recording' : ''}`}\n          onClick={isRecording ? stopRecording : startRecording}\n          disabled={sessionExpired || apiKeyStatus === 'invalid'}\n          title={isRecording ? \"Stop Recording\" : \"Start Recording\"}\n        >\n          {isRecording ? <StopIcon /> : <MicIcon />}\n          {isRecording ? \"Stop Recording\" : \"Start Recording\"}\n        </button>\n\n        <button\n          className=\"clear-button\"\n          onClick={clearTranscripts}\n          disabled={transcripts.length === 0 || sessionExpired}\n          title=\"Clear Transcripts\"\n        >\n          <DeleteIcon fontSize=\"small\" />\n          Clear\n        </button>\n\n        <button\n          className=\"download-button\"\n          onClick={downloadTranscript}\n          disabled={transcripts.length === 0 || sessionExpired}\n          title=\"Download Transcript\"\n        >\n          Download\n        </button>\n      </div>\n\n      <div className=\"deepgram-content\">\n        <div className=\"transcript-section\">\n          <div\n            className=\"transcript-container\"\n            ref={transcriptContainerRef}\n            onMouseUp={handleTextSelection}\n          >\n            {transcripts.length === 0 ? (\n              <div className=\"empty-transcript\">\n                {apiKeyStatus === 'invalid' ? (\n                  <p>API key validation failed. Please check your Deepgram API key.</p>\n                ) : (\n                  <p>No transcripts yet. Click \"Start Recording\" to begin.</p>\n                )}\n              </div>\n            ) : (\n              transcripts.map((segment, index) => (\n                <div className=\"deepgram-segment\" key={index}>\n                  <span className=\"deepgram-speaker\">Speaker {segment.speaker}:</span>\n                  <span>{segment.words.join(' ')}</span>\n                </div>\n              ))\n            )}\n          </div>\n\n          {selectedText && (\n            <div className=\"selected-text-container\">\n              <div className=\"selected-text-header\">\n                <h3>Selected Text</h3>\n                <button\n                  className=\"send-to-gpt-button\"\n                  onClick={sendToGPT}\n                  disabled={!selectedText || isLoadingGpt || sessionExpired}\n                  title=\"Analyze with GPT\"\n                >\n                  {isLoadingGpt ? <CircularProgress size={20} /> : <SendIcon />}\n                  Analyze with GPT\n                </button>\n              </div>\n              <div className=\"selected-text\">{selectedText}</div>\n            </div>\n          )}\n        </div>\n\n        {(gptResponse || isLoadingGpt) && (\n          <div className=\"gpt-response-section\">\n            <div className=\"gpt-response-header\">\n              <h3>AI Analysis</h3>\n              <button\n                className=\"clear-button\"\n                onClick={clearGptResponse}\n                disabled={!gptResponse && !isLoadingGpt}\n                title=\"Clear Analysis\"\n              >\n                <DeleteIcon fontSize=\"small\" />\n                Clear\n              </button>\n            </div>\n            <div className=\"gpt-response\">\n              {isLoadingGpt && !gptResponse ? (\n                <div className=\"loading-container\">\n                  <CircularProgress size={30} />\n                  <p>Analyzing transcript...</p>\n                </div>\n              ) : (\n                gptResponse\n              )}\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Payment Dialog */}\n      <Dialog\n        open={showPaymentDialog}\n        onClose={handlePaymentDialogClose}\n        aria-labelledby=\"payment-dialog-title\"\n        aria-describedby=\"payment-dialog-description\"\n      >\n        <DialogTitle id=\"payment-dialog-title\">\n          {\"Session Expiring Soon\"}\n        </DialogTitle>\n        <DialogContent>\n          <DialogContentText id=\"payment-dialog-description\">\n            Your session will expire in one minute. Would you like to make a payment to extend your session?\n          </DialogContentText>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={handlePaymentDialogClose} color=\"primary\">\n            Not Now\n          </Button>\n          <Button onClick={handlePaymentConfirm} color=\"primary\" autoFocus>\n            Make Payment\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Session Expired Dialog */}\n      <Dialog\n        open={sessionExpired}\n        aria-labelledby=\"expired-dialog-title\"\n        aria-describedby=\"expired-dialog-description\"\n      >\n        <DialogTitle id=\"expired-dialog-title\">\n          {\"Session Expired\"}\n        </DialogTitle>\n        <DialogContent>\n          <DialogContentText id=\"expired-dialog-description\">\n            Your session has expired. Please make a payment to continue using the service.\n          </DialogContentText>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={handleExit} color=\"primary\">\n            Exit\n          </Button>\n          <Button onClick={handlePaymentConfirm} color=\"primary\" autoFocus>\n            Make Payment\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* API Key Error Snackbar */}\n      <Snackbar\n        open={showApiKeyError}\n        autoHideDuration={6000}\n        onClose={handleApiKeyErrorClose}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n      >\n        <Alert onClose={handleApiKeyErrorClose} severity=\"error\" sx={{ width: '100%' }}>\n          {apiKeyError}\n        </Alert>\n      </Snackbar>\n    </div>\n  );\n};\n\nexport default DeepgramLiveTranscript;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AACvE,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAO,8BAA8B;AACrC,OAAOC,GAAG,MAAM,cAAc;AAC9B,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,iBAAiB,MAAM,iCAAiC;AAC/D,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,OAAOC,KAAK,MAAM,qBAAqB;AACvC,OAAOC,QAAQ,MAAM,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,MAAMC,sBAAsB,GAAGA,CAAC;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EAC7C;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC0B,WAAW,EAAEC,cAAc,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC4B,YAAY,EAAEC,eAAe,CAAC,GAAG7B,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;EAC7D,MAAM,CAAC8B,WAAW,EAAEC,cAAc,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACgC,eAAe,EAAEC,kBAAkB,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACA,MAAM,CAACkC,WAAW,EAAEC,cAAc,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACoC,YAAY,EAAEC,eAAe,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACsC,YAAY,EAAEC,eAAe,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;;EAEpD;EACA,MAAMwC,gBAAgB,GAAG,GAAG,CAAC,CAAC;EAC9B,MAAMC,YAAY,GAAG,EAAE,CAAC,CAAC;EACzB,MAAM,CAACC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG3C,QAAQ,CAACwC,gBAAgB,CAAC;EAClF,MAAM,CAACI,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC8C,cAAc,EAAEC,iBAAiB,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;;EAE3D;EACA,MAAMgD,KAAK,GAAGjD,MAAM,CAAC,IAAI,CAAC;EAC1B,MAAMkD,QAAQ,GAAGlD,MAAM,CAAC,IAAIG,WAAW,CAAC;IAAEgD,OAAO,EAAE;EAAI,CAAC,CAAC,CAAC;EAC1D,MAAMC,WAAW,GAAGpD,MAAM,CAAC,IAAI,CAAC;EAChC,MAAMqD,eAAe,GAAGrD,MAAM,CAAC,IAAI,CAAC;EACpC,MAAMsD,sBAAsB,GAAGtD,MAAM,CAAC,IAAI,CAAC;;EAE3C;EACA,MAAMuD,iBAAiB,GAAGrD,WAAW,CAAEsD,YAAY,IAAK;IACtD,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,YAAY,GAAG,EAAE,CAAC;IAC7C,MAAMI,OAAO,GAAGJ,YAAY,GAAG,EAAE;IACjC,OAAO,GAAGC,OAAO,CAACI,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,OAAO,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACxF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,iBAAiB,GAAG7D,WAAW,CAAC,MAAM;IAC1C;IACA0C,uBAAuB,CAACH,gBAAgB,CAAC;IACzCO,iBAAiB,CAAC,KAAK,CAAC;;IAExB;IACA,IAAIK,eAAe,CAACW,OAAO,EAAE;MAC3BC,aAAa,CAACZ,eAAe,CAACW,OAAO,CAAC;IACxC;;IAEA;IACAX,eAAe,CAACW,OAAO,GAAGE,WAAW,CAAC,MAAM;MAC1CtB,uBAAuB,CAACuB,IAAI,IAAI;QAC9B,MAAMC,OAAO,GAAGD,IAAI,GAAG,CAAC;;QAExB;QACA,IAAIC,OAAO,KAAK1B,YAAY,EAAE;UAC5BI,oBAAoB,CAAC,IAAI,CAAC;QAC5B;;QAEA;QACA,IAAIsB,OAAO,IAAI,CAAC,EAAE;UAChBH,aAAa,CAACZ,eAAe,CAACW,OAAO,CAAC;UACtCX,eAAe,CAACW,OAAO,GAAG,IAAI;UAC9BhB,iBAAiB,CAAC,IAAI,CAAC;UACvBqB,aAAa,CAAC,CAAC;UACf,OAAO,CAAC;QACV;QAEA,OAAOD,OAAO;MAChB,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC;EACV,CAAC,EAAE,CAAC3B,gBAAgB,EAAEC,YAAY,CAAC,CAAC;;EAEpC;EACA,MAAM4B,wBAAwB,GAAGpE,WAAW,CAAC,MAAM;IACjD4C,oBAAoB,CAAC,KAAK,CAAC;EAC7B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMyB,oBAAoB,GAAGrE,WAAW,CAAC,MAAM;IAC7C;IACA4C,oBAAoB,CAAC,KAAK,CAAC;IAC3BF,uBAAuB,CAACH,gBAAgB,CAAC;IACzCO,iBAAiB,CAAC,KAAK,CAAC;;IAExB;IACA,IAAIK,eAAe,CAACW,OAAO,EAAE;MAC3BC,aAAa,CAACZ,eAAe,CAACW,OAAO,CAAC;IACxC;IAEAX,eAAe,CAACW,OAAO,GAAGE,WAAW,CAAC,MAAM;MAC1CtB,uBAAuB,CAACuB,IAAI,IAAI;QAC9B,MAAMC,OAAO,GAAGD,IAAI,GAAG,CAAC;;QAExB;QACA,IAAIC,OAAO,KAAK1B,YAAY,EAAE;UAC5BI,oBAAoB,CAAC,IAAI,CAAC;QAC5B;;QAEA;QACA,IAAIsB,OAAO,IAAI,CAAC,EAAE;UAChBH,aAAa,CAACZ,eAAe,CAACW,OAAO,CAAC;UACtCX,eAAe,CAACW,OAAO,GAAG,IAAI;UAC9BhB,iBAAiB,CAAC,IAAI,CAAC;UACvBqB,aAAa,CAAC,CAAC;UACf,OAAO,CAAC;QACV;QAEA,OAAOD,OAAO;MAChB,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC;EACV,CAAC,EAAE,CAAC3B,gBAAgB,EAAEC,YAAY,CAAC,CAAC;;EAEpC;EACA3C,SAAS,CAAC,MAAM;IACdgE,iBAAiB,CAAC,CAAC;;IAEnB;IACA,OAAO,MAAM;MACX,IAAIV,eAAe,CAACW,OAAO,EAAE;QAC3BC,aAAa,CAACZ,eAAe,CAACW,OAAO,CAAC;MACxC;IACF,CAAC;EACH,CAAC,EAAE,CAACD,iBAAiB,CAAC,CAAC;;EAEvB;EACA,MAAMS,oBAAoB,GAAGtE,WAAW,CAAC,YAAY;IACnD,IAAI;MACF;MACA,MAAMuE,gBAAgB,GAAGrE,GAAG,CAACqE,gBAAgB;MAE7C,IAAI,CAACA,gBAAgB,EAAE;QACrB3C,eAAe,CAAC,SAAS,CAAC;QAC1BE,cAAc,CAAC,yGAAyG,CAAC;QACzHE,kBAAkB,CAAC,IAAI,CAAC;QACxB,OAAO,KAAK;MACd;;MAEA;MACA,MAAMwC,QAAQ,GAAG,MAAMC,KAAK,CAAC,sCAAsC,EAAE;QACnEC,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACP,eAAe,EAAE,SAASJ,gBAAgB;QAC5C;MACF,CAAC,CAAC;MAEF,IAAIC,QAAQ,CAACI,EAAE,EAAE;QACfhD,eAAe,CAAC,OAAO,CAAC;QACxB,OAAO,IAAI;MACb,CAAC,MAAM;QACLA,eAAe,CAAC,SAAS,CAAC;QAC1BE,cAAc,CAAC,oEAAoE,CAAC;QACpFE,kBAAkB,CAAC,IAAI,CAAC;QACxB,OAAO,KAAK;MACd;IACF,CAAC,CAAC,OAAO6C,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzDjD,eAAe,CAAC,SAAS,CAAC;MAC1BE,cAAc,CAAC,oCAAoC,GAAG+C,KAAK,CAACE,OAAO,CAAC;MACpE/C,kBAAkB,CAAC,IAAI,CAAC;MACxB,OAAO,KAAK;IACd;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMgD,cAAc,GAAGhF,WAAW,CAAC,YAAY;IAC7C,IAAI6C,cAAc,EAAE;;IAEpB;IACA,MAAMoC,aAAa,GAAG,MAAMX,oBAAoB,CAAC,CAAC;IAClD,IAAI,CAACW,aAAa,EAAE;IAEpB,IAAI;MACF,MAAMV,gBAAgB,GAAGrE,GAAG,CAACqE,gBAAgB;;MAE7C;MACA,MAAMW,MAAM,GAAG,IAAIC,SAAS,CAAC,8DAA8D,CAAC;;MAE5F;MACAD,MAAM,CAACE,MAAM,GAAG,YAAY;QAC1BN,OAAO,CAACO,GAAG,CAAC,qBAAqB,CAAC;;QAElC;QACAH,MAAM,CAACI,IAAI,CAACC,IAAI,CAACC,SAAS,CAAC;UACzBC,aAAa,EAAElB;QACjB,CAAC,CAAC,CAAC;;QAEH;QACA,MAAMvB,QAAQ,CAACc,OAAO,CAAC4B,KAAK,CAAC,CAAC;QAC9BhE,cAAc,CAAC,IAAI,CAAC;;QAEpB;QACAwB,WAAW,CAACY,OAAO,GAAGE,WAAW,CAAC,YAAY;UAC5C,IAAI;YACF,MAAM,CAAC2B,MAAM,EAAEC,IAAI,CAAC,GAAG,MAAM5C,QAAQ,CAACc,OAAO,CAAC+B,MAAM,CAAC,CAAC;YACtD,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;YAC/BD,MAAM,CAACE,iBAAiB,CAACJ,IAAI,CAAC;YAC9BE,MAAM,CAACG,SAAS,GAAG,MAAM;cACvB,IAAIf,MAAM,CAACgB,UAAU,KAAKf,SAAS,CAACgB,IAAI,EAAE;gBACxCjB,MAAM,CAACI,IAAI,CAACQ,MAAM,CAACM,MAAM,CAAC;cAC5B;YACF,CAAC;UACH,CAAC,CAAC,OAAOvB,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;UACjD;QACF,CAAC,EAAE,IAAI,CAAC;QAER9B,KAAK,CAACe,OAAO,GAAGoB,MAAM;MACxB,CAAC;;MAED;MACAA,MAAM,CAACmB,SAAS,GAAItB,OAAO,IAAK;QAC9B,IAAI;UAAA,IAAAuB,aAAA,EAAAC,qBAAA;UACF,MAAMC,IAAI,GAAGjB,IAAI,CAACkB,KAAK,CAAC1B,OAAO,CAACyB,IAAI,CAAC;UACrC,MAAME,KAAK,GAAG,EAAAJ,aAAA,GAAAE,IAAI,CAACG,OAAO,cAAAL,aAAA,wBAAAC,qBAAA,GAAZD,aAAA,CAAcM,YAAY,CAAC,CAAC,CAAC,cAAAL,qBAAA,uBAA7BA,qBAAA,CAA+BG,KAAK,KAAI,EAAE;UAExD,IAAIA,KAAK,CAACG,MAAM,KAAK,CAAC,EAAE;UAExB,MAAMC,gBAAgB,GAAGJ,KAAK,CAACK,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAK;YACnD,IAAI,CAACA,IAAI,CAACC,OAAO,EAAE,OAAOF,GAAG;YAC7B,MAAMG,SAAS,GAAGH,GAAG,CAACA,GAAG,CAACH,MAAM,GAAG,CAAC,CAAC;YAErC,IAAIM,SAAS,IAAIA,SAAS,CAACD,OAAO,KAAKD,IAAI,CAACC,OAAO,EAAE;cACnDC,SAAS,CAACT,KAAK,CAACU,IAAI,CAACH,IAAI,CAACA,IAAI,CAAC;YACjC,CAAC,MAAM;cACLD,GAAG,CAACI,IAAI,CAAC;gBACPF,OAAO,EAAED,IAAI,CAACC,OAAO;gBACrBR,KAAK,EAAE,CAACO,IAAI,CAACA,IAAI,CAAC;gBAClBI,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;cACpC,CAAC,CAAC;YACJ;YAEA,OAAOP,GAAG;UACZ,CAAC,EAAE,EAAE,CAAC;UAEN,IAAIF,gBAAgB,CAACD,MAAM,GAAG,CAAC,EAAE;YAC/BrF,cAAc,CAACyC,IAAI,IAAI;cACrB;cACA,MAAMuD,cAAc,GAAG,CAAC,GAAGvD,IAAI,CAAC;cAEhC6C,gBAAgB,CAACW,OAAO,CAACC,KAAK,IAAI;gBAChC,MAAMP,SAAS,GAAGK,cAAc,CAACA,cAAc,CAACX,MAAM,GAAG,CAAC,CAAC;gBAE3D,IAAIM,SAAS,IAAIA,SAAS,CAACD,OAAO,KAAKQ,KAAK,CAACR,OAAO,EAAE;kBACpD;kBACAC,SAAS,CAACT,KAAK,GAAG,CAAC,GAAGS,SAAS,CAACT,KAAK,EAAE,GAAGgB,KAAK,CAAChB,KAAK,CAAC;gBACxD,CAAC,MAAM;kBACL;kBACAc,cAAc,CAACJ,IAAI,CAACM,KAAK,CAAC;gBAC5B;cACF,CAAC,CAAC;cAEF,OAAOF,cAAc;YACvB,CAAC,CAAC;;YAEF;YACA,IAAIpE,sBAAsB,CAACU,OAAO,EAAE;cAClCV,sBAAsB,CAACU,OAAO,CAAC6D,SAAS,GAAGvE,sBAAsB,CAACU,OAAO,CAAC8D,YAAY;YACxF;UACF;QACF,CAAC,CAAC,OAAO/C,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAChD;MACF,CAAC;;MAED;MACAK,MAAM,CAAC2C,OAAO,GAAIhD,KAAK,IAAK;QAC1BC,OAAO,CAACD,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;QACxC/C,cAAc,CAAC,mBAAmB,GAAG+C,KAAK,CAACE,OAAO,CAAC;QACnD/C,kBAAkB,CAAC,IAAI,CAAC;MAC1B,CAAC;;MAED;MACAkD,MAAM,CAAC4C,OAAO,GAAG,MAAM;QACrBhD,OAAO,CAACO,GAAG,CAAC,kBAAkB,CAAC;QAC/BlB,aAAa,CAAC,CAAC;MACjB,CAAC;IACH,CAAC,CAAC,OAAOU,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD/C,cAAc,CAAC,4BAA4B,GAAG+C,KAAK,CAACE,OAAO,CAAC;MAC5D/C,kBAAkB,CAAC,IAAI,CAAC;IAC1B;EACF,CAAC,EAAE,CAACa,cAAc,EAAEyB,oBAAoB,CAAC,CAAC;;EAE1C;EACA,MAAMH,aAAa,GAAGnE,WAAW,CAAC,MAAM;IACtC0B,cAAc,CAAC,KAAK,CAAC;;IAErB;IACA,IAAIsB,QAAQ,CAACc,OAAO,EAAE;MACpBd,QAAQ,CAACc,OAAO,CAACiE,IAAI,CAAC,CAAC;IACzB;;IAEA;IACA,IAAI7E,WAAW,CAACY,OAAO,EAAE;MACvBC,aAAa,CAACb,WAAW,CAACY,OAAO,CAAC;MAClCZ,WAAW,CAACY,OAAO,GAAG,IAAI;IAC5B;;IAEA;IACA,IAAIf,KAAK,CAACe,OAAO,EAAE;MACjBf,KAAK,CAACe,OAAO,CAACkE,KAAK,CAAC,CAAC;MACrBjF,KAAK,CAACe,OAAO,GAAG,IAAI;IACtB;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAjE,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACXsE,aAAa,CAAC,CAAC;MAEf,IAAIhB,eAAe,CAACW,OAAO,EAAE;QAC3BC,aAAa,CAACZ,eAAe,CAACW,OAAO,CAAC;MACxC;IACF,CAAC;EACH,CAAC,EAAE,CAACK,aAAa,CAAC,CAAC;;EAEnB;EACA,MAAM8D,mBAAmB,GAAGjI,WAAW,CAAC,MAAM;IAC5C,MAAMkI,SAAS,GAAGC,MAAM,CAACC,YAAY,CAAC,CAAC;IACvC,MAAM/F,YAAY,GAAG6F,SAAS,CAACvE,QAAQ,CAAC,CAAC,CAAC0E,IAAI,CAAC,CAAC;IAEhD,IAAIhG,YAAY,EAAE;MAChBC,eAAe,CAACD,YAAY,CAAC;IAC/B;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMiG,SAAS,GAAGtI,WAAW,CAAC,YAAY;IACxC,IAAI,CAACqC,YAAY,IAAIF,YAAY,IAAIU,cAAc,EAAE;IAErD,MAAM0F,MAAM,GAAGrI,GAAG,CAACsI,cAAc;IAEjC,IAAI,CAACD,MAAM,EAAE;MACXzG,cAAc,CAAC,qGAAqG,CAAC;MACrHE,kBAAkB,CAAC,IAAI,CAAC;MACxB;IACF;IAEAI,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACF,MAAMoC,QAAQ,GAAG,MAAMC,KAAK,CAAC,4CAA4C,EAAE;QACzEC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAE,UAAU4D,MAAM;QACnC,CAAC;QACDE,IAAI,EAAElD,IAAI,CAACC,SAAS,CAAC;UACnBkD,KAAK,EAAE,cAAc;UACrBC,QAAQ,EAAE,CACR;YACEC,IAAI,EAAE,QAAQ;YACdC,OAAO,EAAE;UACX,CAAC,EACD;YACED,IAAI,EAAE,MAAM;YACZC,OAAO,EAAE,kFAAkFxG,YAAY;UACzG,CAAC,CACF;UACDyG,MAAM,EAAE;QACV,CAAC;MACH,CAAC,CAAC;MAEF,IAAI,CAACtE,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAM,IAAImE,KAAK,CAAC,cAAcvE,QAAQ,CAACwE,MAAM,IAAIxE,QAAQ,CAACyE,UAAU,EAAE,CAAC;MACzE;MAEA,MAAMnD,MAAM,GAAGtB,QAAQ,CAACiE,IAAI,CAACS,SAAS,CAAC,CAAC;MACxC,MAAMC,OAAO,GAAG,IAAIC,WAAW,CAAC,OAAO,CAAC;MACxC,IAAIhD,MAAM,GAAG,EAAE;MAEf,OAAO,IAAI,EAAE;QACX,MAAM;UAAEiD,KAAK;UAAEC;QAAK,CAAC,GAAG,MAAMxD,MAAM,CAACyD,IAAI,CAAC,CAAC;QAC3C,IAAID,IAAI,EAAE;QAEV,MAAME,KAAK,GAAGL,OAAO,CAACM,MAAM,CAACJ,KAAK,EAAE;UAAEP,MAAM,EAAE;QAAK,CAAC,CAAC;QACrD,MAAMY,KAAK,GAAGF,KAAK,CAACG,KAAK,CAAC,IAAI,CAAC,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACxB,IAAI,CAAC,CAAC,CAACyB,UAAU,CAAC,OAAO,CAAC,CAAC;QAE/E,KAAK,MAAMD,IAAI,IAAIH,KAAK,EAAE;UACxB,MAAMlD,IAAI,GAAGqD,IAAI,CAACE,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;UACxC,IAAIvD,IAAI,KAAK,QAAQ,EAAE;UAEvB,IAAI;YAAA,IAAAwD,aAAA,EAAAC,cAAA,EAAAC,oBAAA;YACF,MAAMC,IAAI,GAAG5E,IAAI,CAACkB,KAAK,CAACD,IAAI,CAAC;YAC7B,MAAMqC,OAAO,IAAAmB,aAAA,GAAGG,IAAI,CAACC,OAAO,cAAAJ,aAAA,wBAAAC,cAAA,GAAZD,aAAA,CAAe,CAAC,CAAC,cAAAC,cAAA,wBAAAC,oBAAA,GAAjBD,cAAA,CAAmBI,KAAK,cAAAH,oBAAA,uBAAxBA,oBAAA,CAA0BrB,OAAO;YACjD,IAAIA,OAAO,EAAE;cACXzC,MAAM,IAAIyC,OAAO;cACjB3G,cAAc,CAACkE,MAAM,CAAC;YACxB;UACF,CAAC,CAAC,OAAOkE,CAAC,EAAE;YACVxF,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEyF,CAAC,CAAC;UACzC;QACF;MACF;IACF,CAAC,CAAC,OAAOzF,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;MACtC3C,cAAc,CAAC,SAAS,GAAG2C,KAAK,CAACE,OAAO,CAAC;IAC3C,CAAC,SAAS;MACR3C,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC,EAAE,CAACC,YAAY,EAAEF,YAAY,EAAEU,cAAc,CAAC,CAAC;;EAEhD;EACA,MAAM0H,gBAAgB,GAAGvK,WAAW,CAAC,MAAM;IACzCwB,cAAc,CAAC,EAAE,CAAC;IAClBU,cAAc,CAAC,EAAE,CAAC;IAClBI,eAAe,CAAC,EAAE,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMkI,gBAAgB,GAAGxK,WAAW,CAAC,MAAM;IACzCkC,cAAc,CAAC,EAAE,CAAC;IAClBI,eAAe,CAAC,EAAE,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMmI,UAAU,GAAGzK,WAAW,CAAC,MAAM;IACnCmE,aAAa,CAAC,CAAC;IACf,IAAI9C,MAAM,EAAEA,MAAM,CAAC,CAAC;EACtB,CAAC,EAAE,CAAC8C,aAAa,EAAE9C,MAAM,CAAC,CAAC;;EAE3B;EACA,MAAMqJ,sBAAsB,GAAG1K,WAAW,CAAC,MAAM;IAC/CgC,kBAAkB,CAAC,KAAK,CAAC;EAC3B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM2I,iBAAiB,GAAG3K,WAAW,CAAC,MAAM;IAC1C,OAAOuB,WAAW,CAACqJ,GAAG,CAACC,OAAO,IAC5B,WAAWA,OAAO,CAAC3D,OAAO,KAAK2D,OAAO,CAACnE,KAAK,CAACoE,IAAI,CAAC,GAAG,CAAC,EACxD,CAAC,CAACA,IAAI,CAAC,IAAI,CAAC;EACd,CAAC,EAAE,CAACvJ,WAAW,CAAC,CAAC;;EAEjB;EACA,MAAMwJ,kBAAkB,GAAG/K,WAAW,CAAC,MAAM;IAC3C,MAAMgL,QAAQ,GAAGL,iBAAiB,CAAC,CAAC;IACpC,IAAI,CAACK,QAAQ,EAAE;IAEf,MAAMpF,IAAI,GAAG,IAAIqF,IAAI,CAAC,CAACD,QAAQ,CAAC,EAAE;MAAEE,IAAI,EAAE;IAAa,CAAC,CAAC;IACzD,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACzF,IAAI,CAAC;IACrC,MAAM0F,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACrCF,CAAC,CAACG,IAAI,GAAGN,GAAG;IACZG,CAAC,CAACI,QAAQ,GAAG,cAAc,IAAIpE,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACoE,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC5B,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM;IACzFwB,QAAQ,CAAC9C,IAAI,CAACmD,WAAW,CAACN,CAAC,CAAC;IAC5BA,CAAC,CAACO,KAAK,CAAC,CAAC;IACTN,QAAQ,CAAC9C,IAAI,CAACqD,WAAW,CAACR,CAAC,CAAC;IAC5BF,GAAG,CAACW,eAAe,CAACZ,GAAG,CAAC;EAC1B,CAAC,EAAE,CAACR,iBAAiB,CAAC,CAAC;EAEvB,oBACExJ,OAAA;IAAK6K,SAAS,EAAC,+BAA+B;IAAAC,QAAA,gBAE5C9K,OAAA;MAAQ6K,SAAS,EAAC,aAAa;MAACE,OAAO,EAAEzB,UAAW;MAAC0B,KAAK,EAAC,MAAM;MAAAF,QAAA,eAC/D9K,OAAA,CAAChB,SAAS;QAAAiM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,eAGTpL,OAAA;MAAK6K,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9B9K,OAAA,CAACX,cAAc;QAACwL,SAAS,EAAC;MAAY;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACzCpL,OAAA;QAAM6K,SAAS,EAAC,6BAA6B;QAAAC,QAAA,GAAC,WACnC,EAAC5I,iBAAiB,CAACZ,oBAAoB,CAAC;MAAA;QAAA2J,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC,EACN9K,WAAW,iBAAIN,OAAA;QAAM6K,SAAS,EAAC,qBAAqB;QAAAC,QAAA,EAAC;MAAY;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtE,CAAC,eAENpL,OAAA;MAAI6K,SAAS,EAAC,2BAA2B;MAAAC,QAAA,EAAC;IAAwC;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAEvFpL,OAAA;MAAK6K,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChC9K,OAAA;QACE6K,SAAS,EAAE,iBAAiBvK,WAAW,GAAG,WAAW,GAAG,EAAE,EAAG;QAC7DyK,OAAO,EAAEzK,WAAW,GAAG0C,aAAa,GAAGa,cAAe;QACtDwH,QAAQ,EAAE3J,cAAc,IAAIlB,YAAY,KAAK,SAAU;QACvDwK,KAAK,EAAE1K,WAAW,GAAG,gBAAgB,GAAG,iBAAkB;QAAAwK,QAAA,GAEzDxK,WAAW,gBAAGN,OAAA,CAACd,QAAQ;UAAA+L,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGpL,OAAA,CAACf,OAAO;UAAAgM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACxC9K,WAAW,GAAG,gBAAgB,GAAG,iBAAiB;MAAA;QAAA2K,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC,eAETpL,OAAA;QACE6K,SAAS,EAAC,cAAc;QACxBE,OAAO,EAAE3B,gBAAiB;QAC1BiC,QAAQ,EAAEjL,WAAW,CAACsF,MAAM,KAAK,CAAC,IAAIhE,cAAe;QACrDsJ,KAAK,EAAC,mBAAmB;QAAAF,QAAA,gBAEzB9K,OAAA,CAACZ,UAAU;UAACkM,QAAQ,EAAC;QAAO;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,SAEjC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAETpL,OAAA;QACE6K,SAAS,EAAC,iBAAiB;QAC3BE,OAAO,EAAEnB,kBAAmB;QAC5ByB,QAAQ,EAAEjL,WAAW,CAACsF,MAAM,KAAK,CAAC,IAAIhE,cAAe;QACrDsJ,KAAK,EAAC,qBAAqB;QAAAF,QAAA,EAC5B;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENpL,OAAA;MAAK6K,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/B9K,OAAA;QAAK6K,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjC9K,OAAA;UACE6K,SAAS,EAAC,sBAAsB;UAChCU,GAAG,EAAEtJ,sBAAuB;UAC5BuJ,SAAS,EAAE1E,mBAAoB;UAAAgE,QAAA,EAE9B1K,WAAW,CAACsF,MAAM,KAAK,CAAC,gBACvB1F,OAAA;YAAK6K,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAC9BtK,YAAY,KAAK,SAAS,gBACzBR,OAAA;cAAA8K,QAAA,EAAG;YAA8D;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,gBAErEpL,OAAA;cAAA8K,QAAA,EAAG;YAAqD;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAC5D;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,GAENhL,WAAW,CAACqJ,GAAG,CAAC,CAACC,OAAO,EAAE+B,KAAK,kBAC7BzL,OAAA;YAAK6K,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/B9K,OAAA;cAAM6K,SAAS,EAAC,kBAAkB;cAAAC,QAAA,GAAC,UAAQ,EAACpB,OAAO,CAAC3D,OAAO,EAAC,GAAC;YAAA;cAAAkF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpEpL,OAAA;cAAA8K,QAAA,EAAOpB,OAAO,CAACnE,KAAK,CAACoE,IAAI,CAAC,GAAG;YAAC;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA,GAFDK,KAAK;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGvC,CACN;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAELlK,YAAY,iBACXlB,OAAA;UAAK6K,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACtC9K,OAAA;YAAK6K,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnC9K,OAAA;cAAA8K,QAAA,EAAI;YAAa;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtBpL,OAAA;cACE6K,SAAS,EAAC,oBAAoB;cAC9BE,OAAO,EAAE5D,SAAU;cACnBkE,QAAQ,EAAE,CAACnK,YAAY,IAAIF,YAAY,IAAIU,cAAe;cAC1DsJ,KAAK,EAAC,kBAAkB;cAAAF,QAAA,GAEvB9J,YAAY,gBAAGhB,OAAA,CAACJ,gBAAgB;gBAAC8L,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGpL,OAAA,CAACb,QAAQ;gBAAA8L,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EAAC,kBAEhE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNpL,OAAA;YAAK6K,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAE5J;UAAY;YAAA+J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAEL,CAACtK,WAAW,IAAIE,YAAY,kBAC3BhB,OAAA;QAAK6K,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACnC9K,OAAA;UAAK6K,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAClC9K,OAAA;YAAA8K,QAAA,EAAI;UAAW;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpBpL,OAAA;YACE6K,SAAS,EAAC,cAAc;YACxBE,OAAO,EAAE1B,gBAAiB;YAC1BgC,QAAQ,EAAE,CAACvK,WAAW,IAAI,CAACE,YAAa;YACxCgK,KAAK,EAAC,gBAAgB;YAAAF,QAAA,gBAEtB9K,OAAA,CAACZ,UAAU;cAACkM,QAAQ,EAAC;YAAO;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,SAEjC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNpL,OAAA;UAAK6K,SAAS,EAAC,cAAc;UAAAC,QAAA,EAC1B9J,YAAY,IAAI,CAACF,WAAW,gBAC3Bd,OAAA;YAAK6K,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChC9K,OAAA,CAACJ,gBAAgB;cAAC8L,IAAI,EAAE;YAAG;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9BpL,OAAA;cAAA8K,QAAA,EAAG;YAAuB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,GAENtK;QACD;UAAAmK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNpL,OAAA,CAACV,MAAM;MACLqM,IAAI,EAAEnK,iBAAkB;MACxBoK,OAAO,EAAE3I,wBAAyB;MAClC,mBAAgB,sBAAsB;MACtC,oBAAiB,4BAA4B;MAAA6H,QAAA,gBAE7C9K,OAAA,CAACN,WAAW;QAACmM,EAAE,EAAC,sBAAsB;QAAAf,QAAA,EACnC;MAAuB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC,eACdpL,OAAA,CAACR,aAAa;QAAAsL,QAAA,eACZ9K,OAAA,CAACP,iBAAiB;UAACoM,EAAE,EAAC,4BAA4B;UAAAf,QAAA,EAAC;QAEnD;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eAChBpL,OAAA,CAACT,aAAa;QAAAuL,QAAA,gBACZ9K,OAAA,CAACL,MAAM;UAACoL,OAAO,EAAE9H,wBAAyB;UAAC6I,KAAK,EAAC,SAAS;UAAAhB,QAAA,EAAC;QAE3D;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTpL,OAAA,CAACL,MAAM;UAACoL,OAAO,EAAE7H,oBAAqB;UAAC4I,KAAK,EAAC,SAAS;UAACC,SAAS;UAAAjB,QAAA,EAAC;QAEjE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTpL,OAAA,CAACV,MAAM;MACLqM,IAAI,EAAEjK,cAAe;MACrB,mBAAgB,sBAAsB;MACtC,oBAAiB,4BAA4B;MAAAoJ,QAAA,gBAE7C9K,OAAA,CAACN,WAAW;QAACmM,EAAE,EAAC,sBAAsB;QAAAf,QAAA,EACnC;MAAiB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eACdpL,OAAA,CAACR,aAAa;QAAAsL,QAAA,eACZ9K,OAAA,CAACP,iBAAiB;UAACoM,EAAE,EAAC,4BAA4B;UAAAf,QAAA,EAAC;QAEnD;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eAChBpL,OAAA,CAACT,aAAa;QAAAuL,QAAA,gBACZ9K,OAAA,CAACL,MAAM;UAACoL,OAAO,EAAEzB,UAAW;UAACwC,KAAK,EAAC,SAAS;UAAAhB,QAAA,EAAC;QAE7C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTpL,OAAA,CAACL,MAAM;UAACoL,OAAO,EAAE7H,oBAAqB;UAAC4I,KAAK,EAAC,SAAS;UAACC,SAAS;UAAAjB,QAAA,EAAC;QAEjE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTpL,OAAA,CAACF,QAAQ;MACP6L,IAAI,EAAE/K,eAAgB;MACtBoL,gBAAgB,EAAE,IAAK;MACvBJ,OAAO,EAAErC,sBAAuB;MAChC0C,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAArB,QAAA,eAE3D9K,OAAA,CAACH,KAAK;QAAC+L,OAAO,EAAErC,sBAAuB;QAAC6C,QAAQ,EAAC,OAAO;QAACC,EAAE,EAAE;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAxB,QAAA,EAC5EpK;MAAW;QAAAuK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEV,CAAC;AAACjL,EAAA,CArnBIF,sBAAsB;AAAAsM,EAAA,GAAtBtM,sBAAsB;AAunB5B,eAAeA,sBAAsB;AAAC,IAAAsM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}