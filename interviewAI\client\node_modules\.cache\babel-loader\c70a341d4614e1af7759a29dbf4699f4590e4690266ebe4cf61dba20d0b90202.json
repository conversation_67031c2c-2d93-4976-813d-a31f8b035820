{"ast": null, "code": "// Simple utility to access environment variables\nconst env = {\n  OPENAI_API_KEY: process.env.REACT_APP_OPENAI_API_KEY,\n  GOOGLE_SPEECH_API_KEY: process.env.REACT_APP_GOOGLE_SPEECH_API_KEY,\n  DEEPGRAM_API_KEY: process.env.REACT_APP_DEEPGRAM_API_KEY,\n  REACT_APP_API_URL: process.env.REACT_APP_API_URL,\n  // Add other environment variables as needed\n  isDevelopment: process.env.NODE_ENV === 'development',\n  isProduction: process.env.NODE_ENV === 'production',\n  // Helper method to check if a key exists\n  hasKey: function (key) {\n    return !!this[key];\n  },\n  // Helper to check if Google Speech-to-Text is configured\n  hasGoogleSpeechAPI: function () {\n    return !!this.GOOGLE_SPEECH_API_KEY;\n  },\n  // Helper to check if Deepgram API is configured\n  hasDeepgramAPI: function () {\n    return !!this.DEEPGRAM_API_KEY;\n  },\n  // Helper to check if OpenAI API is configured\n  hasOpenAIAPI: function () {\n    return !!this.OPENAI_API_KEY;\n  },\n  // Get a sample API key for testing (only in development)\n  getSampleDeepgramKey: function () {\n    if (this.isDevelopment) {\n      return '3e94fc5a6e60d63146576d3bd188a8737cfeb360'; // This is a placeholder key for development only\n    }\n    return null;\n  }\n};\nexport default env;", "map": {"version": 3, "names": ["env", "OPENAI_API_KEY", "process", "REACT_APP_OPENAI_API_KEY", "GOOGLE_SPEECH_API_KEY", "REACT_APP_GOOGLE_SPEECH_API_KEY", "DEEPGRAM_API_KEY", "REACT_APP_DEEPGRAM_API_KEY", "REACT_APP_API_URL", "isDevelopment", "NODE_ENV", "isProduction", "<PERSON><PERSON><PERSON>", "key", "hasGoogleSpeechAPI", "hasDeepgramAPI", "hasOpenAIAPI", "getSampleDeepgramKey"], "sources": ["C:/Users/<USER>/Documents/augment-projects/interview-ai-app/interviewAI/client/src/utils/env.js"], "sourcesContent": ["// Simple utility to access environment variables\r\nconst env = {\r\n  OPENAI_API_KEY: process.env.REACT_APP_OPENAI_API_KEY,\r\n  GOOGLE_SPEECH_API_KEY: process.env.REACT_APP_GOOGLE_SPEECH_API_KEY,\r\n  DEEPGRAM_API_KEY: process.env.REACT_APP_DEEPGRAM_API_KEY,\r\n  REACT_APP_API_URL: process.env.REACT_APP_API_URL,\r\n\r\n  // Add other environment variables as needed\r\n  isDevelopment: process.env.NODE_ENV === 'development',\r\n  isProduction: process.env.NODE_ENV === 'production',\r\n\r\n  // Helper method to check if a key exists\r\n  hasKey: function(key) {\r\n    return !!this[key];\r\n  },\r\n\r\n  // Helper to check if Google Speech-to-Text is configured\r\n  hasGoogleSpeechAPI: function() {\r\n    return !!this.GOOGLE_SPEECH_API_KEY;\r\n  },\r\n\r\n  // Helper to check if Deepgram API is configured\r\n  hasDeepgramAPI: function() {\r\n    return !!this.DEEPGRAM_API_KEY;\r\n  },\r\n\r\n  // Helper to check if OpenAI API is configured\r\n  hasOpenAIAPI: function() {\r\n    return !!this.OPENAI_API_KEY;\r\n  },\r\n\r\n  // Get a sample API key for testing (only in development)\r\n  getSampleDeepgramKey: function() {\r\n    if (this.isDevelopment) {\r\n      return '3e94fc5a6e60d63146576d3bd188a8737cfeb360'; // This is a placeholder key for development only\r\n    }\r\n    return null;\r\n  }\r\n};\r\n\r\nexport default env;\r\n"], "mappings": "AAAA;AACA,MAAMA,GAAG,GAAG;EACVC,cAAc,EAAEC,OAAO,CAACF,GAAG,CAACG,wBAAwB;EACpDC,qBAAqB,EAAEF,OAAO,CAACF,GAAG,CAACK,+BAA+B;EAClEC,gBAAgB,EAAEJ,OAAO,CAACF,GAAG,CAACO,0BAA0B;EACxDC,iBAAiB,EAAEN,OAAO,CAACF,GAAG,CAACQ,iBAAiB;EAEhD;EACAC,aAAa,EAAEP,OAAO,CAACF,GAAG,CAACU,QAAQ,KAAK,aAAa;EACrDC,YAAY,EAAET,OAAO,CAACF,GAAG,CAACU,QAAQ,KAAK,YAAY;EAEnD;EACAE,MAAM,EAAE,SAAAA,CAASC,GAAG,EAAE;IACpB,OAAO,CAAC,CAAC,IAAI,CAACA,GAAG,CAAC;EACpB,CAAC;EAED;EACAC,kBAAkB,EAAE,SAAAA,CAAA,EAAW;IAC7B,OAAO,CAAC,CAAC,IAAI,CAACV,qBAAqB;EACrC,CAAC;EAED;EACAW,cAAc,EAAE,SAAAA,CAAA,EAAW;IACzB,OAAO,CAAC,CAAC,IAAI,CAACT,gBAAgB;EAChC,CAAC;EAED;EACAU,YAAY,EAAE,SAAAA,CAAA,EAAW;IACvB,OAAO,CAAC,CAAC,IAAI,CAACf,cAAc;EAC9B,CAAC;EAED;EACAgB,oBAAoB,EAAE,SAAAA,CAAA,EAAW;IAC/B,IAAI,IAAI,CAACR,aAAa,EAAE;MACtB,OAAO,0CAA0C,CAAC,CAAC;IACrD;IACA,OAAO,IAAI;EACb;AACF,CAAC;AAED,eAAeT,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}