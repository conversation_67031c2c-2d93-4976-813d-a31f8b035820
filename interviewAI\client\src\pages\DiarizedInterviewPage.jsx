import { useState, useRef, useCallback, useEffect } from 'react';
import DiarizedTranscription from '../components/DiarizedTranscription';
import './DiarizedInterviewPage.css';
import env from '../utils/env';

function DiarizedInterviewPage() {
  const [transcriptSegments, setTranscriptSegments] = useState([]);
  const [currentTranscript, setCurrentTranscript] = useState('');
  const [response, setResponse] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [autoSubmit, setAutoSubmit] = useState(true);

  // Timer for auto-submission
  const autoSubmitTimerRef = useRef(null);

  // Response area ref for scrolling
  const responseAreaRef = useRef(null);

  // Handle transcript changes from the DiarizedTranscription component
  const handleTranscriptChange = useCallback((transcript, segments) => {
    setCurrentTranscript(transcript);

    // Update segments if provided
    if (segments && segments.length > 0) {
      setTranscriptSegments(segments);
    }

    // Set up auto-submit timer
    if (autoSubmit && transcript.trim()) {
      if (autoSubmitTimerRef.current) {
        clearTimeout(autoSubmitTimerRef.current);
      }

      autoSubmitTimerRef.current = setTimeout(() => {
        // Only auto-submit if we have a non-empty transcript and we're not already loading
        if (transcript.trim() && !isLoading) {
          console.log("Auto-submitting transcript");
          handleSubmit(transcript);
        }
      }, 2000); // 2 second pause detection
    }
  }, [autoSubmit, isLoading]);

  // Handle manual submission
  const handleSubmit = useCallback(async (textToSubmit) => {
    const userText = textToSubmit || currentTranscript.trim();

    if (!userText) {
      console.warn("No transcript to send");
      return;
    }

    const apiKey = env.OPENAI_API_KEY;
    if (!apiKey) {
      alert("Please add your OpenAI API key to the .env file as REACT_APP_OPENAI_API_KEY and restart the development server.");
      return;
    }

    setIsLoading(true);

    try {
      console.log("Sending to GPT API:", userText);
      const response = await fetch("https://api.openai.com/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${apiKey}`
        },
        body: JSON.stringify({
          model: "gpt-4.1-nano",
          messages: [
            {
              role: "system",
              content: "You are Sensei, an AI interview assistant. Provide detailed, well-structured responses to interview questions. Format your answers with bullet points for clarity. Keep responses concise but comprehensive."
            },
            { role: "user", content: userText }
          ],
          stream: true
        })
      });

      if (!response.ok || !response.body) throw new Error("Failed to stream response.");

      const reader = response.body.getReader();
      const decoder = new TextDecoder("utf-8");

      let result = "";
      setResponse(''); // Clear previous response

      while (true) {
        const { value, done } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value, { stream: true });
        const lines = chunk.split("\n").filter(line => line.trim().startsWith("data:"));

        for (const line of lines) {
          const data = line.replace(/^data: /, '');
          if (data === "[DONE]") break;

          try {
            const json = JSON.parse(data);
            const content = json.choices?.[0]?.delta?.content;
            if (content) {
              result += content;
              setResponse(result);
            }
          } catch (e) {
            console.error("Error parsing JSON:", e);
          }
        }
      }
    } catch (error) {
      console.error("Streaming Error:", error);
      setResponse("Error occurred: " + error.message);
    } finally {
      setIsLoading(false);
    }
  }, [currentTranscript]);

  // Auto-scroll response area when content changes
  const scrollToBottom = () => {
    if (responseAreaRef.current) {
      responseAreaRef.current.scrollTop = responseAreaRef.current.scrollHeight;
    }
  };

  // Call scrollToBottom whenever response changes
  useEffect(() => {
    scrollToBottom();
  }, [response]);

  return (
    <div className="diarized-interview-page">
      <div className="page-header">
        <h1>Enhanced Interview with Speaker Diarization</h1>
        <p className="description">
          This page uses advanced audio analysis to separate different speakers in the conversation.
        </p>
      </div>

      <div className="interview-container">
        <div className="transcription-panel">
          <div className="panel-header">
            <h2>Live Transcription</h2>
            <div className="auto-submit-toggle">
              <label>
                <input
                  type="checkbox"
                  checked={autoSubmit}
                  onChange={() => setAutoSubmit(!autoSubmit)}
                />
                Auto-submit
              </label>
            </div>
          </div>

          <DiarizedTranscription onTranscriptChange={handleTranscriptChange} />

          <div className="manual-submit">
            <button
              className="submit-button"
              onClick={() => handleSubmit()}
              disabled={isLoading || !currentTranscript.trim()}
            >
              {isLoading ? 'Processing...' : 'Submit Transcript'}
            </button>
          </div>
        </div>

        <div className="response-panel">
          <div className="panel-header">
            <h2>AI Response</h2>
          </div>

          <div
            ref={responseAreaRef}
            className="response-content"
          >
            {response ? (
              <div className="response-message">
                <div className="message-text">{response}</div>
              </div>
            ) : (
              <div className="empty-state">AI responses will appear here</div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default DiarizedInterviewPage;
