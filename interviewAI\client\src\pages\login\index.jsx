import React, { useEffect } from "react";
import { useN<PERSON><PERSON>, Link } from "react-router-dom";
import { IconButton, InputAdornment, TextField } from "@mui/material";
import { Visibility, VisibilityOff } from "@mui/icons-material";
import HomeIcon from "@mui/icons-material/Home";

import { postApiCall } from "../../utils/Apicalls";
import "./login.css";

function LoginScreen() {
  const navigate = useNavigate();
  const [showPassword, setShowPassword] = React.useState(false);
  const [userName, setUserName] = React.useState("");
  const [password, setPassword] = React.useState("");

  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(() => {
    const userId = localStorage.getItem("userDetails");
    if (userId) {
      navigate("/home");
    }
  }, [navigate]);

  const handleTogglePassword = () => {
    setShowPassword((prev) => !prev);
  };

  const onSubmit = async () => {
    const result = await postApiCall(`interview-app/auth/login`, {
      username: userName,
      password: password,
    });
    if (result?.status && result?.result && result?.token) {
      localStorage.setItem("userDetails", JSON.stringify(result.result));
      localStorage.setItem("token", result.token);
      navigate("home");
    }
  };

  return (
    <>
      <div style={{ position: 'absolute', top: 20, left: 20, zIndex: 10 }}>
        <Link to="/">
          <HomeIcon style={{ fontSize: 32, color: '#1976d2' }} titleAccess="Home" />
        </Link>
      </div>
      <div className="login-container">
        <div className="screen">
          <div className="screen__content">
            <div className="login">
              <div className="login__field">
                <TextField
                  id="outlined-basic"
                  label="Username"
                  variant="outlined"
                  value={userName}
                  onChange={(text) => setUserName(text.target.value)}
                  placeholder="Enter username"
                  className="w-100"
                />
              </div>
              <div className="login__field">
                <TextField
                  id="outlined-basic"
                  label="Password"
                  variant="outlined"
                  value={password}
                  type={showPassword ? "text" : "password"}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton onClick={handleTogglePassword} edge="end">
                          {showPassword ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                  onChange={(text) => setPassword(text.target.value)}
                  className="w-100"
                />
              </div>
              <button className="button login__submit" onClick={onSubmit}>
                <span className="button__text">Log In Now</span>
                <i className="button__icon fas fa-chevron-right"></i>
              </button>
              <div style={{ marginTop: 16, textAlign: 'center' }}>
                <span>Don't have an account? </span>
                <a href="/signup/free" className="signup-link">Sign up</a>
              </div>
            </div>
            {/* <div className="social-login">
              <h3>log in via</h3>
              <div className="social-icons">
                <a href="#" className="social-login__icon fab fa-instagram"></a>
                <a href="#" className="social-login__icon fab fa-facebook"></a>
                <a href="#" className="social-login__icon fab fa-twitter"></a>
              </div>
            </div> */}
          </div>
          <div className="screen__background">
            <span className="screen__background__shape screen__background__shape4"></span>
            <span className="screen__background__shape screen__background__shape3"></span>
            <span className="screen__background__shape screen__background__shape2"></span>
            <span className="screen__background__shape screen__background__shape1"></span>
          </div>
        </div>
      </div>
    </>
  );
}
export default LoginScreen;
