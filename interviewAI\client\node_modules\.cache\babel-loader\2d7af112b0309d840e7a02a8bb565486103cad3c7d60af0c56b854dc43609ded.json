{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\interview-ai-app\\\\interviewAI\\\\client\\\\src\\\\components\\\\SpeechToText.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect, useCallback } from 'react';\nimport './SpeechToText.css';\nimport env from '../utils/env';\nimport MicIcon from '@mui/icons-material/Mic';\nimport StopIcon from '@mui/icons-material/Stop';\nimport SendIcon from '@mui/icons-material/Send';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport AccessTimeIcon from '@mui/icons-material/AccessTime';\nimport CloseIcon from '@mui/icons-material/Close';\nimport Dialog from '@mui/material/Dialog';\nimport DialogActions from '@mui/material/DialogActions';\nimport DialogContent from '@mui/material/DialogContent';\nimport DialogContentText from '@mui/material/DialogContentText';\nimport DialogTitle from '@mui/material/DialogTitle';\nimport Button from '@mui/material/Button';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction SpeechToText({\n  onBack\n}) {\n  _s();\n  const [isListening, setIsListening] = useState(false);\n  const [transcript, setTranscript] = useState('');\n  const [response, setResponse] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n\n  // Session timer state (15 minutes = 900 seconds)\n  const SESSION_DURATION = 300; // 15 minutes in seconds\n  const WARNING_TIME = 60; // Show warning when 60 seconds remain\n  const [sessionTimeRemaining, setSessionTimeRemaining] = useState(SESSION_DURATION);\n  const [showPaymentDialog, setShowPaymentDialog] = useState(false);\n  const [sessionExpired, setSessionExpired] = useState(false);\n\n  // Session timer reference\n  const sessionTimerRef = useRef(null);\n\n  // Speech recognition and text area references\n  const recognitionRef = useRef(null);\n  const transcriptAreaRef = useRef(null);\n  const responseAreaRef = useRef(null);\n\n  // Format session time remaining (MM:SS)\n  const formatSessionTime = useCallback(totalSeconds => {\n    const minutes = Math.floor(totalSeconds / 60);\n    const seconds = totalSeconds % 60;\n    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\n  }, []);\n\n  // Start the session timer\n  const startSessionTimer = useCallback(() => {\n    // Reset session timer when starting\n    setSessionTimeRemaining(SESSION_DURATION);\n    setSessionExpired(false);\n\n    // Clear any existing interval\n    if (sessionTimerRef.current) {\n      clearInterval(sessionTimerRef.current);\n    }\n\n    // Start a new interval that counts down\n    sessionTimerRef.current = setInterval(() => {\n      setSessionTimeRemaining(prev => {\n        const newTime = prev - 1;\n\n        // Show payment dialog when 1 minute remains\n        if (newTime === WARNING_TIME) {\n          setShowPaymentDialog(true);\n        }\n\n        // Session expired\n        if (newTime <= 0) {\n          clearInterval(sessionTimerRef.current);\n          sessionTimerRef.current = null;\n          setSessionExpired(true);\n          return 0;\n        }\n        return newTime;\n      });\n    }, 1000);\n  }, [SESSION_DURATION, WARNING_TIME]);\n\n  // Handle payment dialog close\n  const handlePaymentDialogClose = useCallback(() => {\n    setShowPaymentDialog(false);\n  }, []);\n\n  // Handle payment confirmation\n  const handlePaymentConfirm = useCallback(() => {\n    // Instead of resetting timer, navigate to plans page\n    window.location.href = '/plans';\n  }, []);\n\n  // Start session timer on component mount\n  useEffect(() => {\n    startSessionTimer();\n\n    // Clean up session timer on unmount\n    return () => {\n      if (sessionTimerRef.current) {\n        clearInterval(sessionTimerRef.current);\n      }\n    };\n  }, [startSessionTimer]);\n\n  // Store finalTranscript in a ref to access it from event handlers\n  const finalTranscriptRef = useRef(\"\");\n  useEffect(() => {\n    // Initialize speech recognition\n    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n    if (SpeechRecognition) {\n      recognitionRef.current = new SpeechRecognition();\n      recognitionRef.current.lang = 'en-US';\n      recognitionRef.current.interimResults = true;\n      recognitionRef.current.continuous = true;\n\n      // Initialize finalTranscript from the current transcript value\n      finalTranscriptRef.current = transcript;\n      recognitionRef.current.onresult = event => {\n        let interimTranscript = \"\";\n        for (let i = event.resultIndex; i < event.results.length; i++) {\n          const transcript = event.results[i][0].transcript;\n          if (event.results[i].isFinal) {\n            finalTranscriptRef.current += transcript + \" \";\n          } else {\n            interimTranscript += transcript;\n          }\n        }\n        setTranscript(finalTranscriptRef.current + interimTranscript);\n      };\n      recognitionRef.current.onerror = event => {\n        console.error(\"Speech recognition error\", event.error);\n        alert(\"Error occurred: \" + event.error);\n        setIsListening(false);\n      };\n    } else {\n      alert(\"Your browser doesn't support speech recognition. Try Chrome or Edge.\");\n    }\n\n    // Cleanup function\n    return () => {\n      if (recognitionRef.current) {\n        recognitionRef.current.stop();\n      }\n    };\n  }, [transcript]);\n  useEffect(() => {\n    // Auto-scroll transcript area when content changes\n    if (transcriptAreaRef.current) {\n      transcriptAreaRef.current.scrollTop = transcriptAreaRef.current.scrollHeight;\n    }\n  }, [transcript]);\n  useEffect(() => {\n    // Auto-scroll response area when content changes\n    if (responseAreaRef.current) {\n      responseAreaRef.current.scrollTop = responseAreaRef.current.scrollHeight;\n    }\n  }, [response]);\n  const startListening = () => {\n    if (recognitionRef.current && !isListening && !sessionExpired) {\n      try {\n        // Clear the transcript when starting a new recording\n        setTranscript('');\n\n        // Reset the finalTranscript reference\n        finalTranscriptRef.current = \"\";\n\n        // Reinitialize speech recognition to clear its state\n        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n        recognitionRef.current = new SpeechRecognition();\n        recognitionRef.current.lang = 'en-US';\n        recognitionRef.current.interimResults = true;\n        recognitionRef.current.continuous = true;\n\n        // Set up event handlers with the reset finalTranscriptRef\n        recognitionRef.current.onresult = event => {\n          let interimTranscript = \"\";\n          for (let i = event.resultIndex; i < event.results.length; i++) {\n            const transcript = event.results[i][0].transcript;\n            if (event.results[i].isFinal) {\n              finalTranscriptRef.current += transcript + \" \";\n            } else {\n              interimTranscript += transcript;\n            }\n          }\n          setTranscript(finalTranscriptRef.current + interimTranscript);\n        };\n        recognitionRef.current.onerror = event => {\n          console.error(\"Speech recognition error\", event.error);\n          alert(\"Error occurred: \" + event.error);\n          setIsListening(false);\n        };\n\n        // Start the recognition\n        recognitionRef.current.start();\n        setIsListening(true);\n      } catch (error) {\n        console.error(\"Speech recognition error:\", error);\n        // If recognition is already running, stop it first then restart\n        if (error.message.includes(\"already started\")) {\n          recognitionRef.current.stop();\n          setTimeout(() => {\n            startListening(); // Call the function recursively to restart properly\n          }, 100);\n        }\n      }\n    }\n  };\n  const stopListening = () => {\n    if (recognitionRef.current && isListening) {\n      try {\n        recognitionRef.current.stop();\n        setIsListening(false);\n\n        // When stopping, make sure the finalTranscriptRef matches the current transcript\n        // This ensures consistency if the user has manually edited the text\n        finalTranscriptRef.current = transcript;\n      } catch (error) {\n        console.error(\"Speech recognition error:\", error);\n      }\n    }\n  };\n  const sendToGPT = async () => {\n    const apiKey = env.OPENAI_API_KEY;\n    const userText = transcript.trim();\n    console.log(\"Environment:\", process.env.NODE_ENV);\n    console.log(\"API Key available:\", apiKey ? \"Yes\" : \"No\");\n    if (!apiKey) {\n      alert(\"Please add your OpenAI API key to the .env file as REACT_APP_OPENAI_API_KEY and restart the development server.\");\n      return;\n    }\n    if (!userText) {\n      alert(\"Please record or enter some text to send to GPT.\");\n      return;\n    }\n    const prompt = `In 5 lines, give only the definition and a simple example. ${userText}`;\n    setIsLoading(true);\n    setResponse('');\n    try {\n      const response = await fetch(\"https://api.openai.com/v1/chat/completions\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          \"Authorization\": `Bearer ${apiKey}`\n        },\n        body: JSON.stringify({\n          model: \"gpt-4.1-nano\",\n          messages: [{\n            role: \"user\",\n            content: prompt\n          }],\n          stream: true\n        })\n      });\n      if (!response.ok || !response.body) throw new Error(\"Failed to stream response.\");\n      const reader = response.body.getReader();\n      const decoder = new TextDecoder(\"utf-8\");\n      let result = \"\";\n      while (true) {\n        const {\n          value,\n          done\n        } = await reader.read();\n        if (done) break;\n        const chunk = decoder.decode(value, {\n          stream: true\n        });\n        const lines = chunk.split(\"\\n\").filter(line => line.trim().startsWith(\"data:\"));\n        for (const line of lines) {\n          const data = line.replace(/^data: /, '');\n          if (data === \"[DONE]\") break;\n          try {\n            var _json$choices, _json$choices$, _json$choices$$delta;\n            const json = JSON.parse(data);\n            const content = (_json$choices = json.choices) === null || _json$choices === void 0 ? void 0 : (_json$choices$ = _json$choices[0]) === null || _json$choices$ === void 0 ? void 0 : (_json$choices$$delta = _json$choices$.delta) === null || _json$choices$$delta === void 0 ? void 0 : _json$choices$$delta.content;\n            if (content) {\n              result += content;\n              setResponse(result);\n            }\n          } catch (e) {\n            console.error(\"Error parsing JSON:\", e);\n          }\n        }\n      }\n    } catch (error) {\n      console.error(\"Streaming Error:\", error);\n      setResponse(\"Error occurred: \" + error.message);\n    } finally {\n      if (isListening) {\n        stopListening();\n      }\n      setIsLoading(false);\n\n      // Clear the transcript after getting the answer\n      setTranscript('');\n      // Also reset the finalTranscript reference\n      finalTranscriptRef.current = \"\";\n    }\n  };\n  const clearTranscript = () => {\n    // Clear the transcript state\n    setTranscript('');\n\n    // Reset the finalTranscript reference\n    finalTranscriptRef.current = \"\";\n\n    // Reset the speech recognition if it's active\n    if (recognitionRef.current) {\n      // We need to stop and restart recognition to clear its internal state\n      const wasListening = isListening;\n      try {\n        // Only stop if currently listening\n        if (wasListening) {\n          recognitionRef.current.stop();\n          setIsListening(false);\n        }\n\n        // Wait a moment to ensure recognition has fully stopped\n        setTimeout(() => {\n          // Reinitialize speech recognition to clear its state\n          const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n          recognitionRef.current = new SpeechRecognition();\n          recognitionRef.current.lang = 'en-US';\n          recognitionRef.current.interimResults = true;\n          recognitionRef.current.continuous = true;\n\n          // Set up event handlers with a fresh finalTranscript\n          recognitionRef.current.onresult = event => {\n            let interimTranscript = \"\";\n            for (let i = event.resultIndex; i < event.results.length; i++) {\n              const transcript = event.results[i][0].transcript;\n              if (event.results[i].isFinal) {\n                finalTranscriptRef.current += transcript + \" \";\n              } else {\n                interimTranscript += transcript;\n              }\n            }\n            setTranscript(finalTranscriptRef.current + interimTranscript);\n          };\n          recognitionRef.current.onerror = event => {\n            console.error(\"Speech recognition error\", event.error);\n            setIsListening(false);\n          };\n\n          // Restart recognition if it was active\n          if (wasListening) {\n            try {\n              recognitionRef.current.start();\n              setIsListening(true);\n            } catch (error) {\n              console.error(\"Failed to restart recognition:\", error);\n            }\n          }\n        }, 200); // Add a small delay to ensure recognition has fully stopped\n      } catch (error) {\n        console.error(\"Error during transcript clearing:\", error);\n      }\n    }\n  };\n  const clearResponse = () => {\n    setResponse('');\n  };\n\n  // Handle exit button click\n  const handleExit = useCallback(() => {\n    // Use the onBack prop if provided, otherwise navigate to home\n    if (onBack) {\n      onBack();\n    } else {\n      window.location.href = '/home';\n    }\n  }, [onBack]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"speech-to-text\",\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"exit-button\",\n      onClick: handleExit,\n      title: \"Exit\",\n      children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 397,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 396,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"timer-container\",\n      children: [/*#__PURE__*/_jsxDEV(AccessTimeIcon, {\n        className: \"timer-icon\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 402,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"timer-display session-timer\",\n        children: formatSessionTime(sessionTimeRemaining)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 403,\n        columnNumber: 9\n      }, this), isListening && /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"listening-indicator\",\n        children: \"Recording...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 406,\n        columnNumber: 25\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 401,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"answer-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"section-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"AI Response\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"clear-button side-button\",\n            onClick: clearResponse,\n            disabled: !response || sessionExpired,\n            title: \"Clear Response\",\n            children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 414,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 412,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n          ref: responseAreaRef,\n          className: \"response-area\",\n          value: response,\n          readOnly: true,\n          placeholder: \"AI answer will appear here...\",\n          disabled: sessionExpired\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 423,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 411,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"question-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"section-header\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"control-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: `mic-button ${isListening ? 'listening' : ''}`,\n              onClick: isListening ? stopListening : startListening,\n              title: isListening ? \"Stop Recording\" : \"Start Recording\",\n              disabled: sessionExpired,\n              children: isListening ? /*#__PURE__*/_jsxDEV(StopIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 443,\n                columnNumber: 32\n              }, this) : /*#__PURE__*/_jsxDEV(MicIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 443,\n                columnNumber: 47\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 437,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"send-button\",\n              onClick: sendToGPT,\n              disabled: isLoading || !transcript.trim() || sessionExpired,\n              title: \"Get Answer\",\n              children: isLoading ? \"...\" : /*#__PURE__*/_jsxDEV(SendIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 452,\n                columnNumber: 38\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 446,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"clear-button side-button\",\n              onClick: clearTranscript,\n              disabled: !transcript || sessionExpired,\n              title: \"Clear Question\",\n              children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n                fontSize: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 461,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 455,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 436,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 435,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n          ref: transcriptAreaRef,\n          className: \"transcript-area\",\n          value: transcript,\n          onChange: e => setTranscript(e.target.value),\n          placeholder: sessionExpired ? \"Session expired. Please make a payment to continue.\" : \"Type or record your question here...\",\n          disabled: sessionExpired\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 465,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 434,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 409,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showPaymentDialog,\n      onClose: handlePaymentDialogClose,\n      \"aria-labelledby\": \"payment-dialog-title\",\n      \"aria-describedby\": \"payment-dialog-description\",\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        id: \"payment-dialog-title\",\n        children: \"Session Expiring Soon\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 483,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(DialogContentText, {\n          id: \"payment-dialog-description\",\n          children: \"Your session will expire in one minute. Would you like to make a payment to extend your session?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 487,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 486,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handlePaymentDialogClose,\n          color: \"primary\",\n          children: \"Not Now\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 492,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handlePaymentConfirm,\n          color: \"primary\",\n          autoFocus: true,\n          children: \"Make Payment\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 495,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 491,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 477,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: sessionExpired,\n      \"aria-labelledby\": \"expired-dialog-title\",\n      \"aria-describedby\": \"expired-dialog-description\",\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        id: \"expired-dialog-title\",\n        children: \"Session Expired\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 507,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(DialogContentText, {\n          id: \"expired-dialog-description\",\n          children: \"Your session has expired. Please make a payment to continue using the service.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 511,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 510,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleExit,\n          color: \"primary\",\n          children: \"Exit\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 516,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handlePaymentConfirm,\n          color: \"primary\",\n          autoFocus: true,\n          children: \"Make Payment\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 519,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 515,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 502,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 394,\n    columnNumber: 5\n  }, this);\n}\n_s(SpeechToText, \"9Vh5dfGqEDbPL8H5vtijjy1X250=\");\n_c = SpeechToText;\nexport default SpeechToText;\nvar _c;\n$RefreshReg$(_c, \"SpeechToText\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "useCallback", "env", "MicIcon", "StopIcon", "SendIcon", "DeleteIcon", "AccessTimeIcon", "CloseIcon", "Dialog", "DialogActions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogContentText", "DialogTitle", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "SpeechToText", "onBack", "_s", "isListening", "setIsListening", "transcript", "setTranscript", "response", "setResponse", "isLoading", "setIsLoading", "SESSION_DURATION", "WARNING_TIME", "sessionTimeRemaining", "setSessionTimeRemaining", "showPaymentDialog", "setShowPaymentDialog", "sessionExpired", "setSessionExpired", "sessionTimerRef", "recognitionRef", "transcriptAreaRef", "responseAreaRef", "formatSessionTime", "totalSeconds", "minutes", "Math", "floor", "seconds", "toString", "padStart", "startSessionTimer", "current", "clearInterval", "setInterval", "prev", "newTime", "handlePaymentDialogClose", "handlePaymentConfirm", "window", "location", "href", "finalTranscriptRef", "SpeechRecognition", "webkitSpeechRecognition", "lang", "interimResults", "continuous", "on<PERSON>ult", "event", "interimTranscript", "i", "resultIndex", "results", "length", "isFinal", "onerror", "console", "error", "alert", "stop", "scrollTop", "scrollHeight", "startListening", "start", "message", "includes", "setTimeout", "stopListening", "sendToGPT", "<PERSON><PERSON><PERSON><PERSON>", "OPENAI_API_KEY", "userText", "trim", "log", "process", "NODE_ENV", "prompt", "fetch", "method", "headers", "body", "JSON", "stringify", "model", "messages", "role", "content", "stream", "ok", "Error", "reader", "<PERSON><PERSON><PERSON><PERSON>", "decoder", "TextDecoder", "result", "value", "done", "read", "chunk", "decode", "lines", "split", "filter", "line", "startsWith", "data", "replace", "_json$choices", "_json$choices$", "_json$choices$$delta", "json", "parse", "choices", "delta", "e", "clearTranscript", "wasListening", "clearResponse", "handleExit", "className", "children", "onClick", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "disabled", "fontSize", "ref", "readOnly", "placeholder", "onChange", "target", "open", "onClose", "id", "color", "autoFocus", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/interview-ai-app/interviewAI/client/src/components/SpeechToText.jsx"], "sourcesContent": ["import React, { useState, useRef, useEffect, useCallback } from 'react';\r\nimport './SpeechToText.css';\r\nimport env from '../utils/env';\r\nimport MicIcon from '@mui/icons-material/Mic';\r\nimport StopIcon from '@mui/icons-material/Stop';\r\nimport SendIcon from '@mui/icons-material/Send';\r\nimport DeleteIcon from '@mui/icons-material/Delete';\r\nimport AccessTimeIcon from '@mui/icons-material/AccessTime';\r\nimport CloseIcon from '@mui/icons-material/Close';\r\nimport Dialog from '@mui/material/Dialog';\r\nimport DialogActions from '@mui/material/DialogActions';\r\nimport DialogContent from '@mui/material/DialogContent';\r\nimport DialogContentText from '@mui/material/DialogContentText';\r\nimport DialogTitle from '@mui/material/DialogTitle';\r\nimport Button from '@mui/material/Button';\r\n\r\nfunction SpeechToText({ onBack }) {\r\n  const [isListening, setIsListening] = useState(false);\r\n  const [transcript, setTranscript] = useState('');\r\n  const [response, setResponse] = useState('');\r\n  const [isLoading, setIsLoading] = useState(false);\r\n\r\n  // Session timer state (15 minutes = 900 seconds)\r\n  const SESSION_DURATION = 300; // 15 minutes in seconds\r\n  const WARNING_TIME = 60; // Show warning when 60 seconds remain\r\n  const [sessionTimeRemaining, setSessionTimeRemaining] = useState(SESSION_DURATION);\r\n  const [showPaymentDialog, setShowPaymentDialog] = useState(false);\r\n  const [sessionExpired, setSessionExpired] = useState(false);\r\n\r\n  // Session timer reference\r\n  const sessionTimerRef = useRef(null);\r\n\r\n  // Speech recognition and text area references\r\n  const recognitionRef = useRef(null);\r\n  const transcriptAreaRef = useRef(null);\r\n  const responseAreaRef = useRef(null);\r\n\r\n  // Format session time remaining (MM:SS)\r\n  const formatSessionTime = useCallback((totalSeconds) => {\r\n    const minutes = Math.floor(totalSeconds / 60);\r\n    const seconds = totalSeconds % 60;\r\n    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\r\n  }, []);\r\n\r\n  // Start the session timer\r\n  const startSessionTimer = useCallback(() => {\r\n    // Reset session timer when starting\r\n    setSessionTimeRemaining(SESSION_DURATION);\r\n    setSessionExpired(false);\r\n\r\n    // Clear any existing interval\r\n    if (sessionTimerRef.current) {\r\n      clearInterval(sessionTimerRef.current);\r\n    }\r\n\r\n    // Start a new interval that counts down\r\n    sessionTimerRef.current = setInterval(() => {\r\n      setSessionTimeRemaining(prev => {\r\n        const newTime = prev - 1;\r\n\r\n        // Show payment dialog when 1 minute remains\r\n        if (newTime === WARNING_TIME) {\r\n          setShowPaymentDialog(true);\r\n        }\r\n\r\n        // Session expired\r\n        if (newTime <= 0) {\r\n          clearInterval(sessionTimerRef.current);\r\n          sessionTimerRef.current = null;\r\n          setSessionExpired(true);\r\n          return 0;\r\n        }\r\n\r\n        return newTime;\r\n      });\r\n    }, 1000);\r\n  }, [SESSION_DURATION, WARNING_TIME]);\r\n\r\n  // Handle payment dialog close\r\n  const handlePaymentDialogClose = useCallback(() => {\r\n    setShowPaymentDialog(false);\r\n  }, []);\r\n\r\n  // Handle payment confirmation\r\n  const handlePaymentConfirm = useCallback(() => {\r\n    // Instead of resetting timer, navigate to plans page\r\n    window.location.href = '/plans';\r\n  }, []);\r\n\r\n  // Start session timer on component mount\r\n  useEffect(() => {\r\n    startSessionTimer();\r\n\r\n    // Clean up session timer on unmount\r\n    return () => {\r\n      if (sessionTimerRef.current) {\r\n        clearInterval(sessionTimerRef.current);\r\n      }\r\n    };\r\n  }, [startSessionTimer]);\r\n\r\n  // Store finalTranscript in a ref to access it from event handlers\r\n  const finalTranscriptRef = useRef(\"\");\r\n\r\n  useEffect(() => {\r\n    // Initialize speech recognition\r\n    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\r\n    if (SpeechRecognition) {\r\n      recognitionRef.current = new SpeechRecognition();\r\n      recognitionRef.current.lang = 'en-US';\r\n      recognitionRef.current.interimResults = true;\r\n      recognitionRef.current.continuous = true;\r\n\r\n      // Initialize finalTranscript from the current transcript value\r\n      finalTranscriptRef.current = transcript;\r\n\r\n      recognitionRef.current.onresult = (event) => {\r\n        let interimTranscript = \"\";\r\n\r\n        for (let i = event.resultIndex; i < event.results.length; i++) {\r\n          const transcript = event.results[i][0].transcript;\r\n          if (event.results[i].isFinal) {\r\n            finalTranscriptRef.current += transcript + \" \";\r\n          } else {\r\n            interimTranscript += transcript;\r\n          }\r\n        }\r\n\r\n        setTranscript(finalTranscriptRef.current + interimTranscript);\r\n      };\r\n\r\n      recognitionRef.current.onerror = (event) => {\r\n        console.error(\"Speech recognition error\", event.error);\r\n        alert(\"Error occurred: \" + event.error);\r\n        setIsListening(false);\r\n      };\r\n    } else {\r\n      alert(\"Your browser doesn't support speech recognition. Try Chrome or Edge.\");\r\n    }\r\n\r\n    // Cleanup function\r\n    return () => {\r\n      if (recognitionRef.current) {\r\n        recognitionRef.current.stop();\r\n      }\r\n    };\r\n  }, [transcript]);\r\n\r\n  useEffect(() => {\r\n    // Auto-scroll transcript area when content changes\r\n    if (transcriptAreaRef.current) {\r\n      transcriptAreaRef.current.scrollTop = transcriptAreaRef.current.scrollHeight;\r\n    }\r\n  }, [transcript]);\r\n\r\n  useEffect(() => {\r\n    // Auto-scroll response area when content changes\r\n    if (responseAreaRef.current) {\r\n      responseAreaRef.current.scrollTop = responseAreaRef.current.scrollHeight;\r\n    }\r\n  }, [response]);\r\n\r\n  const startListening = () => {\r\n    if (recognitionRef.current && !isListening && !sessionExpired) {\r\n      try {\r\n        // Clear the transcript when starting a new recording\r\n        setTranscript('');\r\n\r\n        // Reset the finalTranscript reference\r\n        finalTranscriptRef.current = \"\";\r\n\r\n        // Reinitialize speech recognition to clear its state\r\n        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\r\n        recognitionRef.current = new SpeechRecognition();\r\n        recognitionRef.current.lang = 'en-US';\r\n        recognitionRef.current.interimResults = true;\r\n        recognitionRef.current.continuous = true;\r\n\r\n        // Set up event handlers with the reset finalTranscriptRef\r\n        recognitionRef.current.onresult = (event) => {\r\n          let interimTranscript = \"\";\r\n\r\n          for (let i = event.resultIndex; i < event.results.length; i++) {\r\n            const transcript = event.results[i][0].transcript;\r\n            if (event.results[i].isFinal) {\r\n              finalTranscriptRef.current += transcript + \" \";\r\n            } else {\r\n              interimTranscript += transcript;\r\n            }\r\n          }\r\n\r\n          setTranscript(finalTranscriptRef.current + interimTranscript);\r\n        };\r\n\r\n        recognitionRef.current.onerror = (event) => {\r\n          console.error(\"Speech recognition error\", event.error);\r\n          alert(\"Error occurred: \" + event.error);\r\n          setIsListening(false);\r\n        };\r\n\r\n        // Start the recognition\r\n        recognitionRef.current.start();\r\n        setIsListening(true);\r\n      } catch (error) {\r\n        console.error(\"Speech recognition error:\", error);\r\n        // If recognition is already running, stop it first then restart\r\n        if (error.message.includes(\"already started\")) {\r\n          recognitionRef.current.stop();\r\n          setTimeout(() => {\r\n            startListening(); // Call the function recursively to restart properly\r\n          }, 100);\r\n        }\r\n      }\r\n    }\r\n  };\r\n\r\n  const stopListening = () => {\r\n    if (recognitionRef.current && isListening) {\r\n      try {\r\n        recognitionRef.current.stop();\r\n        setIsListening(false);\r\n\r\n        // When stopping, make sure the finalTranscriptRef matches the current transcript\r\n        // This ensures consistency if the user has manually edited the text\r\n        finalTranscriptRef.current = transcript;\r\n      } catch (error) {\r\n        console.error(\"Speech recognition error:\", error);\r\n      }\r\n    }\r\n  };\r\n\r\n  const sendToGPT = async () => {\r\n    const apiKey = env.OPENAI_API_KEY;\r\n    const userText = transcript.trim();\r\n\r\n    console.log(\"Environment:\", process.env.NODE_ENV);\r\n    console.log(\"API Key available:\", apiKey ? \"Yes\" : \"No\");\r\n\r\n    if (!apiKey) {\r\n      alert(\"Please add your OpenAI API key to the .env file as REACT_APP_OPENAI_API_KEY and restart the development server.\");\r\n      return;\r\n    }\r\n\r\n    if (!userText) {\r\n      alert(\"Please record or enter some text to send to GPT.\");\r\n      return;\r\n    }\r\n    const prompt = `In 5 lines, give only the definition and a simple example. ${userText}`;\r\n    setIsLoading(true);\r\n    setResponse('');\r\n\r\n    try {\r\n      const response = await fetch(\"https://api.openai.com/v1/chat/completions\", {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n          \"Authorization\": `Bearer ${apiKey}`\r\n        },\r\n        body: JSON.stringify({\r\n          model: \"gpt-4.1-nano\",\r\n          messages: [\r\n            { role: \"user\", content: prompt }\r\n          ],\r\n          stream: true\r\n        })\r\n      });\r\n\r\n      if (!response.ok || !response.body) throw new Error(\"Failed to stream response.\");\r\n\r\n      const reader = response.body.getReader();\r\n      const decoder = new TextDecoder(\"utf-8\");\r\n\r\n      let result = \"\";\r\n\r\n      while (true) {\r\n        const { value, done } = await reader.read();\r\n        if (done) break;\r\n\r\n        const chunk = decoder.decode(value, { stream: true });\r\n        const lines = chunk.split(\"\\n\").filter(line => line.trim().startsWith(\"data:\"));\r\n\r\n        for (const line of lines) {\r\n          const data = line.replace(/^data: /, '');\r\n          if (data === \"[DONE]\") break;\r\n\r\n          try {\r\n            const json = JSON.parse(data);\r\n            const content = json.choices?.[0]?.delta?.content;\r\n            if (content) {\r\n              result += content;\r\n              setResponse(result);\r\n            }\r\n          } catch (e) {\r\n            console.error(\"Error parsing JSON:\", e);\r\n          }\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Streaming Error:\", error);\r\n      setResponse(\"Error occurred: \" + error.message);\r\n    } finally {\r\n      if (isListening) {\r\n        stopListening();\r\n      }\r\n      setIsLoading(false);\r\n\r\n      // Clear the transcript after getting the answer\r\n      setTranscript('');\r\n      // Also reset the finalTranscript reference\r\n      finalTranscriptRef.current = \"\";\r\n    }\r\n  };\r\n\r\n  const clearTranscript = () => {\r\n    // Clear the transcript state\r\n    setTranscript('');\r\n\r\n    // Reset the finalTranscript reference\r\n    finalTranscriptRef.current = \"\";\r\n\r\n    // Reset the speech recognition if it's active\r\n    if (recognitionRef.current) {\r\n      // We need to stop and restart recognition to clear its internal state\r\n      const wasListening = isListening;\r\n\r\n      try {\r\n        // Only stop if currently listening\r\n        if (wasListening) {\r\n          recognitionRef.current.stop();\r\n          setIsListening(false);\r\n        }\r\n\r\n        // Wait a moment to ensure recognition has fully stopped\r\n        setTimeout(() => {\r\n          // Reinitialize speech recognition to clear its state\r\n          const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\r\n          recognitionRef.current = new SpeechRecognition();\r\n          recognitionRef.current.lang = 'en-US';\r\n          recognitionRef.current.interimResults = true;\r\n          recognitionRef.current.continuous = true;\r\n\r\n          // Set up event handlers with a fresh finalTranscript\r\n          recognitionRef.current.onresult = (event) => {\r\n            let interimTranscript = \"\";\r\n\r\n            for (let i = event.resultIndex; i < event.results.length; i++) {\r\n              const transcript = event.results[i][0].transcript;\r\n              if (event.results[i].isFinal) {\r\n                finalTranscriptRef.current += transcript + \" \";\r\n              } else {\r\n                interimTranscript += transcript;\r\n              }\r\n            }\r\n\r\n            setTranscript(finalTranscriptRef.current + interimTranscript);\r\n          };\r\n\r\n          recognitionRef.current.onerror = (event) => {\r\n            console.error(\"Speech recognition error\", event.error);\r\n            setIsListening(false);\r\n          };\r\n\r\n          // Restart recognition if it was active\r\n          if (wasListening) {\r\n            try {\r\n              recognitionRef.current.start();\r\n              setIsListening(true);\r\n            } catch (error) {\r\n              console.error(\"Failed to restart recognition:\", error);\r\n            }\r\n          }\r\n        }, 200); // Add a small delay to ensure recognition has fully stopped\r\n      } catch (error) {\r\n        console.error(\"Error during transcript clearing:\", error);\r\n      }\r\n    }\r\n  };\r\n\r\n  const clearResponse = () => {\r\n    setResponse('');\r\n  };\r\n\r\n  // Handle exit button click\r\n  const handleExit = useCallback(() => {\r\n    // Use the onBack prop if provided, otherwise navigate to home\r\n    if (onBack) {\r\n      onBack();\r\n    } else {\r\n      window.location.href = '/home';\r\n    }\r\n  }, [onBack]);\r\n\r\n  return (\r\n    <div className=\"speech-to-text\">\r\n      {/* Exit button in top right corner */}\r\n      <button className=\"exit-button\" onClick={handleExit} title=\"Exit\">\r\n        <CloseIcon />\r\n      </button>\r\n\r\n      {/* Session timer display at the top right corner */}\r\n      <div className=\"timer-container\">\r\n        <AccessTimeIcon className=\"timer-icon\" />\r\n        <span className=\"timer-display session-timer\">\r\n          {formatSessionTime(sessionTimeRemaining)}\r\n        </span>\r\n        {isListening && <span className=\"listening-indicator\">Recording...</span>}\r\n      </div>\r\n\r\n      <div className=\"flex-container\">\r\n        {/* Answer section - larger and with side clear button */}\r\n        <div className=\"answer-container\">\r\n          <div className=\"section-header\">\r\n            <h3>AI Response</h3>\r\n            <button\r\n              className=\"clear-button side-button\"\r\n              onClick={clearResponse}\r\n              disabled={!response || sessionExpired}\r\n              title=\"Clear Response\"\r\n            >\r\n              <DeleteIcon fontSize=\"small\" />\r\n            </button>\r\n          </div>\r\n          <textarea\r\n            ref={responseAreaRef}\r\n            className=\"response-area\"\r\n            value={response}\r\n            readOnly\r\n            placeholder=\"AI answer will appear here...\"\r\n            disabled={sessionExpired}\r\n          />\r\n        </div>\r\n\r\n        {/* Question section - smaller with side controls */}\r\n        <div className=\"question-container\">\r\n          <div className=\"section-header\">\r\n            <div className=\"control-group\">\r\n              <button\r\n                className={`mic-button ${isListening ? 'listening' : ''}`}\r\n                onClick={isListening ? stopListening : startListening}\r\n                title={isListening ? \"Stop Recording\" : \"Start Recording\"}\r\n                disabled={sessionExpired}\r\n              >\r\n                {isListening ? <StopIcon /> : <MicIcon />}\r\n              </button>\r\n\r\n              <button\r\n                className=\"send-button\"\r\n                onClick={sendToGPT}\r\n                disabled={isLoading || !transcript.trim() || sessionExpired}\r\n                title=\"Get Answer\"\r\n              >\r\n                {isLoading ? \"...\" : <SendIcon />}\r\n              </button>\r\n\r\n              <button\r\n                className=\"clear-button side-button\"\r\n                onClick={clearTranscript}\r\n                disabled={!transcript || sessionExpired}\r\n                title=\"Clear Question\"\r\n              >\r\n                <DeleteIcon fontSize=\"small\" />\r\n              </button>\r\n            </div>\r\n          </div>\r\n          <textarea\r\n            ref={transcriptAreaRef}\r\n            className=\"transcript-area\"\r\n            value={transcript}\r\n            onChange={(e) => setTranscript(e.target.value)}\r\n            placeholder={sessionExpired ? \"Session expired. Please make a payment to continue.\" : \"Type or record your question here...\"}\r\n            disabled={sessionExpired}\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Payment Dialog */}\r\n      <Dialog\r\n        open={showPaymentDialog}\r\n        onClose={handlePaymentDialogClose}\r\n        aria-labelledby=\"payment-dialog-title\"\r\n        aria-describedby=\"payment-dialog-description\"\r\n      >\r\n        <DialogTitle id=\"payment-dialog-title\">\r\n          {\"Session Expiring Soon\"}\r\n        </DialogTitle>\r\n        <DialogContent>\r\n          <DialogContentText id=\"payment-dialog-description\">\r\n            Your session will expire in one minute. Would you like to make a payment to extend your session?\r\n          </DialogContentText>\r\n        </DialogContent>\r\n        <DialogActions>\r\n          <Button onClick={handlePaymentDialogClose} color=\"primary\">\r\n            Not Now\r\n          </Button>\r\n          <Button onClick={handlePaymentConfirm} color=\"primary\" autoFocus>\r\n            Make Payment\r\n          </Button>\r\n        </DialogActions>\r\n      </Dialog>\r\n\r\n      {/* Session Expired Dialog */}\r\n      <Dialog\r\n        open={sessionExpired}\r\n        aria-labelledby=\"expired-dialog-title\"\r\n        aria-describedby=\"expired-dialog-description\"\r\n      >\r\n        <DialogTitle id=\"expired-dialog-title\">\r\n          {\"Session Expired\"}\r\n        </DialogTitle>\r\n        <DialogContent>\r\n          <DialogContentText id=\"expired-dialog-description\">\r\n            Your session has expired. Please make a payment to continue using the service.\r\n          </DialogContentText>\r\n        </DialogContent>\r\n        <DialogActions>\r\n          <Button onClick={handleExit} color=\"primary\">\r\n            Exit\r\n          </Button>\r\n          <Button onClick={handlePaymentConfirm} color=\"primary\" autoFocus>\r\n            Make Payment\r\n          </Button>\r\n        </DialogActions>\r\n      </Dialog>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default SpeechToText;\r\n\r\n\r\n\r\n\r\n\r\n\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AACvE,OAAO,oBAAoB;AAC3B,OAAOC,GAAG,MAAM,cAAc;AAC9B,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,iBAAiB,MAAM,iCAAiC;AAC/D,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,MAAM,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,SAASC,YAAYA,CAAC;EAAEC;AAAO,CAAC,EAAE;EAAAC,EAAA;EAChC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACwB,UAAU,EAAEC,aAAa,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC0B,QAAQ,EAAEC,WAAW,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC4B,SAAS,EAAEC,YAAY,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;;EAEjD;EACA,MAAM8B,gBAAgB,GAAG,GAAG,CAAC,CAAC;EAC9B,MAAMC,YAAY,GAAG,EAAE,CAAC,CAAC;EACzB,MAAM,CAACC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGjC,QAAQ,CAAC8B,gBAAgB,CAAC;EAClF,MAAM,CAACI,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACoC,cAAc,EAAEC,iBAAiB,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;;EAE3D;EACA,MAAMsC,eAAe,GAAGrC,MAAM,CAAC,IAAI,CAAC;;EAEpC;EACA,MAAMsC,cAAc,GAAGtC,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMuC,iBAAiB,GAAGvC,MAAM,CAAC,IAAI,CAAC;EACtC,MAAMwC,eAAe,GAAGxC,MAAM,CAAC,IAAI,CAAC;;EAEpC;EACA,MAAMyC,iBAAiB,GAAGvC,WAAW,CAAEwC,YAAY,IAAK;IACtD,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,YAAY,GAAG,EAAE,CAAC;IAC7C,MAAMI,OAAO,GAAGJ,YAAY,GAAG,EAAE;IACjC,OAAO,GAAGC,OAAO,CAACI,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,OAAO,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACxF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,iBAAiB,GAAG/C,WAAW,CAAC,MAAM;IAC1C;IACA8B,uBAAuB,CAACH,gBAAgB,CAAC;IACzCO,iBAAiB,CAAC,KAAK,CAAC;;IAExB;IACA,IAAIC,eAAe,CAACa,OAAO,EAAE;MAC3BC,aAAa,CAACd,eAAe,CAACa,OAAO,CAAC;IACxC;;IAEA;IACAb,eAAe,CAACa,OAAO,GAAGE,WAAW,CAAC,MAAM;MAC1CpB,uBAAuB,CAACqB,IAAI,IAAI;QAC9B,MAAMC,OAAO,GAAGD,IAAI,GAAG,CAAC;;QAExB;QACA,IAAIC,OAAO,KAAKxB,YAAY,EAAE;UAC5BI,oBAAoB,CAAC,IAAI,CAAC;QAC5B;;QAEA;QACA,IAAIoB,OAAO,IAAI,CAAC,EAAE;UAChBH,aAAa,CAACd,eAAe,CAACa,OAAO,CAAC;UACtCb,eAAe,CAACa,OAAO,GAAG,IAAI;UAC9Bd,iBAAiB,CAAC,IAAI,CAAC;UACvB,OAAO,CAAC;QACV;QAEA,OAAOkB,OAAO;MAChB,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC;EACV,CAAC,EAAE,CAACzB,gBAAgB,EAAEC,YAAY,CAAC,CAAC;;EAEpC;EACA,MAAMyB,wBAAwB,GAAGrD,WAAW,CAAC,MAAM;IACjDgC,oBAAoB,CAAC,KAAK,CAAC;EAC7B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMsB,oBAAoB,GAAGtD,WAAW,CAAC,MAAM;IAC7C;IACAuD,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;EACjC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA1D,SAAS,CAAC,MAAM;IACdgD,iBAAiB,CAAC,CAAC;;IAEnB;IACA,OAAO,MAAM;MACX,IAAIZ,eAAe,CAACa,OAAO,EAAE;QAC3BC,aAAa,CAACd,eAAe,CAACa,OAAO,CAAC;MACxC;IACF,CAAC;EACH,CAAC,EAAE,CAACD,iBAAiB,CAAC,CAAC;;EAEvB;EACA,MAAMW,kBAAkB,GAAG5D,MAAM,CAAC,EAAE,CAAC;EAErCC,SAAS,CAAC,MAAM;IACd;IACA,MAAM4D,iBAAiB,GAAGJ,MAAM,CAACI,iBAAiB,IAAIJ,MAAM,CAACK,uBAAuB;IACpF,IAAID,iBAAiB,EAAE;MACrBvB,cAAc,CAACY,OAAO,GAAG,IAAIW,iBAAiB,CAAC,CAAC;MAChDvB,cAAc,CAACY,OAAO,CAACa,IAAI,GAAG,OAAO;MACrCzB,cAAc,CAACY,OAAO,CAACc,cAAc,GAAG,IAAI;MAC5C1B,cAAc,CAACY,OAAO,CAACe,UAAU,GAAG,IAAI;;MAExC;MACAL,kBAAkB,CAACV,OAAO,GAAG3B,UAAU;MAEvCe,cAAc,CAACY,OAAO,CAACgB,QAAQ,GAAIC,KAAK,IAAK;QAC3C,IAAIC,iBAAiB,GAAG,EAAE;QAE1B,KAAK,IAAIC,CAAC,GAAGF,KAAK,CAACG,WAAW,EAAED,CAAC,GAAGF,KAAK,CAACI,OAAO,CAACC,MAAM,EAAEH,CAAC,EAAE,EAAE;UAC7D,MAAM9C,UAAU,GAAG4C,KAAK,CAACI,OAAO,CAACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC9C,UAAU;UACjD,IAAI4C,KAAK,CAACI,OAAO,CAACF,CAAC,CAAC,CAACI,OAAO,EAAE;YAC5Bb,kBAAkB,CAACV,OAAO,IAAI3B,UAAU,GAAG,GAAG;UAChD,CAAC,MAAM;YACL6C,iBAAiB,IAAI7C,UAAU;UACjC;QACF;QAEAC,aAAa,CAACoC,kBAAkB,CAACV,OAAO,GAAGkB,iBAAiB,CAAC;MAC/D,CAAC;MAED9B,cAAc,CAACY,OAAO,CAACwB,OAAO,GAAIP,KAAK,IAAK;QAC1CQ,OAAO,CAACC,KAAK,CAAC,0BAA0B,EAAET,KAAK,CAACS,KAAK,CAAC;QACtDC,KAAK,CAAC,kBAAkB,GAAGV,KAAK,CAACS,KAAK,CAAC;QACvCtD,cAAc,CAAC,KAAK,CAAC;MACvB,CAAC;IACH,CAAC,MAAM;MACLuD,KAAK,CAAC,sEAAsE,CAAC;IAC/E;;IAEA;IACA,OAAO,MAAM;MACX,IAAIvC,cAAc,CAACY,OAAO,EAAE;QAC1BZ,cAAc,CAACY,OAAO,CAAC4B,IAAI,CAAC,CAAC;MAC/B;IACF,CAAC;EACH,CAAC,EAAE,CAACvD,UAAU,CAAC,CAAC;EAEhBtB,SAAS,CAAC,MAAM;IACd;IACA,IAAIsC,iBAAiB,CAACW,OAAO,EAAE;MAC7BX,iBAAiB,CAACW,OAAO,CAAC6B,SAAS,GAAGxC,iBAAiB,CAACW,OAAO,CAAC8B,YAAY;IAC9E;EACF,CAAC,EAAE,CAACzD,UAAU,CAAC,CAAC;EAEhBtB,SAAS,CAAC,MAAM;IACd;IACA,IAAIuC,eAAe,CAACU,OAAO,EAAE;MAC3BV,eAAe,CAACU,OAAO,CAAC6B,SAAS,GAAGvC,eAAe,CAACU,OAAO,CAAC8B,YAAY;IAC1E;EACF,CAAC,EAAE,CAACvD,QAAQ,CAAC,CAAC;EAEd,MAAMwD,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI3C,cAAc,CAACY,OAAO,IAAI,CAAC7B,WAAW,IAAI,CAACc,cAAc,EAAE;MAC7D,IAAI;QACF;QACAX,aAAa,CAAC,EAAE,CAAC;;QAEjB;QACAoC,kBAAkB,CAACV,OAAO,GAAG,EAAE;;QAE/B;QACA,MAAMW,iBAAiB,GAAGJ,MAAM,CAACI,iBAAiB,IAAIJ,MAAM,CAACK,uBAAuB;QACpFxB,cAAc,CAACY,OAAO,GAAG,IAAIW,iBAAiB,CAAC,CAAC;QAChDvB,cAAc,CAACY,OAAO,CAACa,IAAI,GAAG,OAAO;QACrCzB,cAAc,CAACY,OAAO,CAACc,cAAc,GAAG,IAAI;QAC5C1B,cAAc,CAACY,OAAO,CAACe,UAAU,GAAG,IAAI;;QAExC;QACA3B,cAAc,CAACY,OAAO,CAACgB,QAAQ,GAAIC,KAAK,IAAK;UAC3C,IAAIC,iBAAiB,GAAG,EAAE;UAE1B,KAAK,IAAIC,CAAC,GAAGF,KAAK,CAACG,WAAW,EAAED,CAAC,GAAGF,KAAK,CAACI,OAAO,CAACC,MAAM,EAAEH,CAAC,EAAE,EAAE;YAC7D,MAAM9C,UAAU,GAAG4C,KAAK,CAACI,OAAO,CAACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC9C,UAAU;YACjD,IAAI4C,KAAK,CAACI,OAAO,CAACF,CAAC,CAAC,CAACI,OAAO,EAAE;cAC5Bb,kBAAkB,CAACV,OAAO,IAAI3B,UAAU,GAAG,GAAG;YAChD,CAAC,MAAM;cACL6C,iBAAiB,IAAI7C,UAAU;YACjC;UACF;UAEAC,aAAa,CAACoC,kBAAkB,CAACV,OAAO,GAAGkB,iBAAiB,CAAC;QAC/D,CAAC;QAED9B,cAAc,CAACY,OAAO,CAACwB,OAAO,GAAIP,KAAK,IAAK;UAC1CQ,OAAO,CAACC,KAAK,CAAC,0BAA0B,EAAET,KAAK,CAACS,KAAK,CAAC;UACtDC,KAAK,CAAC,kBAAkB,GAAGV,KAAK,CAACS,KAAK,CAAC;UACvCtD,cAAc,CAAC,KAAK,CAAC;QACvB,CAAC;;QAED;QACAgB,cAAc,CAACY,OAAO,CAACgC,KAAK,CAAC,CAAC;QAC9B5D,cAAc,CAAC,IAAI,CAAC;MACtB,CAAC,CAAC,OAAOsD,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD;QACA,IAAIA,KAAK,CAACO,OAAO,CAACC,QAAQ,CAAC,iBAAiB,CAAC,EAAE;UAC7C9C,cAAc,CAACY,OAAO,CAAC4B,IAAI,CAAC,CAAC;UAC7BO,UAAU,CAAC,MAAM;YACfJ,cAAc,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,EAAE,GAAG,CAAC;QACT;MACF;IACF;EACF,CAAC;EAED,MAAMK,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIhD,cAAc,CAACY,OAAO,IAAI7B,WAAW,EAAE;MACzC,IAAI;QACFiB,cAAc,CAACY,OAAO,CAAC4B,IAAI,CAAC,CAAC;QAC7BxD,cAAc,CAAC,KAAK,CAAC;;QAErB;QACA;QACAsC,kBAAkB,CAACV,OAAO,GAAG3B,UAAU;MACzC,CAAC,CAAC,OAAOqD,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACnD;IACF;EACF,CAAC;EAED,MAAMW,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,MAAMC,MAAM,GAAGrF,GAAG,CAACsF,cAAc;IACjC,MAAMC,QAAQ,GAAGnE,UAAU,CAACoE,IAAI,CAAC,CAAC;IAElChB,OAAO,CAACiB,GAAG,CAAC,cAAc,EAAEC,OAAO,CAAC1F,GAAG,CAAC2F,QAAQ,CAAC;IACjDnB,OAAO,CAACiB,GAAG,CAAC,oBAAoB,EAAEJ,MAAM,GAAG,KAAK,GAAG,IAAI,CAAC;IAExD,IAAI,CAACA,MAAM,EAAE;MACXX,KAAK,CAAC,iHAAiH,CAAC;MACxH;IACF;IAEA,IAAI,CAACa,QAAQ,EAAE;MACbb,KAAK,CAAC,kDAAkD,CAAC;MACzD;IACF;IACA,MAAMkB,MAAM,GAAG,8DAA8DL,QAAQ,EAAE;IACvF9D,YAAY,CAAC,IAAI,CAAC;IAClBF,WAAW,CAAC,EAAE,CAAC;IAEf,IAAI;MACF,MAAMD,QAAQ,GAAG,MAAMuE,KAAK,CAAC,4CAA4C,EAAE;QACzEC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAE,UAAUV,MAAM;QACnC,CAAC;QACDW,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBC,KAAK,EAAE,cAAc;UACrBC,QAAQ,EAAE,CACR;YAAEC,IAAI,EAAE,MAAM;YAAEC,OAAO,EAAEV;UAAO,CAAC,CAClC;UACDW,MAAM,EAAE;QACV,CAAC;MACH,CAAC,CAAC;MAEF,IAAI,CAACjF,QAAQ,CAACkF,EAAE,IAAI,CAAClF,QAAQ,CAAC0E,IAAI,EAAE,MAAM,IAAIS,KAAK,CAAC,4BAA4B,CAAC;MAEjF,MAAMC,MAAM,GAAGpF,QAAQ,CAAC0E,IAAI,CAACW,SAAS,CAAC,CAAC;MACxC,MAAMC,OAAO,GAAG,IAAIC,WAAW,CAAC,OAAO,CAAC;MAExC,IAAIC,MAAM,GAAG,EAAE;MAEf,OAAO,IAAI,EAAE;QACX,MAAM;UAAEC,KAAK;UAAEC;QAAK,CAAC,GAAG,MAAMN,MAAM,CAACO,IAAI,CAAC,CAAC;QAC3C,IAAID,IAAI,EAAE;QAEV,MAAME,KAAK,GAAGN,OAAO,CAACO,MAAM,CAACJ,KAAK,EAAE;UAAER,MAAM,EAAE;QAAK,CAAC,CAAC;QACrD,MAAMa,KAAK,GAAGF,KAAK,CAACG,KAAK,CAAC,IAAI,CAAC,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAAC/B,IAAI,CAAC,CAAC,CAACgC,UAAU,CAAC,OAAO,CAAC,CAAC;QAE/E,KAAK,MAAMD,IAAI,IAAIH,KAAK,EAAE;UACxB,MAAMK,IAAI,GAAGF,IAAI,CAACG,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;UACxC,IAAID,IAAI,KAAK,QAAQ,EAAE;UAEvB,IAAI;YAAA,IAAAE,aAAA,EAAAC,cAAA,EAAAC,oBAAA;YACF,MAAMC,IAAI,GAAG7B,IAAI,CAAC8B,KAAK,CAACN,IAAI,CAAC;YAC7B,MAAMnB,OAAO,IAAAqB,aAAA,GAAGG,IAAI,CAACE,OAAO,cAAAL,aAAA,wBAAAC,cAAA,GAAZD,aAAA,CAAe,CAAC,CAAC,cAAAC,cAAA,wBAAAC,oBAAA,GAAjBD,cAAA,CAAmBK,KAAK,cAAAJ,oBAAA,uBAAxBA,oBAAA,CAA0BvB,OAAO;YACjD,IAAIA,OAAO,EAAE;cACXQ,MAAM,IAAIR,OAAO;cACjB/E,WAAW,CAACuF,MAAM,CAAC;YACrB;UACF,CAAC,CAAC,OAAOoB,CAAC,EAAE;YACV1D,OAAO,CAACC,KAAK,CAAC,qBAAqB,EAAEyD,CAAC,CAAC;UACzC;QACF;MACF;IACF,CAAC,CAAC,OAAOzD,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MACxClD,WAAW,CAAC,kBAAkB,GAAGkD,KAAK,CAACO,OAAO,CAAC;IACjD,CAAC,SAAS;MACR,IAAI9D,WAAW,EAAE;QACfiE,aAAa,CAAC,CAAC;MACjB;MACA1D,YAAY,CAAC,KAAK,CAAC;;MAEnB;MACAJ,aAAa,CAAC,EAAE,CAAC;MACjB;MACAoC,kBAAkB,CAACV,OAAO,GAAG,EAAE;IACjC;EACF,CAAC;EAED,MAAMoF,eAAe,GAAGA,CAAA,KAAM;IAC5B;IACA9G,aAAa,CAAC,EAAE,CAAC;;IAEjB;IACAoC,kBAAkB,CAACV,OAAO,GAAG,EAAE;;IAE/B;IACA,IAAIZ,cAAc,CAACY,OAAO,EAAE;MAC1B;MACA,MAAMqF,YAAY,GAAGlH,WAAW;MAEhC,IAAI;QACF;QACA,IAAIkH,YAAY,EAAE;UAChBjG,cAAc,CAACY,OAAO,CAAC4B,IAAI,CAAC,CAAC;UAC7BxD,cAAc,CAAC,KAAK,CAAC;QACvB;;QAEA;QACA+D,UAAU,CAAC,MAAM;UACf;UACA,MAAMxB,iBAAiB,GAAGJ,MAAM,CAACI,iBAAiB,IAAIJ,MAAM,CAACK,uBAAuB;UACpFxB,cAAc,CAACY,OAAO,GAAG,IAAIW,iBAAiB,CAAC,CAAC;UAChDvB,cAAc,CAACY,OAAO,CAACa,IAAI,GAAG,OAAO;UACrCzB,cAAc,CAACY,OAAO,CAACc,cAAc,GAAG,IAAI;UAC5C1B,cAAc,CAACY,OAAO,CAACe,UAAU,GAAG,IAAI;;UAExC;UACA3B,cAAc,CAACY,OAAO,CAACgB,QAAQ,GAAIC,KAAK,IAAK;YAC3C,IAAIC,iBAAiB,GAAG,EAAE;YAE1B,KAAK,IAAIC,CAAC,GAAGF,KAAK,CAACG,WAAW,EAAED,CAAC,GAAGF,KAAK,CAACI,OAAO,CAACC,MAAM,EAAEH,CAAC,EAAE,EAAE;cAC7D,MAAM9C,UAAU,GAAG4C,KAAK,CAACI,OAAO,CAACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC9C,UAAU;cACjD,IAAI4C,KAAK,CAACI,OAAO,CAACF,CAAC,CAAC,CAACI,OAAO,EAAE;gBAC5Bb,kBAAkB,CAACV,OAAO,IAAI3B,UAAU,GAAG,GAAG;cAChD,CAAC,MAAM;gBACL6C,iBAAiB,IAAI7C,UAAU;cACjC;YACF;YAEAC,aAAa,CAACoC,kBAAkB,CAACV,OAAO,GAAGkB,iBAAiB,CAAC;UAC/D,CAAC;UAED9B,cAAc,CAACY,OAAO,CAACwB,OAAO,GAAIP,KAAK,IAAK;YAC1CQ,OAAO,CAACC,KAAK,CAAC,0BAA0B,EAAET,KAAK,CAACS,KAAK,CAAC;YACtDtD,cAAc,CAAC,KAAK,CAAC;UACvB,CAAC;;UAED;UACA,IAAIiH,YAAY,EAAE;YAChB,IAAI;cACFjG,cAAc,CAACY,OAAO,CAACgC,KAAK,CAAC,CAAC;cAC9B5D,cAAc,CAAC,IAAI,CAAC;YACtB,CAAC,CAAC,OAAOsD,KAAK,EAAE;cACdD,OAAO,CAACC,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;YACxD;UACF;QACF,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,OAAOA,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MAC3D;IACF;EACF,CAAC;EAED,MAAM4D,aAAa,GAAGA,CAAA,KAAM;IAC1B9G,WAAW,CAAC,EAAE,CAAC;EACjB,CAAC;;EAED;EACA,MAAM+G,UAAU,GAAGvI,WAAW,CAAC,MAAM;IACnC;IACA,IAAIiB,MAAM,EAAE;MACVA,MAAM,CAAC,CAAC;IACV,CAAC,MAAM;MACLsC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,OAAO;IAChC;EACF,CAAC,EAAE,CAACxC,MAAM,CAAC,CAAC;EAEZ,oBACEF,OAAA;IAAKyH,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAE7B1H,OAAA;MAAQyH,SAAS,EAAC,aAAa;MAACE,OAAO,EAAEH,UAAW;MAACI,KAAK,EAAC,MAAM;MAAAF,QAAA,eAC/D1H,OAAA,CAACR,SAAS;QAAAqI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,eAGThI,OAAA;MAAKyH,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9B1H,OAAA,CAACT,cAAc;QAACkI,SAAS,EAAC;MAAY;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACzChI,OAAA;QAAMyH,SAAS,EAAC,6BAA6B;QAAAC,QAAA,EAC1ClG,iBAAiB,CAACV,oBAAoB;MAAC;QAAA+G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,EACN5H,WAAW,iBAAIJ,OAAA;QAAMyH,SAAS,EAAC,qBAAqB;QAAAC,QAAA,EAAC;MAAY;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtE,CAAC,eAENhI,OAAA;MAAKyH,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAE7B1H,OAAA;QAAKyH,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B1H,OAAA;UAAKyH,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B1H,OAAA;YAAA0H,QAAA,EAAI;UAAW;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpBhI,OAAA;YACEyH,SAAS,EAAC,0BAA0B;YACpCE,OAAO,EAAEJ,aAAc;YACvBU,QAAQ,EAAE,CAACzH,QAAQ,IAAIU,cAAe;YACtC0G,KAAK,EAAC,gBAAgB;YAAAF,QAAA,eAEtB1H,OAAA,CAACV,UAAU;cAAC4I,QAAQ,EAAC;YAAO;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNhI,OAAA;UACEmI,GAAG,EAAE5G,eAAgB;UACrBkG,SAAS,EAAC,eAAe;UACzBxB,KAAK,EAAEzF,QAAS;UAChB4H,QAAQ;UACRC,WAAW,EAAC,+BAA+B;UAC3CJ,QAAQ,EAAE/G;QAAe;UAAA2G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNhI,OAAA;QAAKyH,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjC1H,OAAA;UAAKyH,SAAS,EAAC,gBAAgB;UAAAC,QAAA,eAC7B1H,OAAA;YAAKyH,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B1H,OAAA;cACEyH,SAAS,EAAE,cAAcrH,WAAW,GAAG,WAAW,GAAG,EAAE,EAAG;cAC1DuH,OAAO,EAAEvH,WAAW,GAAGiE,aAAa,GAAGL,cAAe;cACtD4D,KAAK,EAAExH,WAAW,GAAG,gBAAgB,GAAG,iBAAkB;cAC1D6H,QAAQ,EAAE/G,cAAe;cAAAwG,QAAA,EAExBtH,WAAW,gBAAGJ,OAAA,CAACZ,QAAQ;gBAAAyI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGhI,OAAA,CAACb,OAAO;gBAAA0I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eAEThI,OAAA;cACEyH,SAAS,EAAC,aAAa;cACvBE,OAAO,EAAErD,SAAU;cACnB2D,QAAQ,EAAEvH,SAAS,IAAI,CAACJ,UAAU,CAACoE,IAAI,CAAC,CAAC,IAAIxD,cAAe;cAC5D0G,KAAK,EAAC,YAAY;cAAAF,QAAA,EAEjBhH,SAAS,GAAG,KAAK,gBAAGV,OAAA,CAACX,QAAQ;gBAAAwI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,eAEThI,OAAA;cACEyH,SAAS,EAAC,0BAA0B;cACpCE,OAAO,EAAEN,eAAgB;cACzBY,QAAQ,EAAE,CAAC3H,UAAU,IAAIY,cAAe;cACxC0G,KAAK,EAAC,gBAAgB;cAAAF,QAAA,eAEtB1H,OAAA,CAACV,UAAU;gBAAC4I,QAAQ,EAAC;cAAO;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNhI,OAAA;UACEmI,GAAG,EAAE7G,iBAAkB;UACvBmG,SAAS,EAAC,iBAAiB;UAC3BxB,KAAK,EAAE3F,UAAW;UAClBgI,QAAQ,EAAGlB,CAAC,IAAK7G,aAAa,CAAC6G,CAAC,CAACmB,MAAM,CAACtC,KAAK,CAAE;UAC/CoC,WAAW,EAAEnH,cAAc,GAAG,qDAAqD,GAAG,sCAAuC;UAC7H+G,QAAQ,EAAE/G;QAAe;UAAA2G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhI,OAAA,CAACP,MAAM;MACL+I,IAAI,EAAExH,iBAAkB;MACxByH,OAAO,EAAEnG,wBAAyB;MAClC,mBAAgB,sBAAsB;MACtC,oBAAiB,4BAA4B;MAAAoF,QAAA,gBAE7C1H,OAAA,CAACH,WAAW;QAAC6I,EAAE,EAAC,sBAAsB;QAAAhB,QAAA,EACnC;MAAuB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC,eACdhI,OAAA,CAACL,aAAa;QAAA+H,QAAA,eACZ1H,OAAA,CAACJ,iBAAiB;UAAC8I,EAAE,EAAC,4BAA4B;UAAAhB,QAAA,EAAC;QAEnD;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eAChBhI,OAAA,CAACN,aAAa;QAAAgI,QAAA,gBACZ1H,OAAA,CAACF,MAAM;UAAC6H,OAAO,EAAErF,wBAAyB;UAACqG,KAAK,EAAC,SAAS;UAAAjB,QAAA,EAAC;QAE3D;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACThI,OAAA,CAACF,MAAM;UAAC6H,OAAO,EAAEpF,oBAAqB;UAACoG,KAAK,EAAC,SAAS;UAACC,SAAS;UAAAlB,QAAA,EAAC;QAEjE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGThI,OAAA,CAACP,MAAM;MACL+I,IAAI,EAAEtH,cAAe;MACrB,mBAAgB,sBAAsB;MACtC,oBAAiB,4BAA4B;MAAAwG,QAAA,gBAE7C1H,OAAA,CAACH,WAAW;QAAC6I,EAAE,EAAC,sBAAsB;QAAAhB,QAAA,EACnC;MAAiB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eACdhI,OAAA,CAACL,aAAa;QAAA+H,QAAA,eACZ1H,OAAA,CAACJ,iBAAiB;UAAC8I,EAAE,EAAC,4BAA4B;UAAAhB,QAAA,EAAC;QAEnD;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eAChBhI,OAAA,CAACN,aAAa;QAAAgI,QAAA,gBACZ1H,OAAA,CAACF,MAAM;UAAC6H,OAAO,EAAEH,UAAW;UAACmB,KAAK,EAAC,SAAS;UAAAjB,QAAA,EAAC;QAE7C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACThI,OAAA,CAACF,MAAM;UAAC6H,OAAO,EAAEpF,oBAAqB;UAACoG,KAAK,EAAC,SAAS;UAACC,SAAS;UAAAlB,QAAA,EAAC;QAEjE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV;AAAC7H,EAAA,CA7fQF,YAAY;AAAA4I,EAAA,GAAZ5I,YAAY;AA+frB,eAAeA,YAAY;AAAC,IAAA4I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}