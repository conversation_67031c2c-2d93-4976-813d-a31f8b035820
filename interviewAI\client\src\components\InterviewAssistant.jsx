import React, { useState, useRef, useEffect, useCallback } from 'react';
import './InterviewAssistant.css';
import env from '../utils/env';

function InterviewAssistant() {
  const [isListening, setIsListening] = useState(false);
  const [transcriptMessages, setTranscriptMessages] = useState([]);
  const [currentTranscript, setCurrentTranscript] = useState('');
  const [response, setResponse] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [autoSubmit, setAutoSubmit] = useState(false); // Toggle for auto-submission
  const [previousTranscript, setPreviousTranscript] = useState(''); // Track previous transcript for comparison

  // Timer state
  const [timerSeconds, setTimerSeconds] = useState(0);
  const timerIntervalRef = useRef(null);
  const pauseTimerRef = useRef(null); // Timer for detecting speech pauses

  const recognitionRef = useRef(null);
  const transcriptAreaRef = useRef(null);
  const responseAreaRef = useRef(null);

  // Format seconds to MM:SS
  const formatTime = useCallback((totalSeconds) => {
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }, []);

  // Start the timer
  const startTimer = useCallback(() => {
    // Reset timer when starting
    setTimerSeconds(0);

    // Clear any existing interval
    if (timerIntervalRef.current) {
      clearInterval(timerIntervalRef.current);
    }

    // Start a new interval
    timerIntervalRef.current = setInterval(() => {
      setTimerSeconds(prev => prev + 1);
    }, 1000);
  }, []);

  // Stop the timer
  const stopTimer = useCallback(() => {
    if (timerIntervalRef.current) {
      clearInterval(timerIntervalRef.current);
      timerIntervalRef.current = null;
    }
  }, []);

  // Function to check for speech pause and auto-submit
  const checkForSpeechPause = useCallback(() => {
    // Clear any existing pause timer
    if (pauseTimerRef.current) {
      clearTimeout(pauseTimerRef.current);
    }

    // Set a new pause timer
    pauseTimerRef.current = setTimeout(() => {
      console.log("Checking auto-submit conditions:", {
        autoSubmit,
        hasText: !!currentTranscript.trim(),
        isListening,
        isLoading
      });

      // Only auto-submit if:
      // 1. Auto-submit is enabled
      // 2. We have a non-empty transcript
      // 3. We're currently listening
      // 4. We're not already loading a response
      if (autoSubmit &&
          currentTranscript.trim() &&
          isListening &&
          !isLoading) {

        console.log("Auto-submit conditions met, sending to GPT");

        // Call the submit button click directly
        const submitButton = document.querySelector('.submit-button');
        if (submitButton && !submitButton.disabled) {
          submitButton.click();
        }
      }
    }, 2000); // 2 second pause detection
  }, [autoSubmit, currentTranscript, isListening, isLoading]);

  // Clean up timer on unmount
  useEffect(() => {
    return () => {
      if (timerIntervalRef.current) {
        clearInterval(timerIntervalRef.current);
      }
      if (pauseTimerRef.current) {
        clearTimeout(pauseTimerRef.current);
      }
    };
  }, []);

  useEffect(() => {
    // Initialize speech recognition
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    if (SpeechRecognition) {
      recognitionRef.current = new SpeechRecognition();
      recognitionRef.current.lang = 'en-US';
      recognitionRef.current.interimResults = true;
      recognitionRef.current.continuous = true;

      let finalTranscript = "";

      recognitionRef.current.onresult = (event) => {
        let interimTranscript = "";

        // Store previous transcript for comparison
        setPreviousTranscript(finalTranscript + interimTranscript);

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const transcript = event.results[i][0].transcript;
          if (event.results[i].isFinal) {
            finalTranscript += transcript + " ";
          } else {
            interimTranscript += transcript;
          }
        }

        const newTranscript = finalTranscript + interimTranscript;
        setCurrentTranscript(newTranscript);

        // Check for pause if transcript has changed
        if (newTranscript !== previousTranscript) {
          checkForSpeechPause();
        }
      };

      recognitionRef.current.onerror = (event) => {
        console.error("Speech recognition error", event.error);
        alert("Error occurred: " + event.error);
        setIsListening(false);
        stopTimer();
      };
    } else {
      alert("Your browser doesn't support speech recognition. Try Chrome or Edge.");
    }

    // Cleanup function
    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.stop();
      }
    };
  }, [stopTimer, checkForSpeechPause, previousTranscript]);

  useEffect(() => {
    // Auto-scroll transcript area when content changes
    if (transcriptAreaRef.current) {
      transcriptAreaRef.current.scrollTop = transcriptAreaRef.current.scrollHeight;
    }
  }, [transcriptMessages, currentTranscript]);

  useEffect(() => {
    // Auto-scroll response area when content changes
    if (responseAreaRef.current) {
      responseAreaRef.current.scrollTop = responseAreaRef.current.scrollHeight;
    }
  }, [response]);

  // Debug effect for auto-submit mode
  useEffect(() => {
    console.log(`Auto-submit mode ${autoSubmit ? 'enabled' : 'disabled'}`);
  }, [autoSubmit]);

  const startListening = useCallback(() => {
    if (recognitionRef.current && !isListening) {
      try {
        recognitionRef.current.start();
        setIsListening(true);
        startTimer(); // Start the timer when listening begins
      } catch (error) {
        console.error("Speech recognition error:", error);
        // If recognition is already running, stop it first then restart
        if (error.message.includes("already started")) {
          recognitionRef.current.stop();
          setTimeout(() => {
            recognitionRef.current.start();
            setIsListening(true);
            startTimer(); // Start the timer when listening begins
          }, 100);
        }
      }
    }
  }, [isListening, startTimer]);

  const stopListening = useCallback(() => {
    if (recognitionRef.current && isListening) {
      try {
        recognitionRef.current.stop();
        setIsListening(false);
        stopTimer(); // Stop the timer when listening ends
      } catch (error) {
        console.error("Speech recognition error:", error);
      }
    }
  }, [isListening, stopTimer]);

  // Send transcript to GPT for response
  const sendToGPT = useCallback(async () => {
    const apiKey = env.OPENAI_API_KEY;
    const userText = currentTranscript.trim();

    if (!apiKey) {
      alert("Please add your OpenAI API key to the .env file as REACT_APP_OPENAI_API_KEY and restart the development server.");
      return;
    }

    if (!userText) {
      alert("Please record or enter some text to send to GPT.");
      return;
    }

    // Add the current transcript to the messages array
    setTranscriptMessages(prev => [
      ...prev,
      { text: userText, timestamp: new Date(), time: timerSeconds }
    ]);

    // Clear the current transcript input
    setCurrentTranscript('');

    setIsLoading(true);
    setResponse('');

    try {
      console.log("Sending to GPT API:", userText);
      const response = await fetch("https://api.openai.com/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${apiKey}`
        },
        body: JSON.stringify({
          model: "gpt-4.1-nano",
          messages: [
            {
              role: "system",
              content: "You are Sensei, an AI interview assistant. Provide detailed, well-structured responses to interview questions. Format your answers with bullet points for clarity. Keep responses concise but comprehensive."
            },
            { role: "user", content: userText }
          ],
          stream: true
        })
      });

      if (!response.ok || !response.body) throw new Error("Failed to stream response.");

      const reader = response.body.getReader();
      const decoder = new TextDecoder("utf-8");

      let result = "";

      while (true) {
        const { value, done } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value, { stream: true });
        const lines = chunk.split("\n").filter(line => line.trim().startsWith("data:"));

        for (const line of lines) {
          const data = line.replace(/^data: /, '');
          if (data === "[DONE]") break;

          try {
            const json = JSON.parse(data);
            const content = json.choices?.[0]?.delta?.content;
            if (content) {
              result += content;
              setResponse(result);
            }
          } catch (e) {
            console.error("Error parsing JSON:", e);
          }
        }
      }
    } catch (error) {
      console.error("Streaming Error:", error);
      setResponse("Error occurred: " + error.message);
    } finally {
      setIsLoading(false);
    }
  }, [currentTranscript, timerSeconds]);

  const clearTranscript = () => {
    setCurrentTranscript('');
    setTranscriptMessages([]);
  };

  const clearResponse = () => {
    setResponse('');
  };

  // Handle end interview with confirmation popup
  const [showEndConfirmation, setShowEndConfirmation] = useState(false);

  const handleEndInterview = () => {
    setShowEndConfirmation(true);
  };

  const confirmEndInterview = () => {
    // Here you would handle the actual end interview logic
    // For example, redirect to a summary page or reset the state
    setShowEndConfirmation(false);
    clearTranscript();
    clearResponse();
    // You could also redirect to another page
    // window.location.href = '/';
  };

  const cancelEndInterview = () => {
    setShowEndConfirmation(false);
  };

  const generateQuestion = () => {
    // This would generate a sample interview question
    const questions = [
      "Tell me about yourself.",
      "What are your greatest strengths?",
      "What do you consider to be your weaknesses?",
      "Why do you want this job?",
      "Where do you see yourself in five years?",
      "Why should we hire you?",
      "What is your greatest professional achievement?",
      "Tell me about a challenge or conflict you've faced at work, and how you dealt with it.",
      "Tell me about a time you demonstrated leadership skills.",
      "What's your management style?",
      "How do you handle stress and pressure?",
      "What are your salary expectations?",
      "What do you like to do outside of work?",
      "What are your career goals?",
      "Why are you leaving your current job?",
      "How do you prioritize your work?",
      "What are you passionate about?",
      "What makes you unique?",
      "What should I know that's not on your resume?",
      "What would your first 30, 60, or 90 days look like in this role?"
    ];

    const randomQuestion = questions[Math.floor(Math.random() * questions.length)];

    // Add the generated question directly to the transcript messages
    setTranscriptMessages(prev => [
      ...prev,
      { text: randomQuestion, timestamp: new Date(), time: timerSeconds }
    ]);
  };

  return (
    <div className="interview-assistant">
      <div className="interview-container">
        <div className="transcription-panel">
          {/* <div className="panel-header">
            <h3>Transcription Messages</h3>
            <p className="panel-description">
              Interviewer questions will be transcribed in this section for your review and response.
            </p>
          </div> */}

          <div className="panel-content">
            <div
              ref={transcriptAreaRef}
              className="transcript-content"
            >
              {transcriptMessages.length > 0 ? (
                <div className="transcript-messages">
                  {transcriptMessages.map((msg, index) => (
                    <div key={index} className="transcript-message">
                      <div className="timestamp">{formatTime(msg.time)}</div>
                      <div className="message-text">{msg.text}</div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="empty-state">Your interview questions will appear here</div>
              )}

              {currentTranscript && (
                <div className="transcript-message current">
                  <div className="timestamp">{formatTime(timerSeconds)}</div>
                  <div className="message-text">{currentTranscript}</div>
                </div>
              )}
            </div>
          </div>

          <div className="panel-footer">
            <div className="user-label">Interviewer</div>
            <button
              className="generate-button"
              onClick={generateQuestion}
            >
              Generate
            </button>
          </div>
        </div>

        <div className="response-panel">
          <div className="panel-header">
            <h3>Answers</h3>
            <p className="panel-description">
              Displayed here are the intelligent responses generated by Sensei. Whenever the interviewer concludes a question,
              provides a tailored answer for you.
            </p>
          </div>

          <div className="panel-content">
            <div
              ref={responseAreaRef}
              className="response-content"
            >
              {response ? (
                <div className="response-message">
                  <div className="message-text">{response}</div>
                </div>
              ) : (
                <div className="empty-state">InterviewAssistant's responses will appear here</div>
              )}
            </div>
          </div>

          <div className="panel-footer">
            <div className="user-label">InterviewAssistant's</div>
          </div>
        </div>
      </div>

      <div className="controls-container">
        <div className="timer-display">
          {isListening ? (
            <>
              <span className="recording-dot"></span>
              {formatTime(timerSeconds)}
            </>
          ) : (
            formatTime(timerSeconds)
          )}
        </div>

        <div className="limit-indicator">15-minute limit</div>

        <div className="input-controls">
          <input
            type="text"
            className="text-input"
            value={currentTranscript}
            onChange={(e) => setCurrentTranscript(e.target.value)}
            placeholder="Type your question here..."
          />

          <button
            className="submit-button"
            onClick={sendToGPT}
            disabled={isLoading || !currentTranscript.trim()}
          >
            <span className="arrow-icon">↑</span>
          </button>
        </div>

        <div className="action-buttons">
          <button
            className={`toggle-button ${autoSubmit ? 'active' : ''}`}
            onClick={() => {
              const newValue = !autoSubmit;
              console.log(`Setting auto-submit to: ${newValue}`);
              setAutoSubmit(newValue);
            }}
            title={autoSubmit ? "Auto-submit enabled" : "Auto-submit disabled"}
            data-active={autoSubmit}
            style={{
              backgroundColor: autoSubmit ? '#4caf50' : '#f0f0f0',
              color: autoSubmit ? 'white' : '#333'
            }}
          >
            {autoSubmit ? "Auto" : "Manual"}
          </button>

          <button
            className={`mic-button ${isListening ? 'active' : ''}`}
            onClick={isListening ? stopListening : startListening}
          >
            {isListening ? "Stop" : "Start"}
          </button>

          <button
            className="settings-button"
          >
            ⚙️
          </button>

          <button
            className="end-button"
            onClick={handleEndInterview}
          >
            End
          </button>
        </div>
      </div>

      {/* End Interview Confirmation Modal */}
      {showEndConfirmation && (
        <div className="modal-overlay">
          <div className="modal-container">
            <div className="modal-header">
              <h3>End interview confirmation</h3>
            </div>
            <div className="modal-content">
              <p>This action cannot be undone, and you will need to start a new interview to continue.</p>
            </div>
            <div className="modal-footer">
              <button className="cancel-button" onClick={cancelEndInterview}>
                Cancel
              </button>
              <button className="confirm-button" onClick={confirmEndInterview}>
                OK
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default InterviewAssistant;
