import { useState, useEffect, useRef } from 'react';
import './DiarizedTranscription.css';

function DiarizedTranscription({ onTranscriptChange }) {
  // State for transcription
  const [transcript, setTranscript] = useState('');
  const [diarizedSegments, setDiarizedSegments] = useState([]);
  const [isListening, setIsListening] = useState(false);
  const [isCapturingTabAudio, setIsCapturingTabAudio] = useState(false);
  // Default speaker is based on audio source - tab audio is speaker1 (interviewer), mic is speaker2 (candidate)
  const [currentSpeaker, setCurrentSpeaker] = useState('speaker2');

  // Refs
  const recognitionRef = useRef(null);
  const finalTranscriptRef = useRef('');
  const audioContextRef = useRef(null);
  const mediaStreamRef = useRef(null);

  // Initialize speech recognition
  useEffect(() => {
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    if (!SpeechRecognition) {
      console.error("Speech Recognition API not supported in this browser");
      return;
    }

    // Clean up function
    return () => {
      if (recognitionRef.current) {
        try {
          recognitionRef.current.stop();
        } catch (error) {
          console.error("Error stopping recognition:", error);
        }
      }

      if (audioContextRef.current) {
        try {
          audioContextRef.current.close();
        } catch (error) {
          console.error("Error closing audio context:", error);
        }
      }

      if (mediaStreamRef.current) {
        mediaStreamRef.current.getTracks().forEach(track => track.stop());
      }
    };
  }, []);

  // Start listening with microphone
  const startListening = async () => {
    try {
      if (isListening) return;

      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      recognitionRef.current = new SpeechRecognition();
      recognitionRef.current.continuous = true;
      recognitionRef.current.interimResults = true;
      recognitionRef.current.lang = 'en-US';

      // Reset transcript
      finalTranscriptRef.current = '';
      setTranscript('');
      setDiarizedSegments([]);

      // Set up audio context for voice analysis
      if (!audioContextRef.current) {
        // eslint-disable-next-line
        audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();
      }

      // Get microphone stream
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      mediaStreamRef.current = stream;

      // Set speaker to candidate (speaker2) for microphone input
      setCurrentSpeaker('speaker2');

      // Set up recognition event handlers
      setupRecognitionHandlers();

      // Start recognition
      recognitionRef.current.start();
      setIsListening(true);
      setIsCapturingTabAudio(false);

      console.log("Microphone listening started - You are identified as the Candidate");
    } catch (error) {
      console.error("Error starting microphone listening:", error);
      alert("Failed to start microphone: " + error.message);
    }
  };

  // Start capturing tab audio
  const startTabAudioCapture = async () => {
    try {
      if (isListening) return;

      // Request screen sharing with audio
      const stream = await navigator.mediaDevices.getDisplayMedia({
        video: true,
        audio: true
      });

      // Check if audio is included
      const audioTracks = stream.getAudioTracks();
      if (audioTracks.length === 0) {
        alert("No audio track found. Please make sure to select 'Share audio' when sharing the tab.");
        stream.getTracks().forEach(track => track.stop());
        return;
      }

      mediaStreamRef.current = stream;

      // Set up audio context for analysis
      if (!audioContextRef.current) {
        // eslint-disable-next-line
        audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();
      }

      // Set speaker to interviewer (speaker1) for tab audio
      setCurrentSpeaker('speaker1');

      // Initialize speech recognition
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      recognitionRef.current = new SpeechRecognition();
      recognitionRef.current.continuous = true;
      recognitionRef.current.interimResults = true;
      recognitionRef.current.lang = 'en-US';

      // Reset transcript
      finalTranscriptRef.current = '';
      setTranscript('');
      setDiarizedSegments([]);

      // Set up recognition event handlers
      setupRecognitionHandlers();

      // Start recognition
      recognitionRef.current.start();
      setIsListening(true);
      setIsCapturingTabAudio(true);

      // Handle stream ending
      stream.getVideoTracks()[0].onended = () => {
        stopTranscription();
      };

      console.log("Tab audio capture started - Tab audio is identified as the Interviewer");
    } catch (error) {
      console.error("Error capturing tab audio:", error);
      alert("Failed to capture tab audio: " + error.message);
    }
  };

  // Stop transcription
  const stopTranscription = () => {
    if (recognitionRef.current) {
      try {
        recognitionRef.current.stop();
      } catch (error) {
        console.error("Error stopping recognition:", error);
      }
    }

    if (mediaStreamRef.current) {
      mediaStreamRef.current.getTracks().forEach(track => track.stop());
      mediaStreamRef.current = null;
    }

    setIsListening(false);
    setIsCapturingTabAudio(false);
    console.log("Transcription stopped");
  };

  // Set up recognition event handlers
  const setupRecognitionHandlers = () => {
    if (!recognitionRef.current) return;

    recognitionRef.current.onresult = (event) => {
      let interimTranscript = '';

      for (let i = event.resultIndex; i < event.results.length; i++) {
        const result = event.results[i];
        const text = result[0].transcript;

        if (result.isFinal) {
          // Add to final transcript with current speaker
          finalTranscriptRef.current += text + ' ';

          // Create new segment with the current speaker
          const newSegment = {
            text,
            speaker: currentSpeaker,
            timestamp: new Date(),
            isFinal: true
          };

          // Add to diarized segments
          setDiarizedSegments(prev => [...prev, newSegment]);

          // Only notify parent component if this is an interviewer question (speaker1)
          // This ensures we only send interviewer questions to GPT, not candidate responses
          if (currentSpeaker === 'speaker1' && onTranscriptChange) {
            console.log("Interviewer question detected, sending to GPT:", text);
            onTranscriptChange(text, [newSegment]);
          }
        } else {
          interimTranscript += text;
        }
      }

      const fullTranscript = finalTranscriptRef.current + interimTranscript;
      setTranscript(fullTranscript);
    };

    recognitionRef.current.onerror = (event) => {
      console.error("Recognition error:", event.error);
      if (event.error !== 'no-speech') {
        stopTranscription();
      }
    };

    recognitionRef.current.onend = () => {
      console.log("Recognition ended");
      // Restart if we're still supposed to be listening
      if (isListening) {
        try {
          recognitionRef.current.start();
        } catch (error) {
          console.error("Failed to restart recognition:", error);
          setIsListening(false);
        }
      }
    };
  };

  // Note: We're not using audio analysis for speaker diarization anymore
  // Instead, we're using the audio source to determine the speaker
  // Tab audio = Interviewer (speaker1), Microphone = Candidate (speaker2)

  return (
    <div className="diarized-transcription">
      {!window.SpeechRecognition && !window.webkitSpeechRecognition && (
        <div className="browser-warning">
          <p>
            <strong>Warning:</strong> Your browser doesn't support speech recognition.
            Please use Chrome, Edge, or another Chromium-based browser for this feature to work.
          </p>
        </div>
      )}

      <div className="transcript-display">
        {diarizedSegments.length > 0 ? (
          <div className="diarized-segments">
            {diarizedSegments.map((segment, index) => (
              <div
                key={index}
                className={`segment ${segment.speaker}`}
              >
                <div className="speaker-label">
                  {segment.speaker === 'speaker1' ? 'Interviewer (Tab Audio)' : 'Candidate (Your Mic)'}
                </div>
                <div className="segment-text">{segment.text}</div>
              </div>
            ))}

            {/* Current interim transcript */}
            {transcript && finalTranscriptRef.current !== transcript && (
              <div className={`segment ${currentSpeaker} interim`}>
                <div className="speaker-label">
                  {currentSpeaker === 'speaker1' ? 'Interviewer (Tab Audio)' : 'Candidate (Your Mic)'}
                </div>
                <div className="segment-text">{transcript.substring(finalTranscriptRef.current.length)}</div>
              </div>
            )}
          </div>
        ) : (
          <p className="placeholder">Waiting for speech...</p>
        )}
      </div>

      <div className="controls">
        <button
          className={`control-button mic ${isListening && !isCapturingTabAudio ? 'listening' : ''}`}
          onClick={isListening ? stopTranscription : startListening}
          disabled={!window.SpeechRecognition && !window.webkitSpeechRecognition}
          title="Capture your microphone audio (you will be identified as the Candidate)"
        >
          {isListening && !isCapturingTabAudio ? (
            <span role="img" aria-label="Stop Mic" style={{fontSize: '1.5em'}}>&#x23F9;</span> // Stop icon
          ) : (
            <span role="img" aria-label="Start Mic" style={{fontSize: '1.5em'}}>&#x1F3A4;</span> // Microphone icon
          )}
        </button>

        <button
          className={`control-button tab-audio ${isCapturingTabAudio ? 'capturing' : ''}`}
          onClick={isCapturingTabAudio ? stopTranscription : startTabAudioCapture}
          disabled={!window.SpeechRecognition && !window.webkitSpeechRecognition}
          title="Capture audio from another tab like YouTube (will be identified as the Interviewer)"
        >
          {isCapturingTabAudio ? (
            <span role="img" aria-label="Stop Tab Audio" style={{fontSize: '1.5em'}}>&#x23F9;</span> // Stop icon
          ) : (
            <span role="img" aria-label="Start Tab Audio" style={{fontSize: '1.5em'}}>&#x1F50A;</span> // Speaker icon
          )}
        </button>

        {isListening && (
          <div className="status">
            <span className={`listening-indicator ${isCapturingTabAudio ? 'interviewer' : 'candidate'}`}></span>
            {isCapturingTabAudio
              ? 'Capturing Tab Audio (Interviewer)...'
              : 'Listening to Your Microphone (Candidate)...'}
          </div>
        )}
      </div>
    </div>
  );
}

export default DiarizedTranscription;
