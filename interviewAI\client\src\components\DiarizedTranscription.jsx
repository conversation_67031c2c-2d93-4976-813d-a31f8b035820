import { useState, useEffect, useRef } from 'react';
import './DiarizedTranscription.css';
import env from '../utils/env';

function DiarizedTranscription({ onTranscriptChange }) {
  // State for transcription
  const [transcript, setTranscript] = useState('');
  const [diarizedSegments, setDiarizedSegments] = useState([]);
  const [isListening, setIsListening] = useState(false);
  const [isCapturingTabAudio, setIsCapturingTabAudio] = useState(false);
  const [currentSpeaker, setCurrentSpeaker] = useState('unknown');
  
  // Refs
  const recognitionRef = useRef(null);
  const finalTranscriptRef = useRef('');
  const audioContextRef = useRef(null);
  const analyserRef = useRef(null);
  const mediaStreamRef = useRef(null);
  const speakerChangeThresholdRef = useRef(0.3); // Threshold for detecting speaker changes
  
  // Initialize speech recognition
  useEffect(() => {
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    if (!SpeechRecognition) {
      console.error("Speech Recognition API not supported in this browser");
      return;
    }
    
    // Clean up function
    return () => {
      if (recognitionRef.current) {
        try {
          recognitionRef.current.stop();
        } catch (error) {
          console.error("Error stopping recognition:", error);
        }
      }
      
      if (audioContextRef.current) {
        try {
          audioContextRef.current.close();
        } catch (error) {
          console.error("Error closing audio context:", error);
        }
      }
      
      if (mediaStreamRef.current) {
        mediaStreamRef.current.getTracks().forEach(track => track.stop());
      }
    };
  }, []);
  
  // Start listening with microphone
  const startListening = async () => {
    try {
      if (isListening) return;
      
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      recognitionRef.current = new SpeechRecognition();
      recognitionRef.current.continuous = true;
      recognitionRef.current.interimResults = true;
      recognitionRef.current.lang = 'en-US';
      
      // Reset transcript
      finalTranscriptRef.current = '';
      setTranscript('');
      setDiarizedSegments([]);
      
      // Set up audio context for voice analysis
      if (!audioContextRef.current) {
        audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();
      }
      
      // Get microphone stream
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      mediaStreamRef.current = stream;
      
      // Set up audio analyzer
      const source = audioContextRef.current.createMediaStreamSource(stream);
      analyserRef.current = audioContextRef.current.createAnalyser();
      analyserRef.current.fftSize = 2048;
      source.connect(analyserRef.current);
      
      // Start monitoring audio characteristics for speaker changes
      monitorAudioCharacteristics();
      
      // Set up recognition event handlers
      setupRecognitionHandlers();
      
      // Start recognition
      recognitionRef.current.start();
      setIsListening(true);
      setIsCapturingTabAudio(false);
      
      console.log("Microphone listening started");
    } catch (error) {
      console.error("Error starting microphone listening:", error);
      alert("Failed to start microphone: " + error.message);
    }
  };
  
  // Start capturing tab audio
  const startTabAudioCapture = async () => {
    try {
      if (isListening) return;
      
      // Request screen sharing with audio
      const stream = await navigator.mediaDevices.getDisplayMedia({
        video: true,
        audio: true
      });
      
      // Check if audio is included
      const audioTracks = stream.getAudioTracks();
      if (audioTracks.length === 0) {
        alert("No audio track found. Please make sure to select 'Share audio' when sharing the tab.");
        stream.getTracks().forEach(track => track.stop());
        return;
      }
      
      mediaStreamRef.current = stream;
      
      // Set up audio context for analysis
      if (!audioContextRef.current) {
        audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();
      }
      
      // Set up audio analyzer
      const source = audioContextRef.current.createMediaStreamSource(stream);
      analyserRef.current = audioContextRef.current.createAnalyser();
      analyserRef.current.fftSize = 2048;
      source.connect(analyserRef.current);
      
      // Start monitoring audio characteristics
      monitorAudioCharacteristics();
      
      // Initialize speech recognition
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      recognitionRef.current = new SpeechRecognition();
      recognitionRef.current.continuous = true;
      recognitionRef.current.interimResults = true;
      recognitionRef.current.lang = 'en-US';
      
      // Reset transcript
      finalTranscriptRef.current = '';
      setTranscript('');
      setDiarizedSegments([]);
      
      // Set up recognition event handlers
      setupRecognitionHandlers();
      
      // Start recognition
      recognitionRef.current.start();
      setIsListening(true);
      setIsCapturingTabAudio(true);
      
      // Handle stream ending
      stream.getVideoTracks()[0].onended = () => {
        stopTranscription();
      };
      
      console.log("Tab audio capture started");
    } catch (error) {
      console.error("Error capturing tab audio:", error);
      alert("Failed to capture tab audio: " + error.message);
    }
  };
  
  // Stop transcription
  const stopTranscription = () => {
    if (recognitionRef.current) {
      try {
        recognitionRef.current.stop();
      } catch (error) {
        console.error("Error stopping recognition:", error);
      }
    }
    
    if (mediaStreamRef.current) {
      mediaStreamRef.current.getTracks().forEach(track => track.stop());
      mediaStreamRef.current = null;
    }
    
    setIsListening(false);
    setIsCapturingTabAudio(false);
    console.log("Transcription stopped");
  };
  
  // Set up recognition event handlers
  const setupRecognitionHandlers = () => {
    if (!recognitionRef.current) return;
    
    recognitionRef.current.onresult = (event) => {
      let interimTranscript = '';
      
      for (let i = event.resultIndex; i < event.results.length; i++) {
        const result = event.results[i];
        const text = result[0].transcript;
        
        if (result.isFinal) {
          // Add to final transcript with current speaker
          finalTranscriptRef.current += text + ' ';
          
          // Add as a new segment with the current speaker
          setDiarizedSegments(prev => [
            ...prev,
            {
              text,
              speaker: currentSpeaker,
              timestamp: new Date(),
              isFinal: true
            }
          ]);
        } else {
          interimTranscript += text;
        }
      }
      
      const fullTranscript = finalTranscriptRef.current + interimTranscript;
      setTranscript(fullTranscript);
      
      // Notify parent component
      if (onTranscriptChange) {
        onTranscriptChange(fullTranscript, diarizedSegments);
      }
    };
    
    recognitionRef.current.onerror = (event) => {
      console.error("Recognition error:", event.error);
      if (event.error !== 'no-speech') {
        stopTranscription();
      }
    };
    
    recognitionRef.current.onend = () => {
      console.log("Recognition ended");
      // Restart if we're still supposed to be listening
      if (isListening) {
        try {
          recognitionRef.current.start();
        } catch (error) {
          console.error("Failed to restart recognition:", error);
          setIsListening(false);
        }
      }
    };
  };
  
  // Monitor audio characteristics to detect speaker changes
  const monitorAudioCharacteristics = () => {
    if (!analyserRef.current) return;
    
    const bufferLength = analyserRef.current.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);
    let previousAudioProfile = null;
    let speakerChangeCount = 0;
    
    const analyze = () => {
      if (!analyserRef.current || !isListening) return;
      
      analyserRef.current.getByteFrequencyData(dataArray);
      
      // Create a simple audio profile from frequency data
      // This is a simplified approach - real diarization uses more complex algorithms
      const audioProfile = calculateAudioProfile(dataArray);
      
      // Detect potential speaker change
      if (previousAudioProfile) {
        const difference = calculateProfileDifference(audioProfile, previousAudioProfile);
        
        if (difference > speakerChangeThresholdRef.current) {
          speakerChangeCount++;
          
          // Require multiple consecutive differences to confirm speaker change
          if (speakerChangeCount >= 3) {
            // Switch speaker
            const newSpeaker = currentSpeaker === 'speaker1' ? 'speaker2' : 'speaker1';
            setCurrentSpeaker(newSpeaker);
            console.log(`Speaker change detected: ${currentSpeaker} -> ${newSpeaker}`);
            speakerChangeCount = 0;
          }
        } else {
          speakerChangeCount = 0;
        }
      }
      
      previousAudioProfile = audioProfile;
      requestAnimationFrame(analyze);
    };
    
    analyze();
  };
  
  // Calculate a simple audio profile from frequency data
  const calculateAudioProfile = (frequencyData) => {
    // Divide frequency spectrum into bands and calculate average energy
    const bands = 4;
    const bandSize = Math.floor(frequencyData.length / bands);
    const profile = [];
    
    for (let i = 0; i < bands; i++) {
      let sum = 0;
      for (let j = 0; j < bandSize; j++) {
        sum += frequencyData[i * bandSize + j];
      }
      profile.push(sum / bandSize);
    }
    
    return profile;
  };
  
  // Calculate difference between audio profiles
  const calculateProfileDifference = (profile1, profile2) => {
    let totalDiff = 0;
    for (let i = 0; i < profile1.length; i++) {
      totalDiff += Math.abs(profile1[i] - profile2[i]);
    }
    return totalDiff / profile1.length;
  };
  
  return (
    <div className="diarized-transcription">
      {!window.SpeechRecognition && !window.webkitSpeechRecognition && (
        <div className="browser-warning">
          <p>
            <strong>Warning:</strong> Your browser doesn't support speech recognition. 
            Please use Chrome, Edge, or another Chromium-based browser for this feature to work.
          </p>
        </div>
      )}
      
      <div className="transcript-display">
        {diarizedSegments.length > 0 ? (
          <div className="diarized-segments">
            {diarizedSegments.map((segment, index) => (
              <div 
                key={index} 
                className={`segment ${segment.speaker}`}
              >
                <div className="speaker-label">{segment.speaker === 'speaker1' ? 'Interviewer' : 'Candidate'}</div>
                <div className="segment-text">{segment.text}</div>
              </div>
            ))}
            
            {/* Current interim transcript */}
            {transcript && finalTranscriptRef.current !== transcript && (
              <div className={`segment ${currentSpeaker} interim`}>
                <div className="speaker-label">{currentSpeaker === 'speaker1' ? 'Interviewer' : 'Candidate'}</div>
                <div className="segment-text">{transcript.substring(finalTranscriptRef.current.length)}</div>
              </div>
            )}
          </div>
        ) : (
          <p className="placeholder">Waiting for speech...</p>
        )}
      </div>
      
      <div className="controls">
        <button 
          className={`control-button ${isListening && !isCapturingTabAudio ? 'listening' : ''}`}
          onClick={isListening ? stopTranscription : startListening}
          disabled={!window.SpeechRecognition && !window.webkitSpeechRecognition}
          title="Capture microphone audio"
        >
          {isListening && !isCapturingTabAudio ? 'Stop' : 'Start'} Mic Listening
        </button>
        
        <button 
          className={`control-button tab-audio ${isCapturingTabAudio ? 'capturing' : ''}`}
          onClick={isCapturingTabAudio ? stopTranscription : startTabAudioCapture}
          disabled={!window.SpeechRecognition && !window.webkitSpeechRecognition}
          title="Capture audio from another tab (e.g., YouTube)"
        >
          {isCapturingTabAudio ? 'Stop' : 'Start'} Tab Audio Capture
        </button>
        
        {isListening && (
          <div className="status">
            <span className="listening-indicator"></span>
            {isCapturingTabAudio ? 'Capturing Tab Audio...' : 'Listening to Microphone...'}
          </div>
        )}
      </div>
    </div>
  );
}

export default DiarizedTranscription;
