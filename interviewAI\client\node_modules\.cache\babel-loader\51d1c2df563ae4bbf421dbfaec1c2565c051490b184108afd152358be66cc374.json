{"ast": null, "code": "import React,{useState}from'react';// import { useNavigate } from 'react-router-dom';\nimport Header from'../../components/Header';import InterviewForm from'../../components/InterviewForm';import SpeechToText from'../../components/SpeechToText';import VoiceTranscriber from'../../components/VoiceTranscriber';import MeetingTranscriber from'../../components/MeetingTranscriber';import'./admin-home.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function AdminHomeScreen(){const[showSpeechToText,setShowSpeechToText]=useState(false);const[showVoiceTranscriber,setShowVoiceTranscriber]=useState(false);const[showMeetingTranscriber,setShowMeetingTranscriber]=useState(false);React.useEffect(()=>{const userDetails=localStorage.getItem(\"userDetails\");if(!userDetails){window.location.href=\"/login\";}},[]);const startSession=config=>{// Store session time in localStorage if provided\nif(config&&config.sessionTimeRemaining){try{const userDetails=localStorage.getItem('userDetails');let userData=userDetails?JSON.parse(userDetails):{};userData.timeRemaining=config.sessionTimeRemaining;localStorage.setItem('userDetails',JSON.stringify(userData));}catch(error){console.error('Error saving session time to localStorage:',error);}}// Directly show the SpeechToText component\nsetShowSpeechToText(true);};const endSession=()=>{// Just go back to the form\nsetShowSpeechToText(false);};// const toggleSpeechToText = () => {\n//   setShowSpeechToText(!showSpeechToText);\n//   if (!showSpeechToText) {\n//     setSessionActive(false);\n//     setShowVoiceTranscriber(false);\n//     setShowMeetingTranscriber(false);\n//   }\n// };\n// const toggleVoiceTranscriber = () => {\n//   setShowVoiceTranscriber(!showVoiceTranscriber);\n//   if (!showVoiceTranscriber) {\n//     setSessionActive(false);\n//     setShowSpeechToText(false);\n//     setShowMeetingTranscriber(false);\n//   }\n// };\n// const toggleMeetingTranscriber = () => {\n//   setShowMeetingTranscriber(!showMeetingTranscriber);\n//   if (!showMeetingTranscriber) {\n//     setSessionActive(false);\n//     setShowSpeechToText(false);\n//     setShowVoiceTranscriber(false);\n//   }\n// };\nconst handleBackFromVoiceTranscriber=()=>{setShowVoiceTranscriber(false);};const handleBackFromMeetingTranscriber=()=>{setShowMeetingTranscriber(false);};// const navigateToDeepgramTranscription = () => {\n//   navigate('/deepgram-transcription');\n// };\nreturn/*#__PURE__*/_jsxs(\"div\",{className:\"App\",children:[!showVoiceTranscriber&&!showMeetingTranscriber&&!showSpeechToText&&/*#__PURE__*/_jsx(Header,{}),/*#__PURE__*/_jsx(\"main\",{className:showVoiceTranscriber||showMeetingTranscriber||showSpeechToText?'fullscreen':'',children:showSpeechToText?/*#__PURE__*/_jsx(SpeechToText,{onBack:endSession}):showVoiceTranscriber?/*#__PURE__*/_jsx(VoiceTranscriber,{onBack:handleBackFromVoiceTranscriber}):showMeetingTranscriber?/*#__PURE__*/_jsx(MeetingTranscriber,{onBack:handleBackFromMeetingTranscriber}):/*#__PURE__*/_jsx(InterviewForm,{onStartSession:startSession})})]});}export default AdminHomeScreen;", "map": {"version": 3, "names": ["React", "useState", "Header", "InterviewForm", "SpeechToText", "VoiceTranscriber", "MeetingTranscriber", "jsx", "_jsx", "jsxs", "_jsxs", "AdminHomeScreen", "showSpeechToText", "setShowSpeechToText", "showVoiceTranscriber", "setShowVoiceTranscriber", "showMeetingTranscriber", "setShowMeetingTranscriber", "useEffect", "userDetails", "localStorage", "getItem", "window", "location", "href", "startSession", "config", "sessionTimeRemaining", "userData", "JSON", "parse", "timeRemaining", "setItem", "stringify", "error", "console", "endSession", "handleBackFromVoiceTranscriber", "handleBackFromMeetingTranscriber", "className", "children", "onBack", "onStartSession"], "sources": ["C:/Users/<USER>/Documents/augment-projects/interview-ai-app/interviewAI/client/src/pages/admin-home/index.jsx"], "sourcesContent": ["import React, { useState } from 'react';\r\n// import { useNavigate } from 'react-router-dom';\r\nimport Header from '../../components/Header';\r\nimport InterviewForm from '../../components/InterviewForm';\r\nimport SpeechToText from '../../components/SpeechToText';\r\nimport VoiceTranscriber from '../../components/VoiceTranscriber';\r\nimport MeetingTranscriber from '../../components/MeetingTranscriber';\r\nimport './admin-home.css';\r\n\r\nfunction AdminHomeScreen() {\r\n  const [showSpeechToText, setShowSpeechToText] = useState(false);\r\n  const [showVoiceTranscriber, setShowVoiceTranscriber] = useState(false);\r\n  const [showMeetingTranscriber, setShowMeetingTranscriber] = useState(false);\r\n\r\n  React.useEffect(() => {\r\n    const userDetails = localStorage.getItem(\"userDetails\");\r\n    if (!userDetails) {\r\n      window.location.href = \"/login\";\r\n    }\r\n  }, []);\r\n\r\n  const startSession = (config) => {\r\n    // Store session time in localStorage if provided\r\n    if (config && config.sessionTimeRemaining) {\r\n      try {\r\n        const userDetails = localStorage.getItem('userDetails');\r\n        let userData = userDetails ? JSON.parse(userDetails) : {};\r\n\r\n        userData.timeRemaining = config.sessionTimeRemaining;\r\n        localStorage.setItem('userDetails', JSON.stringify(userData));\r\n      } catch (error) {\r\n        console.error('Error saving session time to localStorage:', error);\r\n      }\r\n    }\r\n\r\n    // Directly show the SpeechToText component\r\n    setShowSpeechToText(true);\r\n  };\r\n\r\n  const endSession = () => {\r\n    // Just go back to the form\r\n    setShowSpeechToText(false);\r\n  };\r\n\r\n  // const toggleSpeechToText = () => {\r\n  //   setShowSpeechToText(!showSpeechToText);\r\n  //   if (!showSpeechToText) {\r\n  //     setSessionActive(false);\r\n  //     setShowVoiceTranscriber(false);\r\n  //     setShowMeetingTranscriber(false);\r\n  //   }\r\n  // };\r\n\r\n  // const toggleVoiceTranscriber = () => {\r\n  //   setShowVoiceTranscriber(!showVoiceTranscriber);\r\n  //   if (!showVoiceTranscriber) {\r\n  //     setSessionActive(false);\r\n  //     setShowSpeechToText(false);\r\n  //     setShowMeetingTranscriber(false);\r\n  //   }\r\n  // };\r\n\r\n  // const toggleMeetingTranscriber = () => {\r\n  //   setShowMeetingTranscriber(!showMeetingTranscriber);\r\n  //   if (!showMeetingTranscriber) {\r\n  //     setSessionActive(false);\r\n  //     setShowSpeechToText(false);\r\n  //     setShowVoiceTranscriber(false);\r\n  //   }\r\n  // };\r\n\r\n  const handleBackFromVoiceTranscriber = () => {\r\n    setShowVoiceTranscriber(false);\r\n  };\r\n\r\n  const handleBackFromMeetingTranscriber = () => {\r\n    setShowMeetingTranscriber(false);\r\n  };\r\n\r\n  // const navigateToDeepgramTranscription = () => {\r\n  //   navigate('/deepgram-transcription');\r\n  // };\r\n\r\n  return (\r\n    <div className=\"App\">\r\n      {!showVoiceTranscriber && !showMeetingTranscriber && !showSpeechToText && <Header />}\r\n\r\n      {/* {!showVoiceTranscriber && !showMeetingTranscriber && !showSpeechToText && (\r\n        <div className=\"nav-buttons\">\r\n          <button\r\n            onClick={() => {\r\n              setShowSpeechToText(false);\r\n              setShowVoiceTranscriber(false);\r\n              setShowMeetingTranscriber(false);\r\n              setSessionActive(false);\r\n            }}\r\n            className={!sessionActive && !showSpeechToText && !showVoiceTranscriber && !showMeetingTranscriber ? 'active' : ''}\r\n          >\r\n            Home\r\n          </button>\r\n          <button\r\n            onClick={toggleSpeechToText}\r\n            className={showSpeechToText ? 'active' : ''}\r\n          >\r\n            Interview AI Tool\r\n          </button>\r\n          <button\r\n            onClick={toggleVoiceTranscriber}\r\n            className={showVoiceTranscriber ? 'active' : ''}\r\n          >\r\n            Voice Transcriber\r\n          </button>\r\n          <button\r\n            onClick={toggleMeetingTranscriber}\r\n            className={showMeetingTranscriber ? 'active' : ''}\r\n          >\r\n            Meeting Transcriber\r\n          </button>\r\n          <button\r\n            onClick={navigateToDeepgramTranscription}\r\n            className=\"highlight\"\r\n          >\r\n            Deepgram Transcription\r\n          </button>\r\n        </div>\r\n      )} */}\r\n\r\n      <main className={showVoiceTranscriber || showMeetingTranscriber || showSpeechToText ? 'fullscreen' : ''}>\r\n        {showSpeechToText ? (\r\n          <SpeechToText onBack={endSession} />\r\n        ) : showVoiceTranscriber ? (\r\n          <VoiceTranscriber onBack={handleBackFromVoiceTranscriber} />\r\n        ) : showMeetingTranscriber ? (\r\n          <MeetingTranscriber onBack={handleBackFromMeetingTranscriber} />\r\n        ) : (\r\n          <InterviewForm onStartSession={startSession} />\r\n        )}\r\n      </main>\r\n    </div>\r\n  );\r\n}\r\nexport default AdminHomeScreen;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC;AACA,MAAO,CAAAC,MAAM,KAAM,yBAAyB,CAC5C,MAAO,CAAAC,aAAa,KAAM,gCAAgC,CAC1D,MAAO,CAAAC,YAAY,KAAM,+BAA+B,CACxD,MAAO,CAAAC,gBAAgB,KAAM,mCAAmC,CAChE,MAAO,CAAAC,kBAAkB,KAAM,qCAAqC,CACpE,MAAO,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1B,QAAS,CAAAC,eAAeA,CAAA,CAAG,CACzB,KAAM,CAACC,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGZ,QAAQ,CAAC,KAAK,CAAC,CAC/D,KAAM,CAACa,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGd,QAAQ,CAAC,KAAK,CAAC,CACvE,KAAM,CAACe,sBAAsB,CAAEC,yBAAyB,CAAC,CAAGhB,QAAQ,CAAC,KAAK,CAAC,CAE3ED,KAAK,CAACkB,SAAS,CAAC,IAAM,CACpB,KAAM,CAAAC,WAAW,CAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC,CACvD,GAAI,CAACF,WAAW,CAAE,CAChBG,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAG,QAAQ,CACjC,CACF,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAC,YAAY,CAAIC,MAAM,EAAK,CAC/B;AACA,GAAIA,MAAM,EAAIA,MAAM,CAACC,oBAAoB,CAAE,CACzC,GAAI,CACF,KAAM,CAAAR,WAAW,CAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC,CACvD,GAAI,CAAAO,QAAQ,CAAGT,WAAW,CAAGU,IAAI,CAACC,KAAK,CAACX,WAAW,CAAC,CAAG,CAAC,CAAC,CAEzDS,QAAQ,CAACG,aAAa,CAAGL,MAAM,CAACC,oBAAoB,CACpDP,YAAY,CAACY,OAAO,CAAC,aAAa,CAAEH,IAAI,CAACI,SAAS,CAACL,QAAQ,CAAC,CAAC,CAC/D,CAAE,MAAOM,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,4CAA4C,CAAEA,KAAK,CAAC,CACpE,CACF,CAEA;AACArB,mBAAmB,CAAC,IAAI,CAAC,CAC3B,CAAC,CAED,KAAM,CAAAuB,UAAU,CAAGA,CAAA,GAAM,CACvB;AACAvB,mBAAmB,CAAC,KAAK,CAAC,CAC5B,CAAC,CAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,KAAM,CAAAwB,8BAA8B,CAAGA,CAAA,GAAM,CAC3CtB,uBAAuB,CAAC,KAAK,CAAC,CAChC,CAAC,CAED,KAAM,CAAAuB,gCAAgC,CAAGA,CAAA,GAAM,CAC7CrB,yBAAyB,CAAC,KAAK,CAAC,CAClC,CAAC,CAED;AACA;AACA;AAEA,mBACEP,KAAA,QAAK6B,SAAS,CAAC,KAAK,CAAAC,QAAA,EACjB,CAAC1B,oBAAoB,EAAI,CAACE,sBAAsB,EAAI,CAACJ,gBAAgB,eAAIJ,IAAA,CAACN,MAAM,GAAE,CAAC,cA0CpFM,IAAA,SAAM+B,SAAS,CAAEzB,oBAAoB,EAAIE,sBAAsB,EAAIJ,gBAAgB,CAAG,YAAY,CAAG,EAAG,CAAA4B,QAAA,CACrG5B,gBAAgB,cACfJ,IAAA,CAACJ,YAAY,EAACqC,MAAM,CAAEL,UAAW,CAAE,CAAC,CAClCtB,oBAAoB,cACtBN,IAAA,CAACH,gBAAgB,EAACoC,MAAM,CAAEJ,8BAA+B,CAAE,CAAC,CAC1DrB,sBAAsB,cACxBR,IAAA,CAACF,kBAAkB,EAACmC,MAAM,CAAEH,gCAAiC,CAAE,CAAC,cAEhE9B,IAAA,CAACL,aAAa,EAACuC,cAAc,CAAEjB,YAAa,CAAE,CAC/C,CACG,CAAC,EACJ,CAAC,CAEV,CACA,cAAe,CAAAd,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}