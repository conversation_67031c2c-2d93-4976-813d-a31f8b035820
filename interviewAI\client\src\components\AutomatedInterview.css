.automated-interview {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  background-color: #f8f9fa;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Configure Step */
.configure-step {
  max-width: 600px;
  margin: 50px auto;
  padding: 30px;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.configure-step h2 {
  margin-top: 0;
  color: #333;
  font-size: 24px;
  margin-bottom: 20px;
}

.config-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 30px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-weight: 500;
  color: #555;
}

.form-group select {
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 16px;
  background-color: white;
}

.api-status {
  margin: 20px 0;
  padding: 12px 16px;
  border-radius: 6px;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.api-status.success {
  background-color: #e6f4ea;
  color: #137333;
  border: 1px solid #ceead6;
}

.api-status.warning {
  background-color: #fef7e0;
  color: #b06000;
  border: 1px solid #feefc3;
}

.api-status .icon {
  font-size: 18px;
  font-weight: bold;
}

.connect-button {
  background-color: #4285f4;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 5px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-top: 10px;
}

.connect-button:hover {
  background-color: #3367d6;
}

/* Connect Step */
.connect-step {
  max-width: 600px;
  margin: 50px auto;
  padding: 30px;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.connect-step h2 {
  margin-top: 0;
  color: #333;
  font-size: 24px;
  margin-bottom: 20px;
}

.connect-step p {
  color: #555;
  margin-bottom: 10px;
  line-height: 1.5;
}

.connection-options {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin: 20px 0;
  width: 100%;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

.share-screen-button {
  background-color: #4285f4;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 5px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-bottom: 5px;
}

.share-screen-button:hover {
  background-color: #3367d6;
}

.share-screen-button.with-audio {
  background-color: #34a853;
}

.share-screen-button.with-audio:hover {
  background-color: #2d9249;
}

.option-description {
  background-color: #f8f9fa;
  border-radius: 4px;
  padding: 15px;
  margin-top: 10px;
  font-size: 14px;
  color: #5f6368;
  text-align: left;
}

.option-description p {
  margin: 5px 0;
}

.back-button {
  background-color: transparent;
  color: #555;
  border: 1px solid #ddd;
  padding: 10px 20px;
  border-radius: 5px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.back-button:hover {
  background-color: #f5f5f5;
}

/* Interview Step */
.interview-step {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
}

.interview-container {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.transcription-panel,
.response-panel {
  display: flex;
  flex-direction: column;
  flex: 1;
  border-right: 1px solid #eee;
  background-color: white;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
  background-color: #f8f9fa;
}

.panel-header h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.connection-status {
  font-size: 14px;
}

.connected {
  color: #34a853;
  display: flex;
  align-items: center;
}

.connected::before {
  content: '';
  display: inline-block;
  width: 8px;
  height: 8px;
  background-color: #34a853;
  border-radius: 50%;
  margin-right: 6px;
}

.disconnected {
  color: #ea4335;
  display: flex;
  align-items: center;
}

.disconnected::before {
  content: '';
  display: inline-block;
  width: 8px;
  height: 8px;
  background-color: #ea4335;
  border-radius: 50%;
  margin-right: 6px;
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.transcript-content,
.response-content {
  height: 100%;
  overflow-y: auto;
}

.transcript-messages {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.transcript-message {
  background-color: #f0f4f8;
  padding: 12px 16px;
  border-radius: 12px;
  position: relative;
  max-width: 85%;
  align-self: flex-start;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  border-bottom-left-radius: 4px;
}

.transcript-message:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: -6px;
  width: 12px;
  height: 12px;
  background-color: #f0f4f8;
  border-bottom-right-radius: 12px;
  clip-path: polygon(100% 0, 100% 100%, 0 100%);
}

.transcript-message.current {
  background-color: #e6f2ff;
  border-left: 3px solid #4285f4;
}

.transcript-message.current:after {
  background-color: #e6f2ff;
}

.response-message {
  background-color: #f0f8f4;
  padding: 12px 16px;
  border-radius: 12px;
  position: relative;
  max-width: 85%;
  align-self: flex-end;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  margin-left: auto;
  border-bottom-right-radius: 4px;
}

.timestamp {
  font-size: 12px;
  color: #888;
  margin-bottom: 4px;
}

.message-text {
  font-size: 15px;
  line-height: 1.5;
  color: #333;
  white-space: pre-wrap;
  word-break: break-word;
}

.message-text .placeholder {
  color: #999;
  font-style: italic;
}

.message-text .active-transcript {
  color: #1a73e8;
  font-weight: 500;
  animation: pulse-text 2s infinite;
}

@keyframes pulse-text {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

.debug-info {
  margin-top: 20px;
  padding: 10px;
  background-color: #f8f9fa;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
  color: #333;
}

.empty-state {
  color: #999;
  text-align: center;
  padding: 40px 20px;
  font-style: italic;
}

.controls-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: #f8f9fa;
  border-top: 1px solid #eee;
}

.status-indicators {
  display: flex;
  align-items: center;
  gap: 15px;
}

.timer-display {
  font-size: 18px;
  font-weight: 500;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.recording-dot {
  width: 10px;
  height: 10px;
  background-color: #ea4335;
  border-radius: 50%;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

.status-label {
  font-size: 14px;
  color: #555;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
}

.api-badge {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
  margin-top: 2px;
}

.api-badge.google {
  background-color: #4285f4;
  color: white;
}

.api-badge.browser {
  background-color: #f1f3f4;
  color: #5f6368;
  border: 1px solid #dadce0;
}

.api-badge.tab-audio {
  background-color: #34a853;
  color: white;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.mic-button {
  background-color: #4285f4;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  font-size: 15px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.mic-button:hover {
  background-color: #3367d6;
}

.mic-button.active {
  background-color: #34a853;
}

.mic-button.active:hover {
  background-color: #2e7d32;
}

.stop-button {
  background-color: #ea4335;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  font-size: 15px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.stop-button:hover {
  background-color: #d32f2f;
}

.test-button {
  background-color: #fbbc04;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  font-size: 15px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.test-button:hover {
  background-color: #f9a825;
}

.test-button.speech {
  background-color: #9c27b0;
}

.test-button.speech:hover {
  background-color: #7b1fa2;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .interview-container {
    flex-direction: column;
  }

  .transcription-panel,
  .response-panel {
    flex: none;
    height: 50%;
    border-right: none;
    border-bottom: 1px solid #eee;
  }
}
