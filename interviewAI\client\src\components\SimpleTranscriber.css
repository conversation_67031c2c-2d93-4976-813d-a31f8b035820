.simple-transcriber {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 15px;
  margin: 20px 0;
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.transcript-display {
  min-height: 100px;
  max-height: 200px;
  overflow-y: auto;
  padding: 10px;
  border: 1px solid #eee;
  border-radius: 4px;
  margin-bottom: 15px;
  background-color: #f9f9f9;
}

.transcript-text {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
  color: #333;
  font-size: 16px;
  line-height: 1.5;
}

.placeholder {
  color: #999;
  font-style: italic;
  margin: 0;
}

.controls {
  display: flex;
  align-items: center;
  gap: 15px;
}

.control-button {
  background-color: #4285f4;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.control-button:hover {
  background-color: #3367d6;
}

.control-button.listening {
  background-color: #ea4335;
}

.control-button.listening:hover {
  background-color: #d33426;
}

.status {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #555;
  font-size: 14px;
}

.listening-indicator {
  display: inline-block;
  width: 10px;
  height: 10px;
  background-color: #ea4335;
  border-radius: 50%;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.4;
  }
  100% {
    opacity: 1;
  }
}
