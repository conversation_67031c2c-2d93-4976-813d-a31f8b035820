.simple-transcriber {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 15px;
  margin: 20px 0;
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.transcript-display {
  min-height: 100px;
  max-height: 200px;
  overflow-y: auto;
  padding: 10px;
  border: 1px solid #eee;
  border-radius: 4px;
  margin-bottom: 15px;
  background-color: #f9f9f9;
  transition: all 0.3s ease;
}

.transcript-display.listening {
  border-color: #4285f4;
  box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
  background-color: #f0f7ff;
}

.transcript-text {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
  color: #333;
  font-size: 16px;
  line-height: 1.5;
}

.placeholder {
  color: #999;
  font-style: italic;
  margin: 0;
}

.controls {
  display: flex;
  align-items: center;
  gap: 15px;
}

.control-button {
  background-color: #4285f4;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.control-button:hover {
  background-color: #3367d6;
}

.control-button.listening {
  background-color: #ea4335;
}

.control-button.listening:hover {
  background-color: #d33426;
}

.control-button.tab-audio {
  background-color: #34a853;
  margin-left: 10px;
}

.control-button.tab-audio:hover {
  background-color: #2d9249;
}

.control-button.tab-audio.capturing {
  background-color: #fbbc05;
  color: #333;
}

.control-button.tab-audio.capturing:hover {
  background-color: #f0b400;
}

.status {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #555;
  font-size: 14px;
}

.listening-indicator {
  display: inline-block;
  width: 10px;
  height: 10px;
  background-color: #ea4335;
  border-radius: 50%;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.4;
  }
  100% {
    opacity: 1;
  }
}

.browser-warning {
  background-color: #fff3cd;
  color: #856404;
  padding: 10px 15px;
  border: 1px solid #ffeeba;
  border-radius: 4px;
  margin-bottom: 15px;
}

.browser-warning p {
  margin: 0;
  font-size: 14px;
}

.debug-info {
  margin-top: 20px;
  padding: 10px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
}

.debug-info p {
  margin: 5px 0;
}
