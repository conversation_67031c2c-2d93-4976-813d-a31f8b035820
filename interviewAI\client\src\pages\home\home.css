/* Home Page Styles */
.home-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.home-content {
  flex: 1;
}

/* Hero Section */
.hero-section {
  background: linear-gradient(135deg, #6e8efb, #a777e3);
  color: white;
  padding: 80px 0;
  text-align: center;
}

.hero-section h1 {
  font-size: 3rem;
  margin-bottom: 20px;
}

.subtitle {
  font-size: 1.5rem;
  margin-bottom: 40px;
  opacity: 0.9;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.cta-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.cta-button {
  display: inline-block;
  padding: 15px 30px;
  border-radius: 30px;
  font-weight: bold;
  text-decoration: none;
  transition: all 0.3s ease;
}

.cta-button.primary {
  background-color: white;
  color: #6e8efb;
}

.cta-button.primary:hover {
  background-color: #f0f0f0;
  transform: translateY(-3px);
}

.cta-button.secondary {
  background-color: transparent;
  border: 2px solid white;
  color: white;
}

.cta-button.secondary:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-3px);
}

.cta-button.highlight {
  background-color: #34a853;
  color: white;
  border: none;
  margin-top: 10px;
  animation: pulse-button 2s infinite;
}

.cta-button.highlight:hover {
  background-color: #2d9249;
  transform: translateY(-3px);
}

.cta-button.highlight-new {
  background-color: #9c27b0;
  color: white;
  border: none;
  margin-top: 10px;
  animation: pulse-button-purple 2s infinite;
}

.cta-button.highlight-new:hover {
  background-color: #7b1fa2;
  transform: translateY(-3px);
}

.cta-button.highlight-google {
  background-color: #4285f4;
  color: white;
  border: none;
  margin-top: 10px;
  animation: pulse-button-blue 2s infinite;
}

.cta-button.highlight-google:hover {
  background-color: #3367d6;
  transform: translateY(-3px);
}

@keyframes pulse-button {
  0% {
    box-shadow: 0 0 0 0 rgba(52, 168, 83, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(52, 168, 83, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(52, 168, 83, 0);
  }
}

@keyframes pulse-button-purple {
  0% {
    box-shadow: 0 0 0 0 rgba(156, 39, 176, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(156, 39, 176, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(156, 39, 176, 0);
  }
}

.cta-button.logout {
  background-color: #f44336;
  color: white;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-weight: 500;
  font-size: 16px;
}

.cta-button.logout:hover {
  background-color: #d32f2f;
  transform: translateY(-3px);
}

.logout-icon {
  margin-right: 8px;
  font-size: 20px;
}

@keyframes pulse-button-blue {
  0% {
    box-shadow: 0 0 0 0 rgba(66, 133, 244, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(66, 133, 244, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(66, 133, 244, 0);
  }
}

/* How It Works Section */
.how-it-works-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  position: relative;
}

.how-it-works-section h2 {
  text-align: center;
  font-size: 3rem;
  color: #333;
  margin-bottom: 20px;
  font-weight: 700;
}

.section-subtitle {
  text-align: center;
  font-size: 1.3rem;
  color: #666;
  margin-bottom: 60px;
  font-weight: 400;
}

.steps-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 40px;
  margin-bottom: 80px;
}

.step-card {
  background: white;
  border-radius: 20px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.step-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.step-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #6e8efb, #a777e3);
}

.step-content {
  order: 1;
  margin-bottom: 25px;
}

.step-number {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #6e8efb, #a777e3);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: bold;
  margin: 0 auto 20px;
  box-shadow: 0 5px 15px rgba(110, 142, 251, 0.3);
}

.step-content h3 {
  text-align: center;
  font-size: 1.4rem;
  color: #333;
  margin-bottom: 15px;
  font-weight: 600;
}

.step-content p {
  text-align: center;
  color: #666;
  line-height: 1.6;
  margin-bottom: 0;
  font-size: 0.95rem;
}

.step-visual {
  order: 2;
  display: flex;
  justify-content: center;
  margin-top: 0;
}

/* Mini Interface Styles */
.mini-interface {
  background: #ffffff;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 12px;
  max-width: 200px;
  width: 100%;
  font-size: 11px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: 0 auto;
}

.mini-header {
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  padding-bottom: 6px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.mini-timer {
  background: #6e8efb;
  color: white;
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 10px;
  font-weight: normal;
}

.mini-field {
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
  margin-bottom: 4px;
  font-size: 10px;
  color: #555;
  border: 1px solid #e9ecef;
}

.mini-button {
  background: #28a745;
  color: white;
  padding: 6px;
  border-radius: 4px;
  text-align: center;
  margin-top: 8px;
  font-weight: 600;
  font-size: 10px;
}

.mini-response {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 8px;
  margin-bottom: 8px;
  min-height: 30px;
  font-size: 10px;
  color: #666;
  line-height: 1.3;
}

.mini-response.filled {
  background: #e8f5e8;
  border-color: #28a745;
  color: #333;
}

.mini-question {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 6px;
  font-size: 10px;
  color: #555;
  font-style: italic;
}

.mini-stats {
  text-align: center;
  margin: 8px 0;
  font-size: 10px;
  color: #666;
  font-weight: 500;
}

.mini-stats span {
  color: #333;
  font-weight: 600;
}

.mini-success {
  text-align: center;
  font-weight: 600;
  padding: 6px;
  background: #e8f5e8;
  border-radius: 4px;
  color: #28a745;
  font-size: 10px;
}

.demo-cta {
  text-align: center;
  margin-top: 60px;
}

.demo-button {
  display: inline-block;
  background: linear-gradient(135deg, #6e8efb, #a777e3);
  color: white;
  padding: 18px 40px;
  border-radius: 50px;
  text-decoration: none;
  font-weight: bold;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  box-shadow: 0 5px 20px rgba(110, 142, 251, 0.3);
}

.demo-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(110, 142, 251, 0.4);
}

.demo-subtitle {
  margin-top: 15px;
  color: #666;
  font-style: italic;
}

/* Demo Interface Preview */
.demo-interface-preview {
  margin-top: 40px;
  padding: 40px 20px;
  background: #f8f9fa;
  border-radius: 20px;
  border: 1px solid #e9ecef;
}

.demo-arrow {
  text-align: center;
  margin-bottom: 30px;
}

.demo-conversation {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  margin-bottom: 30px;
  max-width: 1000px;
  margin-left: auto;
  margin-right: auto;
}

.demo-interviewer,
.demo-ai {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
}

.demo-avatar {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.avatar-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.avatar-icon.ai {
  background: linear-gradient(135deg, #6e8efb, #a777e3);
}

.speaker-label {
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.demo-message {
  color: #555;
  line-height: 1.6;
  font-size: 14px;
  margin-bottom: 15px;
}

.demo-message.ai-response {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  border-left: 4px solid #6e8efb;
}

.response-section {
  margin-bottom: 15px;
}

.response-section:last-child {
  margin-bottom: 0;
}

.response-section strong {
  color: #333;
  display: block;
  margin-bottom: 8px;
}

.response-section p {
  margin: 4px 0;
  color: #666;
  font-size: 13px;
  line-height: 1.5;
}

.demo-status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #6e8efb;
  font-style: italic;
}

.status-dot {
  width: 8px;
  height: 8px;
  background: #6e8efb;
  border-radius: 50%;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.demo-input-area {
  max-width: 600px;
  margin: 0 auto;
}

.demo-input-container {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
}

.demo-response-box {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 15px;
  min-height: 100px;
  display: flex;
  align-items: center;
}

.response-placeholder {
  color: #999;
  font-style: italic;
  font-size: 14px;
}

.demo-question-box {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 15px;
  min-height: 50px;
  display: flex;
  align-items: center;
}

.question-placeholder {
  color: #999;
  font-style: italic;
  font-size: 14px;
}

/* Responsive Design for How It Works */
@media (max-width: 768px) {
  .how-it-works-section {
    padding: 60px 0;
  }

  .how-it-works-section h2 {
    font-size: 2.2rem;
  }

  .steps-container {
    grid-template-columns: 1fr;
    gap: 30px;
    margin-bottom: 50px;
  }

  .step-card {
    padding: 25px 20px;
  }

  .mini-interface {
    max-width: 180px;
  }

  .demo-button {
    padding: 15px 30px;
    font-size: 1rem;
  }

  .demo-interface-preview {
    padding: 20px 15px;
  }

  .demo-conversation {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .demo-interviewer,
  .demo-ai {
    padding: 15px;
  }

  .demo-message {
    font-size: 13px;
  }

  .response-section p {
    font-size: 12px;
  }

  .demo-input-container {
    padding: 15px;
  }

  .demo-response-box {
    padding: 15px;
    min-height: 80px;
  }

  .demo-question-box {
    padding: 12px;
    min-height: 40px;
  }
}

/* Features Section */
.features-section {
  padding: 80px 0;
  background-color: #f9f9f9;
}

.features-section h2 {
  text-align: center;
  margin-bottom: 50px;
  font-size: 2.5rem;
  color: #333;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.feature-card {
  background-color: white;
  border-radius: 10px;
  padding: 30px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-10px);
}

.feature-card.highlight {
  border: 2px solid #6e8efb;
  position: relative;
  overflow: hidden;
}

.feature-card.highlight::before {
  content: 'NEW';
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: #6e8efb;
  color: white;
  padding: 4px 8px;
  font-size: 12px;
  font-weight: bold;
  border-radius: 4px;
}

.feature-icon {
  font-size: 2.5rem;
  margin-bottom: 20px;
}

.feature-card h3 {
  margin-bottom: 15px;
  color: #333;
}

.feature-card p {
  color: #666;
  line-height: 1.6;
}

/* Pricing Section Styles */
.pricing-section {
  padding: 80px 0;
  background-color: #f8f9ff;
}

.pricing-section h2 {
  text-align: center;
  margin-bottom: 50px;
  font-size: 2.5rem;
  color: #333;
}

.pricing-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-top: 50px;
}

.pricing-card {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
}

.pricing-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.pricing-card.premium {
  background: linear-gradient(to bottom, #ffffff, #f0f4ff);
  border: 2px solid #6e8efb;
  transform: scale(1.05);
}

.pricing-card.premium:hover {
  transform: scale(1.05) translateY(-10px);
}

.pricing-badge {
  position: absolute;
  top: 0;
  right: 0;
  background: #6e8efb;
  color: white;
  padding: 8px 15px;
  font-size: 14px;
  font-weight: 600;
  border-bottom-left-radius: 8px;
}

.pricing-header {
  padding: 30px;
  text-align: center;
  border-bottom: 1px solid #eee;
}

.pricing-header h3 {
  margin: 0;
  font-size: 24px;
  color: #333;
}

.price {
  margin-top: 15px;
  display: flex;
  align-items: baseline;
  justify-content: center;
}

.currency {
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.amount {
  font-size: 48px;
  font-weight: 700;
  color: #333;
  line-height: 1;
  margin: 0 5px;
}

.period {
  font-size: 14px;
  color: #777;
}

.pricing-features {
  padding: 30px;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  font-size: 15px;
}

.feature-item:last-child {
  margin-bottom: 0;
}

.feature-item span:first-child {
  margin-right: 10px;
  flex-shrink: 0;
}

.feature-item.available .check {
  color: #4CAF50;
  font-weight: bold;
}

.feature-item.unavailable {
  color: #999;
}

.feature-item.unavailable .cross {
  color: #F44336;
}

.pricing-cta {
  display: block;
  text-align: center;
  margin: 0 30px 30px;
  padding: 12px 0;
  background-color: #6e8efb;
  color: white;
  text-decoration: none;
  border-radius: 6px;
  font-weight: 600;
  transition: background-color 0.3s;
}

.pricing-cta:hover {
  background-color: #5a7df9;
}

@media (max-width: 992px) {
  .pricing-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }

  .pricing-card.premium {
    transform: scale(1);
  }

  .pricing-card.premium:hover {
    transform: translateY(-10px);
  }
}

@media (max-width: 768px) {
  .pricing-section {
    padding: 60px 0;
  }

  .pricing-grid {
    gap: 20px;
  }
}

/* FAQ Section */
.faq-section {
  padding: 80px 0;
  background-color: #f9f9f9;
}

.faq-section h2 {
  text-align: center;
  margin-bottom: 50px;
  font-size: 2.5rem;
  color: #333;
}

.faq-list {
  max-width: 800px;
  margin: 0 auto;
}

.faq-item {
  background-color: white;
  border-radius: 10px;
  padding: 25px;
  margin-bottom: 20px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.faq-item h3 {
  color: #333;
  margin-bottom: 15px;
  font-size: 1.2rem;
}

.faq-item p {
  color: #666;
  line-height: 1.6;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .hero-section h1 {
    font-size: 2.5rem;
  }

  .subtitle {
    font-size: 1.2rem;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }

  .pricing-grid {
    grid-template-columns: 1fr;
  }
}
