import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { <PERSON><PERSON>, TextField, Button } from "@mui/material";
import { signupApiCall } from "../../utils/Apicalls";
import SpinnerShow from "../../components/Spinner";
import "./signup.css";

function SignupPage() {
  const navigate = useNavigate();
  const [firstName, setFirstName] = useState("");
  const [userName, setUserName] = useState("");
  const [mobile, setMobile] = useState("");
  const [loading, setLoading] = useState(false);
  const [msgType, setMsgType] = useState("");
  const [error, setError] = useState("");

  const validateForm = () => {
    if (!firstName.trim()) {
      setError("Please enter your name");
      return false;
    }
    if (!userName.trim()) {
      setError("Please enter your email");
      return false;
    }
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(userName)) {
      setError("Please enter a valid email address");
      return false;
    }
    // mobile is optional, no validation
    return true;
  };

  const onSubmit = async () => {
    if (!validateForm()) {
      return;
    }
    setLoading(true);
    setError("");
    try {
      const result = await signupApiCall({
        firstName: firstName,
        email: userName,
        mobile: mobile,
      });
      setLoading(false);
      if (result?.status) {
        setMsgType("redirectLogin");
        setTimeout(() => {
          navigate("/login");
        }, 3000);
      } else {
        setError(result?.message || "Failed to create account. Please try again.");
      }
    } catch (err) {
      setLoading(false);
      setError("An error occurred while creating your account. Please try again later.");
      console.error("Signup error:", err);
    }
  };

  const handleLoginClick = (e) => {
    e.preventDefault();
    navigate("/login");
  };

  return (
    <div className="login-container">
      <div className="screen">
        <div className="screen__content">
          {loading && (
            <div style={{ marginTop: 20 }}>
              <SpinnerShow />
            </div>
          )}
          {error && !loading && (
            <div style={{ padding: "20px 30px 0" }}>
              <Alert severity="error">{error}</Alert>
            </div>
          )}
          {!loading && !msgType && (
            <div className="login">
              <h2 className="signup-title">Create Your Account</h2>
              <div className="login__field">
                <TextField
                  id="outlined-basic"
                  label="Name"
                  variant="outlined"
                  value={firstName}
                  onChange={(e) => setFirstName(e.target.value)}
                  placeholder="Enter your full name"
                  className="w-100"
                  fullWidth
                />
              </div>
              <div className="login__field">
                <TextField
                  id="outlined-basic"
                  label="Email"
                  variant="outlined"
                  value={userName}
                  onChange={(e) => setUserName(e.target.value)}
                  placeholder="Enter your email"
                  className="w-100"
                  fullWidth
                  type="email"
                />
              </div>
              <div className="login__field">
                <TextField
                  id="outlined-basic"
                  label="Mobile (optional)"
                  variant="outlined"
                  value={mobile}
                  onChange={(e) => setMobile(e.target.value)}
                  placeholder="Enter your mobile number"
                  className="w-100"
                  fullWidth
                  type="tel"
                />
              </div>
              <button className="button login__submit" onClick={onSubmit}>
                <span className="button__text">Create Account</span>
                <i className="button__icon fas fa-chevron-right"></i>
              </button>
              <div className="login-link">
                Already have an account? <button className="text-button" onClick={handleLoginClick}>Log in</button>
              </div>
            </div>
          )}
          {!loading && msgType === "redirectLogin" && (
            <div className="login">
              <h3>Account Created Successfully!</h3>
              <p>Please check your email for a temporary password to login.</p>
              <Button variant="contained" onClick={() => navigate("/login")} style={{ marginTop: 20 }}>
                Go to Login
              </Button>
            </div>
          )}
        </div>
        <div className="screen__background">
          <span className="screen__background__shape screen__background__shape4"></span>
          <span className="screen__background__shape screen__background__shape3"></span>
          <span className="screen__background__shape screen__background__shape2"></span>
          <span className="screen__background__shape screen__background__shape1"></span>
        </div>
      </div>
    </div>
  );
}

export default SignupPage;
