{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\interview-ai-app\\\\interviewAI\\\\client\\\\src\\\\components\\\\AutomatedInterview.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect, useCallback } from 'react';\nimport './AutomatedInterview.css';\nimport env from '../utils/env';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction AutomatedInterview() {\n  _s();\n  // Main states\n  const [step, setStep] = useState('configure'); // configure, connect, interview\n  const [isConnected, setIsConnected] = useState(false);\n  const [isListening, setIsListening] = useState(false);\n  const [isRecording, setIsRecording] = useState(false);\n  const [transcriptMessages, setTranscriptMessages] = useState([]);\n  const [currentTranscript, setCurrentTranscript] = useState('');\n  const [response, setResponse] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [usingGoogleAPI, setUsingGoogleAPI] = useState(false);\n  const [isCapturingTabAudio, setIsCapturingTabAudio] = useState(false);\n\n  // Configuration states\n  const [specialization, setSpecialization] = useState('software-engineering');\n  const [language, setLanguage] = useState('english');\n\n  // Timer state\n  const [timerSeconds, setTimerSeconds] = useState(0);\n  const timerIntervalRef = useRef(null);\n  const pauseTimerRef = useRef(null); // Timer for detecting speech pauses\n\n  // Refs\n  const recognitionRef = useRef(null);\n  const transcriptAreaRef = useRef(null);\n  const responseAreaRef = useRef(null);\n  const mediaStreamRef = useRef(null); // For screen sharing stream\n\n  // Format seconds to MM:SS\n  const formatTime = useCallback(totalSeconds => {\n    const minutes = Math.floor(totalSeconds / 60);\n    const seconds = totalSeconds % 60;\n    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\n  }, []);\n\n  // Start the timer\n  const startTimer = useCallback(() => {\n    // Reset timer when starting\n    setTimerSeconds(0);\n\n    // Clear any existing interval\n    if (timerIntervalRef.current) {\n      clearInterval(timerIntervalRef.current);\n    }\n\n    // Start a new interval\n    timerIntervalRef.current = setInterval(() => {\n      setTimerSeconds(prev => prev + 1);\n    }, 1000);\n  }, []);\n\n  // Stop the timer\n  const stopTimer = useCallback(() => {\n    if (timerIntervalRef.current) {\n      clearInterval(timerIntervalRef.current);\n      timerIntervalRef.current = null;\n    }\n  }, []);\n\n  // Send transcript to GPT for response\n  const sendToGPT = useCallback(async text => {\n    console.log(\"sendToGPT called with text:\", text);\n    const apiKey = env.OPENAI_API_KEY;\n    const userText = text || currentTranscript.trim();\n    if (!apiKey) {\n      alert(\"Please add your OpenAI API key to the .env file as REACT_APP_OPENAI_API_KEY and restart the development server.\");\n      return;\n    }\n    if (!userText) {\n      console.warn(\"No transcript to send.\");\n      return;\n    }\n    console.log(\"Processing transcript:\", userText);\n\n    // Note: We're not adding to transcriptMessages here anymore\n    // That's now handled in the checkForSpeechPause function\n    // to avoid duplicate messages\n\n    // Clear the current transcript input if it matches what we're sending\n    // This prevents sending the same text twice\n    if (currentTranscript.trim() === userText) {\n      console.log(\"Clearing current transcript\");\n      setCurrentTranscript('');\n    }\n    setIsLoading(true);\n    setResponse('');\n    try {\n      const response = await fetch(\"https://api.openai.com/v1/chat/completions\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          \"Authorization\": `Bearer ${apiKey}`\n        },\n        body: JSON.stringify({\n          model: \"gpt-4.1-nano\",\n          messages: [{\n            role: \"system\",\n            content: `You are an AI interview assistant for ${specialization} interviews. Provide detailed, well-structured responses to interview questions. Format your answers with bullet points for clarity. Keep responses concise but comprehensive.`\n          }, {\n            role: \"user\",\n            content: userText\n          }],\n          stream: true\n        })\n      });\n      if (!response.ok || !response.body) throw new Error(\"Failed to stream response.\");\n      const reader = response.body.getReader();\n      const decoder = new TextDecoder(\"utf-8\");\n      let result = \"\";\n      while (true) {\n        const {\n          value,\n          done\n        } = await reader.read();\n        if (done) break;\n        const chunk = decoder.decode(value, {\n          stream: true\n        });\n        const lines = chunk.split(\"\\n\").filter(line => line.trim().startsWith(\"data:\"));\n        for (const line of lines) {\n          const data = line.replace(/^data: /, '');\n          if (data === \"[DONE]\") break;\n          try {\n            var _json$choices, _json$choices$, _json$choices$$delta;\n            const json = JSON.parse(data);\n            const content = (_json$choices = json.choices) === null || _json$choices === void 0 ? void 0 : (_json$choices$ = _json$choices[0]) === null || _json$choices$ === void 0 ? void 0 : (_json$choices$$delta = _json$choices$.delta) === null || _json$choices$$delta === void 0 ? void 0 : _json$choices$$delta.content;\n            if (content) {\n              result += content;\n              setResponse(result);\n            }\n          } catch (e) {\n            console.error(\"Error parsing JSON:\", e);\n          }\n        }\n      }\n    } catch (error) {\n      console.error(\"Streaming Error:\", error);\n      setResponse(\"Error occurred: \" + error.message);\n    } finally {\n      setIsLoading(false);\n    }\n  }, [currentTranscript, specialization]); // timerSeconds is not needed here\n\n  // Function to check for speech pause and auto-submit\n  const checkForSpeechPause = useCallback(() => {\n    console.log(\"Checking for speech pause...\");\n\n    // Clear any existing pause timer\n    if (pauseTimerRef.current) {\n      clearTimeout(pauseTimerRef.current);\n      console.log(\"Cleared existing pause timer\");\n    }\n\n    // Set a new pause timer\n    pauseTimerRef.current = setTimeout(() => {\n      console.log(\"Pause timer triggered\");\n\n      // Only auto-submit if:\n      // 1. We have a non-empty transcript\n      // 2. We're currently listening\n      // 3. We're not already loading a response\n      const userText = currentTranscript.trim();\n      console.log(\"Checking conditions for auto-submit:\", {\n        hasText: !!userText,\n        isListening,\n        notLoading: !isLoading\n      });\n      if (userText && isListening && !isLoading) {\n        console.log(\"Auto-submitting transcript:\", userText);\n\n        // Add the transcript to the messages array\n        setTranscriptMessages(prev => {\n          const newMessages = [...prev, {\n            text: userText,\n            timestamp: new Date(),\n            time: timerSeconds\n          }];\n          console.log(\"Updated transcript messages:\", newMessages);\n          return newMessages;\n        });\n\n        // Clear the current transcript\n        setCurrentTranscript(\"\");\n\n        // Send to GPT for response\n        sendToGPT(userText);\n      }\n    }, 2000); // 2 second pause detection (increased from 1.5s for better reliability)\n  }, [currentTranscript, isListening, isLoading, sendToGPT, timerSeconds]);\n\n  // Clean up timer on unmount\n  useEffect(() => {\n    return () => {\n      if (timerIntervalRef.current) {\n        clearInterval(timerIntervalRef.current);\n      }\n      if (pauseTimerRef.current) {\n        clearTimeout(pauseTimerRef.current);\n      }\n    };\n  }, []);\n\n  // Initialize speech recognition\n  useEffect(() => {\n    try {\n      console.log(\"Initializing speech recognition...\");\n\n      // Use the browser's built-in SpeechRecognition API directly for better reliability\n      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n      if (!SpeechRecognition) {\n        throw new Error(\"Speech recognition is not supported in this browser\");\n      }\n\n      // Create a new recognition instance\n      recognitionRef.current = new SpeechRecognition();\n\n      // Configure the recognition\n      recognitionRef.current.lang = language === 'english' ? 'en-US' : language === 'spanish' ? 'es-ES' : language === 'french' ? 'fr-FR' : language === 'german' ? 'de-DE' : language === 'chinese' ? 'zh-CN' : language === 'japanese' ? 'ja-JP' : 'en-US';\n      recognitionRef.current.continuous = true;\n      recognitionRef.current.interimResults = true;\n\n      // Check if Google API is available\n      const hasGoogleAPI = env.hasGoogleSpeechAPI();\n      setUsingGoogleAPI(hasGoogleAPI);\n      console.log(`Using ${hasGoogleAPI ? 'Google Speech-to-Text API' : 'Web Speech API'}`);\n\n      // Reset the transcript\n      let finalTranscript = \"\";\n      console.log(\"Setting up speech recognition event handlers...\");\n      recognitionRef.current.onresult = event => {\n        console.log(\"Speech recognition result received:\", event);\n        let interimTranscript = \"\";\n        for (let i = event.resultIndex; i < event.results.length; i++) {\n          const transcript = event.results[i][0].transcript;\n          const confidence = event.results[i][0].confidence;\n          console.log(`Transcript: \"${transcript}\" (Confidence: ${confidence.toFixed(2)})`);\n          if (event.results[i].isFinal) {\n            finalTranscript += transcript + \" \";\n            console.log(\"Final transcript updated:\", finalTranscript);\n          } else {\n            interimTranscript += transcript;\n            console.log(\"Interim transcript updated:\", interimTranscript);\n          }\n        }\n        const newTranscript = finalTranscript + interimTranscript;\n        console.log(\"Setting current transcript to:\", newTranscript);\n\n        // Force UI update with the new transcript - use a callback to ensure state is updated\n        setCurrentTranscript(prev => {\n          console.log(`Updating transcript from \"${prev}\" to \"${newTranscript}\"`);\n\n          // Force a DOM update by dispatching a custom event\n          window.dispatchEvent(new CustomEvent('transcriptUpdated', {\n            detail: {\n              transcript: newTranscript\n            }\n          }));\n          return newTranscript;\n        });\n\n        // Also update the DOM directly as a fallback\n        const transcriptElement = document.querySelector('.message-text .active-transcript');\n        if (transcriptElement) {\n          transcriptElement.textContent = newTranscript;\n        }\n\n        // Check for pause if transcript has changed\n        if (newTranscript.trim() !== \"\") {\n          console.log(\"Transcript not empty, checking for speech pause\");\n          checkForSpeechPause();\n        }\n      };\n      recognitionRef.current.onerror = event => {\n        console.error(\"Speech recognition error\", event.error, event);\n        if (event.error !== 'no-speech') {\n          console.error(\"Critical speech recognition error:\", event.error);\n          alert(\"Error occurred: \" + event.error);\n          setIsListening(false);\n          stopTimer();\n        } else {\n          console.log(\"No speech detected, continuing to listen\");\n        }\n      };\n      recognitionRef.current.onend = () => {\n        console.log(\"Speech recognition ended\");\n        // Restart recognition if we're still supposed to be listening\n        if (isListening) {\n          console.log(\"Still listening, attempting to restart recognition\");\n          try {\n            recognitionRef.current.start();\n            console.log(\"Recognition restarted successfully\");\n          } catch (error) {\n            console.error(\"Failed to restart recognition:\", error);\n          }\n        } else {\n          console.log(\"Not listening anymore, not restarting recognition\");\n        }\n      };\n\n      // We're not using MediaRecorder anymore to avoid errors\n      console.log(\"Using built-in speech recognition without MediaRecorder\");\n    } catch (error) {\n      console.error(\"Failed to initialize speech recognition:\", error);\n      alert(\"Your browser doesn't support speech recognition. Try Chrome or Edge.\");\n    }\n\n    // Cleanup function\n    return () => {\n      if (recognitionRef.current) {\n        try {\n          recognitionRef.current.stop();\n        } catch (error) {\n          console.error(\"Error stopping recognition:\", error);\n        }\n      }\n    };\n  }, [checkForSpeechPause, isListening, stopTimer, language, usingGoogleAPI]);\n\n  // Auto-scroll transcript area when content changes\n  useEffect(() => {\n    if (transcriptAreaRef.current) {\n      transcriptAreaRef.current.scrollTop = transcriptAreaRef.current.scrollHeight;\n    }\n  }, [transcriptMessages, currentTranscript]);\n\n  // Auto-scroll response area when content changes\n  useEffect(() => {\n    if (responseAreaRef.current) {\n      responseAreaRef.current.scrollTop = responseAreaRef.current.scrollHeight;\n    }\n  }, [response]);\n\n  // Start listening for speech\n  const startListening = useCallback(() => {\n    console.log(\"Starting speech recognition...\");\n    if (recognitionRef.current && !isListening) {\n      try {\n        console.log(\"Calling recognition.start()\");\n        recognitionRef.current.start();\n        console.log(\"Recognition started successfully\");\n        setIsListening(true);\n        startTimer(); // Start the timer when listening begins\n      } catch (error) {\n        console.error(\"Speech recognition error:\", error);\n        // If recognition is already running, stop it first then restart\n        if (error.message.includes(\"already started\")) {\n          console.log(\"Recognition already started, stopping and restarting\");\n          recognitionRef.current.stop();\n          setTimeout(() => {\n            console.log(\"Restarting recognition after stop\");\n            recognitionRef.current.start();\n            setIsListening(true);\n            startTimer(); // Start the timer when listening begins\n          }, 100);\n        }\n      }\n    } else {\n      console.log(\"Cannot start listening:\", recognitionRef.current ? \"Already listening\" : \"No recognition object\");\n    }\n  }, [isListening, startTimer]);\n\n  // Test speech recognition with a simulated result\n  const testSpeechRecognition = useCallback(() => {\n    console.log(\"Testing speech recognition with simulated result\");\n\n    // Create a simulated SpeechRecognitionEvent\n    const simulatedEvent = {\n      resultIndex: 0,\n      results: [{\n        0: {\n          transcript: \"This is a simulated speech recognition result.\",\n          confidence: 0.9\n        },\n        isFinal: true,\n        length: 1\n      }]\n    };\n\n    // If we have a recognition object, manually trigger its onresult handler\n    if (recognitionRef.current && recognitionRef.current.onresult) {\n      console.log(\"Manually triggering onresult handler with simulated event\");\n      recognitionRef.current.onresult(simulatedEvent);\n    } else {\n      console.log(\"Cannot test speech recognition: No recognition object or onresult handler\");\n      // Directly set the transcript as a fallback\n      setCurrentTranscript(\"This is a simulated speech recognition result.\");\n    }\n  }, []);\n\n  // Stop listening for speech\n  const stopListening = useCallback(() => {\n    if (recognitionRef.current && isListening) {\n      try {\n        recognitionRef.current.stop();\n        setIsListening(false);\n        stopTimer(); // Stop the timer when listening ends\n      } catch (error) {\n        console.error(\"Speech recognition error:\", error);\n      }\n    }\n  }, [isListening, stopTimer]);\n\n  // This section was removed to fix the duplicate declaration error\n\n  // Request screen sharing - with audio capture option\n  const requestScreenSharing = async (captureAudio = false) => {\n    try {\n      console.log(`Requesting screen sharing with audio: ${captureAudio}`);\n\n      // Get screen sharing permission with audio if requested\n      const displayStream = await navigator.mediaDevices.getDisplayMedia({\n        video: true,\n        audio: captureAudio // Request audio from screen if captureAudio is true\n      });\n      console.log(\"Screen sharing access granted\");\n      console.log(\"Screen tracks:\", displayStream.getTracks().map(t => `${t.kind} (${t.label})`));\n\n      // Check if audio was included when requested\n      if (captureAudio) {\n        const audioTracks = displayStream.getAudioTracks();\n        if (audioTracks.length === 0) {\n          console.warn(\"No audio track found. Make sure to select 'Share audio' when sharing the tab.\");\n        } else {\n          console.log(\"Audio tracks captured:\", audioTracks.map(t => t.label));\n        }\n      }\n\n      // Store the display stream\n      mediaStreamRef.current = displayStream;\n\n      // Handle the case when user stops sharing\n      displayStream.getVideoTracks()[0].onended = () => {\n        console.log(\"User stopped screen sharing\");\n        setIsConnected(false);\n        stopRecording();\n      };\n\n      // Set connected state and move to interview step\n      setIsConnected(true);\n      setStep('interview');\n\n      // If we're capturing audio from the screen, we need to use that stream for speech recognition\n      if (captureAudio && displayStream.getAudioTracks().length > 0) {\n        console.log(\"Using screen audio for speech recognition\");\n\n        // Create a new recognition instance for the tab audio\n        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n        recognitionRef.current = new SpeechRecognition();\n        recognitionRef.current.continuous = true;\n        recognitionRef.current.interimResults = true;\n        recognitionRef.current.lang = language === 'english' ? 'en-US' : language === 'spanish' ? 'es-ES' : language === 'french' ? 'fr-FR' : language === 'german' ? 'de-DE' : language === 'chinese' ? 'zh-CN' : language === 'japanese' ? 'ja-JP' : 'en-US';\n\n        // Set up event handlers (these will be the same as in the useEffect)\n        let finalTranscript = \"\";\n        recognitionRef.current.onresult = event => {\n          console.log(\"Speech recognition result received:\", event);\n          let interimTranscript = \"\";\n          for (let i = event.resultIndex; i < event.results.length; i++) {\n            const transcript = event.results[i][0].transcript;\n            const confidence = event.results[i][0].confidence;\n            console.log(`Transcript: \"${transcript}\" (Confidence: ${confidence.toFixed(2)})`);\n            if (event.results[i].isFinal) {\n              finalTranscript += transcript + \" \";\n              console.log(\"Final transcript updated:\", finalTranscript);\n            } else {\n              interimTranscript += transcript;\n              console.log(\"Interim transcript updated:\", interimTranscript);\n            }\n          }\n          const newTranscript = finalTranscript + interimTranscript;\n          console.log(\"Setting current transcript to:\", newTranscript);\n          setCurrentTranscript(prev => {\n            console.log(`Updating transcript from \"${prev}\" to \"${newTranscript}\"`);\n            window.dispatchEvent(new CustomEvent('transcriptUpdated', {\n              detail: {\n                transcript: newTranscript\n              }\n            }));\n            return newTranscript;\n          });\n          const transcriptElement = document.querySelector('.message-text .active-transcript');\n          if (transcriptElement) {\n            transcriptElement.textContent = newTranscript;\n          }\n          if (newTranscript.trim() !== \"\") {\n            console.log(\"Transcript not empty, checking for speech pause\");\n            checkForSpeechPause();\n          }\n        };\n        recognitionRef.current.onerror = event => {\n          console.error(\"Speech recognition error\", event.error, event);\n          if (event.error !== 'no-speech') {\n            console.error(\"Critical speech recognition error:\", event.error);\n            alert(\"Error occurred: \" + event.error);\n            setIsListening(false);\n            stopTimer();\n          } else {\n            console.log(\"No speech detected, continuing to listen\");\n          }\n        };\n        recognitionRef.current.onend = () => {\n          console.log(\"Speech recognition ended\");\n          if (isListening) {\n            console.log(\"Still listening, attempting to restart recognition\");\n            try {\n              recognitionRef.current.start();\n              console.log(\"Recognition restarted successfully\");\n            } catch (error) {\n              console.error(\"Failed to restart recognition:\", error);\n            }\n          } else {\n            console.log(\"Not listening anymore, not restarting recognition\");\n          }\n        };\n\n        // Start recognition\n        try {\n          recognitionRef.current.start();\n          setIsListening(true);\n          startTimer();\n          console.log(\"Tab audio recognition started\");\n        } catch (error) {\n          console.error(\"Failed to start tab audio recognition:\", error);\n        }\n      } else {\n        // Start listening for speech from microphone - this is separate from recording\n        startListening();\n        console.log(\"Microphone speech recognition started\");\n      }\n    } catch (error) {\n      console.error(\"Error sharing screen:\", error);\n      alert(\"Failed to share screen: \" + error.message);\n    }\n  };\n\n  // We're not using MediaRecorder anymore to avoid errors\n  // Speech recognition works without recording\n\n  // Stop screen sharing and clean up\n  const stopRecording = () => {\n    console.log(\"Stopping screen sharing...\");\n\n    // We're not using MediaRecorder anymore, but we'll keep the function name for compatibility\n    setIsRecording(false);\n\n    // Safely stop all tracks in the media stream\n    if (mediaStreamRef.current) {\n      try {\n        console.log(\"Stopping all media tracks\");\n        mediaStreamRef.current.getTracks().forEach(track => {\n          try {\n            track.stop();\n            console.log(`Stopped ${track.kind} track: ${track.label}`);\n          } catch (trackError) {\n            console.error(`Error stopping ${track.kind} track:`, trackError);\n          }\n        });\n      } catch (streamError) {\n        console.error(\"Error stopping media stream tracks:\", streamError);\n      }\n\n      // Clear the reference\n      mediaStreamRef.current = null;\n    }\n\n    // Also stop listening\n    stopListening();\n    console.log(\"Screen sharing stopped\");\n  };\n\n  // End the interview\n  const endInterview = () => {\n    stopRecording();\n    stopListening();\n    setStep('configure');\n    setIsConnected(false);\n    setTranscriptMessages([]);\n    setCurrentTranscript('');\n    setResponse('');\n  };\n\n  // Render different steps\n  const renderConfigureStep = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"configure-step\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Configure AI\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 627,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"config-form\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Specialization\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 630,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: specialization,\n          onChange: e => setSpecialization(e.target.value),\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"software-engineering\",\n            children: \"Software Engineering\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 635,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"data-science\",\n            children: \"Data Science\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 636,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"product-management\",\n            children: \"Product Management\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 637,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"marketing\",\n            children: \"Marketing\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 638,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"sales\",\n            children: \"Sales\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 639,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"customer-service\",\n            children: \"Customer Service\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 640,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 631,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 629,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Language\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 645,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: language,\n          onChange: e => setLanguage(e.target.value),\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"english\",\n            children: \"English\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 650,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"spanish\",\n            children: \"Spanish\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 651,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"french\",\n            children: \"French\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 652,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"german\",\n            children: \"German\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 653,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"chinese\",\n            children: \"Chinese\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 654,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"japanese\",\n            children: \"Japanese\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 655,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 646,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 644,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 628,\n      columnNumber: 7\n    }, this), env.hasGoogleSpeechAPI() ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"api-status success\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"icon\",\n        children: \"\\u2713\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 662,\n        columnNumber: 11\n      }, this), \"Google Speech-to-Text API is configured and will be used for better voice recognition\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 661,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"api-status warning\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"icon\",\n        children: \"\\u2139\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 667,\n        columnNumber: 11\n      }, this), \"Google Speech-to-Text API is not configured. The app will use the browser's built-in speech recognition, which may be less accurate.\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 666,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"connect-button\",\n      onClick: () => setStep('connect'),\n      children: \"Next\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 672,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 626,\n    columnNumber: 5\n  }, this);\n  const renderConnectStep = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"connect-step\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Connect to Interview\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 683,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: \"Share your screen to start the interview process.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 684,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: \"Make sure you have the interview window open before proceeding.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 685,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"connection-options\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"share-screen-button\",\n        onClick: () => requestScreenSharing(false),\n        title: \"Share your screen without capturing audio\",\n        children: \"Share Screen (Microphone Input)\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 688,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"share-screen-button with-audio\",\n        onClick: () => requestScreenSharing(true),\n        title: \"Share your screen and capture audio from the shared tab (e.g., YouTube)\",\n        children: \"Share Screen with Tab Audio\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 696,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"option-description\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Microphone Input:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 705,\n            columnNumber: 14\n          }, this), \" Use this option if you want to speak into your microphone.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 705,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Tab Audio:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 706,\n            columnNumber: 14\n          }, this), \" Use this option if you want to capture audio from a YouTube video or other tab.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 706,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 704,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 687,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"back-button\",\n      onClick: () => setStep('configure'),\n      children: \"Back\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 710,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 682,\n    columnNumber: 5\n  }, this);\n  const renderInterviewStep = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"interview-step\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"interview-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"transcription-panel\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"panel-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Interviewer\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 724,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"connection-status\",\n            children: isConnected ? /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"connected\",\n              children: \"Connected\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 727,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"disconnected\",\n              children: \"Disconnected\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 729,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 725,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 723,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"panel-content\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: transcriptAreaRef,\n            className: \"transcript-content\",\n            children: [transcriptMessages.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"transcript-messages\",\n              children: transcriptMessages.map((msg, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"transcript-message\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"timestamp\",\n                  children: formatTime(msg.time)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 743,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"message-text\",\n                  children: msg.text\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 744,\n                  columnNumber: 23\n                }, this)]\n              }, `msg-${index}-${msg.time}`, true, {\n                fileName: _jsxFileName,\n                lineNumber: 742,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 740,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"empty-state\",\n              children: \"Interviewer questions will appear here automatically\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 749,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"transcript-message current\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"timestamp\",\n                children: formatTime(timerSeconds)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 754,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"message-text\",\n                children: currentTranscript ? /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"active-transcript\",\n                  children: currentTranscript\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 757,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"placeholder\",\n                  children: \"Waiting for speech...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 758,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 755,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 753,\n              columnNumber: 15\n            }, this), process.env.NODE_ENV === 'development' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"debug-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [\"Current transcript length: \", (currentTranscript === null || currentTranscript === void 0 ? void 0 : currentTranscript.length) || 0]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 766,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [\"Transcript messages: \", transcriptMessages.length]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 767,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [\"Is listening: \", isListening ? 'Yes' : 'No']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 768,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [\"Is recording: \", isRecording ? 'Yes' : 'No']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 769,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [\"Using Google API: \", usingGoogleAPI ? 'Yes' : 'No']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 770,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 765,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 735,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 734,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 722,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"response-panel\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"panel-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"AI Assistant\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 779,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 778,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"panel-content\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: responseAreaRef,\n            className: \"response-content\",\n            children: response ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"response-message\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"message-text\",\n                children: response\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 789,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 788,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"empty-state\",\n              children: \"AI responses will appear here automatically\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 792,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 783,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 782,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 777,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 721,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"controls-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"status-indicators\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"timer-display\",\n          children: isListening ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"recording-dot\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 804,\n              columnNumber: 17\n            }, this), formatTime(timerSeconds)]\n          }, void 0, true) : formatTime(timerSeconds)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 801,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"status-label\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: isListening ? \"Listening...\" : \"Paused\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 813,\n            columnNumber: 13\n          }, this), usingGoogleAPI && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"api-badge google\",\n            children: \"Google Speech API\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 815,\n            columnNumber: 15\n          }, this), !usingGoogleAPI && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"api-badge browser\",\n            children: \"Browser Speech API\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 818,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 812,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 800,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"action-buttons\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: `mic-button ${isListening ? 'active' : ''}`,\n          onClick: isListening ? stopListening : startListening,\n          children: isListening ? \"Pause\" : \"Resume\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 824,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"stop-button\",\n          onClick: endInterview,\n          children: \"Stop Recording\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 831,\n          columnNumber: 11\n        }, this), process.env.NODE_ENV === 'development' && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"test-button\",\n            onClick: () => {\n              const testText = \"This is a test transcript message. If you can see this, the transcript display is working correctly.\";\n              console.log(\"Adding test transcript:\", testText);\n              setTranscriptMessages(prev => [...prev, {\n                text: testText,\n                timestamp: new Date(),\n                time: timerSeconds\n              }]);\n            },\n            children: \"Add Test Message\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 841,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"test-button speech\",\n            onClick: testSpeechRecognition,\n            children: \"Test Speech Recognition\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 855,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 823,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 799,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 720,\n    columnNumber: 5\n  }, this);\n\n  // Render the appropriate step\n  const renderStep = () => {\n    switch (step) {\n      case 'configure':\n        return renderConfigureStep();\n      case 'connect':\n        return renderConnectStep();\n      case 'interview':\n        return renderInterviewStep();\n      default:\n        return renderConfigureStep();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"automated-interview\",\n    children: renderStep()\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 883,\n    columnNumber: 5\n  }, this);\n}\n_s(AutomatedInterview, \"PmBeBCdmCFDoU81qeraspF3zC7Q=\");\n_c = AutomatedInterview;\nexport default AutomatedInterview;\nvar _c;\n$RefreshReg$(_c, \"AutomatedInterview\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "useCallback", "env", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AutomatedInterview", "_s", "step", "setStep", "isConnected", "setIsConnected", "isListening", "setIsListening", "isRecording", "setIsRecording", "transcriptMessages", "setTranscriptMessages", "currentTranscript", "setCurrentTranscript", "response", "setResponse", "isLoading", "setIsLoading", "usingGoogleAPI", "setUsingGoogleAPI", "isCapturingTabAudio", "setIsCapturingTabAudio", "specialization", "setSpecialization", "language", "setLanguage", "timerSeconds", "setTimerSeconds", "timerIntervalRef", "pauseTimerRef", "recognitionRef", "transcriptAreaRef", "responseAreaRef", "mediaStreamRef", "formatTime", "totalSeconds", "minutes", "Math", "floor", "seconds", "toString", "padStart", "startTimer", "current", "clearInterval", "setInterval", "prev", "stopTimer", "sendToGPT", "text", "console", "log", "<PERSON><PERSON><PERSON><PERSON>", "OPENAI_API_KEY", "userText", "trim", "alert", "warn", "fetch", "method", "headers", "body", "JSON", "stringify", "model", "messages", "role", "content", "stream", "ok", "Error", "reader", "<PERSON><PERSON><PERSON><PERSON>", "decoder", "TextDecoder", "result", "value", "done", "read", "chunk", "decode", "lines", "split", "filter", "line", "startsWith", "data", "replace", "_json$choices", "_json$choices$", "_json$choices$$delta", "json", "parse", "choices", "delta", "e", "error", "message", "checkForSpeechPause", "clearTimeout", "setTimeout", "hasText", "notLoading", "newMessages", "timestamp", "Date", "time", "SpeechRecognition", "window", "webkitSpeechRecognition", "lang", "continuous", "interimResults", "hasGoogleAPI", "hasGoogleSpeechAPI", "finalTranscript", "on<PERSON>ult", "event", "interimTranscript", "i", "resultIndex", "results", "length", "transcript", "confidence", "toFixed", "isFinal", "newTranscript", "dispatchEvent", "CustomEvent", "detail", "transcriptElement", "document", "querySelector", "textContent", "onerror", "onend", "start", "stop", "scrollTop", "scrollHeight", "startListening", "includes", "testSpeechRecognition", "simulatedEvent", "stopListening", "requestScreenSharing", "captureAudio", "displayStream", "navigator", "mediaDevices", "getDisplayMedia", "video", "audio", "getTracks", "map", "t", "kind", "label", "audioTracks", "getAudioTracks", "getVideoTracks", "onended", "stopRecording", "for<PERSON>ach", "track", "trackError", "streamError", "endInterview", "renderConfigureStep", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onChange", "target", "onClick", "renderConnectStep", "title", "renderInterviewStep", "ref", "msg", "index", "process", "NODE_ENV", "testText", "renderStep", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/interview-ai-app/interviewAI/client/src/components/AutomatedInterview.jsx"], "sourcesContent": ["import React, { useState, useRef, useEffect, useCallback } from 'react';\nimport './AutomatedInterview.css';\nimport env from '../utils/env';\n\nfunction AutomatedInterview() {\n  // Main states\n  const [step, setStep] = useState('configure'); // configure, connect, interview\n  const [isConnected, setIsConnected] = useState(false);\n  const [isListening, setIsListening] = useState(false);\n  const [isRecording, setIsRecording] = useState(false);\n  const [transcriptMessages, setTranscriptMessages] = useState([]);\n  const [currentTranscript, setCurrentTranscript] = useState('');\n  const [response, setResponse] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [usingGoogleAPI, setUsingGoogleAPI] = useState(false);\n  const [isCapturingTabAudio, setIsCapturingTabAudio] = useState(false);\n\n  // Configuration states\n  const [specialization, setSpecialization] = useState('software-engineering');\n  const [language, setLanguage] = useState('english');\n\n  // Timer state\n  const [timerSeconds, setTimerSeconds] = useState(0);\n  const timerIntervalRef = useRef(null);\n  const pauseTimerRef = useRef(null); // Timer for detecting speech pauses\n\n  // Refs\n  const recognitionRef = useRef(null);\n  const transcriptAreaRef = useRef(null);\n  const responseAreaRef = useRef(null);\n  const mediaStreamRef = useRef(null); // For screen sharing stream\n\n  // Format seconds to MM:SS\n  const formatTime = useCallback((totalSeconds) => {\n    const minutes = Math.floor(totalSeconds / 60);\n    const seconds = totalSeconds % 60;\n    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\n  }, []);\n\n  // Start the timer\n  const startTimer = useCallback(() => {\n    // Reset timer when starting\n    setTimerSeconds(0);\n\n    // Clear any existing interval\n    if (timerIntervalRef.current) {\n      clearInterval(timerIntervalRef.current);\n    }\n\n    // Start a new interval\n    timerIntervalRef.current = setInterval(() => {\n      setTimerSeconds(prev => prev + 1);\n    }, 1000);\n  }, []);\n\n  // Stop the timer\n  const stopTimer = useCallback(() => {\n    if (timerIntervalRef.current) {\n      clearInterval(timerIntervalRef.current);\n      timerIntervalRef.current = null;\n    }\n  }, []);\n\n  // Send transcript to GPT for response\n  const sendToGPT = useCallback(async (text) => {\n    console.log(\"sendToGPT called with text:\", text);\n\n    const apiKey = env.OPENAI_API_KEY;\n    const userText = text || currentTranscript.trim();\n\n    if (!apiKey) {\n      alert(\"Please add your OpenAI API key to the .env file as REACT_APP_OPENAI_API_KEY and restart the development server.\");\n      return;\n    }\n\n    if (!userText) {\n      console.warn(\"No transcript to send.\");\n      return;\n    }\n\n    console.log(\"Processing transcript:\", userText);\n\n    // Note: We're not adding to transcriptMessages here anymore\n    // That's now handled in the checkForSpeechPause function\n    // to avoid duplicate messages\n\n    // Clear the current transcript input if it matches what we're sending\n    // This prevents sending the same text twice\n    if (currentTranscript.trim() === userText) {\n      console.log(\"Clearing current transcript\");\n      setCurrentTranscript('');\n    }\n\n    setIsLoading(true);\n    setResponse('');\n\n    try {\n      const response = await fetch(\"https://api.openai.com/v1/chat/completions\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          \"Authorization\": `Bearer ${apiKey}`\n        },\n        body: JSON.stringify({\n          model: \"gpt-4.1-nano\",\n          messages: [\n            {\n              role: \"system\",\n              content: `You are an AI interview assistant for ${specialization} interviews. Provide detailed, well-structured responses to interview questions. Format your answers with bullet points for clarity. Keep responses concise but comprehensive.`\n            },\n            { role: \"user\", content: userText }\n          ],\n          stream: true\n        })\n      });\n\n      if (!response.ok || !response.body) throw new Error(\"Failed to stream response.\");\n\n      const reader = response.body.getReader();\n      const decoder = new TextDecoder(\"utf-8\");\n\n      let result = \"\";\n\n      while (true) {\n        const { value, done } = await reader.read();\n        if (done) break;\n\n        const chunk = decoder.decode(value, { stream: true });\n        const lines = chunk.split(\"\\n\").filter(line => line.trim().startsWith(\"data:\"));\n\n        for (const line of lines) {\n          const data = line.replace(/^data: /, '');\n          if (data === \"[DONE]\") break;\n\n          try {\n            const json = JSON.parse(data);\n            const content = json.choices?.[0]?.delta?.content;\n            if (content) {\n              result += content;\n              setResponse(result);\n            }\n          } catch (e) {\n            console.error(\"Error parsing JSON:\", e);\n          }\n        }\n      }\n    } catch (error) {\n      console.error(\"Streaming Error:\", error);\n      setResponse(\"Error occurred: \" + error.message);\n    } finally {\n      setIsLoading(false);\n    }\n  }, [currentTranscript, specialization]); // timerSeconds is not needed here\n\n  // Function to check for speech pause and auto-submit\n  const checkForSpeechPause = useCallback(() => {\n    console.log(\"Checking for speech pause...\");\n\n    // Clear any existing pause timer\n    if (pauseTimerRef.current) {\n      clearTimeout(pauseTimerRef.current);\n      console.log(\"Cleared existing pause timer\");\n    }\n\n    // Set a new pause timer\n    pauseTimerRef.current = setTimeout(() => {\n      console.log(\"Pause timer triggered\");\n\n      // Only auto-submit if:\n      // 1. We have a non-empty transcript\n      // 2. We're currently listening\n      // 3. We're not already loading a response\n      const userText = currentTranscript.trim();\n\n      console.log(\"Checking conditions for auto-submit:\", {\n        hasText: !!userText,\n        isListening,\n        notLoading: !isLoading\n      });\n\n      if (userText && isListening && !isLoading) {\n        console.log(\"Auto-submitting transcript:\", userText);\n\n        // Add the transcript to the messages array\n        setTranscriptMessages(prev => {\n          const newMessages = [\n            ...prev,\n            { text: userText, timestamp: new Date(), time: timerSeconds }\n          ];\n          console.log(\"Updated transcript messages:\", newMessages);\n          return newMessages;\n        });\n\n        // Clear the current transcript\n        setCurrentTranscript(\"\");\n\n        // Send to GPT for response\n        sendToGPT(userText);\n      }\n    }, 2000); // 2 second pause detection (increased from 1.5s for better reliability)\n  }, [currentTranscript, isListening, isLoading, sendToGPT, timerSeconds]);\n\n  // Clean up timer on unmount\n  useEffect(() => {\n    return () => {\n      if (timerIntervalRef.current) {\n        clearInterval(timerIntervalRef.current);\n      }\n      if (pauseTimerRef.current) {\n        clearTimeout(pauseTimerRef.current);\n      }\n    };\n  }, []);\n\n  // Initialize speech recognition\n  useEffect(() => {\n    try {\n      console.log(\"Initializing speech recognition...\");\n\n      // Use the browser's built-in SpeechRecognition API directly for better reliability\n      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n\n      if (!SpeechRecognition) {\n        throw new Error(\"Speech recognition is not supported in this browser\");\n      }\n\n      // Create a new recognition instance\n      recognitionRef.current = new SpeechRecognition();\n\n      // Configure the recognition\n      recognitionRef.current.lang = language === 'english' ? 'en-US' :\n                                   language === 'spanish' ? 'es-ES' :\n                                   language === 'french' ? 'fr-FR' :\n                                   language === 'german' ? 'de-DE' :\n                                   language === 'chinese' ? 'zh-CN' :\n                                   language === 'japanese' ? 'ja-JP' : 'en-US';\n      recognitionRef.current.continuous = true;\n      recognitionRef.current.interimResults = true;\n\n      // Check if Google API is available\n      const hasGoogleAPI = env.hasGoogleSpeechAPI();\n      setUsingGoogleAPI(hasGoogleAPI);\n      console.log(`Using ${hasGoogleAPI ? 'Google Speech-to-Text API' : 'Web Speech API'}`);\n\n      // Reset the transcript\n      let finalTranscript = \"\";\n\n      console.log(\"Setting up speech recognition event handlers...\");\n\n      recognitionRef.current.onresult = (event) => {\n        console.log(\"Speech recognition result received:\", event);\n        let interimTranscript = \"\";\n\n        for (let i = event.resultIndex; i < event.results.length; i++) {\n          const transcript = event.results[i][0].transcript;\n          const confidence = event.results[i][0].confidence;\n          console.log(`Transcript: \"${transcript}\" (Confidence: ${confidence.toFixed(2)})`);\n\n          if (event.results[i].isFinal) {\n            finalTranscript += transcript + \" \";\n            console.log(\"Final transcript updated:\", finalTranscript);\n          } else {\n            interimTranscript += transcript;\n            console.log(\"Interim transcript updated:\", interimTranscript);\n          }\n        }\n\n        const newTranscript = finalTranscript + interimTranscript;\n        console.log(\"Setting current transcript to:\", newTranscript);\n\n        // Force UI update with the new transcript - use a callback to ensure state is updated\n        setCurrentTranscript(prev => {\n          console.log(`Updating transcript from \"${prev}\" to \"${newTranscript}\"`);\n\n          // Force a DOM update by dispatching a custom event\n          window.dispatchEvent(new CustomEvent('transcriptUpdated', {\n            detail: { transcript: newTranscript }\n          }));\n\n          return newTranscript;\n        });\n\n        // Also update the DOM directly as a fallback\n        const transcriptElement = document.querySelector('.message-text .active-transcript');\n        if (transcriptElement) {\n          transcriptElement.textContent = newTranscript;\n        }\n\n        // Check for pause if transcript has changed\n        if (newTranscript.trim() !== \"\") {\n          console.log(\"Transcript not empty, checking for speech pause\");\n          checkForSpeechPause();\n        }\n      };\n\n      recognitionRef.current.onerror = (event) => {\n        console.error(\"Speech recognition error\", event.error, event);\n        if (event.error !== 'no-speech') {\n          console.error(\"Critical speech recognition error:\", event.error);\n          alert(\"Error occurred: \" + event.error);\n          setIsListening(false);\n          stopTimer();\n        } else {\n          console.log(\"No speech detected, continuing to listen\");\n        }\n      };\n\n      recognitionRef.current.onend = () => {\n        console.log(\"Speech recognition ended\");\n        // Restart recognition if we're still supposed to be listening\n        if (isListening) {\n          console.log(\"Still listening, attempting to restart recognition\");\n          try {\n            recognitionRef.current.start();\n            console.log(\"Recognition restarted successfully\");\n          } catch (error) {\n            console.error(\"Failed to restart recognition:\", error);\n          }\n        } else {\n          console.log(\"Not listening anymore, not restarting recognition\");\n        }\n      };\n\n      // We're not using MediaRecorder anymore to avoid errors\n      console.log(\"Using built-in speech recognition without MediaRecorder\");\n\n    } catch (error) {\n      console.error(\"Failed to initialize speech recognition:\", error);\n      alert(\"Your browser doesn't support speech recognition. Try Chrome or Edge.\");\n    }\n\n    // Cleanup function\n    return () => {\n      if (recognitionRef.current) {\n        try {\n          recognitionRef.current.stop();\n        } catch (error) {\n          console.error(\"Error stopping recognition:\", error);\n        }\n      }\n    };\n  }, [checkForSpeechPause, isListening, stopTimer, language, usingGoogleAPI]);\n\n  // Auto-scroll transcript area when content changes\n  useEffect(() => {\n    if (transcriptAreaRef.current) {\n      transcriptAreaRef.current.scrollTop = transcriptAreaRef.current.scrollHeight;\n    }\n  }, [transcriptMessages, currentTranscript]);\n\n  // Auto-scroll response area when content changes\n  useEffect(() => {\n    if (responseAreaRef.current) {\n      responseAreaRef.current.scrollTop = responseAreaRef.current.scrollHeight;\n    }\n  }, [response]);\n\n  // Start listening for speech\n  const startListening = useCallback(() => {\n    console.log(\"Starting speech recognition...\");\n    if (recognitionRef.current && !isListening) {\n      try {\n        console.log(\"Calling recognition.start()\");\n        recognitionRef.current.start();\n        console.log(\"Recognition started successfully\");\n        setIsListening(true);\n        startTimer(); // Start the timer when listening begins\n      } catch (error) {\n        console.error(\"Speech recognition error:\", error);\n        // If recognition is already running, stop it first then restart\n        if (error.message.includes(\"already started\")) {\n          console.log(\"Recognition already started, stopping and restarting\");\n          recognitionRef.current.stop();\n          setTimeout(() => {\n            console.log(\"Restarting recognition after stop\");\n            recognitionRef.current.start();\n            setIsListening(true);\n            startTimer(); // Start the timer when listening begins\n          }, 100);\n        }\n      }\n    } else {\n      console.log(\"Cannot start listening:\",\n        recognitionRef.current ? \"Already listening\" : \"No recognition object\");\n    }\n  }, [isListening, startTimer]);\n\n  // Test speech recognition with a simulated result\n  const testSpeechRecognition = useCallback(() => {\n    console.log(\"Testing speech recognition with simulated result\");\n\n    // Create a simulated SpeechRecognitionEvent\n    const simulatedEvent = {\n      resultIndex: 0,\n      results: [\n        {\n          0: { transcript: \"This is a simulated speech recognition result.\", confidence: 0.9 },\n          isFinal: true,\n          length: 1\n        }\n      ]\n    };\n\n    // If we have a recognition object, manually trigger its onresult handler\n    if (recognitionRef.current && recognitionRef.current.onresult) {\n      console.log(\"Manually triggering onresult handler with simulated event\");\n      recognitionRef.current.onresult(simulatedEvent);\n    } else {\n      console.log(\"Cannot test speech recognition: No recognition object or onresult handler\");\n      // Directly set the transcript as a fallback\n      setCurrentTranscript(\"This is a simulated speech recognition result.\");\n    }\n  }, []);\n\n  // Stop listening for speech\n  const stopListening = useCallback(() => {\n    if (recognitionRef.current && isListening) {\n      try {\n        recognitionRef.current.stop();\n        setIsListening(false);\n        stopTimer(); // Stop the timer when listening ends\n      } catch (error) {\n        console.error(\"Speech recognition error:\", error);\n      }\n    }\n  }, [isListening, stopTimer]);\n\n  // This section was removed to fix the duplicate declaration error\n\n  // Request screen sharing - with audio capture option\n  const requestScreenSharing = async (captureAudio = false) => {\n    try {\n      console.log(`Requesting screen sharing with audio: ${captureAudio}`);\n\n      // Get screen sharing permission with audio if requested\n      const displayStream = await navigator.mediaDevices.getDisplayMedia({\n        video: true,\n        audio: captureAudio // Request audio from screen if captureAudio is true\n      });\n\n      console.log(\"Screen sharing access granted\");\n      console.log(\"Screen tracks:\", displayStream.getTracks().map(t => `${t.kind} (${t.label})`));\n\n      // Check if audio was included when requested\n      if (captureAudio) {\n        const audioTracks = displayStream.getAudioTracks();\n        if (audioTracks.length === 0) {\n          console.warn(\"No audio track found. Make sure to select 'Share audio' when sharing the tab.\");\n        } else {\n          console.log(\"Audio tracks captured:\", audioTracks.map(t => t.label));\n        }\n      }\n\n      // Store the display stream\n      mediaStreamRef.current = displayStream;\n\n      // Handle the case when user stops sharing\n      displayStream.getVideoTracks()[0].onended = () => {\n        console.log(\"User stopped screen sharing\");\n        setIsConnected(false);\n        stopRecording();\n      };\n\n      // Set connected state and move to interview step\n      setIsConnected(true);\n      setStep('interview');\n\n      // If we're capturing audio from the screen, we need to use that stream for speech recognition\n      if (captureAudio && displayStream.getAudioTracks().length > 0) {\n        console.log(\"Using screen audio for speech recognition\");\n\n        // Create a new recognition instance for the tab audio\n        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n        recognitionRef.current = new SpeechRecognition();\n        recognitionRef.current.continuous = true;\n        recognitionRef.current.interimResults = true;\n        recognitionRef.current.lang = language === 'english' ? 'en-US' :\n                                     language === 'spanish' ? 'es-ES' :\n                                     language === 'french' ? 'fr-FR' :\n                                     language === 'german' ? 'de-DE' :\n                                     language === 'chinese' ? 'zh-CN' :\n                                     language === 'japanese' ? 'ja-JP' : 'en-US';\n\n        // Set up event handlers (these will be the same as in the useEffect)\n        let finalTranscript = \"\";\n\n        recognitionRef.current.onresult = (event) => {\n          console.log(\"Speech recognition result received:\", event);\n          let interimTranscript = \"\";\n\n          for (let i = event.resultIndex; i < event.results.length; i++) {\n            const transcript = event.results[i][0].transcript;\n            const confidence = event.results[i][0].confidence;\n            console.log(`Transcript: \"${transcript}\" (Confidence: ${confidence.toFixed(2)})`);\n\n            if (event.results[i].isFinal) {\n              finalTranscript += transcript + \" \";\n              console.log(\"Final transcript updated:\", finalTranscript);\n            } else {\n              interimTranscript += transcript;\n              console.log(\"Interim transcript updated:\", interimTranscript);\n            }\n          }\n\n          const newTranscript = finalTranscript + interimTranscript;\n          console.log(\"Setting current transcript to:\", newTranscript);\n\n          setCurrentTranscript(prev => {\n            console.log(`Updating transcript from \"${prev}\" to \"${newTranscript}\"`);\n\n            window.dispatchEvent(new CustomEvent('transcriptUpdated', {\n              detail: { transcript: newTranscript }\n            }));\n\n            return newTranscript;\n          });\n\n          const transcriptElement = document.querySelector('.message-text .active-transcript');\n          if (transcriptElement) {\n            transcriptElement.textContent = newTranscript;\n          }\n\n          if (newTranscript.trim() !== \"\") {\n            console.log(\"Transcript not empty, checking for speech pause\");\n            checkForSpeechPause();\n          }\n        };\n\n        recognitionRef.current.onerror = (event) => {\n          console.error(\"Speech recognition error\", event.error, event);\n          if (event.error !== 'no-speech') {\n            console.error(\"Critical speech recognition error:\", event.error);\n            alert(\"Error occurred: \" + event.error);\n            setIsListening(false);\n            stopTimer();\n          } else {\n            console.log(\"No speech detected, continuing to listen\");\n          }\n        };\n\n        recognitionRef.current.onend = () => {\n          console.log(\"Speech recognition ended\");\n          if (isListening) {\n            console.log(\"Still listening, attempting to restart recognition\");\n            try {\n              recognitionRef.current.start();\n              console.log(\"Recognition restarted successfully\");\n            } catch (error) {\n              console.error(\"Failed to restart recognition:\", error);\n            }\n          } else {\n            console.log(\"Not listening anymore, not restarting recognition\");\n          }\n        };\n\n        // Start recognition\n        try {\n          recognitionRef.current.start();\n          setIsListening(true);\n          startTimer();\n          console.log(\"Tab audio recognition started\");\n        } catch (error) {\n          console.error(\"Failed to start tab audio recognition:\", error);\n        }\n      } else {\n        // Start listening for speech from microphone - this is separate from recording\n        startListening();\n        console.log(\"Microphone speech recognition started\");\n      }\n\n    } catch (error) {\n      console.error(\"Error sharing screen:\", error);\n      alert(\"Failed to share screen: \" + error.message);\n    }\n  };\n\n  // We're not using MediaRecorder anymore to avoid errors\n  // Speech recognition works without recording\n\n  // Stop screen sharing and clean up\n  const stopRecording = () => {\n    console.log(\"Stopping screen sharing...\");\n\n    // We're not using MediaRecorder anymore, but we'll keep the function name for compatibility\n    setIsRecording(false);\n\n    // Safely stop all tracks in the media stream\n    if (mediaStreamRef.current) {\n      try {\n        console.log(\"Stopping all media tracks\");\n        mediaStreamRef.current.getTracks().forEach(track => {\n          try {\n            track.stop();\n            console.log(`Stopped ${track.kind} track: ${track.label}`);\n          } catch (trackError) {\n            console.error(`Error stopping ${track.kind} track:`, trackError);\n          }\n        });\n      } catch (streamError) {\n        console.error(\"Error stopping media stream tracks:\", streamError);\n      }\n\n      // Clear the reference\n      mediaStreamRef.current = null;\n    }\n\n    // Also stop listening\n    stopListening();\n\n    console.log(\"Screen sharing stopped\");\n  };\n\n  // End the interview\n  const endInterview = () => {\n    stopRecording();\n    stopListening();\n    setStep('configure');\n    setIsConnected(false);\n    setTranscriptMessages([]);\n    setCurrentTranscript('');\n    setResponse('');\n  };\n\n  // Render different steps\n  const renderConfigureStep = () => (\n    <div className=\"configure-step\">\n      <h2>Configure AI</h2>\n      <div className=\"config-form\">\n        <div className=\"form-group\">\n          <label>Specialization</label>\n          <select\n            value={specialization}\n            onChange={(e) => setSpecialization(e.target.value)}\n          >\n            <option value=\"software-engineering\">Software Engineering</option>\n            <option value=\"data-science\">Data Science</option>\n            <option value=\"product-management\">Product Management</option>\n            <option value=\"marketing\">Marketing</option>\n            <option value=\"sales\">Sales</option>\n            <option value=\"customer-service\">Customer Service</option>\n          </select>\n        </div>\n\n        <div className=\"form-group\">\n          <label>Language</label>\n          <select\n            value={language}\n            onChange={(e) => setLanguage(e.target.value)}\n          >\n            <option value=\"english\">English</option>\n            <option value=\"spanish\">Spanish</option>\n            <option value=\"french\">French</option>\n            <option value=\"german\">German</option>\n            <option value=\"chinese\">Chinese</option>\n            <option value=\"japanese\">Japanese</option>\n          </select>\n        </div>\n      </div>\n\n      {env.hasGoogleSpeechAPI() ? (\n        <div className=\"api-status success\">\n          <span className=\"icon\">✓</span>\n          Google Speech-to-Text API is configured and will be used for better voice recognition\n        </div>\n      ) : (\n        <div className=\"api-status warning\">\n          <span className=\"icon\">ℹ</span>\n          Google Speech-to-Text API is not configured. The app will use the browser's built-in speech recognition, which may be less accurate.\n        </div>\n      )}\n\n      <button\n        className=\"connect-button\"\n        onClick={() => setStep('connect')}\n      >\n        Next\n      </button>\n    </div>\n  );\n\n  const renderConnectStep = () => (\n    <div className=\"connect-step\">\n      <h2>Connect to Interview</h2>\n      <p>Share your screen to start the interview process.</p>\n      <p>Make sure you have the interview window open before proceeding.</p>\n\n      <div className=\"connection-options\">\n        <button\n          className=\"share-screen-button\"\n          onClick={() => requestScreenSharing(false)}\n          title=\"Share your screen without capturing audio\"\n        >\n          Share Screen (Microphone Input)\n        </button>\n\n        <button\n          className=\"share-screen-button with-audio\"\n          onClick={() => requestScreenSharing(true)}\n          title=\"Share your screen and capture audio from the shared tab (e.g., YouTube)\"\n        >\n          Share Screen with Tab Audio\n        </button>\n\n        <div className=\"option-description\">\n          <p><strong>Microphone Input:</strong> Use this option if you want to speak into your microphone.</p>\n          <p><strong>Tab Audio:</strong> Use this option if you want to capture audio from a YouTube video or other tab.</p>\n        </div>\n      </div>\n\n      <button\n        className=\"back-button\"\n        onClick={() => setStep('configure')}\n      >\n        Back\n      </button>\n    </div>\n  );\n\n  const renderInterviewStep = () => (\n    <div className=\"interview-step\">\n      <div className=\"interview-container\">\n        <div className=\"transcription-panel\">\n          <div className=\"panel-header\">\n            <h3>Interviewer</h3>\n            <div className=\"connection-status\">\n              {isConnected ? (\n                <span className=\"connected\">Connected</span>\n              ) : (\n                <span className=\"disconnected\">Disconnected</span>\n              )}\n            </div>\n          </div>\n\n          <div className=\"panel-content\">\n            <div\n              ref={transcriptAreaRef}\n              className=\"transcript-content\"\n            >\n              {transcriptMessages.length > 0 ? (\n                <div className=\"transcript-messages\">\n                  {transcriptMessages.map((msg, index) => (\n                    <div key={`msg-${index}-${msg.time}`} className=\"transcript-message\">\n                      <div className=\"timestamp\">{formatTime(msg.time)}</div>\n                      <div className=\"message-text\">{msg.text}</div>\n                    </div>\n                  ))}\n                </div>\n              ) : (\n                <div className=\"empty-state\">Interviewer questions will appear here automatically</div>\n              )}\n\n              {/* Always show current transcript area, even if empty */}\n              <div className=\"transcript-message current\">\n                <div className=\"timestamp\">{formatTime(timerSeconds)}</div>\n                <div className=\"message-text\">\n                  {currentTranscript ?\n                    <span className=\"active-transcript\">{currentTranscript}</span> :\n                    <span className=\"placeholder\">Waiting for speech...</span>\n                  }\n                </div>\n              </div>\n\n              {/* Debug info - only shown in development */}\n              {process.env.NODE_ENV === 'development' && (\n                <div className=\"debug-info\">\n                  <div>Current transcript length: {currentTranscript?.length || 0}</div>\n                  <div>Transcript messages: {transcriptMessages.length}</div>\n                  <div>Is listening: {isListening ? 'Yes' : 'No'}</div>\n                  <div>Is recording: {isRecording ? 'Yes' : 'No'}</div>\n                  <div>Using Google API: {usingGoogleAPI ? 'Yes' : 'No'}</div>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n\n        <div className=\"response-panel\">\n          <div className=\"panel-header\">\n            <h3>AI Assistant</h3>\n          </div>\n\n          <div className=\"panel-content\">\n            <div\n              ref={responseAreaRef}\n              className=\"response-content\"\n            >\n              {response ? (\n                <div className=\"response-message\">\n                  <div className=\"message-text\">{response}</div>\n                </div>\n              ) : (\n                <div className=\"empty-state\">AI responses will appear here automatically</div>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"controls-container\">\n        <div className=\"status-indicators\">\n          <div className=\"timer-display\">\n            {isListening ? (\n              <>\n                <span className=\"recording-dot\"></span>\n                {formatTime(timerSeconds)}\n              </>\n            ) : (\n              formatTime(timerSeconds)\n            )}\n          </div>\n\n          <div className=\"status-label\">\n            <span>{isListening ? \"Listening...\" : \"Paused\"}</span>\n            {usingGoogleAPI && (\n              <span className=\"api-badge google\">Google Speech API</span>\n            )}\n            {!usingGoogleAPI && (\n              <span className=\"api-badge browser\">Browser Speech API</span>\n            )}\n          </div>\n        </div>\n\n        <div className=\"action-buttons\">\n          <button\n            className={`mic-button ${isListening ? 'active' : ''}`}\n            onClick={isListening ? stopListening : startListening}\n          >\n            {isListening ? \"Pause\" : \"Resume\"}\n          </button>\n\n          <button\n            className=\"stop-button\"\n            onClick={endInterview}\n          >\n            Stop Recording\n          </button>\n\n          {/* Test buttons for debugging - only shown in development */}\n          {process.env.NODE_ENV === 'development' && (\n            <>\n              <button\n                className=\"test-button\"\n                onClick={() => {\n                  const testText = \"This is a test transcript message. If you can see this, the transcript display is working correctly.\";\n                  console.log(\"Adding test transcript:\", testText);\n                  setTranscriptMessages(prev => [\n                    ...prev,\n                    { text: testText, timestamp: new Date(), time: timerSeconds }\n                  ]);\n                }}\n              >\n                Add Test Message\n              </button>\n\n              <button\n                className=\"test-button speech\"\n                onClick={testSpeechRecognition}\n              >\n                Test Speech Recognition\n              </button>\n            </>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n\n  // Render the appropriate step\n  const renderStep = () => {\n    switch (step) {\n      case 'configure':\n        return renderConfigureStep();\n      case 'connect':\n        return renderConnectStep();\n      case 'interview':\n        return renderInterviewStep();\n      default:\n        return renderConfigureStep();\n    }\n  };\n\n  return (\n    <div className=\"automated-interview\">\n      {renderStep()}\n    </div>\n  );\n}\n\nexport default AutomatedInterview;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AACvE,OAAO,0BAA0B;AACjC,OAAOC,GAAG,MAAM,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/B,SAASC,kBAAkBA,CAAA,EAAG;EAAAC,EAAA;EAC5B;EACA,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGZ,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACa,WAAW,EAAEC,cAAc,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACe,WAAW,EAAEC,cAAc,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACiB,WAAW,EAAEC,cAAc,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACmB,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAACqB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACuB,QAAQ,EAAEC,WAAW,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACyB,SAAS,EAAEC,YAAY,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC2B,cAAc,EAAEC,iBAAiB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC6B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;;EAErE;EACA,MAAM,CAAC+B,cAAc,EAAEC,iBAAiB,CAAC,GAAGhC,QAAQ,CAAC,sBAAsB,CAAC;EAC5E,MAAM,CAACiC,QAAQ,EAAEC,WAAW,CAAC,GAAGlC,QAAQ,CAAC,SAAS,CAAC;;EAEnD;EACA,MAAM,CAACmC,YAAY,EAAEC,eAAe,CAAC,GAAGpC,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAMqC,gBAAgB,GAAGpC,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMqC,aAAa,GAAGrC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;;EAEpC;EACA,MAAMsC,cAAc,GAAGtC,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMuC,iBAAiB,GAAGvC,MAAM,CAAC,IAAI,CAAC;EACtC,MAAMwC,eAAe,GAAGxC,MAAM,CAAC,IAAI,CAAC;EACpC,MAAMyC,cAAc,GAAGzC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;;EAErC;EACA,MAAM0C,UAAU,GAAGxC,WAAW,CAAEyC,YAAY,IAAK;IAC/C,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,YAAY,GAAG,EAAE,CAAC;IAC7C,MAAMI,OAAO,GAAGJ,YAAY,GAAG,EAAE;IACjC,OAAO,GAAGC,OAAO,CAACI,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,OAAO,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACxF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,UAAU,GAAGhD,WAAW,CAAC,MAAM;IACnC;IACAiC,eAAe,CAAC,CAAC,CAAC;;IAElB;IACA,IAAIC,gBAAgB,CAACe,OAAO,EAAE;MAC5BC,aAAa,CAAChB,gBAAgB,CAACe,OAAO,CAAC;IACzC;;IAEA;IACAf,gBAAgB,CAACe,OAAO,GAAGE,WAAW,CAAC,MAAM;MAC3ClB,eAAe,CAACmB,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IACnC,CAAC,EAAE,IAAI,CAAC;EACV,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,SAAS,GAAGrD,WAAW,CAAC,MAAM;IAClC,IAAIkC,gBAAgB,CAACe,OAAO,EAAE;MAC5BC,aAAa,CAAChB,gBAAgB,CAACe,OAAO,CAAC;MACvCf,gBAAgB,CAACe,OAAO,GAAG,IAAI;IACjC;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMK,SAAS,GAAGtD,WAAW,CAAC,MAAOuD,IAAI,IAAK;IAC5CC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEF,IAAI,CAAC;IAEhD,MAAMG,MAAM,GAAGzD,GAAG,CAAC0D,cAAc;IACjC,MAAMC,QAAQ,GAAGL,IAAI,IAAIrC,iBAAiB,CAAC2C,IAAI,CAAC,CAAC;IAEjD,IAAI,CAACH,MAAM,EAAE;MACXI,KAAK,CAAC,iHAAiH,CAAC;MACxH;IACF;IAEA,IAAI,CAACF,QAAQ,EAAE;MACbJ,OAAO,CAACO,IAAI,CAAC,wBAAwB,CAAC;MACtC;IACF;IAEAP,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEG,QAAQ,CAAC;;IAE/C;IACA;IACA;;IAEA;IACA;IACA,IAAI1C,iBAAiB,CAAC2C,IAAI,CAAC,CAAC,KAAKD,QAAQ,EAAE;MACzCJ,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;MAC1CtC,oBAAoB,CAAC,EAAE,CAAC;IAC1B;IAEAI,YAAY,CAAC,IAAI,CAAC;IAClBF,WAAW,CAAC,EAAE,CAAC;IAEf,IAAI;MACF,MAAMD,QAAQ,GAAG,MAAM4C,KAAK,CAAC,4CAA4C,EAAE;QACzEC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAE,UAAUR,MAAM;QACnC,CAAC;QACDS,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBC,KAAK,EAAE,cAAc;UACrBC,QAAQ,EAAE,CACR;YACEC,IAAI,EAAE,QAAQ;YACdC,OAAO,EAAE,yCAAyC7C,cAAc;UAClE,CAAC,EACD;YAAE4C,IAAI,EAAE,MAAM;YAAEC,OAAO,EAAEb;UAAS,CAAC,CACpC;UACDc,MAAM,EAAE;QACV,CAAC;MACH,CAAC,CAAC;MAEF,IAAI,CAACtD,QAAQ,CAACuD,EAAE,IAAI,CAACvD,QAAQ,CAAC+C,IAAI,EAAE,MAAM,IAAIS,KAAK,CAAC,4BAA4B,CAAC;MAEjF,MAAMC,MAAM,GAAGzD,QAAQ,CAAC+C,IAAI,CAACW,SAAS,CAAC,CAAC;MACxC,MAAMC,OAAO,GAAG,IAAIC,WAAW,CAAC,OAAO,CAAC;MAExC,IAAIC,MAAM,GAAG,EAAE;MAEf,OAAO,IAAI,EAAE;QACX,MAAM;UAAEC,KAAK;UAAEC;QAAK,CAAC,GAAG,MAAMN,MAAM,CAACO,IAAI,CAAC,CAAC;QAC3C,IAAID,IAAI,EAAE;QAEV,MAAME,KAAK,GAAGN,OAAO,CAACO,MAAM,CAACJ,KAAK,EAAE;UAAER,MAAM,EAAE;QAAK,CAAC,CAAC;QACrD,MAAMa,KAAK,GAAGF,KAAK,CAACG,KAAK,CAAC,IAAI,CAAC,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAAC7B,IAAI,CAAC,CAAC,CAAC8B,UAAU,CAAC,OAAO,CAAC,CAAC;QAE/E,KAAK,MAAMD,IAAI,IAAIH,KAAK,EAAE;UACxB,MAAMK,IAAI,GAAGF,IAAI,CAACG,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;UACxC,IAAID,IAAI,KAAK,QAAQ,EAAE;UAEvB,IAAI;YAAA,IAAAE,aAAA,EAAAC,cAAA,EAAAC,oBAAA;YACF,MAAMC,IAAI,GAAG7B,IAAI,CAAC8B,KAAK,CAACN,IAAI,CAAC;YAC7B,MAAMnB,OAAO,IAAAqB,aAAA,GAAGG,IAAI,CAACE,OAAO,cAAAL,aAAA,wBAAAC,cAAA,GAAZD,aAAA,CAAe,CAAC,CAAC,cAAAC,cAAA,wBAAAC,oBAAA,GAAjBD,cAAA,CAAmBK,KAAK,cAAAJ,oBAAA,uBAAxBA,oBAAA,CAA0BvB,OAAO;YACjD,IAAIA,OAAO,EAAE;cACXQ,MAAM,IAAIR,OAAO;cACjBpD,WAAW,CAAC4D,MAAM,CAAC;YACrB;UACF,CAAC,CAAC,OAAOoB,CAAC,EAAE;YACV7C,OAAO,CAAC8C,KAAK,CAAC,qBAAqB,EAAED,CAAC,CAAC;UACzC;QACF;MACF;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd9C,OAAO,CAAC8C,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MACxCjF,WAAW,CAAC,kBAAkB,GAAGiF,KAAK,CAACC,OAAO,CAAC;IACjD,CAAC,SAAS;MACRhF,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,CAACL,iBAAiB,EAAEU,cAAc,CAAC,CAAC,CAAC,CAAC;;EAEzC;EACA,MAAM4E,mBAAmB,GAAGxG,WAAW,CAAC,MAAM;IAC5CwD,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;;IAE3C;IACA,IAAItB,aAAa,CAACc,OAAO,EAAE;MACzBwD,YAAY,CAACtE,aAAa,CAACc,OAAO,CAAC;MACnCO,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;IAC7C;;IAEA;IACAtB,aAAa,CAACc,OAAO,GAAGyD,UAAU,CAAC,MAAM;MACvClD,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;;MAEpC;MACA;MACA;MACA;MACA,MAAMG,QAAQ,GAAG1C,iBAAiB,CAAC2C,IAAI,CAAC,CAAC;MAEzCL,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE;QAClDkD,OAAO,EAAE,CAAC,CAAC/C,QAAQ;QACnBhD,WAAW;QACXgG,UAAU,EAAE,CAACtF;MACf,CAAC,CAAC;MAEF,IAAIsC,QAAQ,IAAIhD,WAAW,IAAI,CAACU,SAAS,EAAE;QACzCkC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEG,QAAQ,CAAC;;QAEpD;QACA3C,qBAAqB,CAACmC,IAAI,IAAI;UAC5B,MAAMyD,WAAW,GAAG,CAClB,GAAGzD,IAAI,EACP;YAAEG,IAAI,EAAEK,QAAQ;YAAEkD,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;YAAEC,IAAI,EAAEhF;UAAa,CAAC,CAC9D;UACDwB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEoD,WAAW,CAAC;UACxD,OAAOA,WAAW;QACpB,CAAC,CAAC;;QAEF;QACA1F,oBAAoB,CAAC,EAAE,CAAC;;QAExB;QACAmC,SAAS,CAACM,QAAQ,CAAC;MACrB;IACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;EACZ,CAAC,EAAE,CAAC1C,iBAAiB,EAAEN,WAAW,EAAEU,SAAS,EAAEgC,SAAS,EAAEtB,YAAY,CAAC,CAAC;;EAExE;EACAjC,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX,IAAImC,gBAAgB,CAACe,OAAO,EAAE;QAC5BC,aAAa,CAAChB,gBAAgB,CAACe,OAAO,CAAC;MACzC;MACA,IAAId,aAAa,CAACc,OAAO,EAAE;QACzBwD,YAAY,CAACtE,aAAa,CAACc,OAAO,CAAC;MACrC;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAlD,SAAS,CAAC,MAAM;IACd,IAAI;MACFyD,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;;MAEjD;MACA,MAAMwD,iBAAiB,GAAGC,MAAM,CAACD,iBAAiB,IAAIC,MAAM,CAACC,uBAAuB;MAEpF,IAAI,CAACF,iBAAiB,EAAE;QACtB,MAAM,IAAIrC,KAAK,CAAC,qDAAqD,CAAC;MACxE;;MAEA;MACAxC,cAAc,CAACa,OAAO,GAAG,IAAIgE,iBAAiB,CAAC,CAAC;;MAEhD;MACA7E,cAAc,CAACa,OAAO,CAACmE,IAAI,GAAGtF,QAAQ,KAAK,SAAS,GAAG,OAAO,GACjCA,QAAQ,KAAK,SAAS,GAAG,OAAO,GAChCA,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAC/BA,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAC/BA,QAAQ,KAAK,SAAS,GAAG,OAAO,GAChCA,QAAQ,KAAK,UAAU,GAAG,OAAO,GAAG,OAAO;MACxEM,cAAc,CAACa,OAAO,CAACoE,UAAU,GAAG,IAAI;MACxCjF,cAAc,CAACa,OAAO,CAACqE,cAAc,GAAG,IAAI;;MAE5C;MACA,MAAMC,YAAY,GAAGtH,GAAG,CAACuH,kBAAkB,CAAC,CAAC;MAC7C/F,iBAAiB,CAAC8F,YAAY,CAAC;MAC/B/D,OAAO,CAACC,GAAG,CAAC,SAAS8D,YAAY,GAAG,2BAA2B,GAAG,gBAAgB,EAAE,CAAC;;MAErF;MACA,IAAIE,eAAe,GAAG,EAAE;MAExBjE,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;MAE9DrB,cAAc,CAACa,OAAO,CAACyE,QAAQ,GAAIC,KAAK,IAAK;QAC3CnE,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEkE,KAAK,CAAC;QACzD,IAAIC,iBAAiB,GAAG,EAAE;QAE1B,KAAK,IAAIC,CAAC,GAAGF,KAAK,CAACG,WAAW,EAAED,CAAC,GAAGF,KAAK,CAACI,OAAO,CAACC,MAAM,EAAEH,CAAC,EAAE,EAAE;UAC7D,MAAMI,UAAU,GAAGN,KAAK,CAACI,OAAO,CAACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAACI,UAAU;UACjD,MAAMC,UAAU,GAAGP,KAAK,CAACI,OAAO,CAACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAACK,UAAU;UACjD1E,OAAO,CAACC,GAAG,CAAC,gBAAgBwE,UAAU,kBAAkBC,UAAU,CAACC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;UAEjF,IAAIR,KAAK,CAACI,OAAO,CAACF,CAAC,CAAC,CAACO,OAAO,EAAE;YAC5BX,eAAe,IAAIQ,UAAU,GAAG,GAAG;YACnCzE,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEgE,eAAe,CAAC;UAC3D,CAAC,MAAM;YACLG,iBAAiB,IAAIK,UAAU;YAC/BzE,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEmE,iBAAiB,CAAC;UAC/D;QACF;QAEA,MAAMS,aAAa,GAAGZ,eAAe,GAAGG,iBAAiB;QACzDpE,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE4E,aAAa,CAAC;;QAE5D;QACAlH,oBAAoB,CAACiC,IAAI,IAAI;UAC3BI,OAAO,CAACC,GAAG,CAAC,6BAA6BL,IAAI,SAASiF,aAAa,GAAG,CAAC;;UAEvE;UACAnB,MAAM,CAACoB,aAAa,CAAC,IAAIC,WAAW,CAAC,mBAAmB,EAAE;YACxDC,MAAM,EAAE;cAAEP,UAAU,EAAEI;YAAc;UACtC,CAAC,CAAC,CAAC;UAEH,OAAOA,aAAa;QACtB,CAAC,CAAC;;QAEF;QACA,MAAMI,iBAAiB,GAAGC,QAAQ,CAACC,aAAa,CAAC,kCAAkC,CAAC;QACpF,IAAIF,iBAAiB,EAAE;UACrBA,iBAAiB,CAACG,WAAW,GAAGP,aAAa;QAC/C;;QAEA;QACA,IAAIA,aAAa,CAACxE,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;UAC/BL,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;UAC9D+C,mBAAmB,CAAC,CAAC;QACvB;MACF,CAAC;MAEDpE,cAAc,CAACa,OAAO,CAAC4F,OAAO,GAAIlB,KAAK,IAAK;QAC1CnE,OAAO,CAAC8C,KAAK,CAAC,0BAA0B,EAAEqB,KAAK,CAACrB,KAAK,EAAEqB,KAAK,CAAC;QAC7D,IAAIA,KAAK,CAACrB,KAAK,KAAK,WAAW,EAAE;UAC/B9C,OAAO,CAAC8C,KAAK,CAAC,oCAAoC,EAAEqB,KAAK,CAACrB,KAAK,CAAC;UAChExC,KAAK,CAAC,kBAAkB,GAAG6D,KAAK,CAACrB,KAAK,CAAC;UACvCzF,cAAc,CAAC,KAAK,CAAC;UACrBwC,SAAS,CAAC,CAAC;QACb,CAAC,MAAM;UACLG,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;QACzD;MACF,CAAC;MAEDrB,cAAc,CAACa,OAAO,CAAC6F,KAAK,GAAG,MAAM;QACnCtF,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;QACvC;QACA,IAAI7C,WAAW,EAAE;UACf4C,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;UACjE,IAAI;YACFrB,cAAc,CAACa,OAAO,CAAC8F,KAAK,CAAC,CAAC;YAC9BvF,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;UACnD,CAAC,CAAC,OAAO6C,KAAK,EAAE;YACd9C,OAAO,CAAC8C,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;UACxD;QACF,CAAC,MAAM;UACL9C,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;QAClE;MACF,CAAC;;MAED;MACAD,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;IAExE,CAAC,CAAC,OAAO6C,KAAK,EAAE;MACd9C,OAAO,CAAC8C,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChExC,KAAK,CAAC,sEAAsE,CAAC;IAC/E;;IAEA;IACA,OAAO,MAAM;MACX,IAAI1B,cAAc,CAACa,OAAO,EAAE;QAC1B,IAAI;UACFb,cAAc,CAACa,OAAO,CAAC+F,IAAI,CAAC,CAAC;QAC/B,CAAC,CAAC,OAAO1C,KAAK,EAAE;UACd9C,OAAO,CAAC8C,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACrD;MACF;IACF,CAAC;EACH,CAAC,EAAE,CAACE,mBAAmB,EAAE5F,WAAW,EAAEyC,SAAS,EAAEvB,QAAQ,EAAEN,cAAc,CAAC,CAAC;;EAE3E;EACAzB,SAAS,CAAC,MAAM;IACd,IAAIsC,iBAAiB,CAACY,OAAO,EAAE;MAC7BZ,iBAAiB,CAACY,OAAO,CAACgG,SAAS,GAAG5G,iBAAiB,CAACY,OAAO,CAACiG,YAAY;IAC9E;EACF,CAAC,EAAE,CAAClI,kBAAkB,EAAEE,iBAAiB,CAAC,CAAC;;EAE3C;EACAnB,SAAS,CAAC,MAAM;IACd,IAAIuC,eAAe,CAACW,OAAO,EAAE;MAC3BX,eAAe,CAACW,OAAO,CAACgG,SAAS,GAAG3G,eAAe,CAACW,OAAO,CAACiG,YAAY;IAC1E;EACF,CAAC,EAAE,CAAC9H,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAM+H,cAAc,GAAGnJ,WAAW,CAAC,MAAM;IACvCwD,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;IAC7C,IAAIrB,cAAc,CAACa,OAAO,IAAI,CAACrC,WAAW,EAAE;MAC1C,IAAI;QACF4C,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;QAC1CrB,cAAc,CAACa,OAAO,CAAC8F,KAAK,CAAC,CAAC;QAC9BvF,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;QAC/C5C,cAAc,CAAC,IAAI,CAAC;QACpBmC,UAAU,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,OAAOsD,KAAK,EAAE;QACd9C,OAAO,CAAC8C,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD;QACA,IAAIA,KAAK,CAACC,OAAO,CAAC6C,QAAQ,CAAC,iBAAiB,CAAC,EAAE;UAC7C5F,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;UACnErB,cAAc,CAACa,OAAO,CAAC+F,IAAI,CAAC,CAAC;UAC7BtC,UAAU,CAAC,MAAM;YACflD,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;YAChDrB,cAAc,CAACa,OAAO,CAAC8F,KAAK,CAAC,CAAC;YAC9BlI,cAAc,CAAC,IAAI,CAAC;YACpBmC,UAAU,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,EAAE,GAAG,CAAC;QACT;MACF;IACF,CAAC,MAAM;MACLQ,OAAO,CAACC,GAAG,CAAC,yBAAyB,EACnCrB,cAAc,CAACa,OAAO,GAAG,mBAAmB,GAAG,uBAAuB,CAAC;IAC3E;EACF,CAAC,EAAE,CAACrC,WAAW,EAAEoC,UAAU,CAAC,CAAC;;EAE7B;EACA,MAAMqG,qBAAqB,GAAGrJ,WAAW,CAAC,MAAM;IAC9CwD,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;;IAE/D;IACA,MAAM6F,cAAc,GAAG;MACrBxB,WAAW,EAAE,CAAC;MACdC,OAAO,EAAE,CACP;QACE,CAAC,EAAE;UAAEE,UAAU,EAAE,gDAAgD;UAAEC,UAAU,EAAE;QAAI,CAAC;QACpFE,OAAO,EAAE,IAAI;QACbJ,MAAM,EAAE;MACV,CAAC;IAEL,CAAC;;IAED;IACA,IAAI5F,cAAc,CAACa,OAAO,IAAIb,cAAc,CAACa,OAAO,CAACyE,QAAQ,EAAE;MAC7DlE,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;MACxErB,cAAc,CAACa,OAAO,CAACyE,QAAQ,CAAC4B,cAAc,CAAC;IACjD,CAAC,MAAM;MACL9F,OAAO,CAACC,GAAG,CAAC,2EAA2E,CAAC;MACxF;MACAtC,oBAAoB,CAAC,gDAAgD,CAAC;IACxE;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMoI,aAAa,GAAGvJ,WAAW,CAAC,MAAM;IACtC,IAAIoC,cAAc,CAACa,OAAO,IAAIrC,WAAW,EAAE;MACzC,IAAI;QACFwB,cAAc,CAACa,OAAO,CAAC+F,IAAI,CAAC,CAAC;QAC7BnI,cAAc,CAAC,KAAK,CAAC;QACrBwC,SAAS,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,OAAOiD,KAAK,EAAE;QACd9C,OAAO,CAAC8C,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACnD;IACF;EACF,CAAC,EAAE,CAAC1F,WAAW,EAAEyC,SAAS,CAAC,CAAC;;EAE5B;;EAEA;EACA,MAAMmG,oBAAoB,GAAG,MAAAA,CAAOC,YAAY,GAAG,KAAK,KAAK;IAC3D,IAAI;MACFjG,OAAO,CAACC,GAAG,CAAC,yCAAyCgG,YAAY,EAAE,CAAC;;MAEpE;MACA,MAAMC,aAAa,GAAG,MAAMC,SAAS,CAACC,YAAY,CAACC,eAAe,CAAC;QACjEC,KAAK,EAAE,IAAI;QACXC,KAAK,EAAEN,YAAY,CAAC;MACtB,CAAC,CAAC;MAEFjG,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC5CD,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEiG,aAAa,CAACM,SAAS,CAAC,CAAC,CAACC,GAAG,CAACC,CAAC,IAAI,GAAGA,CAAC,CAACC,IAAI,KAAKD,CAAC,CAACE,KAAK,GAAG,CAAC,CAAC;;MAE3F;MACA,IAAIX,YAAY,EAAE;QAChB,MAAMY,WAAW,GAAGX,aAAa,CAACY,cAAc,CAAC,CAAC;QAClD,IAAID,WAAW,CAACrC,MAAM,KAAK,CAAC,EAAE;UAC5BxE,OAAO,CAACO,IAAI,CAAC,+EAA+E,CAAC;QAC/F,CAAC,MAAM;UACLP,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE4G,WAAW,CAACJ,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACE,KAAK,CAAC,CAAC;QACtE;MACF;;MAEA;MACA7H,cAAc,CAACU,OAAO,GAAGyG,aAAa;;MAEtC;MACAA,aAAa,CAACa,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,OAAO,GAAG,MAAM;QAChDhH,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;QAC1C9C,cAAc,CAAC,KAAK,CAAC;QACrB8J,aAAa,CAAC,CAAC;MACjB,CAAC;;MAED;MACA9J,cAAc,CAAC,IAAI,CAAC;MACpBF,OAAO,CAAC,WAAW,CAAC;;MAEpB;MACA,IAAIgJ,YAAY,IAAIC,aAAa,CAACY,cAAc,CAAC,CAAC,CAACtC,MAAM,GAAG,CAAC,EAAE;QAC7DxE,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;;QAExD;QACA,MAAMwD,iBAAiB,GAAGC,MAAM,CAACD,iBAAiB,IAAIC,MAAM,CAACC,uBAAuB;QACpF/E,cAAc,CAACa,OAAO,GAAG,IAAIgE,iBAAiB,CAAC,CAAC;QAChD7E,cAAc,CAACa,OAAO,CAACoE,UAAU,GAAG,IAAI;QACxCjF,cAAc,CAACa,OAAO,CAACqE,cAAc,GAAG,IAAI;QAC5ClF,cAAc,CAACa,OAAO,CAACmE,IAAI,GAAGtF,QAAQ,KAAK,SAAS,GAAG,OAAO,GACjCA,QAAQ,KAAK,SAAS,GAAG,OAAO,GAChCA,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAC/BA,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAC/BA,QAAQ,KAAK,SAAS,GAAG,OAAO,GAChCA,QAAQ,KAAK,UAAU,GAAG,OAAO,GAAG,OAAO;;QAExE;QACA,IAAI2F,eAAe,GAAG,EAAE;QAExBrF,cAAc,CAACa,OAAO,CAACyE,QAAQ,GAAIC,KAAK,IAAK;UAC3CnE,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEkE,KAAK,CAAC;UACzD,IAAIC,iBAAiB,GAAG,EAAE;UAE1B,KAAK,IAAIC,CAAC,GAAGF,KAAK,CAACG,WAAW,EAAED,CAAC,GAAGF,KAAK,CAACI,OAAO,CAACC,MAAM,EAAEH,CAAC,EAAE,EAAE;YAC7D,MAAMI,UAAU,GAAGN,KAAK,CAACI,OAAO,CAACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAACI,UAAU;YACjD,MAAMC,UAAU,GAAGP,KAAK,CAACI,OAAO,CAACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAACK,UAAU;YACjD1E,OAAO,CAACC,GAAG,CAAC,gBAAgBwE,UAAU,kBAAkBC,UAAU,CAACC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;YAEjF,IAAIR,KAAK,CAACI,OAAO,CAACF,CAAC,CAAC,CAACO,OAAO,EAAE;cAC5BX,eAAe,IAAIQ,UAAU,GAAG,GAAG;cACnCzE,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEgE,eAAe,CAAC;YAC3D,CAAC,MAAM;cACLG,iBAAiB,IAAIK,UAAU;cAC/BzE,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEmE,iBAAiB,CAAC;YAC/D;UACF;UAEA,MAAMS,aAAa,GAAGZ,eAAe,GAAGG,iBAAiB;UACzDpE,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE4E,aAAa,CAAC;UAE5DlH,oBAAoB,CAACiC,IAAI,IAAI;YAC3BI,OAAO,CAACC,GAAG,CAAC,6BAA6BL,IAAI,SAASiF,aAAa,GAAG,CAAC;YAEvEnB,MAAM,CAACoB,aAAa,CAAC,IAAIC,WAAW,CAAC,mBAAmB,EAAE;cACxDC,MAAM,EAAE;gBAAEP,UAAU,EAAEI;cAAc;YACtC,CAAC,CAAC,CAAC;YAEH,OAAOA,aAAa;UACtB,CAAC,CAAC;UAEF,MAAMI,iBAAiB,GAAGC,QAAQ,CAACC,aAAa,CAAC,kCAAkC,CAAC;UACpF,IAAIF,iBAAiB,EAAE;YACrBA,iBAAiB,CAACG,WAAW,GAAGP,aAAa;UAC/C;UAEA,IAAIA,aAAa,CAACxE,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;YAC/BL,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;YAC9D+C,mBAAmB,CAAC,CAAC;UACvB;QACF,CAAC;QAEDpE,cAAc,CAACa,OAAO,CAAC4F,OAAO,GAAIlB,KAAK,IAAK;UAC1CnE,OAAO,CAAC8C,KAAK,CAAC,0BAA0B,EAAEqB,KAAK,CAACrB,KAAK,EAAEqB,KAAK,CAAC;UAC7D,IAAIA,KAAK,CAACrB,KAAK,KAAK,WAAW,EAAE;YAC/B9C,OAAO,CAAC8C,KAAK,CAAC,oCAAoC,EAAEqB,KAAK,CAACrB,KAAK,CAAC;YAChExC,KAAK,CAAC,kBAAkB,GAAG6D,KAAK,CAACrB,KAAK,CAAC;YACvCzF,cAAc,CAAC,KAAK,CAAC;YACrBwC,SAAS,CAAC,CAAC;UACb,CAAC,MAAM;YACLG,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;UACzD;QACF,CAAC;QAEDrB,cAAc,CAACa,OAAO,CAAC6F,KAAK,GAAG,MAAM;UACnCtF,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;UACvC,IAAI7C,WAAW,EAAE;YACf4C,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;YACjE,IAAI;cACFrB,cAAc,CAACa,OAAO,CAAC8F,KAAK,CAAC,CAAC;cAC9BvF,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;YACnD,CAAC,CAAC,OAAO6C,KAAK,EAAE;cACd9C,OAAO,CAAC8C,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;YACxD;UACF,CAAC,MAAM;YACL9C,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;UAClE;QACF,CAAC;;QAED;QACA,IAAI;UACFrB,cAAc,CAACa,OAAO,CAAC8F,KAAK,CAAC,CAAC;UAC9BlI,cAAc,CAAC,IAAI,CAAC;UACpBmC,UAAU,CAAC,CAAC;UACZQ,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;QAC9C,CAAC,CAAC,OAAO6C,KAAK,EAAE;UACd9C,OAAO,CAAC8C,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;QAChE;MACF,CAAC,MAAM;QACL;QACA6C,cAAc,CAAC,CAAC;QAChB3F,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MACtD;IAEF,CAAC,CAAC,OAAO6C,KAAK,EAAE;MACd9C,OAAO,CAAC8C,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CxC,KAAK,CAAC,0BAA0B,GAAGwC,KAAK,CAACC,OAAO,CAAC;IACnD;EACF,CAAC;;EAED;EACA;;EAEA;EACA,MAAMkE,aAAa,GAAGA,CAAA,KAAM;IAC1BjH,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;;IAEzC;IACA1C,cAAc,CAAC,KAAK,CAAC;;IAErB;IACA,IAAIwB,cAAc,CAACU,OAAO,EAAE;MAC1B,IAAI;QACFO,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;QACxClB,cAAc,CAACU,OAAO,CAAC+G,SAAS,CAAC,CAAC,CAACU,OAAO,CAACC,KAAK,IAAI;UAClD,IAAI;YACFA,KAAK,CAAC3B,IAAI,CAAC,CAAC;YACZxF,OAAO,CAACC,GAAG,CAAC,WAAWkH,KAAK,CAACR,IAAI,WAAWQ,KAAK,CAACP,KAAK,EAAE,CAAC;UAC5D,CAAC,CAAC,OAAOQ,UAAU,EAAE;YACnBpH,OAAO,CAAC8C,KAAK,CAAC,kBAAkBqE,KAAK,CAACR,IAAI,SAAS,EAAES,UAAU,CAAC;UAClE;QACF,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOC,WAAW,EAAE;QACpBrH,OAAO,CAAC8C,KAAK,CAAC,qCAAqC,EAAEuE,WAAW,CAAC;MACnE;;MAEA;MACAtI,cAAc,CAACU,OAAO,GAAG,IAAI;IAC/B;;IAEA;IACAsG,aAAa,CAAC,CAAC;IAEf/F,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;EACvC,CAAC;;EAED;EACA,MAAMqH,YAAY,GAAGA,CAAA,KAAM;IACzBL,aAAa,CAAC,CAAC;IACflB,aAAa,CAAC,CAAC;IACf9I,OAAO,CAAC,WAAW,CAAC;IACpBE,cAAc,CAAC,KAAK,CAAC;IACrBM,qBAAqB,CAAC,EAAE,CAAC;IACzBE,oBAAoB,CAAC,EAAE,CAAC;IACxBE,WAAW,CAAC,EAAE,CAAC;EACjB,CAAC;;EAED;EACA,MAAM0J,mBAAmB,GAAGA,CAAA,kBAC1B5K,OAAA;IAAK6K,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC7B9K,OAAA;MAAA8K,QAAA,EAAI;IAAY;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACrBlL,OAAA;MAAK6K,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1B9K,OAAA;QAAK6K,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB9K,OAAA;UAAA8K,QAAA,EAAO;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC7BlL,OAAA;UACE+E,KAAK,EAAEtD,cAAe;UACtB0J,QAAQ,EAAGjF,CAAC,IAAKxE,iBAAiB,CAACwE,CAAC,CAACkF,MAAM,CAACrG,KAAK,CAAE;UAAA+F,QAAA,gBAEnD9K,OAAA;YAAQ+E,KAAK,EAAC,sBAAsB;YAAA+F,QAAA,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAClElL,OAAA;YAAQ+E,KAAK,EAAC,cAAc;YAAA+F,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAClDlL,OAAA;YAAQ+E,KAAK,EAAC,oBAAoB;YAAA+F,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC9DlL,OAAA;YAAQ+E,KAAK,EAAC,WAAW;YAAA+F,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC5ClL,OAAA;YAAQ+E,KAAK,EAAC,OAAO;YAAA+F,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpClL,OAAA;YAAQ+E,KAAK,EAAC,kBAAkB;YAAA+F,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENlL,OAAA;QAAK6K,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB9K,OAAA;UAAA8K,QAAA,EAAO;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACvBlL,OAAA;UACE+E,KAAK,EAAEpD,QAAS;UAChBwJ,QAAQ,EAAGjF,CAAC,IAAKtE,WAAW,CAACsE,CAAC,CAACkF,MAAM,CAACrG,KAAK,CAAE;UAAA+F,QAAA,gBAE7C9K,OAAA;YAAQ+E,KAAK,EAAC,SAAS;YAAA+F,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACxClL,OAAA;YAAQ+E,KAAK,EAAC,SAAS;YAAA+F,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACxClL,OAAA;YAAQ+E,KAAK,EAAC,QAAQ;YAAA+F,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtClL,OAAA;YAAQ+E,KAAK,EAAC,QAAQ;YAAA+F,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtClL,OAAA;YAAQ+E,KAAK,EAAC,SAAS;YAAA+F,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACxClL,OAAA;YAAQ+E,KAAK,EAAC,UAAU;YAAA+F,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELpL,GAAG,CAACuH,kBAAkB,CAAC,CAAC,gBACvBrH,OAAA;MAAK6K,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBACjC9K,OAAA;QAAM6K,SAAS,EAAC,MAAM;QAAAC,QAAA,EAAC;MAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,yFAEjC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,gBAENlL,OAAA;MAAK6K,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBACjC9K,OAAA;QAAM6K,SAAS,EAAC,MAAM;QAAAC,QAAA,EAAC;MAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,wIAEjC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CACN,eAEDlL,OAAA;MACE6K,SAAS,EAAC,gBAAgB;MAC1BQ,OAAO,EAAEA,CAAA,KAAM/K,OAAO,CAAC,SAAS,CAAE;MAAAwK,QAAA,EACnC;IAED;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CACN;EAED,MAAMI,iBAAiB,GAAGA,CAAA,kBACxBtL,OAAA;IAAK6K,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3B9K,OAAA;MAAA8K,QAAA,EAAI;IAAoB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC7BlL,OAAA;MAAA8K,QAAA,EAAG;IAAiD;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eACxDlL,OAAA;MAAA8K,QAAA,EAAG;IAA+D;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eAEtElL,OAAA;MAAK6K,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBACjC9K,OAAA;QACE6K,SAAS,EAAC,qBAAqB;QAC/BQ,OAAO,EAAEA,CAAA,KAAMhC,oBAAoB,CAAC,KAAK,CAAE;QAC3CkC,KAAK,EAAC,2CAA2C;QAAAT,QAAA,EAClD;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAETlL,OAAA;QACE6K,SAAS,EAAC,gCAAgC;QAC1CQ,OAAO,EAAEA,CAAA,KAAMhC,oBAAoB,CAAC,IAAI,CAAE;QAC1CkC,KAAK,EAAC,yEAAyE;QAAAT,QAAA,EAChF;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAETlL,OAAA;QAAK6K,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjC9K,OAAA;UAAA8K,QAAA,gBAAG9K,OAAA;YAAA8K,QAAA,EAAQ;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,+DAA2D;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACpGlL,OAAA;UAAA8K,QAAA,gBAAG9K,OAAA;YAAA8K,QAAA,EAAQ;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,oFAAgF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/G,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENlL,OAAA;MACE6K,SAAS,EAAC,aAAa;MACvBQ,OAAO,EAAEA,CAAA,KAAM/K,OAAO,CAAC,WAAW,CAAE;MAAAwK,QAAA,EACrC;IAED;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CACN;EAED,MAAMM,mBAAmB,GAAGA,CAAA,kBAC1BxL,OAAA;IAAK6K,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC7B9K,OAAA;MAAK6K,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClC9K,OAAA;QAAK6K,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAClC9K,OAAA;UAAK6K,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B9K,OAAA;YAAA8K,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpBlL,OAAA;YAAK6K,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAC/BvK,WAAW,gBACVP,OAAA;cAAM6K,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,gBAE5ClL,OAAA;cAAM6K,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAClD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlL,OAAA;UAAK6K,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5B9K,OAAA;YACEyL,GAAG,EAAEvJ,iBAAkB;YACvB2I,SAAS,EAAC,oBAAoB;YAAAC,QAAA,GAE7BjK,kBAAkB,CAACgH,MAAM,GAAG,CAAC,gBAC5B7H,OAAA;cAAK6K,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EACjCjK,kBAAkB,CAACiJ,GAAG,CAAC,CAAC4B,GAAG,EAAEC,KAAK,kBACjC3L,OAAA;gBAAsC6K,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBAClE9K,OAAA;kBAAK6K,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAEzI,UAAU,CAACqJ,GAAG,CAAC7E,IAAI;gBAAC;kBAAAkE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACvDlL,OAAA;kBAAK6K,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAEY,GAAG,CAACtI;gBAAI;kBAAA2H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA,GAFtC,OAAOS,KAAK,IAAID,GAAG,CAAC7E,IAAI,EAAE;gBAAAkE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAG/B,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,gBAENlL,OAAA;cAAK6K,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAoD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACvF,eAGDlL,OAAA;cAAK6K,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBACzC9K,OAAA;gBAAK6K,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAEzI,UAAU,CAACR,YAAY;cAAC;gBAAAkJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC3DlL,OAAA;gBAAK6K,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAC1B/J,iBAAiB,gBAChBf,OAAA;kBAAM6K,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,EAAE/J;gBAAiB;kBAAAgK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,gBAC9DlL,OAAA;kBAAM6K,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAqB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEzD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAGLU,OAAO,CAAC9L,GAAG,CAAC+L,QAAQ,KAAK,aAAa,iBACrC7L,OAAA;cAAK6K,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB9K,OAAA;gBAAA8K,QAAA,GAAK,6BAA2B,EAAC,CAAA/J,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAE8G,MAAM,KAAI,CAAC;cAAA;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtElL,OAAA;gBAAA8K,QAAA,GAAK,uBAAqB,EAACjK,kBAAkB,CAACgH,MAAM;cAAA;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC3DlL,OAAA;gBAAA8K,QAAA,GAAK,gBAAc,EAACrK,WAAW,GAAG,KAAK,GAAG,IAAI;cAAA;gBAAAsK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrDlL,OAAA;gBAAA8K,QAAA,GAAK,gBAAc,EAACnK,WAAW,GAAG,KAAK,GAAG,IAAI;cAAA;gBAAAoK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrDlL,OAAA;gBAAA8K,QAAA,GAAK,oBAAkB,EAACzJ,cAAc,GAAG,KAAK,GAAG,IAAI;cAAA;gBAAA0J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENlL,OAAA;QAAK6K,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B9K,OAAA;UAAK6K,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3B9K,OAAA;YAAA8K,QAAA,EAAI;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,eAENlL,OAAA;UAAK6K,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5B9K,OAAA;YACEyL,GAAG,EAAEtJ,eAAgB;YACrB0I,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAE3B7J,QAAQ,gBACPjB,OAAA;cAAK6K,SAAS,EAAC,kBAAkB;cAAAC,QAAA,eAC/B9K,OAAA;gBAAK6K,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAE7J;cAAQ;gBAAA8J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,gBAENlL,OAAA;cAAK6K,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAA2C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAC9E;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENlL,OAAA;MAAK6K,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBACjC9K,OAAA;QAAK6K,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC9K,OAAA;UAAK6K,SAAS,EAAC,eAAe;UAAAC,QAAA,EAC3BrK,WAAW,gBACVT,OAAA,CAAAE,SAAA;YAAA4K,QAAA,gBACE9K,OAAA;cAAM6K,SAAS,EAAC;YAAe;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACtC7I,UAAU,CAACR,YAAY,CAAC;UAAA,eACzB,CAAC,GAEHQ,UAAU,CAACR,YAAY;QACxB;UAAAkJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAENlL,OAAA;UAAK6K,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B9K,OAAA;YAAA8K,QAAA,EAAOrK,WAAW,GAAG,cAAc,GAAG;UAAQ;YAAAsK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EACrD7J,cAAc,iBACbrB,OAAA;YAAM6K,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAC3D,EACA,CAAC7J,cAAc,iBACdrB,OAAA;YAAM6K,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAC7D;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENlL,OAAA;QAAK6K,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B9K,OAAA;UACE6K,SAAS,EAAE,cAAcpK,WAAW,GAAG,QAAQ,GAAG,EAAE,EAAG;UACvD4K,OAAO,EAAE5K,WAAW,GAAG2I,aAAa,GAAGJ,cAAe;UAAA8B,QAAA,EAErDrK,WAAW,GAAG,OAAO,GAAG;QAAQ;UAAAsK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eAETlL,OAAA;UACE6K,SAAS,EAAC,aAAa;UACvBQ,OAAO,EAAEV,YAAa;UAAAG,QAAA,EACvB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EAGRU,OAAO,CAAC9L,GAAG,CAAC+L,QAAQ,KAAK,aAAa,iBACrC7L,OAAA,CAAAE,SAAA;UAAA4K,QAAA,gBACE9K,OAAA;YACE6K,SAAS,EAAC,aAAa;YACvBQ,OAAO,EAAEA,CAAA,KAAM;cACb,MAAMS,QAAQ,GAAG,sGAAsG;cACvHzI,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEwI,QAAQ,CAAC;cAChDhL,qBAAqB,CAACmC,IAAI,IAAI,CAC5B,GAAGA,IAAI,EACP;gBAAEG,IAAI,EAAE0I,QAAQ;gBAAEnF,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;gBAAEC,IAAI,EAAEhF;cAAa,CAAC,CAC9D,CAAC;YACJ,CAAE;YAAAiJ,QAAA,EACH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAETlL,OAAA;YACE6K,SAAS,EAAC,oBAAoB;YAC9BQ,OAAO,EAAEnC,qBAAsB;YAAA4B,QAAA,EAChC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,eACT,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;;EAED;EACA,MAAMa,UAAU,GAAGA,CAAA,KAAM;IACvB,QAAQ1L,IAAI;MACV,KAAK,WAAW;QACd,OAAOuK,mBAAmB,CAAC,CAAC;MAC9B,KAAK,SAAS;QACZ,OAAOU,iBAAiB,CAAC,CAAC;MAC5B,KAAK,WAAW;QACd,OAAOE,mBAAmB,CAAC,CAAC;MAC9B;QACE,OAAOZ,mBAAmB,CAAC,CAAC;IAChC;EACF,CAAC;EAED,oBACE5K,OAAA;IAAK6K,SAAS,EAAC,qBAAqB;IAAAC,QAAA,EACjCiB,UAAU,CAAC;EAAC;IAAAhB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEV;AAAC9K,EAAA,CAl3BQD,kBAAkB;AAAA6L,EAAA,GAAlB7L,kBAAkB;AAo3B3B,eAAeA,kBAAkB;AAAC,IAAA6L,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}