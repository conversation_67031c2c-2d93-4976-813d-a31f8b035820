{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\interview-ai-app\\\\interviewAI\\\\client\\\\src\\\\components\\\\InterviewForm.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport './InterviewForm.css';\nimport SpeechToText from './SpeechToText';\nimport Dialog from '@mui/material/Dialog';\nimport DialogActions from '@mui/material/DialogActions';\nimport DialogContent from '@mui/material/DialogContent';\nimport DialogContentText from '@mui/material/DialogContentText';\nimport DialogTitle from '@mui/material/DialogTitle';\nimport Button from '@mui/material/Button';\nimport MicIcon from '@mui/icons-material/Mic';\nimport StopIcon from '@mui/icons-material/Stop';\nimport PlayArrowIcon from '@mui/icons-material/PlayArrow';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction InterviewForm({\n  onStartSession\n}) {\n  _s();\n  // Form fields\n  const [jobTitle, setJobTitle] = useState('');\n  const [skillset, setSkillset] = useState('');\n  const [jobDescription, setJobDescription] = useState('');\n  const [focusAreas, setFocusAreas] = useState([]);\n  const [resumeFile, setResumeFile] = useState(null);\n\n  // Validation states\n  const [fileError, setFileError] = useState('');\n  const [jobDescriptionError, setJobDescriptionError] = useState('');\n\n  // Recording states\n  const [isRecording, setIsRecording] = useState(false);\n  const [recordingTime, setRecordingTime] = useState(0);\n  const [audioBlob, setAudioBlob] = useState(null);\n  const [audioUrl, setAudioUrl] = useState('');\n  const mediaRecorderRef = useRef(null);\n  const recordingTimerRef = useRef(null);\n\n  // Session states\n  const [redirectToSpeech, setRedirectToSpeechToText] = useState(false);\n  const [showTimeAlert, setShowTimeAlert] = useState(false);\n  const [sessionTimeRemaining, setSessionTimeRemaining] = useState(5); // Default 5 minutes\n\n  // Check for remaining time in localStorage\n  useEffect(() => {\n    const userDetails = localStorage.getItem('userDetails');\n    if (userDetails) {\n      try {\n        const userData = JSON.parse(userDetails);\n        if (userData.timeRemaining) {\n          setSessionTimeRemaining(userData.timeRemaining);\n        }\n      } catch (error) {\n        console.error('Error parsing user data:', error);\n      }\n    }\n  }, []);\n\n  // Start recording voice\n  const startRecording = async () => {\n    try {\n      // Reset recording state\n      setRecordingTime(0);\n      setAudioBlob(null);\n      setAudioUrl('');\n\n      // Request microphone access\n      const stream = await navigator.mediaDevices.getUserMedia({\n        audio: true\n      });\n\n      // Create media recorder\n      const mediaRecorder = new MediaRecorder(stream);\n      mediaRecorderRef.current = mediaRecorder;\n\n      // Set up data handling\n      const audioChunks = [];\n      mediaRecorder.ondataavailable = event => {\n        if (event.data.size > 0) {\n          audioChunks.push(event.data);\n        }\n      };\n\n      // Handle recording stop\n      mediaRecorder.onstop = () => {\n        // Create blob from chunks\n        const audioBlob = new Blob(audioChunks, {\n          type: 'audio/wav'\n        });\n        const audioUrl = URL.createObjectURL(audioBlob);\n\n        // Update state\n        setAudioBlob(audioBlob);\n        setAudioUrl(audioUrl);\n        setIsRecording(false);\n\n        // Stop all tracks in the stream\n        stream.getTracks().forEach(track => track.stop());\n      };\n\n      // Start recording\n      mediaRecorder.start();\n      setIsRecording(true);\n\n      // Set up timer (limit to 2 minutes = 120 seconds)\n      const MAX_RECORDING_TIME = 120;\n      let seconds = 0;\n      recordingTimerRef.current = setInterval(() => {\n        seconds += 1;\n        setRecordingTime(seconds);\n\n        // Auto-stop after 2 minutes\n        if (seconds >= MAX_RECORDING_TIME) {\n          stopRecording();\n        }\n      }, 1000);\n    } catch (error) {\n      console.error('Error starting recording:', error);\n      alert('Could not access microphone. Please check your permissions.');\n    }\n  };\n\n  // Stop recording\n  const stopRecording = () => {\n    if (mediaRecorderRef.current && isRecording) {\n      mediaRecorderRef.current.stop();\n      clearInterval(recordingTimerRef.current);\n    }\n  };\n\n  // Play recorded audio\n  const playRecording = () => {\n    if (audioUrl) {\n      const audio = new Audio(audioUrl);\n      audio.play();\n    }\n  };\n\n  // Delete recorded audio\n  const deleteRecording = () => {\n    if (audioUrl) {\n      URL.revokeObjectURL(audioUrl);\n      setAudioBlob(null);\n      setAudioUrl('');\n      setRecordingTime(0);\n    }\n  };\n\n  // Format recording time as MM:SS\n  const formatRecordingTime = seconds => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n\n  // Validate job description word count\n  const validateJobDescription = text => {\n    if (!text) return true;\n    const wordCount = text.trim().split(/\\s+/).length;\n    if (wordCount > 10000) {\n      setJobDescriptionError(`Job description exceeds 10,000 words (current: ${wordCount})`);\n      return false;\n    }\n    setJobDescriptionError('');\n    return true;\n  };\n\n  // Handle form submission\n  const handleSubmit = e => {\n    e.preventDefault();\n\n    // Validate form\n    if (!jobTitle.trim()) {\n      return; // Don't submit if job title is empty\n    }\n    if (!validateJobDescription(jobDescription)) {\n      return; // Don't submit if job description is too long\n    }\n\n    // Show time alert before starting\n    setShowTimeAlert(true);\n  };\n\n  // Handle confirmation from time alert dialog\n  const handleTimeAlertConfirm = () => {\n    setShowTimeAlert(false);\n\n    // Create session config\n    const config = {\n      jobTitle,\n      skillset,\n      jobDescription,\n      focusAreas,\n      resumeFile,\n      audioBlob,\n      sessionTimeRemaining\n    };\n\n    // Pass the configuration to the parent component\n    onStartSession(config);\n\n    // Redirect to speech-to-text component\n    setRedirectToSpeechToText(true);\n  };\n  if (redirectToSpeech) {\n    return /*#__PURE__*/_jsxDEV(SpeechToText, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 12\n    }, this);\n  }\n\n  //   if (sessionStarted && sessionConfig) {\n  //   return (\n  //     <InterviewSession config={sessionConfig} onEndSession={() => setSessionStarted(false)} />\n  //   );\n  // }\n\n  const handleFocusAreaChange = area => {\n    if (focusAreas.includes(area)) {\n      setFocusAreas(focusAreas.filter(a => a !== area));\n    } else {\n      setFocusAreas([...focusAreas, area]);\n    }\n  };\n  const handleFileChange = e => {\n    const file = e.target.files[0];\n    setFileError('');\n    if (!file) {\n      setResumeFile(null);\n      return;\n    }\n\n    // Check file type\n    const fileType = file.type;\n    const validTypes = ['application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];\n    if (!validTypes.includes(fileType)) {\n      setFileError('Please upload a PDF or DOCX file');\n      setResumeFile(null);\n      e.target.value = ''; // Reset file input\n      return;\n    }\n\n    // Check file size (limit to 5MB)\n    if (file.size > 5 * 1024 * 1024) {\n      setFileError('File size should be less than 5MB');\n      setResumeFile(null);\n      e.target.value = ''; // Reset file input\n      return;\n    }\n    setResumeFile(file);\n  };\n  const clearResume = () => {\n    setResumeFile(null);\n    setFileError('');\n    // Reset the file input\n    const fileInput = document.getElementById('resumeUpload');\n    if (fileInput) fileInput.value = '';\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"interview-form\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Simulate Your Interview Session\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 266,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"jobTitle\",\n          children: \"Job Title\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          id: \"jobTitle\",\n          value: jobTitle,\n          onChange: e => setJobTitle(e.target.value),\n          placeholder: \"e.g. Frontend Developer\",\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"skillset\",\n          children: \"Your Skillset\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          id: \"skillset\",\n          value: skillset,\n          onChange: e => setSkillset(e.target.value),\n          placeholder: \"Input skills separated by commas (e.g. React, JavaScript, CSS)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"help-text\",\n          children: \"Enter your skills separated by commas\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"jobDescription\",\n          children: \"Job Description\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n          id: \"jobDescription\",\n          value: jobDescription,\n          onChange: e => {\n            setJobDescription(e.target.value);\n            validateJobDescription(e.target.value);\n          },\n          placeholder: \"Paste the job description here (max 10,000 words)\",\n          rows: 6,\n          className: \"job-description-textarea\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 11\n        }, this), jobDescriptionError && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"error-text\",\n          children: jobDescriptionError\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 35\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"help-text\",\n          children: \"Paste the job description to get more targeted questions (max 10,000 words)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Focus Areas\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"checkbox-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              checked: focusAreas.includes('technical'),\n              onChange: () => handleFocusAreaChange('technical')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 15\n            }, this), \"Technical Skills\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              checked: focusAreas.includes('behavioral'),\n              onChange: () => handleFocusAreaChange('behavioral')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 15\n            }, this), \"Behavioral Questions\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              checked: focusAreas.includes('problemSolving'),\n              onChange: () => handleFocusAreaChange('problemSolving')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 15\n            }, this), \"Problem Solving\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"resumeUpload\",\n          children: \"Resume (Optional)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"file-upload-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"file-input-wrapper\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"file-input-button\",\n              children: \"Choose File\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"file\",\n              id: \"resumeUpload\",\n              onChange: handleFileChange,\n              accept: \".pdf,.docx,application/pdf,application/vnd.openxmlformats-officedocument.wordprocessingml.document\",\n              className: \"file-input\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"file-name\",\n              children: resumeFile ? resumeFile.name : 'No file chosen'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 13\n          }, this), resumeFile && /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            className: \"clear-file\",\n            onClick: clearResume,\n            children: \"Remove\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 11\n        }, this), fileError && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"error-text\",\n          children: fileError\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"help-text\",\n          children: \"Upload your resume to personalize interview questions (PDF or DOCX, max 5MB)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"help-text\",\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Note:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 36\n          }, this), \" Remove any sensitive information from your resume\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 339,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Record Your Voice (Optional - 2 mins max)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 374,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"voice-recorder-container\",\n          children: [!audioUrl ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"recording-controls\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: `record-button ${isRecording ? 'recording' : ''}`,\n              onClick: isRecording ? stopRecording : startRecording,\n              children: isRecording ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(StopIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 385,\n                  columnNumber: 23\n                }, this), \" Stop Recording (\", formatRecordingTime(recordingTime), \")\"]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(MicIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 389,\n                  columnNumber: 23\n                }, this), \" Start Recording\"]\n              }, void 0, true)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 17\n            }, this), isRecording && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"recording-indicator\",\n              children: [\"Recording in progress... (\", formatRecordingTime(recordingTime), \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 394,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"playback-controls\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"audio-info\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Recording complete: \", formatRecordingTime(recordingTime)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 400,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"audio-buttons\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                className: \"play-button\",\n                onClick: playRecording,\n                children: [/*#__PURE__*/_jsxDEV(PlayArrowIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 404,\n                  columnNumber: 21\n                }, this), \" Play\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                className: \"delete-button\",\n                onClick: deleteRecording,\n                children: [/*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 407,\n                  columnNumber: 21\n                }, this), \" Delete\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"help-text\",\n            children: \"Recording your voice helps the AI understand your speaking style and accent\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 373,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        className: \"start-button\",\n        children: \"Start Interview\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 416,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 267,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showTimeAlert,\n      onClose: () => setShowTimeAlert(false),\n      \"aria-labelledby\": \"alert-dialog-title\",\n      \"aria-describedby\": \"alert-dialog-description\",\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        id: \"alert-dialog-title\",\n        children: \"Session Time Information\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 426,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(DialogContentText, {\n          id: \"alert-dialog-description\",\n          children: [\"You have \", sessionTimeRemaining, \" minutes remaining in your session. Would you like to continue with the interview?\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 430,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 429,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setShowTimeAlert(false),\n          color: \"primary\",\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 436,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleTimeAlertConfirm,\n          color: \"primary\",\n          autoFocus: true,\n          children: \"Continue\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 439,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 435,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 420,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 265,\n    columnNumber: 5\n  }, this);\n}\n_s(InterviewForm, \"tdJHTX/Vo1SKBRMN3rJLJEVbEiY=\");\n_c = InterviewForm;\nexport default InterviewForm;\nvar _c;\n$RefreshReg$(_c, \"InterviewForm\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "SpeechToText", "Dialog", "DialogActions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogContentText", "DialogTitle", "<PERSON><PERSON>", "MicIcon", "StopIcon", "PlayArrowIcon", "DeleteIcon", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "InterviewForm", "onStartSession", "_s", "jobTitle", "setJobTitle", "skillset", "setSkillset", "jobDescription", "setJobDescription", "focusAreas", "setFocusA<PERSON>s", "resumeFile", "setResumeFile", "fileError", "setFileError", "jobDescriptionError", "setJobDescriptionError", "isRecording", "setIsRecording", "recordingTime", "setRecordingTime", "audioBlob", "setAudioBlob", "audioUrl", "setAudioUrl", "mediaRecorderRef", "recordingTimerRef", "redirectToSpeech", "setRedirectToSpeechToText", "showTimeAlert", "setShowTimeAlert", "sessionTimeRemaining", "setSessionTimeRemaining", "userDetails", "localStorage", "getItem", "userData", "JSON", "parse", "timeRemaining", "error", "console", "startRecording", "stream", "navigator", "mediaDevices", "getUserMedia", "audio", "mediaRecorder", "MediaRecorder", "current", "audioChunks", "ondataavailable", "event", "data", "size", "push", "onstop", "Blob", "type", "URL", "createObjectURL", "getTracks", "for<PERSON>ach", "track", "stop", "start", "MAX_RECORDING_TIME", "seconds", "setInterval", "stopRecording", "alert", "clearInterval", "playRecording", "Audio", "play", "deleteRecording", "revokeObjectURL", "formatRecordingTime", "minutes", "Math", "floor", "remainingSeconds", "toString", "padStart", "validateJobDescription", "text", "wordCount", "trim", "split", "length", "handleSubmit", "e", "preventDefault", "handleTimeAlertConfirm", "config", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "handleFocusAreaChange", "area", "includes", "filter", "a", "handleFileChange", "file", "target", "files", "fileType", "validTypes", "value", "clearResume", "fileInput", "document", "getElementById", "className", "children", "onSubmit", "htmlFor", "id", "onChange", "placeholder", "required", "rows", "checked", "accept", "name", "onClick", "open", "onClose", "color", "autoFocus", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/interview-ai-app/interviewAI/client/src/components/InterviewForm.jsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\r\nimport './InterviewForm.css';\r\nimport SpeechToText from './SpeechToText';\r\nimport Dialog from '@mui/material/Dialog';\r\nimport DialogActions from '@mui/material/DialogActions';\r\nimport DialogContent from '@mui/material/DialogContent';\r\nimport DialogContentText from '@mui/material/DialogContentText';\r\nimport DialogTitle from '@mui/material/DialogTitle';\r\nimport Button from '@mui/material/Button';\r\nimport MicIcon from '@mui/icons-material/Mic';\r\nimport StopIcon from '@mui/icons-material/Stop';\r\nimport PlayArrowIcon from '@mui/icons-material/PlayArrow';\r\nimport DeleteIcon from '@mui/icons-material/Delete';\r\n\r\nfunction InterviewForm({ onStartSession }) {\r\n  // Form fields\r\n  const [jobTitle, setJobTitle] = useState('');\r\n  const [skillset, setSkillset] = useState('');\r\n  const [jobDescription, setJobDescription] = useState('');\r\n  const [focusAreas, setFocusAreas] = useState([]);\r\n  const [resumeFile, setResumeFile] = useState(null);\r\n\r\n  // Validation states\r\n  const [fileError, setFileError] = useState('');\r\n  const [jobDescriptionError, setJobDescriptionError] = useState('');\r\n\r\n  // Recording states\r\n  const [isRecording, setIsRecording] = useState(false);\r\n  const [recordingTime, setRecordingTime] = useState(0);\r\n  const [audioBlob, setAudioBlob] = useState(null);\r\n  const [audioUrl, setAudioUrl] = useState('');\r\n  const mediaRecorderRef = useRef(null);\r\n  const recordingTimerRef = useRef(null);\r\n\r\n  // Session states\r\n  const [redirectToSpeech, setRedirectToSpeechToText] = useState(false);\r\n  const [showTimeAlert, setShowTimeAlert] = useState(false);\r\n  const [sessionTimeRemaining, setSessionTimeRemaining] = useState(5); // Default 5 minutes\r\n\r\n  // Check for remaining time in localStorage\r\n  useEffect(() => {\r\n    const userDetails = localStorage.getItem('userDetails');\r\n    if (userDetails) {\r\n      try {\r\n        const userData = JSON.parse(userDetails);\r\n        if (userData.timeRemaining) {\r\n          setSessionTimeRemaining(userData.timeRemaining);\r\n        }\r\n      } catch (error) {\r\n        console.error('Error parsing user data:', error);\r\n      }\r\n    }\r\n  }, []);\r\n\r\n\r\n  // Start recording voice\r\n  const startRecording = async () => {\r\n    try {\r\n      // Reset recording state\r\n      setRecordingTime(0);\r\n      setAudioBlob(null);\r\n      setAudioUrl('');\r\n\r\n      // Request microphone access\r\n      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });\r\n\r\n      // Create media recorder\r\n      const mediaRecorder = new MediaRecorder(stream);\r\n      mediaRecorderRef.current = mediaRecorder;\r\n\r\n      // Set up data handling\r\n      const audioChunks = [];\r\n      mediaRecorder.ondataavailable = (event) => {\r\n        if (event.data.size > 0) {\r\n          audioChunks.push(event.data);\r\n        }\r\n      };\r\n\r\n      // Handle recording stop\r\n      mediaRecorder.onstop = () => {\r\n        // Create blob from chunks\r\n        const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });\r\n        const audioUrl = URL.createObjectURL(audioBlob);\r\n\r\n        // Update state\r\n        setAudioBlob(audioBlob);\r\n        setAudioUrl(audioUrl);\r\n        setIsRecording(false);\r\n\r\n        // Stop all tracks in the stream\r\n        stream.getTracks().forEach(track => track.stop());\r\n      };\r\n\r\n      // Start recording\r\n      mediaRecorder.start();\r\n      setIsRecording(true);\r\n\r\n      // Set up timer (limit to 2 minutes = 120 seconds)\r\n      const MAX_RECORDING_TIME = 120;\r\n      let seconds = 0;\r\n\r\n      recordingTimerRef.current = setInterval(() => {\r\n        seconds += 1;\r\n        setRecordingTime(seconds);\r\n\r\n        // Auto-stop after 2 minutes\r\n        if (seconds >= MAX_RECORDING_TIME) {\r\n          stopRecording();\r\n        }\r\n      }, 1000);\r\n    } catch (error) {\r\n      console.error('Error starting recording:', error);\r\n      alert('Could not access microphone. Please check your permissions.');\r\n    }\r\n  };\r\n\r\n  // Stop recording\r\n  const stopRecording = () => {\r\n    if (mediaRecorderRef.current && isRecording) {\r\n      mediaRecorderRef.current.stop();\r\n      clearInterval(recordingTimerRef.current);\r\n    }\r\n  };\r\n\r\n  // Play recorded audio\r\n  const playRecording = () => {\r\n    if (audioUrl) {\r\n      const audio = new Audio(audioUrl);\r\n      audio.play();\r\n    }\r\n  };\r\n\r\n  // Delete recorded audio\r\n  const deleteRecording = () => {\r\n    if (audioUrl) {\r\n      URL.revokeObjectURL(audioUrl);\r\n      setAudioBlob(null);\r\n      setAudioUrl('');\r\n      setRecordingTime(0);\r\n    }\r\n  };\r\n\r\n  // Format recording time as MM:SS\r\n  const formatRecordingTime = (seconds) => {\r\n    const minutes = Math.floor(seconds / 60);\r\n    const remainingSeconds = seconds % 60;\r\n    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;\r\n  };\r\n\r\n  // Validate job description word count\r\n  const validateJobDescription = (text) => {\r\n    if (!text) return true;\r\n\r\n    const wordCount = text.trim().split(/\\s+/).length;\r\n    if (wordCount > 10000) {\r\n      setJobDescriptionError(`Job description exceeds 10,000 words (current: ${wordCount})`);\r\n      return false;\r\n    }\r\n\r\n    setJobDescriptionError('');\r\n    return true;\r\n  };\r\n\r\n  // Handle form submission\r\n  const handleSubmit = (e) => {\r\n    e.preventDefault();\r\n\r\n    // Validate form\r\n    if (!jobTitle.trim()) {\r\n      return; // Don't submit if job title is empty\r\n    }\r\n\r\n    if (!validateJobDescription(jobDescription)) {\r\n      return; // Don't submit if job description is too long\r\n    }\r\n\r\n    // Show time alert before starting\r\n    setShowTimeAlert(true);\r\n  };\r\n\r\n  // Handle confirmation from time alert dialog\r\n  const handleTimeAlertConfirm = () => {\r\n    setShowTimeAlert(false);\r\n\r\n    // Create session config\r\n    const config = {\r\n      jobTitle,\r\n      skillset,\r\n      jobDescription,\r\n      focusAreas,\r\n      resumeFile,\r\n      audioBlob,\r\n      sessionTimeRemaining\r\n    };\r\n\r\n    // Pass the configuration to the parent component\r\n    onStartSession(config);\r\n\r\n    // Redirect to speech-to-text component\r\n    setRedirectToSpeechToText(true);\r\n  };\r\n\r\n    if (redirectToSpeech) {\r\n    return <SpeechToText />;\r\n  }\r\n\r\n\r\n  //   if (sessionStarted && sessionConfig) {\r\n  //   return (\r\n  //     <InterviewSession config={sessionConfig} onEndSession={() => setSessionStarted(false)} />\r\n  //   );\r\n  // }\r\n\r\n  const handleFocusAreaChange = (area) => {\r\n    if (focusAreas.includes(area)) {\r\n      setFocusAreas(focusAreas.filter(a => a !== area));\r\n    } else {\r\n      setFocusAreas([...focusAreas, area]);\r\n    }\r\n  };\r\n\r\n  const handleFileChange = (e) => {\r\n    const file = e.target.files[0];\r\n    setFileError('');\r\n\r\n    if (!file) {\r\n      setResumeFile(null);\r\n      return;\r\n    }\r\n\r\n    // Check file type\r\n    const fileType = file.type;\r\n    const validTypes = [\r\n      'application/pdf',\r\n      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'\r\n    ];\r\n\r\n    if (!validTypes.includes(fileType)) {\r\n      setFileError('Please upload a PDF or DOCX file');\r\n      setResumeFile(null);\r\n      e.target.value = ''; // Reset file input\r\n      return;\r\n    }\r\n\r\n    // Check file size (limit to 5MB)\r\n    if (file.size > 5 * 1024 * 1024) {\r\n      setFileError('File size should be less than 5MB');\r\n      setResumeFile(null);\r\n      e.target.value = ''; // Reset file input\r\n      return;\r\n    }\r\n\r\n    setResumeFile(file);\r\n  };\r\n\r\n  const clearResume = () => {\r\n    setResumeFile(null);\r\n    setFileError('');\r\n    // Reset the file input\r\n    const fileInput = document.getElementById('resumeUpload');\r\n    if (fileInput) fileInput.value = '';\r\n  };\r\n\r\n  return (\r\n    <div className=\"interview-form\">\r\n      <h2>Simulate Your Interview Session</h2>\r\n      <form onSubmit={handleSubmit}>\r\n        <div className=\"form-group\">\r\n          <label htmlFor=\"jobTitle\">Job Title</label>\r\n          <input\r\n            type=\"text\"\r\n            id=\"jobTitle\"\r\n            value={jobTitle}\r\n            onChange={(e) => setJobTitle(e.target.value)}\r\n            placeholder=\"e.g. Frontend Developer\"\r\n            required\r\n          />\r\n        </div>\r\n\r\n        <div className=\"form-group\">\r\n          <label htmlFor=\"skillset\">Your Skillset</label>\r\n          <input\r\n            type=\"text\"\r\n            id=\"skillset\"\r\n            value={skillset}\r\n            onChange={(e) => setSkillset(e.target.value)}\r\n            placeholder=\"Input skills separated by commas (e.g. React, JavaScript, CSS)\"\r\n          />\r\n          <p className=\"help-text\">Enter your skills separated by commas</p>\r\n        </div>\r\n\r\n        <div className=\"form-group\">\r\n          <label htmlFor=\"jobDescription\">Job Description</label>\r\n          <textarea\r\n            id=\"jobDescription\"\r\n            value={jobDescription}\r\n            onChange={(e) => {\r\n              setJobDescription(e.target.value);\r\n              validateJobDescription(e.target.value);\r\n            }}\r\n            placeholder=\"Paste the job description here (max 10,000 words)\"\r\n            rows={6}\r\n            className=\"job-description-textarea\"\r\n          />\r\n          {jobDescriptionError && <p className=\"error-text\">{jobDescriptionError}</p>}\r\n          <p className=\"help-text\">Paste the job description to get more targeted questions (max 10,000 words)</p>\r\n        </div>\r\n\r\n        <div className=\"form-group\">\r\n          <label>Focus Areas</label>\r\n          <div className=\"checkbox-group\">\r\n            <label>\r\n              <input\r\n                type=\"checkbox\"\r\n                checked={focusAreas.includes('technical')}\r\n                onChange={() => handleFocusAreaChange('technical')}\r\n              />\r\n              Technical Skills\r\n            </label>\r\n            <label>\r\n              <input\r\n                type=\"checkbox\"\r\n                checked={focusAreas.includes('behavioral')}\r\n                onChange={() => handleFocusAreaChange('behavioral')}\r\n              />\r\n              Behavioral Questions\r\n            </label>\r\n            <label>\r\n              <input\r\n                type=\"checkbox\"\r\n                checked={focusAreas.includes('problemSolving')}\r\n                onChange={() => handleFocusAreaChange('problemSolving')}\r\n              />\r\n              Problem Solving\r\n            </label>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"form-group\">\r\n          <label htmlFor=\"resumeUpload\">Resume (Optional)</label>\r\n          <div className=\"file-upload-container\">\r\n            <div className=\"file-input-wrapper\">\r\n              <button type=\"button\" className=\"file-input-button\">\r\n                Choose File\r\n              </button>\r\n              <input\r\n                type=\"file\"\r\n                id=\"resumeUpload\"\r\n                onChange={handleFileChange}\r\n                accept=\".pdf,.docx,application/pdf,application/vnd.openxmlformats-officedocument.wordprocessingml.document\"\r\n                className=\"file-input\"\r\n              />\r\n              <span className=\"file-name\">\r\n                {resumeFile ? resumeFile.name : 'No file chosen'}\r\n              </span>\r\n            </div>\r\n\r\n            {resumeFile && (\r\n              <button\r\n                type=\"button\"\r\n                className=\"clear-file\"\r\n                onClick={clearResume}\r\n              >\r\n                Remove\r\n              </button>\r\n            )}\r\n          </div>\r\n          {fileError && <p className=\"error-text\">{fileError}</p>}\r\n          <p className=\"help-text\">Upload your resume to personalize interview questions (PDF or DOCX, max 5MB)</p>\r\n          <p className=\"help-text\"><strong>Note:</strong> Remove any sensitive information from your resume</p>\r\n        </div>\r\n\r\n        <div className=\"form-group\">\r\n          <label>Record Your Voice (Optional - 2 mins max)</label>\r\n          <div className=\"voice-recorder-container\">\r\n            {!audioUrl ? (\r\n              <div className=\"recording-controls\">\r\n                <button\r\n                  type=\"button\"\r\n                  className={`record-button ${isRecording ? 'recording' : ''}`}\r\n                  onClick={isRecording ? stopRecording : startRecording}\r\n                >\r\n                  {isRecording ? (\r\n                    <>\r\n                      <StopIcon /> Stop Recording ({formatRecordingTime(recordingTime)})\r\n                    </>\r\n                  ) : (\r\n                    <>\r\n                      <MicIcon /> Start Recording\r\n                    </>\r\n                  )}\r\n                </button>\r\n                {isRecording && (\r\n                  <p className=\"recording-indicator\">Recording in progress... ({formatRecordingTime(recordingTime)})</p>\r\n                )}\r\n              </div>\r\n            ) : (\r\n              <div className=\"playback-controls\">\r\n                <div className=\"audio-info\">\r\n                  <span>Recording complete: {formatRecordingTime(recordingTime)}</span>\r\n                </div>\r\n                <div className=\"audio-buttons\">\r\n                  <button type=\"button\" className=\"play-button\" onClick={playRecording}>\r\n                    <PlayArrowIcon /> Play\r\n                  </button>\r\n                  <button type=\"button\" className=\"delete-button\" onClick={deleteRecording}>\r\n                    <DeleteIcon /> Delete\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            )}\r\n            <p className=\"help-text\">Recording your voice helps the AI understand your speaking style and accent</p>\r\n          </div>\r\n        </div>\r\n\r\n        <button type=\"submit\" className=\"start-button\">Start Interview</button>\r\n      </form>\r\n\r\n      {/* Time Alert Dialog */}\r\n      <Dialog\r\n        open={showTimeAlert}\r\n        onClose={() => setShowTimeAlert(false)}\r\n        aria-labelledby=\"alert-dialog-title\"\r\n        aria-describedby=\"alert-dialog-description\"\r\n      >\r\n        <DialogTitle id=\"alert-dialog-title\">\r\n          {\"Session Time Information\"}\r\n        </DialogTitle>\r\n        <DialogContent>\r\n          <DialogContentText id=\"alert-dialog-description\">\r\n            You have {sessionTimeRemaining} minutes remaining in your session.\r\n            Would you like to continue with the interview?\r\n          </DialogContentText>\r\n        </DialogContent>\r\n        <DialogActions>\r\n          <Button onClick={() => setShowTimeAlert(false)} color=\"primary\">\r\n            Cancel\r\n          </Button>\r\n          <Button onClick={handleTimeAlertConfirm} color=\"primary\" autoFocus>\r\n            Continue\r\n          </Button>\r\n        </DialogActions>\r\n      </Dialog>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default InterviewForm;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,OAAO,qBAAqB;AAC5B,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,iBAAiB,MAAM,iCAAiC;AAC/D,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,UAAU,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpD,SAASC,aAAaA,CAAC;EAAEC;AAAe,CAAC,EAAE;EAAAC,EAAA;EACzC;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACuB,QAAQ,EAAEC,WAAW,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACyB,cAAc,EAAEC,iBAAiB,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC6B,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAM,CAAC+B,SAAS,EAAEC,YAAY,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACiC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;;EAElE;EACA,MAAM,CAACmC,WAAW,EAAEC,cAAc,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACqC,aAAa,EAAEC,gBAAgB,CAAC,GAAGtC,QAAQ,CAAC,CAAC,CAAC;EACrD,MAAM,CAACuC,SAAS,EAAEC,YAAY,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACyC,QAAQ,EAAEC,WAAW,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM2C,gBAAgB,GAAG1C,MAAM,CAAC,IAAI,CAAC;EACrC,MAAM2C,iBAAiB,GAAG3C,MAAM,CAAC,IAAI,CAAC;;EAEtC;EACA,MAAM,CAAC4C,gBAAgB,EAAEC,yBAAyB,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC+C,aAAa,EAAEC,gBAAgB,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACiD,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGlD,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;;EAErE;EACAE,SAAS,CAAC,MAAM;IACd,MAAMiD,WAAW,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IACvD,IAAIF,WAAW,EAAE;MACf,IAAI;QACF,MAAMG,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACL,WAAW,CAAC;QACxC,IAAIG,QAAQ,CAACG,aAAa,EAAE;UAC1BP,uBAAuB,CAACI,QAAQ,CAACG,aAAa,CAAC;QACjD;MACF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAClD;IACF;EACF,CAAC,EAAE,EAAE,CAAC;;EAGN;EACA,MAAME,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF;MACAtB,gBAAgB,CAAC,CAAC,CAAC;MACnBE,YAAY,CAAC,IAAI,CAAC;MAClBE,WAAW,CAAC,EAAE,CAAC;;MAEf;MACA,MAAMmB,MAAM,GAAG,MAAMC,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;QAAEC,KAAK,EAAE;MAAK,CAAC,CAAC;;MAEzE;MACA,MAAMC,aAAa,GAAG,IAAIC,aAAa,CAACN,MAAM,CAAC;MAC/ClB,gBAAgB,CAACyB,OAAO,GAAGF,aAAa;;MAExC;MACA,MAAMG,WAAW,GAAG,EAAE;MACtBH,aAAa,CAACI,eAAe,GAAIC,KAAK,IAAK;QACzC,IAAIA,KAAK,CAACC,IAAI,CAACC,IAAI,GAAG,CAAC,EAAE;UACvBJ,WAAW,CAACK,IAAI,CAACH,KAAK,CAACC,IAAI,CAAC;QAC9B;MACF,CAAC;;MAED;MACAN,aAAa,CAACS,MAAM,GAAG,MAAM;QAC3B;QACA,MAAMpC,SAAS,GAAG,IAAIqC,IAAI,CAACP,WAAW,EAAE;UAAEQ,IAAI,EAAE;QAAY,CAAC,CAAC;QAC9D,MAAMpC,QAAQ,GAAGqC,GAAG,CAACC,eAAe,CAACxC,SAAS,CAAC;;QAE/C;QACAC,YAAY,CAACD,SAAS,CAAC;QACvBG,WAAW,CAACD,QAAQ,CAAC;QACrBL,cAAc,CAAC,KAAK,CAAC;;QAErB;QACAyB,MAAM,CAACmB,SAAS,CAAC,CAAC,CAACC,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACC,IAAI,CAAC,CAAC,CAAC;MACnD,CAAC;;MAED;MACAjB,aAAa,CAACkB,KAAK,CAAC,CAAC;MACrBhD,cAAc,CAAC,IAAI,CAAC;;MAEpB;MACA,MAAMiD,kBAAkB,GAAG,GAAG;MAC9B,IAAIC,OAAO,GAAG,CAAC;MAEf1C,iBAAiB,CAACwB,OAAO,GAAGmB,WAAW,CAAC,MAAM;QAC5CD,OAAO,IAAI,CAAC;QACZhD,gBAAgB,CAACgD,OAAO,CAAC;;QAEzB;QACA,IAAIA,OAAO,IAAID,kBAAkB,EAAE;UACjCG,aAAa,CAAC,CAAC;QACjB;MACF,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC,OAAO9B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD+B,KAAK,CAAC,6DAA6D,CAAC;IACtE;EACF,CAAC;;EAED;EACA,MAAMD,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI7C,gBAAgB,CAACyB,OAAO,IAAIjC,WAAW,EAAE;MAC3CQ,gBAAgB,CAACyB,OAAO,CAACe,IAAI,CAAC,CAAC;MAC/BO,aAAa,CAAC9C,iBAAiB,CAACwB,OAAO,CAAC;IAC1C;EACF,CAAC;;EAED;EACA,MAAMuB,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIlD,QAAQ,EAAE;MACZ,MAAMwB,KAAK,GAAG,IAAI2B,KAAK,CAACnD,QAAQ,CAAC;MACjCwB,KAAK,CAAC4B,IAAI,CAAC,CAAC;IACd;EACF,CAAC;;EAED;EACA,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAIrD,QAAQ,EAAE;MACZqC,GAAG,CAACiB,eAAe,CAACtD,QAAQ,CAAC;MAC7BD,YAAY,CAAC,IAAI,CAAC;MAClBE,WAAW,CAAC,EAAE,CAAC;MACfJ,gBAAgB,CAAC,CAAC,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAM0D,mBAAmB,GAAIV,OAAO,IAAK;IACvC,MAAMW,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACb,OAAO,GAAG,EAAE,CAAC;IACxC,MAAMc,gBAAgB,GAAGd,OAAO,GAAG,EAAE;IACrC,OAAO,GAAGW,OAAO,CAACI,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,gBAAgB,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACjG,CAAC;;EAED;EACA,MAAMC,sBAAsB,GAAIC,IAAI,IAAK;IACvC,IAAI,CAACA,IAAI,EAAE,OAAO,IAAI;IAEtB,MAAMC,SAAS,GAAGD,IAAI,CAACE,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,KAAK,CAAC,CAACC,MAAM;IACjD,IAAIH,SAAS,GAAG,KAAK,EAAE;MACrBvE,sBAAsB,CAAC,kDAAkDuE,SAAS,GAAG,CAAC;MACtF,OAAO,KAAK;IACd;IAEAvE,sBAAsB,CAAC,EAAE,CAAC;IAC1B,OAAO,IAAI;EACb,CAAC;;EAED;EACA,MAAM2E,YAAY,GAAIC,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;;IAElB;IACA,IAAI,CAAC1F,QAAQ,CAACqF,IAAI,CAAC,CAAC,EAAE;MACpB,OAAO,CAAC;IACV;IAEA,IAAI,CAACH,sBAAsB,CAAC9E,cAAc,CAAC,EAAE;MAC3C,OAAO,CAAC;IACV;;IAEA;IACAuB,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;;EAED;EACA,MAAMgE,sBAAsB,GAAGA,CAAA,KAAM;IACnChE,gBAAgB,CAAC,KAAK,CAAC;;IAEvB;IACA,MAAMiE,MAAM,GAAG;MACb5F,QAAQ;MACRE,QAAQ;MACRE,cAAc;MACdE,UAAU;MACVE,UAAU;MACVU,SAAS;MACTU;IACF,CAAC;;IAED;IACA9B,cAAc,CAAC8F,MAAM,CAAC;;IAEtB;IACAnE,yBAAyB,CAAC,IAAI,CAAC;EACjC,CAAC;EAEC,IAAID,gBAAgB,EAAE;IACtB,oBAAO9B,OAAA,CAACZ,YAAY;MAAA+G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACzB;;EAGA;EACA;EACA;EACA;EACA;;EAEA,MAAMC,qBAAqB,GAAIC,IAAI,IAAK;IACtC,IAAI5F,UAAU,CAAC6F,QAAQ,CAACD,IAAI,CAAC,EAAE;MAC7B3F,aAAa,CAACD,UAAU,CAAC8F,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKH,IAAI,CAAC,CAAC;IACnD,CAAC,MAAM;MACL3F,aAAa,CAAC,CAAC,GAAGD,UAAU,EAAE4F,IAAI,CAAC,CAAC;IACtC;EACF,CAAC;EAED,MAAMI,gBAAgB,GAAIb,CAAC,IAAK;IAC9B,MAAMc,IAAI,GAAGd,CAAC,CAACe,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9B9F,YAAY,CAAC,EAAE,CAAC;IAEhB,IAAI,CAAC4F,IAAI,EAAE;MACT9F,aAAa,CAAC,IAAI,CAAC;MACnB;IACF;;IAEA;IACA,MAAMiG,QAAQ,GAAGH,IAAI,CAAC/C,IAAI;IAC1B,MAAMmD,UAAU,GAAG,CACjB,iBAAiB,EACjB,yEAAyE,CAC1E;IAED,IAAI,CAACA,UAAU,CAACR,QAAQ,CAACO,QAAQ,CAAC,EAAE;MAClC/F,YAAY,CAAC,kCAAkC,CAAC;MAChDF,aAAa,CAAC,IAAI,CAAC;MACnBgF,CAAC,CAACe,MAAM,CAACI,KAAK,GAAG,EAAE,CAAC,CAAC;MACrB;IACF;;IAEA;IACA,IAAIL,IAAI,CAACnD,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;MAC/BzC,YAAY,CAAC,mCAAmC,CAAC;MACjDF,aAAa,CAAC,IAAI,CAAC;MACnBgF,CAAC,CAACe,MAAM,CAACI,KAAK,GAAG,EAAE,CAAC,CAAC;MACrB;IACF;IAEAnG,aAAa,CAAC8F,IAAI,CAAC;EACrB,CAAC;EAED,MAAMM,WAAW,GAAGA,CAAA,KAAM;IACxBpG,aAAa,CAAC,IAAI,CAAC;IACnBE,YAAY,CAAC,EAAE,CAAC;IAChB;IACA,MAAMmG,SAAS,GAAGC,QAAQ,CAACC,cAAc,CAAC,cAAc,CAAC;IACzD,IAAIF,SAAS,EAAEA,SAAS,CAACF,KAAK,GAAG,EAAE;EACrC,CAAC;EAED,oBACElH,OAAA;IAAKuH,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC7BxH,OAAA;MAAAwH,QAAA,EAAI;IAA+B;MAAArB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACxCtG,OAAA;MAAMyH,QAAQ,EAAE3B,YAAa;MAAA0B,QAAA,gBAC3BxH,OAAA;QAAKuH,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBxH,OAAA;UAAO0H,OAAO,EAAC,UAAU;UAAAF,QAAA,EAAC;QAAS;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC3CtG,OAAA;UACE8D,IAAI,EAAC,MAAM;UACX6D,EAAE,EAAC,UAAU;UACbT,KAAK,EAAE5G,QAAS;UAChBsH,QAAQ,EAAG7B,CAAC,IAAKxF,WAAW,CAACwF,CAAC,CAACe,MAAM,CAACI,KAAK,CAAE;UAC7CW,WAAW,EAAC,yBAAyB;UACrCC,QAAQ;QAAA;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENtG,OAAA;QAAKuH,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBxH,OAAA;UAAO0H,OAAO,EAAC,UAAU;UAAAF,QAAA,EAAC;QAAa;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC/CtG,OAAA;UACE8D,IAAI,EAAC,MAAM;UACX6D,EAAE,EAAC,UAAU;UACbT,KAAK,EAAE1G,QAAS;UAChBoH,QAAQ,EAAG7B,CAAC,IAAKtF,WAAW,CAACsF,CAAC,CAACe,MAAM,CAACI,KAAK,CAAE;UAC7CW,WAAW,EAAC;QAAgE;UAAA1B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7E,CAAC,eACFtG,OAAA;UAAGuH,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAqC;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D,CAAC,eAENtG,OAAA;QAAKuH,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBxH,OAAA;UAAO0H,OAAO,EAAC,gBAAgB;UAAAF,QAAA,EAAC;QAAe;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACvDtG,OAAA;UACE2H,EAAE,EAAC,gBAAgB;UACnBT,KAAK,EAAExG,cAAe;UACtBkH,QAAQ,EAAG7B,CAAC,IAAK;YACfpF,iBAAiB,CAACoF,CAAC,CAACe,MAAM,CAACI,KAAK,CAAC;YACjC1B,sBAAsB,CAACO,CAAC,CAACe,MAAM,CAACI,KAAK,CAAC;UACxC,CAAE;UACFW,WAAW,EAAC,mDAAmD;UAC/DE,IAAI,EAAE,CAAE;UACRR,SAAS,EAAC;QAA0B;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC,EACDpF,mBAAmB,iBAAIlB,OAAA;UAAGuH,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAEtG;QAAmB;UAAAiF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3EtG,OAAA;UAAGuH,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAA2E;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrG,CAAC,eAENtG,OAAA;QAAKuH,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBxH,OAAA;UAAAwH,QAAA,EAAO;QAAW;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC1BtG,OAAA;UAAKuH,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BxH,OAAA;YAAAwH,QAAA,gBACExH,OAAA;cACE8D,IAAI,EAAC,UAAU;cACfkE,OAAO,EAAEpH,UAAU,CAAC6F,QAAQ,CAAC,WAAW,CAAE;cAC1CmB,QAAQ,EAAEA,CAAA,KAAMrB,qBAAqB,CAAC,WAAW;YAAE;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,oBAEJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRtG,OAAA;YAAAwH,QAAA,gBACExH,OAAA;cACE8D,IAAI,EAAC,UAAU;cACfkE,OAAO,EAAEpH,UAAU,CAAC6F,QAAQ,CAAC,YAAY,CAAE;cAC3CmB,QAAQ,EAAEA,CAAA,KAAMrB,qBAAqB,CAAC,YAAY;YAAE;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC,wBAEJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRtG,OAAA;YAAAwH,QAAA,gBACExH,OAAA;cACE8D,IAAI,EAAC,UAAU;cACfkE,OAAO,EAAEpH,UAAU,CAAC6F,QAAQ,CAAC,gBAAgB,CAAE;cAC/CmB,QAAQ,EAAEA,CAAA,KAAMrB,qBAAqB,CAAC,gBAAgB;YAAE;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC,mBAEJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENtG,OAAA;QAAKuH,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBxH,OAAA;UAAO0H,OAAO,EAAC,cAAc;UAAAF,QAAA,EAAC;QAAiB;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACvDtG,OAAA;UAAKuH,SAAS,EAAC,uBAAuB;UAAAC,QAAA,gBACpCxH,OAAA;YAAKuH,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjCxH,OAAA;cAAQ8D,IAAI,EAAC,QAAQ;cAACyD,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAC;YAEpD;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTtG,OAAA;cACE8D,IAAI,EAAC,MAAM;cACX6D,EAAE,EAAC,cAAc;cACjBC,QAAQ,EAAEhB,gBAAiB;cAC3BqB,MAAM,EAAC,oGAAoG;cAC3GV,SAAS,EAAC;YAAY;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eACFtG,OAAA;cAAMuH,SAAS,EAAC,WAAW;cAAAC,QAAA,EACxB1G,UAAU,GAAGA,UAAU,CAACoH,IAAI,GAAG;YAAgB;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,EAELxF,UAAU,iBACTd,OAAA;YACE8D,IAAI,EAAC,QAAQ;YACbyD,SAAS,EAAC,YAAY;YACtBY,OAAO,EAAEhB,WAAY;YAAAK,QAAA,EACtB;UAED;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EACLtF,SAAS,iBAAIhB,OAAA;UAAGuH,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAExG;QAAS;UAAAmF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvDtG,OAAA;UAAGuH,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAA4E;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACzGtG,OAAA;UAAGuH,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAACxH,OAAA;YAAAwH,QAAA,EAAQ;UAAK;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,sDAAkD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClG,CAAC,eAENtG,OAAA;QAAKuH,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBxH,OAAA;UAAAwH,QAAA,EAAO;QAAyC;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACxDtG,OAAA;UAAKuH,SAAS,EAAC,0BAA0B;UAAAC,QAAA,GACtC,CAAC9F,QAAQ,gBACR1B,OAAA;YAAKuH,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjCxH,OAAA;cACE8D,IAAI,EAAC,QAAQ;cACbyD,SAAS,EAAE,iBAAiBnG,WAAW,GAAG,WAAW,GAAG,EAAE,EAAG;cAC7D+G,OAAO,EAAE/G,WAAW,GAAGqD,aAAa,GAAG5B,cAAe;cAAA2E,QAAA,EAErDpG,WAAW,gBACVpB,OAAA,CAAAE,SAAA;gBAAAsH,QAAA,gBACExH,OAAA,CAACJ,QAAQ;kBAAAuG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,qBAAiB,EAACrB,mBAAmB,CAAC3D,aAAa,CAAC,EAAC,GACnE;cAAA,eAAE,CAAC,gBAEHtB,OAAA,CAAAE,SAAA;gBAAAsH,QAAA,gBACExH,OAAA,CAACL,OAAO;kBAAAwG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,oBACb;cAAA,eAAE;YACH;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC,EACRlF,WAAW,iBACVpB,OAAA;cAAGuH,SAAS,EAAC,qBAAqB;cAAAC,QAAA,GAAC,4BAA0B,EAACvC,mBAAmB,CAAC3D,aAAa,CAAC,EAAC,GAAC;YAAA;cAAA6E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CACtG;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,gBAENtG,OAAA;YAAKuH,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCxH,OAAA;cAAKuH,SAAS,EAAC,YAAY;cAAAC,QAAA,eACzBxH,OAAA;gBAAAwH,QAAA,GAAM,sBAAoB,EAACvC,mBAAmB,CAAC3D,aAAa,CAAC;cAAA;gBAAA6E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE,CAAC,eACNtG,OAAA;cAAKuH,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BxH,OAAA;gBAAQ8D,IAAI,EAAC,QAAQ;gBAACyD,SAAS,EAAC,aAAa;gBAACY,OAAO,EAAEvD,aAAc;gBAAA4C,QAAA,gBACnExH,OAAA,CAACH,aAAa;kBAAAsG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,SACnB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTtG,OAAA;gBAAQ8D,IAAI,EAAC,QAAQ;gBAACyD,SAAS,EAAC,eAAe;gBAACY,OAAO,EAAEpD,eAAgB;gBAAAyC,QAAA,gBACvExH,OAAA,CAACF,UAAU;kBAAAqG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,WAChB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eACDtG,OAAA;YAAGuH,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAA2E;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENtG,OAAA;QAAQ8D,IAAI,EAAC,QAAQ;QAACyD,SAAS,EAAC,cAAc;QAAAC,QAAA,EAAC;MAAe;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnE,CAAC,eAGPtG,OAAA,CAACX,MAAM;MACL+I,IAAI,EAAEpG,aAAc;MACpBqG,OAAO,EAAEA,CAAA,KAAMpG,gBAAgB,CAAC,KAAK,CAAE;MACvC,mBAAgB,oBAAoB;MACpC,oBAAiB,0BAA0B;MAAAuF,QAAA,gBAE3CxH,OAAA,CAACP,WAAW;QAACkI,EAAE,EAAC,oBAAoB;QAAAH,QAAA,EACjC;MAA0B;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC,eACdtG,OAAA,CAACT,aAAa;QAAAiI,QAAA,eACZxH,OAAA,CAACR,iBAAiB;UAACmI,EAAE,EAAC,0BAA0B;UAAAH,QAAA,GAAC,WACtC,EAACtF,oBAAoB,EAAC,oFAEjC;QAAA;UAAAiE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eAChBtG,OAAA,CAACV,aAAa;QAAAkI,QAAA,gBACZxH,OAAA,CAACN,MAAM;UAACyI,OAAO,EAAEA,CAAA,KAAMlG,gBAAgB,CAAC,KAAK,CAAE;UAACqG,KAAK,EAAC,SAAS;UAAAd,QAAA,EAAC;QAEhE;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTtG,OAAA,CAACN,MAAM;UAACyI,OAAO,EAAElC,sBAAuB;UAACqC,KAAK,EAAC,SAAS;UAACC,SAAS;UAAAf,QAAA,EAAC;QAEnE;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV;AAACjG,EAAA,CA/aQF,aAAa;AAAAqI,EAAA,GAAbrI,aAAa;AAibtB,eAAeA,aAAa;AAAC,IAAAqI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}