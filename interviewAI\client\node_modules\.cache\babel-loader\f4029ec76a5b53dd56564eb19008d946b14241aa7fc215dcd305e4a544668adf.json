{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\interview-ai-app\\\\interviewAI\\\\client\\\\src\\\\components\\\\SimpleTranscriber.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport './SimpleTranscriber.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction SimpleTranscriber({\n  onTranscriptChange\n}) {\n  _s();\n  const [transcript, setTranscript] = useState('');\n  const [isListening, setIsListening] = useState(false);\n  const recognitionRef = useRef(null);\n  const finalTranscriptRef = useRef('');\n\n  // Initialize speech recognition\n  useEffect(() => {\n    // Browser compatibility\n    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n    if (!SpeechRecognition) {\n      console.error(\"Speech recognition not supported in this browser\");\n      return;\n    }\n\n    // Create recognition instance\n    recognitionRef.current = new SpeechRecognition();\n\n    // Configure\n    recognitionRef.current.continuous = true;\n    recognitionRef.current.interimResults = true;\n    recognitionRef.current.lang = 'en-US';\n\n    // Set up event handlers\n    recognitionRef.current.onresult = event => {\n      let interimTranscript = '';\n      for (let i = event.resultIndex; i < event.results.length; i++) {\n        const result = event.results[i];\n        const text = result[0].transcript;\n        if (result.isFinal) {\n          finalTranscriptRef.current += text + ' ';\n        } else {\n          interimTranscript += text;\n        }\n      }\n      const fullTranscript = finalTranscriptRef.current + interimTranscript;\n      setTranscript(fullTranscript);\n\n      // Notify parent component\n      if (onTranscriptChange) {\n        onTranscriptChange(fullTranscript);\n      }\n    };\n    recognitionRef.current.onerror = event => {\n      console.error('Speech recognition error:', event.error);\n      if (event.error !== 'no-speech') {\n        setIsListening(false);\n      }\n    };\n    recognitionRef.current.onend = () => {\n      // Restart if still supposed to be listening\n      if (isListening) {\n        try {\n          recognitionRef.current.start();\n        } catch (error) {\n          console.error('Failed to restart recognition:', error);\n        }\n      }\n    };\n\n    // Clean up\n    return () => {\n      if (recognitionRef.current) {\n        try {\n          recognitionRef.current.stop();\n        } catch (error) {\n          console.error('Error stopping recognition:', error);\n        }\n      }\n    };\n  }, [isListening, onTranscriptChange]);\n\n  // Start listening\n  const startListening = () => {\n    if (recognitionRef.current && !isListening) {\n      finalTranscriptRef.current = ''; // Reset transcript\n      setTranscript('');\n      try {\n        recognitionRef.current.start();\n        setIsListening(true);\n      } catch (error) {\n        console.error('Error starting recognition:', error);\n\n        // Handle \"already started\" error\n        if (error.message.includes('already started')) {\n          recognitionRef.current.stop();\n          setTimeout(() => {\n            recognitionRef.current.start();\n            setIsListening(true);\n          }, 100);\n        }\n      }\n    }\n  };\n\n  // Stop listening\n  const stopListening = () => {\n    if (recognitionRef.current && isListening) {\n      recognitionRef.current.stop();\n      setIsListening(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"simple-transcriber\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"transcript-display\",\n      children: transcript ? /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"transcript-text\",\n        children: transcript\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"placeholder\",\n        children: \"Waiting for speech...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"controls\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: `control-button ${isListening ? 'listening' : ''}`,\n        onClick: isListening ? stopListening : startListening,\n        children: [isListening ? 'Stop' : 'Start', \" Listening\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this), isListening && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"status\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"listening-indicator\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 13\n        }, this), \"Listening...\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 115,\n    columnNumber: 5\n  }, this);\n}\n_s(SimpleTranscriber, \"v7Wo2Xnod8tmx62E9oX8NSe/Kv8=\");\n_c = SimpleTranscriber;\nexport default SimpleTranscriber;\nvar _c;\n$RefreshReg$(_c, \"SimpleTranscriber\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "SimpleTranscriber", "onTranscriptChange", "_s", "transcript", "setTranscript", "isListening", "setIsListening", "recognitionRef", "finalTranscriptRef", "SpeechRecognition", "window", "webkitSpeechRecognition", "console", "error", "current", "continuous", "interimResults", "lang", "on<PERSON>ult", "event", "interimTranscript", "i", "resultIndex", "results", "length", "result", "text", "isFinal", "fullTranscript", "onerror", "onend", "start", "stop", "startListening", "message", "includes", "setTimeout", "stopListening", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/interview-ai-app/interviewAI/client/src/components/SimpleTranscriber.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport './SimpleTranscriber.css';\n\nfunction SimpleTranscriber({ onTranscriptChange }) {\n  const [transcript, setTranscript] = useState('');\n  const [isListening, setIsListening] = useState(false);\n  const recognitionRef = useRef(null);\n  const finalTranscriptRef = useRef('');\n\n  // Initialize speech recognition\n  useEffect(() => {\n    // Browser compatibility\n    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n    \n    if (!SpeechRecognition) {\n      console.error(\"Speech recognition not supported in this browser\");\n      return;\n    }\n    \n    // Create recognition instance\n    recognitionRef.current = new SpeechRecognition();\n    \n    // Configure\n    recognitionRef.current.continuous = true;\n    recognitionRef.current.interimResults = true;\n    recognitionRef.current.lang = 'en-US';\n    \n    // Set up event handlers\n    recognitionRef.current.onresult = (event) => {\n      let interimTranscript = '';\n      \n      for (let i = event.resultIndex; i < event.results.length; i++) {\n        const result = event.results[i];\n        const text = result[0].transcript;\n        \n        if (result.isFinal) {\n          finalTranscriptRef.current += text + ' ';\n        } else {\n          interimTranscript += text;\n        }\n      }\n      \n      const fullTranscript = finalTranscriptRef.current + interimTranscript;\n      setTranscript(fullTranscript);\n      \n      // Notify parent component\n      if (onTranscriptChange) {\n        onTranscriptChange(fullTranscript);\n      }\n    };\n    \n    recognitionRef.current.onerror = (event) => {\n      console.error('Speech recognition error:', event.error);\n      if (event.error !== 'no-speech') {\n        setIsListening(false);\n      }\n    };\n    \n    recognitionRef.current.onend = () => {\n      // Restart if still supposed to be listening\n      if (isListening) {\n        try {\n          recognitionRef.current.start();\n        } catch (error) {\n          console.error('Failed to restart recognition:', error);\n        }\n      }\n    };\n    \n    // Clean up\n    return () => {\n      if (recognitionRef.current) {\n        try {\n          recognitionRef.current.stop();\n        } catch (error) {\n          console.error('Error stopping recognition:', error);\n        }\n      }\n    };\n  }, [isListening, onTranscriptChange]);\n  \n  // Start listening\n  const startListening = () => {\n    if (recognitionRef.current && !isListening) {\n      finalTranscriptRef.current = ''; // Reset transcript\n      setTranscript('');\n      \n      try {\n        recognitionRef.current.start();\n        setIsListening(true);\n      } catch (error) {\n        console.error('Error starting recognition:', error);\n        \n        // Handle \"already started\" error\n        if (error.message.includes('already started')) {\n          recognitionRef.current.stop();\n          setTimeout(() => {\n            recognitionRef.current.start();\n            setIsListening(true);\n          }, 100);\n        }\n      }\n    }\n  };\n  \n  // Stop listening\n  const stopListening = () => {\n    if (recognitionRef.current && isListening) {\n      recognitionRef.current.stop();\n      setIsListening(false);\n    }\n  };\n  \n  return (\n    <div className=\"simple-transcriber\">\n      <div className=\"transcript-display\">\n        {transcript ? (\n          <p className=\"transcript-text\">{transcript}</p>\n        ) : (\n          <p className=\"placeholder\">Waiting for speech...</p>\n        )}\n      </div>\n      \n      <div className=\"controls\">\n        <button \n          className={`control-button ${isListening ? 'listening' : ''}`}\n          onClick={isListening ? stopListening : startListening}\n        >\n          {isListening ? 'Stop' : 'Start'} Listening\n        </button>\n        \n        {isListening && (\n          <div className=\"status\">\n            <span className=\"listening-indicator\"></span>\n            Listening...\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n\nexport default SimpleTranscriber;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjC,SAASC,iBAAiBA,CAAC;EAAEC;AAAmB,CAAC,EAAE;EAAAC,EAAA;EACjD,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACU,WAAW,EAAEC,cAAc,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAMY,cAAc,GAAGV,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMW,kBAAkB,GAAGX,MAAM,CAAC,EAAE,CAAC;;EAErC;EACAD,SAAS,CAAC,MAAM;IACd;IACA,MAAMa,iBAAiB,GAAGC,MAAM,CAACD,iBAAiB,IAAIC,MAAM,CAACC,uBAAuB;IAEpF,IAAI,CAACF,iBAAiB,EAAE;MACtBG,OAAO,CAACC,KAAK,CAAC,kDAAkD,CAAC;MACjE;IACF;;IAEA;IACAN,cAAc,CAACO,OAAO,GAAG,IAAIL,iBAAiB,CAAC,CAAC;;IAEhD;IACAF,cAAc,CAACO,OAAO,CAACC,UAAU,GAAG,IAAI;IACxCR,cAAc,CAACO,OAAO,CAACE,cAAc,GAAG,IAAI;IAC5CT,cAAc,CAACO,OAAO,CAACG,IAAI,GAAG,OAAO;;IAErC;IACAV,cAAc,CAACO,OAAO,CAACI,QAAQ,GAAIC,KAAK,IAAK;MAC3C,IAAIC,iBAAiB,GAAG,EAAE;MAE1B,KAAK,IAAIC,CAAC,GAAGF,KAAK,CAACG,WAAW,EAAED,CAAC,GAAGF,KAAK,CAACI,OAAO,CAACC,MAAM,EAAEH,CAAC,EAAE,EAAE;QAC7D,MAAMI,MAAM,GAAGN,KAAK,CAACI,OAAO,CAACF,CAAC,CAAC;QAC/B,MAAMK,IAAI,GAAGD,MAAM,CAAC,CAAC,CAAC,CAACtB,UAAU;QAEjC,IAAIsB,MAAM,CAACE,OAAO,EAAE;UAClBnB,kBAAkB,CAACM,OAAO,IAAIY,IAAI,GAAG,GAAG;QAC1C,CAAC,MAAM;UACLN,iBAAiB,IAAIM,IAAI;QAC3B;MACF;MAEA,MAAME,cAAc,GAAGpB,kBAAkB,CAACM,OAAO,GAAGM,iBAAiB;MACrEhB,aAAa,CAACwB,cAAc,CAAC;;MAE7B;MACA,IAAI3B,kBAAkB,EAAE;QACtBA,kBAAkB,CAAC2B,cAAc,CAAC;MACpC;IACF,CAAC;IAEDrB,cAAc,CAACO,OAAO,CAACe,OAAO,GAAIV,KAAK,IAAK;MAC1CP,OAAO,CAACC,KAAK,CAAC,2BAA2B,EAAEM,KAAK,CAACN,KAAK,CAAC;MACvD,IAAIM,KAAK,CAACN,KAAK,KAAK,WAAW,EAAE;QAC/BP,cAAc,CAAC,KAAK,CAAC;MACvB;IACF,CAAC;IAEDC,cAAc,CAACO,OAAO,CAACgB,KAAK,GAAG,MAAM;MACnC;MACA,IAAIzB,WAAW,EAAE;QACf,IAAI;UACFE,cAAc,CAACO,OAAO,CAACiB,KAAK,CAAC,CAAC;QAChC,CAAC,CAAC,OAAOlB,KAAK,EAAE;UACdD,OAAO,CAACC,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACxD;MACF;IACF,CAAC;;IAED;IACA,OAAO,MAAM;MACX,IAAIN,cAAc,CAACO,OAAO,EAAE;QAC1B,IAAI;UACFP,cAAc,CAACO,OAAO,CAACkB,IAAI,CAAC,CAAC;QAC/B,CAAC,CAAC,OAAOnB,KAAK,EAAE;UACdD,OAAO,CAACC,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACrD;MACF;IACF,CAAC;EACH,CAAC,EAAE,CAACR,WAAW,EAAEJ,kBAAkB,CAAC,CAAC;;EAErC;EACA,MAAMgC,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI1B,cAAc,CAACO,OAAO,IAAI,CAACT,WAAW,EAAE;MAC1CG,kBAAkB,CAACM,OAAO,GAAG,EAAE,CAAC,CAAC;MACjCV,aAAa,CAAC,EAAE,CAAC;MAEjB,IAAI;QACFG,cAAc,CAACO,OAAO,CAACiB,KAAK,CAAC,CAAC;QAC9BzB,cAAc,CAAC,IAAI,CAAC;MACtB,CAAC,CAAC,OAAOO,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;;QAEnD;QACA,IAAIA,KAAK,CAACqB,OAAO,CAACC,QAAQ,CAAC,iBAAiB,CAAC,EAAE;UAC7C5B,cAAc,CAACO,OAAO,CAACkB,IAAI,CAAC,CAAC;UAC7BI,UAAU,CAAC,MAAM;YACf7B,cAAc,CAACO,OAAO,CAACiB,KAAK,CAAC,CAAC;YAC9BzB,cAAc,CAAC,IAAI,CAAC;UACtB,CAAC,EAAE,GAAG,CAAC;QACT;MACF;IACF;EACF,CAAC;;EAED;EACA,MAAM+B,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI9B,cAAc,CAACO,OAAO,IAAIT,WAAW,EAAE;MACzCE,cAAc,CAACO,OAAO,CAACkB,IAAI,CAAC,CAAC;MAC7B1B,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,oBACEP,OAAA;IAAKuC,SAAS,EAAC,oBAAoB;IAAAC,QAAA,gBACjCxC,OAAA;MAAKuC,SAAS,EAAC,oBAAoB;MAAAC,QAAA,EAChCpC,UAAU,gBACTJ,OAAA;QAAGuC,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAEpC;MAAU;QAAAqC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,gBAE/C5C,OAAA;QAAGuC,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IACpD;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEN5C,OAAA;MAAKuC,SAAS,EAAC,UAAU;MAAAC,QAAA,gBACvBxC,OAAA;QACEuC,SAAS,EAAE,kBAAkBjC,WAAW,GAAG,WAAW,GAAG,EAAE,EAAG;QAC9DuC,OAAO,EAAEvC,WAAW,GAAGgC,aAAa,GAAGJ,cAAe;QAAAM,QAAA,GAErDlC,WAAW,GAAG,MAAM,GAAG,OAAO,EAAC,YAClC;MAAA;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EAERtC,WAAW,iBACVN,OAAA;QAAKuC,SAAS,EAAC,QAAQ;QAAAC,QAAA,gBACrBxC,OAAA;UAAMuC,SAAS,EAAC;QAAqB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,gBAE/C;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACzC,EAAA,CAzIQF,iBAAiB;AAAA6C,EAAA,GAAjB7C,iBAAiB;AA2I1B,eAAeA,iBAAiB;AAAC,IAAA6C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}