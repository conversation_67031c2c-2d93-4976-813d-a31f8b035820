.site-header {
  background-color: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.top-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #eee;
  font-size: 14px;
}

.contact-info {
  display: flex;
  gap: 20px;
}

.contact-item {
  color: #666;
  text-decoration: none;
  transition: color 0.3s;
}

.contact-item:hover {
  color: #6e8efb;
}

.contact-item i {
  margin-right: 5px;
}

.social-links {
  display: flex;
  gap: 10px;
}

.social-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #f5f5f5;
  color: #666;
  text-decoration: none;
  transition: all 0.3s ease;
}

.social-icon:hover {
  transform: translateY(-3px);
}

.social-icon.youtube:hover {
  background-color: #ff0000;
  color: white;
}

.social-icon.twitter:hover {
  background-color: #1da1f2;
  color: white;
}

.social-icon.instagram:hover {
  background-color: #e1306c;
  color: white;
}

.social-icon.linkedin:hover {
  background-color: #0077b5;
  color: white;
}

.main-nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
}

.logo-link {
  display: flex;
  align-items: center;
  font-size: 24px;
  font-weight: bold;
  color: #333;
  text-decoration: none;
  transition: color 0.3s;
}

.logo-link:hover {
  color: #6e8efb;
}

.home-icon {
  margin-right: 8px;
  font-size: 28px;
  color: #6e8efb;
}

.menu-toggle {
  display: none;
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #333;
}

.nav-links {
  display: flex;
  align-items: center;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 20px;
}

.nav-links li a {
  color: #333;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s;
}

.nav-links li a:hover {
  color: #6e8efb;
}

.nav-button a {
  display: inline-block;
  padding: 8px 16px;
  border-radius: 4px;
  text-decoration: none;
  transition: all 0.3s;
}

.nav-button a {
  background-color: #f5f5f5;
  color: #333;
}

.nav-button.highlight a {
  background-color: #6e8efb;
  color: white;
}

.nav-button a:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

/* Logout button styles */
.logout-button {
  margin-left: 10px;
}

.logout-btn {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  border-radius: 4px;
  background-color: #f44336;
  color: white;
  border: none;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s;
}

.logout-btn:hover {
  background-color: #d32f2f;
  transform: translateY(-3px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.logout-icon {
  margin-right: 5px;
  font-size: 18px;
}

.header-banner {
  text-align: center;
  padding: 50px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.header-banner h1 {
  margin: 0;
  font-size: 2.5rem;
  font-weight: 700;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
}

.header-banner p {
  margin: 10px 0 0;
  font-size: 1.2rem;
  opacity: 0.9;
}

@media (max-width: 992px) {
  .menu-toggle {
    display: block;
  }

  .nav-links {
    position: fixed;
    top: 0;
    right: -100%;
    width: 70%;
    max-width: 300px;
    height: 100vh;
    background-color: white;
    flex-direction: column;
    align-items: flex-start;
    padding: 80px 20px 20px;
    transition: right 0.3s ease;
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
    z-index: 1000;
  }

  .nav-links.active {
    right: 0;
  }

  .nav-button {
    margin-top: 10px;
    width: 100%;
  }

  .nav-button a {
    display: block;
    text-align: center;
  }

  .logout-btn {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .top-bar {
    flex-direction: column;
    gap: 10px;
  }

  .contact-info {
    flex-direction: column;
    align-items: center;
    gap: 5px;
  }

  .header-banner h1 {
    font-size: 2rem;
  }

  .header-banner p {
    font-size: 1rem;
  }
}



