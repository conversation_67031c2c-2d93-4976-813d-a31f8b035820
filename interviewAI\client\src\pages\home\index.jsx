import React, { useRef } from 'react';
import { Link } from 'react-router-dom';
import Header from '../../components/Header';
import Footer from '../../components/Footer';
import './home.css';

function HomeScreen() {
  const pricingRef = useRef(null);

  const scrollToPricing = (e) => {
    e.preventDefault();
    pricingRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <div className="home-page">
      <Header />

      <main className="home-content">
        <section className="hero-section">
          <div className="container">
            <h1>Prepare for Interviews with AI</h1>
            <p className="subtitle">Practice, improve, and land your dream job with our AI-powered interview coach</p>
            <div className="cta-buttons">
              <Link to="/automated-interview" className="cta-button primary">Try Automated Interview</Link>
              <Link to="/login" className="cta-button secondary">Try Manual Interview</Link>
              <a href="#pricing" className="cta-button secondary" onClick={scrollToPricing}>View Plans</a>
              <Link to="/live-transcription" className="cta-button secondary">Live Transcription Demo</Link>
            </div>
          </div>
        </section>

        <section className="features-section">
          <div className="container">
            <h2>Features</h2>
            <div className="features-grid">
              <div className="feature-card">
                <div className="feature-icon">🎙️</div>
                <h3>Real-time Voice Analysis</h3>
                <p>Practice your answers with our AI that provides instant feedback on your delivery</p>
              </div>
              <div className="feature-card">
                <div className="feature-icon">💼</div>
                <h3>Industry-specific Questions</h3>
                <p>Get questions tailored to your industry and role</p>
              </div>
              <div className="feature-card">
                <div className="feature-icon">📝</div>
                <h3>Personalized Feedback</h3>
                <p>Receive detailed feedback to improve your interview skills</p>
              </div>
              <div className="feature-card highlight">
                <div className="feature-icon">🔄</div>
                <h3>Automated Interview Simulation</h3>
                <p>Experience our new automated interview feature with screen sharing and automatic voice transcription that responds to your answers in real-time</p>
              </div>
            </div>
          </div>
        </section>

        <section id="pricing" className="pricing-section" ref={pricingRef}>
          <div className="container">
            <h2>Choose Your Plan</h2>
            <div className="pricing-grid">
              <div className="pricing-card">
                <div className="pricing-header">
                  <h3>FREE</h3>
                  <div className="price">
                    <span className="currency">$</span>
                    <span className="amount">0</span>
                    <span className="period">/ 15Min Free</span>
                  </div>
                </div>
                <div className="pricing-features">
                  <div className="feature-item available">
                    <span className="check">✓</span>
                    <span>15-min Interview Sessions</span>
                  </div>
                  <div className="feature-item available">
                    <span className="check">✓</span>
                    <span>AI Resume Builder</span>
                  </div>
                  <div className="feature-item available">
                    <span className="check">✓</span>
                    <span>AI Story Editor</span>
                  </div>
                  <div className="feature-item unavailable">
                    <span className="cross">✕</span>
                    <span>Full Customization Suite</span>
                  </div>
                  <div className="feature-item unavailable">
                    <span className="cross">✕</span>
                    <span>Industry Knowledge Base Add-Ons</span>
                  </div>
                  <div className="feature-item unavailable">
                    <span className="cross">✕</span>
                    <span>Coding Interview Practice</span>
                  </div>
                  <div className="feature-item unavailable">
                    <span className="cross">✕</span>
                    <span>AI Playground</span>
                  </div>
                </div>
                <Link to="/signup/free" className="pricing-cta">Try for free</Link>
              </div>

              <div className="pricing-card">
                <div className="pricing-header">
                  <h3>PRO</h3>
                  <div className="price">
                    <span className="currency">$</span>
                    <span className="amount">4.99</span>
                    <span className="period">/ hourly</span>
                  </div>
                </div>
                <div className="pricing-features">
                  <div className="feature-item available">
                    <span className="check">✓</span>
                    <span>Unlimited Interview Sessions</span>
                  </div>
                  <div className="feature-item available">
                    <span className="check">✓</span>
                    <span>AI Resume Builder</span>
                  </div>
                  <div className="feature-item available">
                    <span className="check">✓</span>
                    <span>AI Story Editor</span>
                  </div>
                  <div className="feature-item available">
                    <span className="check">✓</span>
                    <span>Full Customization Suite</span>
                  </div>
                  <div className="feature-item available">
                    <span className="check">✓</span>
                    <span>Industry Knowledge Base Add-Ons</span>
                  </div>
                  <div className="feature-item available">
                    <span className="check">✓</span>
                    <span>Coding Interview Practice</span>
                  </div>
                  <div className="feature-item available">
                    <span className="check">✓</span>
                    <span>AI Playground</span>
                  </div>
                </div>
                <Link to="/signup/pro" className="pricing-cta">Get the plan</Link>
              </div>

              {/* <div className="pricing-card premium">
                <div className="pricing-badge">Most Popular</div>
                <div className="pricing-header">
                  <h3>PRO PLUS</h3>
                  <div className="price">
                    <span className="currency">$</span>
                    <span className="amount">29.99</span>
                    <span className="period">/ month billed annually</span>
                  </div>
                </div>
                <div className="pricing-features">
                  <div className="feature-item available">
                    <span className="check">✓</span>
                    <span>Everything in PRO plan</span>
                  </div>
                  <div className="feature-item available">
                    <span className="check">✓</span>
                    <span>Multi-device support (answer from any device)</span>
                  </div>
                  <div className="feature-item available">
                    <span className="check">✓</span>
                    <span>No eye movement detection</span>
                  </div>
                  <div className="feature-item available">
                    <span className="check">✓</span>
                    <span>All meeting platform integration</span>
                  </div>
                  <div className="feature-item available">
                    <span className="check">✓</span>
                    <span>Pre-trained on your technical skills</span>
                  </div>
                  <div className="feature-item available">
                    <span className="check">✓</span>
                    <span>Undetectable assistance</span>
                  </div>
                  <div className="feature-item available">
                    <span className="check">✓</span>
                    <span>Support for all industries & technologies</span>
                  </div>
                  <div className="feature-item available">
                    <span className="check">✓</span>
                    <span>Multiple language support</span>
                  </div>
                  <div className="feature-item available">
                    <span className="check">✓</span>
                    <span>Create designs & architecture diagrams</span>
                  </div>
                  <div className="feature-item available">
                    <span className="check">✓</span>
                    <span>Zero latency (custom optimized model)</span>
                  </div>
                  <div className="feature-item available">
                    <span className="check">✓</span>
                    <span>Multiple answers before time limit</span>
                  </div>
                </div>
                <Link to="/signup/premium" className="pricing-cta">Get Premium</Link>
              </div> */}
            </div>
          </div>
        </section>

        <section className="faq-section">
          <div className="container">
            <h2>Frequently Asked Questions</h2>
            <div className="faq-list">
              <div className="faq-item">
                <h3>How does the AI interview coach work?</h3>
                <p>Our AI interview coach uses advanced natural language processing to analyze your responses, provide feedback on your delivery, and suggest improvements to help you ace your interviews.</p>
              </div>
              <div className="faq-item">
                <h3>Can I use this for technical interviews?</h3>
                <p>Yes! Our PRO plan includes specialized modules for technical and coding interviews across various programming languages and frameworks.</p>
              </div>
              <div className="faq-item">
                <h3>Is my data secure?</h3>
                <p>We take data privacy seriously. All your interview sessions and personal information are encrypted and never shared with third parties.</p>
              </div>
              <div className="faq-item">
                <h3>Can I cancel my subscription anytime?</h3>
                <p>Absolutely. You can cancel your subscription at any time with no questions asked.</p>
              </div>
              <div className="faq-item">
                <h3>Do you offer support?</h3>
                <p>Yes, we provide 24/7 customer support via email and live chat for all our users.</p>
              </div>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
}

export default HomeScreen;






