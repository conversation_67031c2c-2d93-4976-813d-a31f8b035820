import React, { useRef, useState, useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import Header from '../../components/Header';
import Footer from '../../components/Footer';
import LogoutIcon from '@mui/icons-material/Logout';
import './home.css';

function HomeScreen() {
  const pricingRef = useRef(null);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  // eslint-disable-next-line
  const [userData, setUserData] = useState(null);
  // eslint-disable-next-line
  const navigate = useNavigate();
  const location = useLocation();
  const featuresRef = useRef(null);
  const contactRef = useRef(null);

  useEffect(() => {
    // Check if user is logged in
    const userDetails = localStorage.getItem("userDetails");
    if (userDetails) {
      setIsLoggedIn(true);
      try {
        const parsedData = JSON.parse(userDetails);
        setUserData(parsedData);
      } catch (error) {
        console.error("Error parsing user data:", error);
      }
    }

    // Scroll to section if coming from nav
    if (location.state && location.state.scrollTo) {
      setTimeout(() => {
        if (location.state.scrollTo === 'features' && featuresRef.current) {
          featuresRef.current.scrollIntoView({ behavior: 'smooth' });
        } else if (location.state.scrollTo === 'pricing' && pricingRef.current) {
          pricingRef.current.scrollIntoView({ behavior: 'smooth' });
        } else if (location.state.scrollTo === 'faq') {
          const faqEl = document.getElementById('faq');
          if (faqEl) faqEl.scrollIntoView({ behavior: 'smooth' });
        } else if (location.state.scrollTo === 'contact' && contactRef.current) {
          contactRef.current.scrollIntoView({ behavior: 'smooth' });
        }
      }, 100);
    }
  }, [location]);

  const scrollToPricing = (e) => {
    e.preventDefault();
    pricingRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleLogout = () => {
    // Clear localStorage
    localStorage.removeItem("userDetails");
    localStorage.removeItem("token");
    setIsLoggedIn(false);
    setUserData(null);
    // Refresh the page to update UI
    window.location.reload();
  };

  return (
    <div className="home-page">
      <Header />

      <main className="home-content">
        <section className="hero-section">
          <div className="container">
            <h1>Interview ready.</h1>
            <p className="subtitle">Get Instant answers for your interview questions.
              Ace your interview. Your dream job is right there</p>
            <div className="cta-buttons">
              {isLoggedIn ? (
                <>
                  <Link to="/home" className="cta-button primary">Start Interview</Link>
                  <button onClick={handleLogout} className="cta-button logout">
                    <LogoutIcon className="logout-icon" />
                    Logout
                  </button>
                </>
              ) : (
                <>
                  <Link to="/login" className="cta-button primary">Try now</Link>
                  <a href="#pricing" className="cta-button secondary" onClick={scrollToPricing}>View Plans</a>
                </>
              )}
              {/* <Link to="/live-transcription" className="cta-button secondary">Live Transcription Demo</Link>
              <Link to="/diarized-interview" className="cta-button highlight">Enhanced Interview with Speaker Diarization</Link>
              <Link to="/automatic-diarization" className="cta-button highlight-new">Automatic Speaker Detection</Link>
              <Link to="/google-speech" className="cta-button highlight-google">Google Speech API Diarization</Link> */}
            </div>
          </div>
        </section>

        <section className="how-it-works-section">
          <div className="container">
            <h2>How does our Interview AI work?</h2>
            <p className="section-subtitle">Get interview-ready in 4 simple steps</p>

            <div className="steps-container">
              <div className="step-card">
                <div className="step-content">
                  <div className="step-number">1</div>
                  <h3>Start Your Session</h3>
                  <p>Sign up and choose your plan. Input your skillset, job description, and optionally upload your resume. Click "Start Interview" to begin your AI-powered interview preparation session.</p>
                </div>
                <div className="step-visual">
                  <div className="mini-interface">
                    <div className="mini-header">📝 Setup Form</div>
                    <div className="mini-field">Skillset: React, Node.js</div>
                    <div className="mini-field">Job: Frontend Developer</div>
                    <div className="mini-field">Resume: ✓ Uploaded</div>
                    <div className="mini-button">Start Interview</div>
                  </div>
                </div>
              </div>

              <div className="step-card">
                <div className="step-content">
                  <div className="step-number">2</div>
                  <h3>Ask Questions via Voice or Text</h3>
                  <p>Use our advanced speech-to-text technology or type your interview questions. Our AI supports all industries and technologies, providing instant, accurate responses to help you practice.</p>
                </div>
                <div className="step-visual">
                  <div className="mini-interface">
                    <div className="mini-header">💬 AI Response <span className="mini-timer">14:32</span></div>
                    <div className="mini-response">AI answer will appear here...</div>
                    <div className="mini-question">What is React and how does it work?</div>
                  </div>
                </div>
              </div>

              <div className="step-card">
                <div className="step-content">
                  <div className="step-number">3</div>
                  <h3>Get Instant AI Responses</h3>
                  <p>Receive real-time, comprehensive answers with definitions, examples, and coding solutions. Our ultra-low latency AI ensures you get immediate support exactly when you need it.</p>
                </div>
                <div className="step-visual">
                  <div className="mini-interface">
                    <div className="mini-header">✅ AI Response <span className="mini-timer">14:28</span></div>
                    <div className="mini-response filled">
                      <strong>Definition:</strong> React is a JavaScript library...<br/>
                      <strong>Example:</strong> Component-based architecture<br/>
                      <strong>Code:</strong> function HelloWorld() {`{`}...{`}`}
                    </div>
                    <div className="mini-question">Type or record your question here...</div>
                  </div>
                </div>
              </div>
            </div>

            <div className="demo-cta">
              <Link to="/login" className="demo-button">Try Interactive Demo</Link>
              <p className="demo-subtitle">Experience our AI in action - no commitment required</p>
            </div>

            <div className="demo-interface-preview">
              <div className="demo-arrow">
                <svg width="40" height="60" viewBox="0 0 40 60" fill="none">
                  <path d="M20 5 C25 15, 15 25, 20 35 C25 45, 15 55, 20 55" stroke="#666" strokeWidth="2" fill="none"/>
                </svg>
              </div>

              <div className="demo-conversation">
                <div className="demo-interviewer">
                  <div className="demo-avatar">
                    <span className="avatar-icon">👤</span>
                    <span className="speaker-label">Interviewer</span>
                  </div>
                  <div className="demo-message">
                    Tell me about a time when you explained a technical problem to someone who didn't have a tech background.
                  </div>
                  <div className="demo-status">
                    <span className="status-dot"></span>
                    listening to interviewer...
                  </div>
                </div>

                <div className="demo-ai">
                  <div className="demo-avatar">
                    <span className="avatar-icon ai">🤖</span>
                    <span className="speaker-label">AI Assistant</span>
                  </div>
                  <div className="demo-message ai-response">
                    <div className="response-section">
                      <strong>Situation:</strong>
                      <p>- Building real-time analytics by integrating with third-party CRM systems.</p>
                      <p>- Significant latency for the data synchronization process between our platform and the CRM systems</p>
                      <p>- The way our platform handled API rate limits and managed concurrent data requests is incorrect</p>
                    </div>

                    <div className="response-section">
                      <strong>Task:</strong>
                      <p>- Explain to sales and marketing, who were preparing for a product launch.</p>
                    </div>

                    <div className="response-section">
                      <strong>Action:</strong>
                      <p>- Compare data synchronisation process to a postal service handling large volumes of mail.</p>
                      <p>- The issue was like having too many packages</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="demo-input-area">
                <div className="demo-input-container">
                  <div className="demo-response-box">
                    <div className="response-placeholder">AI answer will appear here...</div>
                  </div>
                  <div className="demo-question-box">
                    <div className="question-placeholder">Type or record your question here...</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <section ref={featuresRef} id="features" className="features-section">
          <div className="container">
            <h2>Features</h2>
            <div className="features-grid">
              <div className="feature-card">
                <div className="feature-icon">🌐</div>
                <h3>Universal Meeting Access</h3>
                <p>Our app supports both browser-based and app-based meetings, giving you the flexibility to connect anytime, anywhere—no installations required.</p>
              </div>
              <div className="feature-card">
                <div className="feature-icon">🔗</div>
                <h3>Seamless Platform Integration</h3>
                <p>Seamless integration with all leading meeting platforms, including Zoom, Microsoft Teams, Google Meet, Webex, Skype, Slack, BlueJeans, GoTo Meeting, Whereby, Jitsi Meet, BigBlueButton, Zoho Meeting, Amazon Chime, Adobe Connect, ClickMeeting, Livestorm, RingCentral Video, 8x8 Meet, Pexip, TrueConf and more.</p>
              </div>
              <div className="feature-card">
                <div className="feature-icon">🏢</div>
                <h3>All Industries Supported</h3>
                <p>Our interview-focused app is proven effective for all industries, including traditional, emerging, and niche sectors like Technology & IT, Healthcare & Medical, Finance & Banking, Education & Academia, Legal Services, Retail & E-commerce, Manufacturing & Production, Construction & Real Estate, Transportation & Logistics, Telecommunications, and more.</p>
              </div>
              <div className="feature-card">
                <div className="feature-icon">💻</div>
                <h3>IT & Tech Interview Mastery</h3>
                <p>Specialized for interviews across all IT technologies: Software Development (Frontend, Backend, Full Stack), DevOps & Cloud (AWS, Azure, GCP, CI/CD), Data Science & Analytics (ML, DL, NLP, BI), Cybersecurity, Database Management, Mobile & Web Development, AI/ML, Blockchain, IT Support, ERP/CRM (SAP, Oracle, Salesforce), QA & Testing, Networking, UI/UX, and more.</p>
              </div>
              <div className="feature-card">
                <div className="feature-icon">⚡</div>
                <h3>Instant Coding Solutions</h3>
                <p>Ace any coding test—our AI delivers instant, proven, and reliable coding solutions across all technologies. Fast, accurate, and always available.</p>
              </div>
              <div className="feature-card highlight">
                <div className="feature-icon">🚀</div>
                <h3>Ultra-Low Latency AI</h3>
                <p>With ultra-low latency, our AI delivers instant answers—no delays, no waiting. You get real-time support exactly when you need it.</p>
              </div>
            </div>
          </div>
        </section>

        <section id="pricing" className="pricing-section" ref={pricingRef}>
          <div className="container">
            <h2>Pick your plan</h2>
            <div className="pricing-grid">
              <div className="pricing-card">
                <div className="pricing-header">
                  <h3>FREE</h3>
                  <div className="price">
                    <span className="currency">$</span>
                    <span className="amount">0</span>
                    <span className="period">/ 5 mins session</span>
                  </div>
                </div>
                <div className="pricing-features">
                  <div className="feature-item available"><span className="check">✓</span><span>5 mins session</span></div>
                  <div className="feature-item available"><span className="check">✓</span><span>Browser based/app based meeting</span></div>
                  <div className="feature-item available"><span className="check">✓</span><span>All meeting platform integration</span></div>
                  <div className="feature-item available"><span className="check">✓</span><span>All industry/All Technology</span></div>
                  <div className="feature-item available"><span className="check">✓</span><span>Instant Coding solution</span></div>
                  <div className="feature-item available"><span className="check">✓</span><span>Instant Image creation</span></div>
                  <div className="feature-item available"><span className="check">✓</span><span>Highly Customized AI playground</span></div>
                </div>
                <Link to="/signup" className="pricing-cta">Try for free</Link>
              </div>

              <div className="pricing-card">
                <div className="pricing-header">
                  <h3>PRO</h3>
                  <div className="price">
                    <span className="currency">$</span>
                    <span className="amount">4.99</span>
                    <span className="period">/ 1 hour session</span>
                  </div>
                </div>
                <div className="pricing-features">
                  <div className="feature-item available"><span className="check">✓</span><span>1 hour session</span></div>
                  <div className="feature-item available"><span className="check">✓</span><span>Browser based/app based meeting</span></div>
                  <div className="feature-item available"><span className="check">✓</span><span>All meeting platform integration</span></div>
                  <div className="feature-item available"><span className="check">✓</span><span>All industry/All Technology</span></div>
                  <div className="feature-item available"><span className="check">✓</span><span>Instant Coding solution</span></div>
                  <div className="feature-item available"><span className="check">✓</span><span>Instant Image creation</span></div>
                  <div className="feature-item available"><span className="check">✓</span><span>Highly Customized AI playground</span></div>
                </div>
                <Link to="/signup" className="pricing-cta">Get the plan</Link>
              </div>
            </div>
          </div>
        </section>

        <section id="faq" className="faq-section">
          <div className="container">
            <h2>Frequently Asked Questions</h2>
            <div className="faq-list">
              <div className="faq-item">
                <div className="faq-question">
                  <h3>Question 1: What is the purpose of this app?</h3>
                </div>
                <div className="faq-answer">
                  <p>Our app is designed to help users prepare for interviews by providing instant answers to interview questions, coding challenges, and more.</p>
                </div>
              </div>
              <div className="faq-item">
                <div className="faq-question">
                  <h3>Question 2: How does the AI work?</h3>
                </div>
                <div className="faq-answer">
                  <p>Our AI uses advanced algorithms and machine learning techniques to deliver accurate and relevant answers to your queries in real-time.</p>
                </div>
              </div>
              <div className="faq-item">
                <div className="faq-question">
                  <h3>Question 3: Is my data safe?</h3>
                </div>
                <div className="faq-answer">
                  <p>Yes, we take data security seriously. Please refer to our Privacy Policy for detailed information on how we protect your data.</p>
                </div>
              </div>
              <div className="faq-item">
                <div className="faq-question">
                  <h3>Question 4: Can I use this app on any device?</h3>
                </div>
                <div className="faq-answer">
                  <p>Absolutely! Our app is web-based and can be accessed from any device with an internet connection.</p>
                </div>
              </div>
              <div className="faq-item">
                <div className="faq-question">
                  <h3>Question 5: What if I have more questions?</h3>
                </div>
                <div className="faq-answer">
                  <p>Feel free to reach out to our support team or check our Help Center for more information.</p>
                </div>
              </div>
            </div>
          </div>
        </section>

        <section id="contact" ref={contactRef} className="contact-section">
          <Footer />
        </section>
      </main>
    </div>
  );
}

export default HomeScreen;






