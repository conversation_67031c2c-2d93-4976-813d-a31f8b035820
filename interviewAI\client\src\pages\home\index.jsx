import React, { useRef, useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import Header from '../../components/Header';
import Footer from '../../components/Footer';
import LogoutIcon from '@mui/icons-material/Logout';
import './home.css';

function HomeScreen() {
  const pricingRef = useRef(null);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  // eslint-disable-next-line
  const [userData, setUserData] = useState(null);
  // eslint-disable-next-line
  const navigate = useNavigate();

  useEffect(() => {
    // Check if user is logged in
    const userDetails = localStorage.getItem("userDetails");
    if (userDetails) {
      setIsLoggedIn(true);
      try {
        const parsedData = JSON.parse(userDetails);
        setUserData(parsedData);
      } catch (error) {
        console.error("Error parsing user data:", error);
      }
    }
  }, []);

  const scrollToPricing = (e) => {
    e.preventDefault();
    pricingRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleLogout = () => {
    // Clear localStorage
    localStorage.removeItem("userDetails");
    localStorage.removeItem("token");
    setIsLoggedIn(false);
    setUserData(null);
    // Refresh the page to update UI
    window.location.reload();
  };

  return (
    <div className="home-page">
      <Header />

      <main className="home-content">
        <section className="hero-section">
          <div className="container">
            <h1>Interview ready.</h1>
            <p className="subtitle">Get Instant answers for your interview questions.
              Ace your interview. Your dream job is right there</p>
            <div className="cta-buttons">
              {isLoggedIn ? (
                <>
                  <Link to="/home" className="cta-button primary">Start Interview</Link>
                  <button onClick={handleLogout} className="cta-button logout">
                    <LogoutIcon className="logout-icon" />
                    Logout
                  </button>
                </>
              ) : (
                <>
                  <Link to="/login" className="cta-button primary">Try now</Link>
                  <a href="#pricing" className="cta-button secondary" onClick={scrollToPricing}>View Plans</a>
                </>
              )}
              {/* <Link to="/live-transcription" className="cta-button secondary">Live Transcription Demo</Link>
              <Link to="/diarized-interview" className="cta-button highlight">Enhanced Interview with Speaker Diarization</Link>
              <Link to="/automatic-diarization" className="cta-button highlight-new">Automatic Speaker Detection</Link>
              <Link to="/google-speech" className="cta-button highlight-google">Google Speech API Diarization</Link> */}
            </div>
          </div>
        </section>

        <section className="features-section">
          <div className="container">
            <h2>Features</h2>
            <div className="features-grid">
              <div className="feature-card">
                <div className="feature-icon">🎙️</div>
                <h3>Real-time Voice Analysis</h3>
                <p>Practice your answers with our AI that provides instant feedback on your delivery</p>
              </div>
              <div className="feature-card">
                <div className="feature-icon">💼</div>
                <h3>Industry-specific Questions</h3>
                <p>Get questions tailored to your industry and role</p>
              </div>
              <div className="feature-card">
                <div className="feature-icon">📝</div>
                <h3>Personalized Feedback</h3>
                <p>Receive detailed feedback to improve your interview skills</p>
              </div>
              <div className="feature-card highlight">
                <div className="feature-icon">🔄</div>
                <h3>Automated Interview Simulation</h3>
                <p>Experience our new automated interview feature with screen sharing and automatic voice transcription that responds to your answers in real-time</p>
              </div>
            </div>
          </div>
        </section>

        <section id="pricing" className="pricing-section" ref={pricingRef}>
          <div className="container">
            <h2>Choose Your Plan</h2>
            <div className="pricing-grid">
              <div className="pricing-card">
                <div className="pricing-header">
                  <h3>FREE</h3>
                  <div className="price">
                    <span className="currency">$</span>
                    <span className="amount">0</span>
                    <span className="period">/ 5Min Free</span>
                  </div>
                </div>
                <div className="pricing-features">
                  <div className="feature-item available">
                    <span className="check">✓</span>
                    <span>5-min Interview Sessions</span>
                  </div>
                  <div className="feature-item available">
                    <span className="check">✓</span>
                    <span>AI Resume Builder</span>
                  </div>
                  <div className="feature-item available">
                    <span className="check">✓</span>
                    <span>AI Story Editor</span>
                  </div>
                  <div className="feature-item unavailable">
                    <span className="cross">✕</span>
                    <span>Full Customization Suite</span>
                  </div>
                  <div className="feature-item unavailable">
                    <span className="cross">✕</span>
                    <span>Industry Knowledge Base Add-Ons</span>
                  </div>
                  <div className="feature-item unavailable">
                    <span className="cross">✕</span>
                    <span>Coding Interview Practice</span>
                  </div>
                  <div className="feature-item unavailable">
                    <span className="cross">✕</span>
                    <span>AI Playground</span>
                  </div>
                </div>
                <Link to="/signup/free" className="pricing-cta">Try for free</Link>
              </div>

              <div className="pricing-card">
                <div className="pricing-header">
                  <h3>PRO</h3>
                  <div className="price">
                    <span className="currency">$</span>
                    <span className="amount">4.99</span>
                    <span className="period">/ hourly</span>
                  </div>
                </div>
                <div className="pricing-features">
                  <div className="feature-item available">
                    <span className="check">✓</span>
                    <span>Unlimited Interview Sessions</span>
                  </div>
                  <div className="feature-item available">
                    <span className="check">✓</span>
                    <span>AI Resume Builder</span>
                  </div>
                  <div className="feature-item available">
                    <span className="check">✓</span>
                    <span>AI Story Editor</span>
                  </div>
                  <div className="feature-item available">
                    <span className="check">✓</span>
                    <span>Full Customization Suite</span>
                  </div>
                  <div className="feature-item available">
                    <span className="check">✓</span>
                    <span>Industry Knowledge Base Add-Ons</span>
                  </div>
                  <div className="feature-item available">
                    <span className="check">✓</span>
                    <span>Coding Interview Practice</span>
                  </div>
                  <div className="feature-item available">
                    <span className="check">✓</span>
                    <span>AI Playground</span>
                  </div>
                </div>
                <Link to="/signup/pro" className="pricing-cta">Get the plan</Link>
              </div>

              {/* <div className="pricing-card premium">
                <div className="pricing-badge">Most Popular</div>
                <div className="pricing-header">
                  <h3>PRO PLUS</h3>
                  <div className="price">
                    <span className="currency">$</span>
                    <span className="amount">29.99</span>
                    <span className="period">/ month billed annually</span>
                  </div>
                </div>
                <div className="pricing-features">
                  <div className="feature-item available">
                    <span className="check">✓</span>
                    <span>Everything in PRO plan</span>
                  </div>
                  <div className="feature-item available">
                    <span className="check">✓</span>
                    <span>Multi-device support (answer from any device)</span>
                  </div>
                  <div className="feature-item available">
                    <span className="check">✓</span>
                    <span>No eye movement detection</span>
                  </div>
                  <div className="feature-item available">
                    <span className="check">✓</span>
                    <span>All meeting platform integration</span>
                  </div>
                  <div className="feature-item available">
                    <span className="check">✓</span>
                    <span>Pre-trained on your technical skills</span>
                  </div>
                  <div className="feature-item available">
                    <span className="check">✓</span>
                    <span>Undetectable assistance</span>
                  </div>
                  <div className="feature-item available">
                    <span className="check">✓</span>
                    <span>Support for all industries & technologies</span>
                  </div>
                  <div className="feature-item available">
                    <span className="check">✓</span>
                    <span>Multiple language support</span>
                  </div>
                  <div className="feature-item available">
                    <span className="check">✓</span>
                    <span>Create designs & architecture diagrams</span>
                  </div>
                  <div className="feature-item available">
                    <span className="check">✓</span>
                    <span>Zero latency (custom optimized model)</span>
                  </div>
                  <div className="feature-item available">
                    <span className="check">✓</span>
                    <span>Multiple answers before time limit</span>
                  </div>
                </div>
                <Link to="/signup/premium" className="pricing-cta">Get Premium</Link>
              </div> */}
            </div>
          </div>
        </section>

        <section className="faq-section">
          <div className="container">
            <h2>Frequently Asked Questions</h2>
            <div className="faq-list">
              <div className="faq-item">
                <h3>Which devices can I use to take or answer interviews?</h3>
                <p>You can practise interviews from any device — web, mobile, iPad, or any other device. Interviews and responses can also be done on different devices for maximum flexibility.</p>
              </div>
              <div className="faq-item">
                <h3>Does the system require any special hardware or software installation?</h3>
                <p>No software or hardware installation is needed to run our application.</p>
              </div>
              <div className="faq-item">
                <h3>What meeting platforms does your system integrate with?</h3>
                <p>We integrate with all major meeting platforms, including Zoom, Microsoft Teams, Google Meet, Webex, Skype, GoToMeeting, BlueJeans, Slack, and more.</p>
              </div>
              <div className="faq-item">
                <h3>How personalized is the interview experience?</h3>
                <p>Our solution is highly personalized. We feed in your technical skills and train our custom AI model beforehand to tailor the interview specifically to your expertise.</p>
              </div>
              <div className="faq-item">
                <h3>Is your AI detection-proof or undetectable?</h3>
                <p>Yes, our AI operates in a way that is undetectable, ensuring a natural and authentic interaction.</p>
              </div>
              <div className="faq-item">
                <h3>Can this platform be used for all industries and technologies?</h3>
                <p>Absolutely! Our platform supports all industries, technologies to accommodate diverse professional needs.</p>
              </div>
              <div className="faq-item">
                <h3>Does your platform support coding and design tasks?</h3>
                <p>Yes, you can perform coding tasks, create web designs, architect diagrams, and more within the platform.</p>
              </div>
              <div className="faq-item">
                <h3>How fast are the responses?</h3>
                <p>We use our own highly customized AI model, resulting in zero latency for instant, real-time answers.</p>
              </div>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
}

export default HomeScreen;






