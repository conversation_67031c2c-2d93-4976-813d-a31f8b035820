{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\interview-ai-app\\\\interviewAI\\\\client\\\\src\\\\components\\\\Footer.jsx\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport './Footer.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Footer() {\n  return /*#__PURE__*/_jsxDEV(\"footer\", {\n    className: \"site-footer\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"footer-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Interview AI\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 11,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Interview ready. Get Instant answers for your interview questions. Ace your interview. Your dream job is right there.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 12,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Quick Links\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 17,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"footer-links\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/\",\n                children: \"Home\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 19,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 19,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/features\",\n                children: \"Features\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 20,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 20,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/pricing\",\n                children: \"Pricing\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 21,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 21,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/faq\",\n                children: \"FAQ\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 22,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 22,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/contact\",\n                children: \"Contact\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 23,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 23,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 18,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Contact Us\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"<EMAIL>\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Phone: ******-235-9247\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"201 E Center St Suite 112, Anaheim CA 92805\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Follow Us\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"social-links\",\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"https://www.linkedin.com/company/interviewsupport-ai/\",\n              target: \"_blank\",\n              rel: \"noopener noreferrer\",\n              className: \"social-icon linkedin\",\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fab fa-linkedin\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 47,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 46,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"footer-bottom\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"\\xA9 \", new Date().getFullYear(), \" Interview AI. All rights reserved.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-bottom-links\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/privacy\",\n            children: \"Privacy Policy\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/terms\",\n            children: \"Terms of Service\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this);\n}\n_c = Footer;\nexport default Footer;\nvar _c;\n$RefreshReg$(_c, \"Footer\");", "map": {"version": 3, "names": ["React", "Link", "jsxDEV", "_jsxDEV", "Footer", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "href", "target", "rel", "Date", "getFullYear", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/interview-ai-app/interviewAI/client/src/components/Footer.jsx"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport './Footer.css';\n\nfunction Footer() {\n  return (\n    <footer className=\"site-footer\">\n      <div className=\"container\">\n        <div className=\"footer-content\">\n          <div className=\"footer-section\">\n            <h3>Interview AI</h3>\n            <p>Interview ready. Get Instant answers for your interview questions.\n                Ace your interview. Your dream job is right there.</p>\n          </div>\n          \n          <div className=\"footer-section\">\n            <h3>Quick Links</h3>\n            <ul className=\"footer-links\">\n              <li><Link to=\"/\">Home</Link></li>\n              <li><Link to=\"/features\">Features</Link></li>\n              <li><Link to=\"/pricing\">Pricing</Link></li>\n              <li><Link to=\"/faq\">FAQ</Link></li>\n              <li><Link to=\"/contact\">Contact</Link></li>\n            </ul>\n          </div>\n          \n          <div className=\"footer-section\">\n            <h3>Contact Us</h3>\n            <p><EMAIL></p>\n            <p>Phone: ******-235-9247</p>\n            <p>201 E Center St Suite 112, Anaheim CA 92805</p>\n          </div>\n          \n          <div className=\"footer-section\">\n            <h3>Follow Us</h3>\n            <div className=\"social-links\">\n              {/* <a href=\"https://youtube.com\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"social-icon youtube\">\n                <i className=\"fab fa-youtube\"></i>\n              </a>\n              <a href=\"https://twitter.com\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"social-icon twitter\">\n                <i className=\"fab fa-twitter\"></i>\n              </a>\n              <a href=\"https://instagram.com\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"social-icon instagram\">\n                <i className=\"fab fa-instagram\"></i>\n              </a> */}\n              <a href=\"https://www.linkedin.com/company/interviewsupport-ai/\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"social-icon linkedin\">\n                <i className=\"fab fa-linkedin\"></i>\n              </a>\n            </div>\n          </div>\n        </div>\n        \n        <div className=\"footer-bottom\">\n          <p>&copy; {new Date().getFullYear()} Interview AI. All rights reserved.</p>\n          <div className=\"footer-bottom-links\">\n            <Link to=\"/privacy\">Privacy Policy</Link>\n            <Link to=\"/terms\">Terms of Service</Link>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n\nexport default Footer;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAO,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,SAASC,MAAMA,CAAA,EAAG;EAChB,oBACED,OAAA;IAAQE,SAAS,EAAC,aAAa;IAAAC,QAAA,eAC7BH,OAAA;MAAKE,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBH,OAAA;QAAKE,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BH,OAAA;UAAKE,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BH,OAAA;YAAAG,QAAA,EAAI;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrBP,OAAA;YAAAG,QAAA,EAAG;UACmD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC,eAENP,OAAA;UAAKE,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BH,OAAA;YAAAG,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpBP,OAAA;YAAIE,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC1BH,OAAA;cAAAG,QAAA,eAAIH,OAAA,CAACF,IAAI;gBAACU,EAAE,EAAC,GAAG;gBAAAL,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjCP,OAAA;cAAAG,QAAA,eAAIH,OAAA,CAACF,IAAI;gBAACU,EAAE,EAAC,WAAW;gBAAAL,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7CP,OAAA;cAAAG,QAAA,eAAIH,OAAA,CAACF,IAAI;gBAACU,EAAE,EAAC,UAAU;gBAAAL,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3CP,OAAA;cAAAG,QAAA,eAAIH,OAAA,CAACF,IAAI;gBAACU,EAAE,EAAC,MAAM;gBAAAL,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnCP,OAAA;cAAAG,QAAA,eAAIH,OAAA,CAACF,IAAI;gBAACU,EAAE,EAAC,UAAU;gBAAAL,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAENP,OAAA;UAAKE,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BH,OAAA;YAAAG,QAAA,EAAI;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnBP,OAAA;YAAAG,QAAA,EAAG;UAA4B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACnCP,OAAA;YAAAG,QAAA,EAAG;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC7BP,OAAA;YAAAG,QAAA,EAAG;UAA2C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC,eAENP,OAAA;UAAKE,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BH,OAAA;YAAAG,QAAA,EAAI;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClBP,OAAA;YAAKE,SAAS,EAAC,cAAc;YAAAC,QAAA,eAU3BH,OAAA;cAAGS,IAAI,EAAC,uDAAuD;cAACC,MAAM,EAAC,QAAQ;cAACC,GAAG,EAAC,qBAAqB;cAACT,SAAS,EAAC,sBAAsB;cAAAC,QAAA,eACxIH,OAAA;gBAAGE,SAAS,EAAC;cAAiB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENP,OAAA;QAAKE,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BH,OAAA;UAAAG,QAAA,GAAG,OAAO,EAAC,IAAIS,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,EAAC,qCAAmC;QAAA;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC3EP,OAAA;UAAKE,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAClCH,OAAA,CAACF,IAAI;YAACU,EAAE,EAAC,UAAU;YAAAL,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzCP,OAAA,CAACF,IAAI;YAACU,EAAE,EAAC,QAAQ;YAAAL,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb;AAACO,EAAA,GA1DQb,MAAM;AA4Df,eAAeA,MAAM;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}