/* Admin Home Page Styles */
.App {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

main {
  flex: 1;
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

/* Navigation Buttons */
.nav-buttons {
  display: flex;
  justify-content: center;
  gap: 15px;
  padding: 15px 0;
  background-color: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 20px;
}

.nav-buttons button {
  padding: 10px 20px;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #333;
}

.nav-buttons button:hover {
  background-color: #f0f0f0;
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.nav-buttons button.active {
  background-color: #4a90e2;
  color: white;
  border-color: #4a90e2;
}

.nav-buttons button.highlight {
  background-color: #34a853;
  color: white;
  border-color: #34a853;
  position: relative;
  overflow: hidden;
}

.nav-buttons button.highlight:after {
  content: "New";
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: #ea4335;
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  transform: rotate(15deg);
  font-weight: bold;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .nav-buttons {
    flex-direction: column;
    align-items: center;
    gap: 10px;
    padding: 10px;
  }

  .nav-buttons button {
    width: 100%;
    max-width: 300px;
  }

  main {
    padding: 15px;
  }
}