import React, { useState, useEffect, useRef } from 'react';
import './AutomaticDiarization.css';

// Controls component for starting/stopping recording
function Controls({ isRecording, onStart, onStop, onCaptureTab }) {
  return (
    <div className="transcription-controls">
      <button
        className={`control-button ${isRecording ? 'recording' : ''}`}
        onClick={isRecording ? onStop : onStart}
      >
        {isRecording ? 'Stop Recording' : 'Start Microphone'}
      </button>
      <button
        className="control-button tab-audio"
        onClick={onCaptureTab}
        disabled={isRecording}
      >
        Capture Tab Audio (Interviewer)
      </button>
    </div>
  );
}

// Live captions component
function LiveCaptions({ interimText, currentSpeaker }) {
  if (!interimText) return null;
  return (
    <div className={`live-captions ${currentSpeaker.toLowerCase().replace(' ', '-')}`}>
      <div className="speaker-label">{currentSpeaker}</div>
      <div className="caption-text">{interimText}</div>
    </div>
  );
}

// Transcript component
function Transcript({ transcriptData }) {
  const transcriptRef = useRef(null);

  useEffect(() => {
    // Auto-scroll to bottom when new entries are added
    if (transcriptRef.current) {
      transcriptRef.current.scrollTop = transcriptRef.current.scrollHeight;
    }
  }, [transcriptData]);

  const downloadTranscript = () => {
    let content = "Conversation Transcript:\n\n";
    transcriptData.forEach(entry => {
      content += `[${entry.timestamp.toLocaleTimeString()}] ${entry.speaker}: ${entry.text}\n`;
    });
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'transcript.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="transcript-container">
      <div className="transcript-header">
        <h2>Full Transcript</h2>
        {transcriptData.length > 0 && (
          <button onClick={downloadTranscript} className="download-button">
            Download Transcript
          </button>
        )}
      </div>
      <div className="transcript-content" ref={transcriptRef}>
        {transcriptData.length === 0 && <p className="empty-state">No speech detected yet.</p>}
        <div className="transcript-entries">
          {transcriptData.map((entry, index) => (
            <div
              key={index}
              className={`transcript-entry ${entry.speaker.toLowerCase().replace(' ', '-')}`}
            >
              <div className="entry-header">
                <span className="timestamp">{entry.timestamp.toLocaleTimeString()}</span>
                <span className="speaker">{entry.speaker}</span>
              </div>
              <div className="entry-text">{entry.text}</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

// Main component
function AutomaticDiarization() {
  const [isRecording, setIsRecording] = useState(false);
  const [isCapturingTab, setIsCapturingTab] = useState(false);
  const [currentSpeaker, setCurrentSpeaker] = useState('Interviewer');
  const [interimTranscript, setInterimTranscript] = useState('');
  const [fullTranscript, setFullTranscript] = useState([]);

  // Refs
  const recognitionRef = useRef(null);
  const audioContextRef = useRef(null);
  const mediaStreamRef = useRef(null);

  // Initialize speech recognition
  useEffect(() => {
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    if (!SpeechRecognition) {
      alert('Speech Recognition API not supported in this browser. Try Chrome or Edge.');
      return;
    }

    // Clean up function
    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.stop();
      }
      if (mediaStreamRef.current) {
        mediaStreamRef.current.getTracks().forEach(track => track.stop());
      }
      if (audioContextRef.current) {
        audioContextRef.current.close();
      }
    };
  }, []);

  // Start microphone recording
  const startRecording = async () => {
    try {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      recognitionRef.current = new SpeechRecognition();
      recognitionRef.current.continuous = true;
      recognitionRef.current.interimResults = true;
      recognitionRef.current.lang = 'en-US';

      // Set up audio context for voice analysis
      if (!audioContextRef.current) {
        // eslint-disable-next-line
        audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();
      }

      // Get microphone stream
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      mediaStreamRef.current = stream;

      // Set default speaker to Candidate for microphone
      setCurrentSpeaker('Candidate');

      // Set up recognition handlers
      setupRecognitionHandlers();

      // Start recognition
      recognitionRef.current.start();
      setIsRecording(true);
      setIsCapturingTab(false);

    } catch (error) {
      console.error("Error starting recording:", error);
      alert("Failed to start recording: " + error.message);
    }
  };

  // Start tab audio capture
  const startTabCapture = async () => {
    try {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      recognitionRef.current = new SpeechRecognition();
      recognitionRef.current.continuous = true;
      recognitionRef.current.interimResults = true;
      recognitionRef.current.lang = 'en-US';

      // Request screen sharing with audio
      const stream = await navigator.mediaDevices.getDisplayMedia({
        video: true,
        audio: true
      });

      // Check if audio is included
      const audioTracks = stream.getAudioTracks();
      if (audioTracks.length === 0) {
        alert("No audio track found. Please make sure to select 'Share audio' when sharing the tab.");
        stream.getTracks().forEach(track => track.stop());
        return;
      }

      mediaStreamRef.current = stream;

      // Set up audio context
      if (!audioContextRef.current) {
        // eslint-disable-next-line
        audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();
      }

      // Set default speaker to Interviewer for tab audio
      setCurrentSpeaker('Interviewer');

      // Set up recognition handlers
      setupRecognitionHandlers();

      // Start recognition
      recognitionRef.current.start();
      setIsRecording(true);
      setIsCapturingTab(true);

      // Handle stream ending
      stream.getVideoTracks()[0].onended = () => {
        stopRecording();
      };

    } catch (error) {
      console.error("Error capturing tab audio:", error);
      alert("Failed to capture tab audio: " + error.message);
    }
  };

  // Stop recording
  const stopRecording = () => {
    if (recognitionRef.current) {
      recognitionRef.current.stop();
    }

    if (mediaStreamRef.current) {
      mediaStreamRef.current.getTracks().forEach(track => track.stop());
      mediaStreamRef.current = null;
    }

    setIsRecording(false);
    setIsCapturingTab(false);

    // Add any remaining interim transcript to the full transcript
    if (interimTranscript.trim()) {
      setFullTranscript(prev => [
        ...prev,
        {
          speaker: currentSpeaker,
          text: interimTranscript.trim(),
          timestamp: new Date()
        }
      ]);
      setInterimTranscript('');
    }
  };

  // Set up recognition event handlers
  const setupRecognitionHandlers = () => {
    if (!recognitionRef.current) return;

    recognitionRef.current.onstart = () => {
      setIsRecording(true);
    };

    recognitionRef.current.onresult = (event) => {
      let interim = '';
      let final = '';

      for (let i = event.resultIndex; i < event.results.length; ++i) {
        if (event.results[i].isFinal) {
          final += event.results[i][0].transcript;
        } else {
          interim += event.results[i][0].transcript;
        }
      }

      if (interim) setInterimTranscript(interim);

      if (final) {
        // Add to full transcript with current speaker
        setFullTranscript(prev => [
          ...prev,
          {
            speaker: currentSpeaker,
            text: final.trim(),
            timestamp: new Date()
          }
        ]);
        setInterimTranscript('');
      }
    };

    recognitionRef.current.onerror = (event) => {
      console.error("Recognition error:", event.error);
      if (event.error !== 'no-speech') {
        stopRecording();
      }
    };

    recognitionRef.current.onend = () => {
      setIsRecording(false);
      // Restart if we're still supposed to be recording
      if (isRecording) {
        try {
          recognitionRef.current.start();
        } catch (error) {
          console.error("Failed to restart recognition:", error);
        }
      }
    };
  };

  return (
    <div className="automatic-diarization">
      <div className="page-header">
        <h1>Automatic Speaker Diarization</h1>
        <p className="description">
          This page automatically identifies speakers based on audio source.
          Tab audio is identified as "Interviewer" and microphone as "Candidate".
        </p>
      </div>

      <Controls
        isRecording={isRecording}
        onStart={startRecording}
        onStop={stopRecording}
        onCaptureTab={startTabCapture}
      />

      <div className="status-indicator">
        {isRecording && (
          <div className="recording-status">
            <span className={`status-dot ${isCapturingTab ? 'interviewer' : 'candidate'}`}></span>
            {isCapturingTab
              ? 'Capturing Tab Audio (Interviewer)'
              : 'Recording Microphone (Candidate)'}
          </div>
        )}
      </div>

      <LiveCaptions
        interimText={interimTranscript}
        currentSpeaker={currentSpeaker}
      />

      <Transcript transcriptData={fullTranscript} />
    </div>
  );
}

export default AutomaticDiarization;
