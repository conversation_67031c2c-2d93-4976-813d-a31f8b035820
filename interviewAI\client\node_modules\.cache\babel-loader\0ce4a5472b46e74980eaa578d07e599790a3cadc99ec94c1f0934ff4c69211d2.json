{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\interview-ai-app\\\\interviewAI\\\\client\\\\src\\\\components\\\\DeepgramLiveTranscript.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState, useCallback } from 'react';\nimport MicRecorder from 'mic-recorder-to-mp3';\nimport './DeepgramLiveTranscript.css';\nimport env from '../utils/env';\nimport CloseIcon from '@mui/icons-material/Close';\nimport MicIcon from '@mui/icons-material/Mic';\nimport StopIcon from '@mui/icons-material/Stop';\nimport SendIcon from '@mui/icons-material/Send';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport AccessTimeIcon from '@mui/icons-material/AccessTime';\nimport Dialog from '@mui/material/Dialog';\nimport DialogActions from '@mui/material/DialogActions';\nimport DialogContent from '@mui/material/DialogContent';\nimport DialogContentText from '@mui/material/DialogContentText';\nimport DialogTitle from '@mui/material/DialogTitle';\nimport Button from '@mui/material/Button';\nimport CircularProgress from '@mui/material/CircularProgress';\nimport Alert from '@mui/material/Alert';\nimport Snackbar from '@mui/material/Snackbar';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DeepgramLiveTranscript = ({\n  onBack\n}) => {\n  _s();\n  // State for transcription\n  const [transcripts, setTranscripts] = useState([]);\n  const [isRecording, setIsRecording] = useState(false);\n  const [apiKeyStatus, setApiKeyStatus] = useState('unknown'); // 'unknown', 'valid', 'invalid'\n  const [apiKeyError, setApiKeyError] = useState('');\n  const [showApiKeyError, setShowApiKeyError] = useState(false);\n\n  // State for GPT integration\n  const [gptResponse, setGptResponse] = useState('');\n  const [isLoadingGpt, setIsLoadingGpt] = useState(false);\n  const [selectedText, setSelectedText] = useState('');\n\n  // Session timer state (15 minutes = 900 seconds)\n  const SESSION_DURATION = 900; // 15 minutes in seconds\n  const WARNING_TIME = 60; // Show warning when 60 seconds remain\n  const [sessionTimeRemaining, setSessionTimeRemaining] = useState(SESSION_DURATION);\n  const [showPaymentDialog, setShowPaymentDialog] = useState(false);\n  const [sessionExpired, setSessionExpired] = useState(false);\n\n  // Refs\n  const wsRef = useRef(null);\n  const recorder = useRef(new MicRecorder({\n    bitRate: 128\n  }));\n  const intervalRef = useRef(null);\n  const sessionTimerRef = useRef(null);\n  const transcriptContainerRef = useRef(null);\n\n  // Format session time remaining (MM:SS)\n  const formatSessionTime = useCallback(totalSeconds => {\n    const minutes = Math.floor(totalSeconds / 60);\n    const seconds = totalSeconds % 60;\n    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\n  }, []);\n\n  // Start the session timer\n  const startSessionTimer = useCallback(() => {\n    // Reset session timer when starting\n    setSessionTimeRemaining(SESSION_DURATION);\n    setSessionExpired(false);\n\n    // Clear any existing interval\n    if (sessionTimerRef.current) {\n      clearInterval(sessionTimerRef.current);\n    }\n\n    // Start a new interval that counts down\n    sessionTimerRef.current = setInterval(() => {\n      setSessionTimeRemaining(prev => {\n        const newTime = prev - 1;\n\n        // Show payment dialog when 1 minute remains\n        if (newTime === WARNING_TIME) {\n          setShowPaymentDialog(true);\n        }\n\n        // Session expired\n        if (newTime <= 0) {\n          clearInterval(sessionTimerRef.current);\n          sessionTimerRef.current = null;\n          setSessionExpired(true);\n          stopRecording();\n          return 0;\n        }\n        return newTime;\n      });\n    }, 1000);\n  }, [SESSION_DURATION, WARNING_TIME]);\n\n  // Handle payment dialog close\n  const handlePaymentDialogClose = useCallback(() => {\n    setShowPaymentDialog(false);\n  }, []);\n\n  // Handle payment confirmation\n  const handlePaymentConfirm = useCallback(() => {\n    // Reset the session timer\n    setShowPaymentDialog(false);\n    setSessionTimeRemaining(SESSION_DURATION);\n    setSessionExpired(false);\n\n    // Restart the session timer\n    if (sessionTimerRef.current) {\n      clearInterval(sessionTimerRef.current);\n    }\n    sessionTimerRef.current = setInterval(() => {\n      setSessionTimeRemaining(prev => {\n        const newTime = prev - 1;\n\n        // Show payment dialog when 1 minute remains\n        if (newTime === WARNING_TIME) {\n          setShowPaymentDialog(true);\n        }\n\n        // Session expired\n        if (newTime <= 0) {\n          clearInterval(sessionTimerRef.current);\n          sessionTimerRef.current = null;\n          setSessionExpired(true);\n          stopRecording();\n          return 0;\n        }\n        return newTime;\n      });\n    }, 1000);\n  }, [SESSION_DURATION, WARNING_TIME]);\n\n  // Start session timer on component mount\n  useEffect(() => {\n    startSessionTimer();\n\n    // Clean up session timer on unmount\n    return () => {\n      if (sessionTimerRef.current) {\n        clearInterval(sessionTimerRef.current);\n      }\n    };\n  }, [startSessionTimer]);\n\n  // Verify Deepgram API key\n  const verifyDeepgramApiKey = useCallback(async () => {\n    try {\n      // Get API key from environment variables\n      const DEEPGRAM_API_KEY = env.DEEPGRAM_API_KEY;\n      if (!DEEPGRAM_API_KEY) {\n        setApiKeyStatus('invalid');\n        setApiKeyError('Deepgram API key is missing. Please add it to your environment variables as REACT_APP_DEEPGRAM_API_KEY.');\n        setShowApiKeyError(true);\n        return false;\n      }\n\n      // Instead of making an HTTP request to verify the key (which can cause CORS issues),\n      // we'll attempt to establish a WebSocket connection which is what we'll use anyway\n      try {\n        // Create a test WebSocket connection\n        const testSocket = new WebSocket(`wss://api.deepgram.com/v1/listen?punctuate=true&diarize=true`);\n\n        // Create a promise that resolves when the connection is established or rejects on error\n        const connectionPromise = new Promise((resolve, reject) => {\n          // Set a timeout to avoid hanging if the connection takes too long\n          const timeout = setTimeout(() => {\n            testSocket.close();\n            reject(new Error('Connection timeout'));\n          }, 5000);\n          testSocket.onopen = () => {\n            // Send authorization header\n            testSocket.send(JSON.stringify({\n              authorization: DEEPGRAM_API_KEY\n            }));\n\n            // Wait a moment to see if we get an error response\n            setTimeout(() => {\n              clearTimeout(timeout);\n              testSocket.close();\n              resolve(true);\n            }, 1000);\n          };\n          testSocket.onerror = error => {\n            clearTimeout(timeout);\n            reject(error);\n          };\n          testSocket.onmessage = message => {\n            try {\n              const data = JSON.parse(message.data);\n              // If we get an error message, the key is invalid\n              if (data.error) {\n                clearTimeout(timeout);\n                testSocket.close();\n                reject(new Error(data.error));\n              }\n            } catch (e) {\n              // Ignore parsing errors\n            }\n          };\n        });\n\n        // Wait for the connection to be established\n        await connectionPromise;\n        setApiKeyStatus('valid');\n        return true;\n      } catch (error) {\n        console.error('WebSocket connection error:', error);\n        setApiKeyStatus('invalid');\n        setApiKeyError('Invalid Deepgram API key or connection issue. Please check your API key and internet connection.');\n        setShowApiKeyError(true);\n        return false;\n      }\n    } catch (error) {\n      console.error('Error verifying Deepgram API key:', error);\n      setApiKeyStatus('invalid');\n      setApiKeyError('Error verifying Deepgram API key: ' + error.message);\n      setShowApiKeyError(true);\n      return false;\n    }\n  }, []);\n\n  // Stop recording\n  const stopRecording = useCallback(() => {\n    setIsRecording(false);\n\n    // Stop the recorder\n    if (recorder.current) {\n      recorder.current.stop();\n    }\n\n    // Clear the interval\n    if (intervalRef.current) {\n      clearInterval(intervalRef.current);\n      intervalRef.current = null;\n    }\n\n    // Close the WebSocket\n    if (wsRef.current) {\n      wsRef.current.close();\n      wsRef.current = null;\n    }\n  }, []);\n\n  // Start recording\n  const startRecording = useCallback(async () => {\n    if (sessionExpired) return;\n\n    // Verify API key first\n    const isApiKeyValid = await verifyDeepgramApiKey();\n    if (!isApiKeyValid) return;\n    try {\n      const DEEPGRAM_API_KEY = env.DEEPGRAM_API_KEY;\n\n      // Create WebSocket connection\n      const socket = new WebSocket(`wss://api.deepgram.com/v1/listen?punctuate=true&diarize=true`);\n\n      // Set up authorization header\n      socket.onopen = async () => {\n        console.log('WebSocket connected');\n\n        // Send authorization header\n        socket.send(JSON.stringify({\n          authorization: DEEPGRAM_API_KEY\n        }));\n\n        // Start recording\n        await recorder.current.start();\n        setIsRecording(true);\n\n        // Send audio data to Deepgram\n        intervalRef.current = setInterval(async () => {\n          try {\n            const [_, blob] = await recorder.current.getMp3();\n            const reader = new FileReader();\n            reader.readAsArrayBuffer(blob);\n            reader.onloadend = () => {\n              if (socket.readyState === WebSocket.OPEN) {\n                socket.send(reader.result);\n              }\n            };\n          } catch (error) {\n            console.error('Error getting MP3 data:', error);\n          }\n        }, 1000);\n        wsRef.current = socket;\n      };\n\n      // Handle incoming messages\n      socket.onmessage = message => {\n        try {\n          var _data$channel, _data$channel$alterna;\n          const data = JSON.parse(message.data);\n          const words = ((_data$channel = data.channel) === null || _data$channel === void 0 ? void 0 : (_data$channel$alterna = _data$channel.alternatives[0]) === null || _data$channel$alterna === void 0 ? void 0 : _data$channel$alterna.words) || [];\n          if (words.length === 0) return;\n          const groupedBySpeaker = words.reduce((acc, word) => {\n            if (!word.speaker) return acc;\n            const lastGroup = acc[acc.length - 1];\n            if (lastGroup && lastGroup.speaker === word.speaker) {\n              lastGroup.words.push(word.word);\n            } else {\n              acc.push({\n                speaker: word.speaker,\n                words: [word.word],\n                timestamp: new Date().toISOString()\n              });\n            }\n            return acc;\n          }, []);\n          if (groupedBySpeaker.length > 0) {\n            setTranscripts(prev => {\n              // Merge with existing transcripts if the speaker is the same\n              const newTranscripts = [...prev];\n              groupedBySpeaker.forEach(group => {\n                const lastGroup = newTranscripts[newTranscripts.length - 1];\n                if (lastGroup && lastGroup.speaker === group.speaker) {\n                  // Merge with the last group\n                  lastGroup.words = [...lastGroup.words, ...group.words];\n                } else {\n                  // Add as a new group\n                  newTranscripts.push(group);\n                }\n              });\n              return newTranscripts;\n            });\n\n            // Auto-scroll to the bottom\n            if (transcriptContainerRef.current) {\n              transcriptContainerRef.current.scrollTop = transcriptContainerRef.current.scrollHeight;\n            }\n          }\n        } catch (error) {\n          console.error('Error parsing message:', error);\n        }\n      };\n\n      // Handle errors\n      socket.onerror = error => {\n        console.error('WebSocket error:', error);\n        setApiKeyError('WebSocket error: ' + error.message);\n        setShowApiKeyError(true);\n      };\n\n      // Handle connection close\n      socket.onclose = () => {\n        console.log('WebSocket closed');\n        stopRecording();\n      };\n    } catch (error) {\n      console.error('Error starting recording:', error);\n      setApiKeyError('Error starting recording: ' + error.message);\n      setShowApiKeyError(true);\n    }\n  }, [sessionExpired, verifyDeepgramApiKey, stopRecording]);\n\n  // Clean up on unmount\n  useEffect(() => {\n    return () => {\n      stopRecording();\n      if (sessionTimerRef.current) {\n        clearInterval(sessionTimerRef.current);\n      }\n    };\n  }, [stopRecording]);\n\n  // Handle text selection for GPT\n  const handleTextSelection = useCallback(() => {\n    const selection = window.getSelection();\n    const selectedText = selection.toString().trim();\n    if (selectedText) {\n      setSelectedText(selectedText);\n    }\n  }, []);\n\n  // Send selected text to GPT\n  const sendToGPT = useCallback(async () => {\n    if (!selectedText || isLoadingGpt || sessionExpired) return;\n    const apiKey = env.OPENAI_API_KEY;\n    if (!apiKey) {\n      setApiKeyError('OpenAI API key is missing. Please add it to your environment variables as REACT_APP_OPENAI_API_KEY.');\n      setShowApiKeyError(true);\n      return;\n    }\n    setIsLoadingGpt(true);\n    try {\n      const response = await fetch(\"https://api.openai.com/v1/chat/completions\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          \"Authorization\": `Bearer ${apiKey}`\n        },\n        body: JSON.stringify({\n          model: \"gpt-4.1-nano\",\n          messages: [{\n            role: \"system\",\n            content: \"You are an AI assistant helping with interview transcripts. Provide concise, helpful responses.\"\n          }, {\n            role: \"user\",\n            content: `Please analyze this interview transcript and provide insights or suggestions: \"${selectedText}\"`\n          }],\n          stream: true\n        })\n      });\n      if (!response.ok) {\n        throw new Error(`API error: ${response.status} ${response.statusText}`);\n      }\n      const reader = response.body.getReader();\n      const decoder = new TextDecoder(\"utf-8\");\n      let result = \"\";\n      while (true) {\n        const {\n          value,\n          done\n        } = await reader.read();\n        if (done) break;\n        const chunk = decoder.decode(value, {\n          stream: true\n        });\n        const lines = chunk.split(\"\\n\").filter(line => line.trim().startsWith(\"data:\"));\n        for (const line of lines) {\n          const data = line.replace(/^data: /, '');\n          if (data === \"[DONE]\") break;\n          try {\n            var _json$choices, _json$choices$, _json$choices$$delta;\n            const json = JSON.parse(data);\n            const content = (_json$choices = json.choices) === null || _json$choices === void 0 ? void 0 : (_json$choices$ = _json$choices[0]) === null || _json$choices$ === void 0 ? void 0 : (_json$choices$$delta = _json$choices$.delta) === null || _json$choices$$delta === void 0 ? void 0 : _json$choices$$delta.content;\n            if (content) {\n              result += content;\n              setGptResponse(result);\n            }\n          } catch (e) {\n            console.error(\"Error parsing JSON:\", e);\n          }\n        }\n      }\n    } catch (error) {\n      console.error(\"GPT API Error:\", error);\n      setGptResponse(\"Error: \" + error.message);\n    } finally {\n      setIsLoadingGpt(false);\n    }\n  }, [selectedText, isLoadingGpt, sessionExpired]);\n\n  // Clear transcripts\n  const clearTranscripts = useCallback(() => {\n    setTranscripts([]);\n    setGptResponse('');\n    setSelectedText('');\n  }, []);\n\n  // Clear GPT response\n  const clearGptResponse = useCallback(() => {\n    setGptResponse('');\n    setSelectedText('');\n  }, []);\n\n  // Handle exit button click\n  const handleExit = useCallback(() => {\n    stopRecording();\n    if (onBack) onBack();\n  }, [stopRecording, onBack]);\n\n  // Handle API key error close\n  const handleApiKeyErrorClose = useCallback(() => {\n    setShowApiKeyError(false);\n  }, []);\n\n  // Get full transcript text\n  const getFullTranscript = useCallback(() => {\n    return transcripts.map(segment => `Speaker ${segment.speaker}: ${segment.words.join(' ')}`).join('\\n');\n  }, [transcripts]);\n\n  // Download transcript\n  const downloadTranscript = useCallback(() => {\n    const fullText = getFullTranscript();\n    if (!fullText) return;\n    const blob = new Blob([fullText], {\n      type: 'text/plain'\n    });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `transcript-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  }, [getFullTranscript]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"deepgram-transcript-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"exit-button\",\n      onClick: handleExit,\n      title: \"Exit\",\n      children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 516,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 515,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"timer-container\",\n      children: [/*#__PURE__*/_jsxDEV(AccessTimeIcon, {\n        className: \"timer-icon\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 521,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"timer-display session-timer\",\n        children: [\"Session: \", formatSessionTime(sessionTimeRemaining)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 522,\n        columnNumber: 9\n      }, this), isRecording && /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"listening-indicator\",\n        children: \"Recording...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 525,\n        columnNumber: 25\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 520,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"deepgram-transcript-title\",\n      children: \"Live Transcript with Speaker Diarization\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 528,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"deepgram-controls\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: `record-button ${isRecording ? 'recording' : ''}`,\n        onClick: isRecording ? stopRecording : startRecording,\n        disabled: sessionExpired || apiKeyStatus === 'invalid',\n        title: isRecording ? \"Stop Recording\" : \"Start Recording\",\n        children: [isRecording ? /*#__PURE__*/_jsxDEV(StopIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 537,\n          columnNumber: 26\n        }, this) : /*#__PURE__*/_jsxDEV(MicIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 537,\n          columnNumber: 41\n        }, this), isRecording ? \"Stop Recording\" : \"Start Recording\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 531,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"clear-button\",\n        onClick: clearTranscripts,\n        disabled: transcripts.length === 0 || sessionExpired,\n        title: \"Clear Transcripts\",\n        children: [/*#__PURE__*/_jsxDEV(DeleteIcon, {\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 547,\n          columnNumber: 11\n        }, this), \"Clear\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 541,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"download-button\",\n        onClick: downloadTranscript,\n        disabled: transcripts.length === 0 || sessionExpired,\n        title: \"Download Transcript\",\n        children: \"Download\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 551,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 530,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"deepgram-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"transcript-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"transcript-container\",\n          ref: transcriptContainerRef,\n          onMouseUp: handleTextSelection,\n          children: transcripts.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"empty-transcript\",\n            children: apiKeyStatus === 'invalid' ? /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"API key validation failed. Please check your Deepgram API key.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 571,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"No transcripts yet. Click \\\"Start Recording\\\" to begin.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 573,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 569,\n            columnNumber: 15\n          }, this) : transcripts.map((segment, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"deepgram-segment\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"deepgram-speaker\",\n              children: [\"Speaker \", segment.speaker, \":\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 579,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: segment.words.join(' ')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 580,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 578,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 563,\n          columnNumber: 11\n        }, this), selectedText && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"selected-text-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"selected-text-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Selected Text\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 589,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"send-to-gpt-button\",\n              onClick: sendToGPT,\n              disabled: !selectedText || isLoadingGpt || sessionExpired,\n              title: \"Analyze with GPT\",\n              children: [isLoadingGpt ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 596,\n                columnNumber: 35\n              }, this) : /*#__PURE__*/_jsxDEV(SendIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 596,\n                columnNumber: 68\n              }, this), \"Analyze with GPT\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 590,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 588,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"selected-text\",\n            children: selectedText\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 600,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 587,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 562,\n        columnNumber: 9\n      }, this), (gptResponse || isLoadingGpt) && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"gpt-response-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"gpt-response-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"AI Analysis\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 608,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"clear-button\",\n            onClick: clearGptResponse,\n            disabled: !gptResponse && !isLoadingGpt,\n            title: \"Clear Analysis\",\n            children: [/*#__PURE__*/_jsxDEV(DeleteIcon, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 615,\n              columnNumber: 17\n            }, this), \"Clear\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 609,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 607,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"gpt-response\",\n          children: isLoadingGpt && !gptResponse ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"loading-container\",\n            children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 30\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 622,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Analyzing transcript...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 623,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 621,\n            columnNumber: 17\n          }, this) : gptResponse\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 619,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 606,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 561,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showPaymentDialog,\n      onClose: handlePaymentDialogClose,\n      \"aria-labelledby\": \"payment-dialog-title\",\n      \"aria-describedby\": \"payment-dialog-description\",\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        id: \"payment-dialog-title\",\n        children: \"Session Expiring Soon\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 640,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(DialogContentText, {\n          id: \"payment-dialog-description\",\n          children: \"Your session will expire in one minute. Would you like to make a payment to extend your session?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 644,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 643,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handlePaymentDialogClose,\n          color: \"primary\",\n          children: \"Not Now\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 649,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handlePaymentConfirm,\n          color: \"primary\",\n          autoFocus: true,\n          children: \"Make Payment\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 652,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 648,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 634,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: sessionExpired,\n      \"aria-labelledby\": \"expired-dialog-title\",\n      \"aria-describedby\": \"expired-dialog-description\",\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        id: \"expired-dialog-title\",\n        children: \"Session Expired\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 664,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(DialogContentText, {\n          id: \"expired-dialog-description\",\n          children: \"Your session has expired. Please make a payment to continue using the service.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 668,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 667,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleExit,\n          color: \"primary\",\n          children: \"Exit\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 673,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handlePaymentConfirm,\n          color: \"primary\",\n          autoFocus: true,\n          children: \"Make Payment\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 676,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 672,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 659,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: showApiKeyError,\n      autoHideDuration: 6000,\n      onClose: handleApiKeyErrorClose,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleApiKeyErrorClose,\n        severity: \"error\",\n        sx: {\n          width: '100%'\n        },\n        children: apiKeyError\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 689,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 683,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 513,\n    columnNumber: 5\n  }, this);\n};\n_s(DeepgramLiveTranscript, \"ApNKvE0l7lbSWi06FiH2DvNs768=\");\n_c = DeepgramLiveTranscript;\nexport default DeepgramLiveTranscript;\nvar _c;\n$RefreshReg$(_c, \"DeepgramLiveTranscript\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "useCallback", "MicRecorder", "env", "CloseIcon", "MicIcon", "StopIcon", "SendIcon", "DeleteIcon", "AccessTimeIcon", "Dialog", "DialogActions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogContentText", "DialogTitle", "<PERSON><PERSON>", "CircularProgress", "<PERSON><PERSON>", "Snackbar", "jsxDEV", "_jsxDEV", "DeepgramLiveTranscript", "onBack", "_s", "transcripts", "setTranscripts", "isRecording", "setIsRecording", "apiKeyStatus", "setApiKeyStatus", "apiKeyError", "setApi<PERSON>eyError", "showApiKeyError", "setShowApiKeyError", "gptResponse", "setGptResponse", "isLoadingGpt", "setIsLoadingGpt", "selectedText", "setSelectedText", "SESSION_DURATION", "WARNING_TIME", "sessionTimeRemaining", "setSessionTimeRemaining", "showPaymentDialog", "setShowPaymentDialog", "sessionExpired", "setSessionExpired", "wsRef", "recorder", "bitRate", "intervalRef", "sessionTimerRef", "transcriptContainerRef", "formatSessionTime", "totalSeconds", "minutes", "Math", "floor", "seconds", "toString", "padStart", "startSessionTimer", "current", "clearInterval", "setInterval", "prev", "newTime", "stopRecording", "handlePaymentDialogClose", "handlePaymentConfirm", "verifyDeepgramApiKey", "DEEPGRAM_API_KEY", "testSocket", "WebSocket", "connectionPromise", "Promise", "resolve", "reject", "timeout", "setTimeout", "close", "Error", "onopen", "send", "JSON", "stringify", "authorization", "clearTimeout", "onerror", "error", "onmessage", "message", "data", "parse", "e", "console", "stop", "startRecording", "isApiKey<PERSON>", "socket", "log", "start", "_", "blob", "getMp3", "reader", "FileReader", "readAsA<PERSON>y<PERSON><PERSON>er", "onloadend", "readyState", "OPEN", "result", "_data$channel", "_data$channel$alterna", "words", "channel", "alternatives", "length", "groupedBySpeaker", "reduce", "acc", "word", "speaker", "lastGroup", "push", "timestamp", "Date", "toISOString", "newTranscripts", "for<PERSON>ach", "group", "scrollTop", "scrollHeight", "onclose", "handleTextSelection", "selection", "window", "getSelection", "trim", "sendToGPT", "<PERSON><PERSON><PERSON><PERSON>", "OPENAI_API_KEY", "response", "fetch", "method", "headers", "body", "model", "messages", "role", "content", "stream", "ok", "status", "statusText", "<PERSON><PERSON><PERSON><PERSON>", "decoder", "TextDecoder", "value", "done", "read", "chunk", "decode", "lines", "split", "filter", "line", "startsWith", "replace", "_json$choices", "_json$choices$", "_json$choices$$delta", "json", "choices", "delta", "clearTranscripts", "clearGptResponse", "handleExit", "handleApiKeyErrorClose", "getFullTranscript", "map", "segment", "join", "downloadTranscript", "fullText", "Blob", "type", "url", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "slice", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "className", "children", "onClick", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "disabled", "fontSize", "ref", "onMouseUp", "index", "size", "open", "onClose", "id", "color", "autoFocus", "autoHideDuration", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "severity", "sx", "width", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/interview-ai-app/interviewAI/client/src/components/DeepgramLiveTranscript.jsx"], "sourcesContent": ["import React, { useEffect, useRef, useState, useCallback } from 'react';\nimport MicRecorder from 'mic-recorder-to-mp3';\nimport './DeepgramLiveTranscript.css';\nimport env from '../utils/env';\nimport CloseIcon from '@mui/icons-material/Close';\nimport MicIcon from '@mui/icons-material/Mic';\nimport StopIcon from '@mui/icons-material/Stop';\nimport SendIcon from '@mui/icons-material/Send';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport AccessTimeIcon from '@mui/icons-material/AccessTime';\nimport Dialog from '@mui/material/Dialog';\nimport DialogActions from '@mui/material/DialogActions';\nimport DialogContent from '@mui/material/DialogContent';\nimport DialogContentText from '@mui/material/DialogContentText';\nimport DialogTitle from '@mui/material/DialogTitle';\nimport Button from '@mui/material/Button';\nimport CircularProgress from '@mui/material/CircularProgress';\nimport Alert from '@mui/material/Alert';\nimport Snackbar from '@mui/material/Snackbar';\n\nconst DeepgramLiveTranscript = ({ onBack }) => {\n  // State for transcription\n  const [transcripts, setTranscripts] = useState([]);\n  const [isRecording, setIsRecording] = useState(false);\n  const [apiKeyStatus, setApiKeyStatus] = useState('unknown'); // 'unknown', 'valid', 'invalid'\n  const [apiKeyError, setApiKeyError] = useState('');\n  const [showApiKeyError, setShowApiKeyError] = useState(false);\n\n  // State for GPT integration\n  const [gptResponse, setGptResponse] = useState('');\n  const [isLoadingGpt, setIsLoadingGpt] = useState(false);\n  const [selectedText, setSelectedText] = useState('');\n\n  // Session timer state (15 minutes = 900 seconds)\n  const SESSION_DURATION = 900; // 15 minutes in seconds\n  const WARNING_TIME = 60; // Show warning when 60 seconds remain\n  const [sessionTimeRemaining, setSessionTimeRemaining] = useState(SESSION_DURATION);\n  const [showPaymentDialog, setShowPaymentDialog] = useState(false);\n  const [sessionExpired, setSessionExpired] = useState(false);\n\n  // Refs\n  const wsRef = useRef(null);\n  const recorder = useRef(new MicRecorder({ bitRate: 128 }));\n  const intervalRef = useRef(null);\n  const sessionTimerRef = useRef(null);\n  const transcriptContainerRef = useRef(null);\n\n  // Format session time remaining (MM:SS)\n  const formatSessionTime = useCallback((totalSeconds) => {\n    const minutes = Math.floor(totalSeconds / 60);\n    const seconds = totalSeconds % 60;\n    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\n  }, []);\n\n  // Start the session timer\n  const startSessionTimer = useCallback(() => {\n    // Reset session timer when starting\n    setSessionTimeRemaining(SESSION_DURATION);\n    setSessionExpired(false);\n\n    // Clear any existing interval\n    if (sessionTimerRef.current) {\n      clearInterval(sessionTimerRef.current);\n    }\n\n    // Start a new interval that counts down\n    sessionTimerRef.current = setInterval(() => {\n      setSessionTimeRemaining(prev => {\n        const newTime = prev - 1;\n\n        // Show payment dialog when 1 minute remains\n        if (newTime === WARNING_TIME) {\n          setShowPaymentDialog(true);\n        }\n\n        // Session expired\n        if (newTime <= 0) {\n          clearInterval(sessionTimerRef.current);\n          sessionTimerRef.current = null;\n          setSessionExpired(true);\n          stopRecording();\n          return 0;\n        }\n\n        return newTime;\n      });\n    }, 1000);\n  }, [SESSION_DURATION, WARNING_TIME]);\n\n  // Handle payment dialog close\n  const handlePaymentDialogClose = useCallback(() => {\n    setShowPaymentDialog(false);\n  }, []);\n\n  // Handle payment confirmation\n  const handlePaymentConfirm = useCallback(() => {\n    // Reset the session timer\n    setShowPaymentDialog(false);\n    setSessionTimeRemaining(SESSION_DURATION);\n    setSessionExpired(false);\n\n    // Restart the session timer\n    if (sessionTimerRef.current) {\n      clearInterval(sessionTimerRef.current);\n    }\n\n    sessionTimerRef.current = setInterval(() => {\n      setSessionTimeRemaining(prev => {\n        const newTime = prev - 1;\n\n        // Show payment dialog when 1 minute remains\n        if (newTime === WARNING_TIME) {\n          setShowPaymentDialog(true);\n        }\n\n        // Session expired\n        if (newTime <= 0) {\n          clearInterval(sessionTimerRef.current);\n          sessionTimerRef.current = null;\n          setSessionExpired(true);\n          stopRecording();\n          return 0;\n        }\n\n        return newTime;\n      });\n    }, 1000);\n  }, [SESSION_DURATION, WARNING_TIME]);\n\n  // Start session timer on component mount\n  useEffect(() => {\n    startSessionTimer();\n\n    // Clean up session timer on unmount\n    return () => {\n      if (sessionTimerRef.current) {\n        clearInterval(sessionTimerRef.current);\n      }\n    };\n  }, [startSessionTimer]);\n\n  // Verify Deepgram API key\n  const verifyDeepgramApiKey = useCallback(async () => {\n    try {\n      // Get API key from environment variables\n      const DEEPGRAM_API_KEY = env.DEEPGRAM_API_KEY;\n\n      if (!DEEPGRAM_API_KEY) {\n        setApiKeyStatus('invalid');\n        setApiKeyError('Deepgram API key is missing. Please add it to your environment variables as REACT_APP_DEEPGRAM_API_KEY.');\n        setShowApiKeyError(true);\n        return false;\n      }\n\n      // Instead of making an HTTP request to verify the key (which can cause CORS issues),\n      // we'll attempt to establish a WebSocket connection which is what we'll use anyway\n      try {\n        // Create a test WebSocket connection\n        const testSocket = new WebSocket(`wss://api.deepgram.com/v1/listen?punctuate=true&diarize=true`);\n\n        // Create a promise that resolves when the connection is established or rejects on error\n        const connectionPromise = new Promise((resolve, reject) => {\n          // Set a timeout to avoid hanging if the connection takes too long\n          const timeout = setTimeout(() => {\n            testSocket.close();\n            reject(new Error('Connection timeout'));\n          }, 5000);\n\n          testSocket.onopen = () => {\n            // Send authorization header\n            testSocket.send(JSON.stringify({\n              authorization: DEEPGRAM_API_KEY\n            }));\n\n            // Wait a moment to see if we get an error response\n            setTimeout(() => {\n              clearTimeout(timeout);\n              testSocket.close();\n              resolve(true);\n            }, 1000);\n          };\n\n          testSocket.onerror = (error) => {\n            clearTimeout(timeout);\n            reject(error);\n          };\n\n          testSocket.onmessage = (message) => {\n            try {\n              const data = JSON.parse(message.data);\n              // If we get an error message, the key is invalid\n              if (data.error) {\n                clearTimeout(timeout);\n                testSocket.close();\n                reject(new Error(data.error));\n              }\n            } catch (e) {\n              // Ignore parsing errors\n            }\n          };\n        });\n\n        // Wait for the connection to be established\n        await connectionPromise;\n\n        setApiKeyStatus('valid');\n        return true;\n      } catch (error) {\n        console.error('WebSocket connection error:', error);\n        setApiKeyStatus('invalid');\n        setApiKeyError('Invalid Deepgram API key or connection issue. Please check your API key and internet connection.');\n        setShowApiKeyError(true);\n        return false;\n      }\n    } catch (error) {\n      console.error('Error verifying Deepgram API key:', error);\n      setApiKeyStatus('invalid');\n      setApiKeyError('Error verifying Deepgram API key: ' + error.message);\n      setShowApiKeyError(true);\n      return false;\n    }\n  }, []);\n\n  // Stop recording\n  const stopRecording = useCallback(() => {\n    setIsRecording(false);\n\n    // Stop the recorder\n    if (recorder.current) {\n      recorder.current.stop();\n    }\n\n    // Clear the interval\n    if (intervalRef.current) {\n      clearInterval(intervalRef.current);\n      intervalRef.current = null;\n    }\n\n    // Close the WebSocket\n    if (wsRef.current) {\n      wsRef.current.close();\n      wsRef.current = null;\n    }\n  }, []);\n\n  // Start recording\n  const startRecording = useCallback(async () => {\n    if (sessionExpired) return;\n\n    // Verify API key first\n    const isApiKeyValid = await verifyDeepgramApiKey();\n    if (!isApiKeyValid) return;\n\n    try {\n      const DEEPGRAM_API_KEY = env.DEEPGRAM_API_KEY;\n\n      // Create WebSocket connection\n      const socket = new WebSocket(`wss://api.deepgram.com/v1/listen?punctuate=true&diarize=true`);\n\n      // Set up authorization header\n      socket.onopen = async () => {\n        console.log('WebSocket connected');\n\n        // Send authorization header\n        socket.send(JSON.stringify({\n          authorization: DEEPGRAM_API_KEY\n        }));\n\n        // Start recording\n        await recorder.current.start();\n        setIsRecording(true);\n\n        // Send audio data to Deepgram\n        intervalRef.current = setInterval(async () => {\n          try {\n            const [_, blob] = await recorder.current.getMp3();\n            const reader = new FileReader();\n            reader.readAsArrayBuffer(blob);\n            reader.onloadend = () => {\n              if (socket.readyState === WebSocket.OPEN) {\n                socket.send(reader.result);\n              }\n            };\n          } catch (error) {\n            console.error('Error getting MP3 data:', error);\n          }\n        }, 1000);\n\n        wsRef.current = socket;\n      };\n\n      // Handle incoming messages\n      socket.onmessage = (message) => {\n        try {\n          const data = JSON.parse(message.data);\n          const words = data.channel?.alternatives[0]?.words || [];\n\n          if (words.length === 0) return;\n\n          const groupedBySpeaker = words.reduce((acc, word) => {\n            if (!word.speaker) return acc;\n            const lastGroup = acc[acc.length - 1];\n\n            if (lastGroup && lastGroup.speaker === word.speaker) {\n              lastGroup.words.push(word.word);\n            } else {\n              acc.push({\n                speaker: word.speaker,\n                words: [word.word],\n                timestamp: new Date().toISOString()\n              });\n            }\n\n            return acc;\n          }, []);\n\n          if (groupedBySpeaker.length > 0) {\n            setTranscripts(prev => {\n              // Merge with existing transcripts if the speaker is the same\n              const newTranscripts = [...prev];\n\n              groupedBySpeaker.forEach(group => {\n                const lastGroup = newTranscripts[newTranscripts.length - 1];\n\n                if (lastGroup && lastGroup.speaker === group.speaker) {\n                  // Merge with the last group\n                  lastGroup.words = [...lastGroup.words, ...group.words];\n                } else {\n                  // Add as a new group\n                  newTranscripts.push(group);\n                }\n              });\n\n              return newTranscripts;\n            });\n\n            // Auto-scroll to the bottom\n            if (transcriptContainerRef.current) {\n              transcriptContainerRef.current.scrollTop = transcriptContainerRef.current.scrollHeight;\n            }\n          }\n        } catch (error) {\n          console.error('Error parsing message:', error);\n        }\n      };\n\n      // Handle errors\n      socket.onerror = (error) => {\n        console.error('WebSocket error:', error);\n        setApiKeyError('WebSocket error: ' + error.message);\n        setShowApiKeyError(true);\n      };\n\n      // Handle connection close\n      socket.onclose = () => {\n        console.log('WebSocket closed');\n        stopRecording();\n      };\n    } catch (error) {\n      console.error('Error starting recording:', error);\n      setApiKeyError('Error starting recording: ' + error.message);\n      setShowApiKeyError(true);\n    }\n  }, [sessionExpired, verifyDeepgramApiKey, stopRecording]);\n\n\n\n  // Clean up on unmount\n  useEffect(() => {\n    return () => {\n      stopRecording();\n\n      if (sessionTimerRef.current) {\n        clearInterval(sessionTimerRef.current);\n      }\n    };\n  }, [stopRecording]);\n\n  // Handle text selection for GPT\n  const handleTextSelection = useCallback(() => {\n    const selection = window.getSelection();\n    const selectedText = selection.toString().trim();\n\n    if (selectedText) {\n      setSelectedText(selectedText);\n    }\n  }, []);\n\n  // Send selected text to GPT\n  const sendToGPT = useCallback(async () => {\n    if (!selectedText || isLoadingGpt || sessionExpired) return;\n\n    const apiKey = env.OPENAI_API_KEY;\n\n    if (!apiKey) {\n      setApiKeyError('OpenAI API key is missing. Please add it to your environment variables as REACT_APP_OPENAI_API_KEY.');\n      setShowApiKeyError(true);\n      return;\n    }\n\n    setIsLoadingGpt(true);\n\n    try {\n      const response = await fetch(\"https://api.openai.com/v1/chat/completions\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          \"Authorization\": `Bearer ${apiKey}`\n        },\n        body: JSON.stringify({\n          model: \"gpt-4.1-nano\",\n          messages: [\n            {\n              role: \"system\",\n              content: \"You are an AI assistant helping with interview transcripts. Provide concise, helpful responses.\"\n            },\n            {\n              role: \"user\",\n              content: `Please analyze this interview transcript and provide insights or suggestions: \"${selectedText}\"`\n            }\n          ],\n          stream: true\n        })\n      });\n\n      if (!response.ok) {\n        throw new Error(`API error: ${response.status} ${response.statusText}`);\n      }\n\n      const reader = response.body.getReader();\n      const decoder = new TextDecoder(\"utf-8\");\n      let result = \"\";\n\n      while (true) {\n        const { value, done } = await reader.read();\n        if (done) break;\n\n        const chunk = decoder.decode(value, { stream: true });\n        const lines = chunk.split(\"\\n\").filter(line => line.trim().startsWith(\"data:\"));\n\n        for (const line of lines) {\n          const data = line.replace(/^data: /, '');\n          if (data === \"[DONE]\") break;\n\n          try {\n            const json = JSON.parse(data);\n            const content = json.choices?.[0]?.delta?.content;\n            if (content) {\n              result += content;\n              setGptResponse(result);\n            }\n          } catch (e) {\n            console.error(\"Error parsing JSON:\", e);\n          }\n        }\n      }\n    } catch (error) {\n      console.error(\"GPT API Error:\", error);\n      setGptResponse(\"Error: \" + error.message);\n    } finally {\n      setIsLoadingGpt(false);\n    }\n  }, [selectedText, isLoadingGpt, sessionExpired]);\n\n  // Clear transcripts\n  const clearTranscripts = useCallback(() => {\n    setTranscripts([]);\n    setGptResponse('');\n    setSelectedText('');\n  }, []);\n\n  // Clear GPT response\n  const clearGptResponse = useCallback(() => {\n    setGptResponse('');\n    setSelectedText('');\n  }, []);\n\n  // Handle exit button click\n  const handleExit = useCallback(() => {\n    stopRecording();\n    if (onBack) onBack();\n  }, [stopRecording, onBack]);\n\n  // Handle API key error close\n  const handleApiKeyErrorClose = useCallback(() => {\n    setShowApiKeyError(false);\n  }, []);\n\n  // Get full transcript text\n  const getFullTranscript = useCallback(() => {\n    return transcripts.map(segment =>\n      `Speaker ${segment.speaker}: ${segment.words.join(' ')}`\n    ).join('\\n');\n  }, [transcripts]);\n\n  // Download transcript\n  const downloadTranscript = useCallback(() => {\n    const fullText = getFullTranscript();\n    if (!fullText) return;\n\n    const blob = new Blob([fullText], { type: 'text/plain' });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `transcript-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  }, [getFullTranscript]);\n\n  return (\n    <div className=\"deepgram-transcript-container\">\n      {/* Exit button in top right corner */}\n      <button className=\"exit-button\" onClick={handleExit} title=\"Exit\">\n        <CloseIcon />\n      </button>\n\n      {/* Session timer display at the top */}\n      <div className=\"timer-container\">\n        <AccessTimeIcon className=\"timer-icon\" />\n        <span className=\"timer-display session-timer\">\n          Session: {formatSessionTime(sessionTimeRemaining)}\n        </span>\n        {isRecording && <span className=\"listening-indicator\">Recording...</span>}\n      </div>\n\n      <h2 className=\"deepgram-transcript-title\">Live Transcript with Speaker Diarization</h2>\n\n      <div className=\"deepgram-controls\">\n        <button\n          className={`record-button ${isRecording ? 'recording' : ''}`}\n          onClick={isRecording ? stopRecording : startRecording}\n          disabled={sessionExpired || apiKeyStatus === 'invalid'}\n          title={isRecording ? \"Stop Recording\" : \"Start Recording\"}\n        >\n          {isRecording ? <StopIcon /> : <MicIcon />}\n          {isRecording ? \"Stop Recording\" : \"Start Recording\"}\n        </button>\n\n        <button\n          className=\"clear-button\"\n          onClick={clearTranscripts}\n          disabled={transcripts.length === 0 || sessionExpired}\n          title=\"Clear Transcripts\"\n        >\n          <DeleteIcon fontSize=\"small\" />\n          Clear\n        </button>\n\n        <button\n          className=\"download-button\"\n          onClick={downloadTranscript}\n          disabled={transcripts.length === 0 || sessionExpired}\n          title=\"Download Transcript\"\n        >\n          Download\n        </button>\n      </div>\n\n      <div className=\"deepgram-content\">\n        <div className=\"transcript-section\">\n          <div\n            className=\"transcript-container\"\n            ref={transcriptContainerRef}\n            onMouseUp={handleTextSelection}\n          >\n            {transcripts.length === 0 ? (\n              <div className=\"empty-transcript\">\n                {apiKeyStatus === 'invalid' ? (\n                  <p>API key validation failed. Please check your Deepgram API key.</p>\n                ) : (\n                  <p>No transcripts yet. Click \"Start Recording\" to begin.</p>\n                )}\n              </div>\n            ) : (\n              transcripts.map((segment, index) => (\n                <div className=\"deepgram-segment\" key={index}>\n                  <span className=\"deepgram-speaker\">Speaker {segment.speaker}:</span>\n                  <span>{segment.words.join(' ')}</span>\n                </div>\n              ))\n            )}\n          </div>\n\n          {selectedText && (\n            <div className=\"selected-text-container\">\n              <div className=\"selected-text-header\">\n                <h3>Selected Text</h3>\n                <button\n                  className=\"send-to-gpt-button\"\n                  onClick={sendToGPT}\n                  disabled={!selectedText || isLoadingGpt || sessionExpired}\n                  title=\"Analyze with GPT\"\n                >\n                  {isLoadingGpt ? <CircularProgress size={20} /> : <SendIcon />}\n                  Analyze with GPT\n                </button>\n              </div>\n              <div className=\"selected-text\">{selectedText}</div>\n            </div>\n          )}\n        </div>\n\n        {(gptResponse || isLoadingGpt) && (\n          <div className=\"gpt-response-section\">\n            <div className=\"gpt-response-header\">\n              <h3>AI Analysis</h3>\n              <button\n                className=\"clear-button\"\n                onClick={clearGptResponse}\n                disabled={!gptResponse && !isLoadingGpt}\n                title=\"Clear Analysis\"\n              >\n                <DeleteIcon fontSize=\"small\" />\n                Clear\n              </button>\n            </div>\n            <div className=\"gpt-response\">\n              {isLoadingGpt && !gptResponse ? (\n                <div className=\"loading-container\">\n                  <CircularProgress size={30} />\n                  <p>Analyzing transcript...</p>\n                </div>\n              ) : (\n                gptResponse\n              )}\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Payment Dialog */}\n      <Dialog\n        open={showPaymentDialog}\n        onClose={handlePaymentDialogClose}\n        aria-labelledby=\"payment-dialog-title\"\n        aria-describedby=\"payment-dialog-description\"\n      >\n        <DialogTitle id=\"payment-dialog-title\">\n          {\"Session Expiring Soon\"}\n        </DialogTitle>\n        <DialogContent>\n          <DialogContentText id=\"payment-dialog-description\">\n            Your session will expire in one minute. Would you like to make a payment to extend your session?\n          </DialogContentText>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={handlePaymentDialogClose} color=\"primary\">\n            Not Now\n          </Button>\n          <Button onClick={handlePaymentConfirm} color=\"primary\" autoFocus>\n            Make Payment\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Session Expired Dialog */}\n      <Dialog\n        open={sessionExpired}\n        aria-labelledby=\"expired-dialog-title\"\n        aria-describedby=\"expired-dialog-description\"\n      >\n        <DialogTitle id=\"expired-dialog-title\">\n          {\"Session Expired\"}\n        </DialogTitle>\n        <DialogContent>\n          <DialogContentText id=\"expired-dialog-description\">\n            Your session has expired. Please make a payment to continue using the service.\n          </DialogContentText>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={handleExit} color=\"primary\">\n            Exit\n          </Button>\n          <Button onClick={handlePaymentConfirm} color=\"primary\" autoFocus>\n            Make Payment\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* API Key Error Snackbar */}\n      <Snackbar\n        open={showApiKeyError}\n        autoHideDuration={6000}\n        onClose={handleApiKeyErrorClose}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n      >\n        <Alert onClose={handleApiKeyErrorClose} severity=\"error\" sx={{ width: '100%' }}>\n          {apiKeyError}\n        </Alert>\n      </Snackbar>\n    </div>\n  );\n};\n\nexport default DeepgramLiveTranscript;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AACvE,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAO,8BAA8B;AACrC,OAAOC,GAAG,MAAM,cAAc;AAC9B,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,iBAAiB,MAAM,iCAAiC;AAC/D,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,OAAOC,KAAK,MAAM,qBAAqB;AACvC,OAAOC,QAAQ,MAAM,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,MAAMC,sBAAsB,GAAGA,CAAC;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EAC7C;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC0B,WAAW,EAAEC,cAAc,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC4B,YAAY,EAAEC,eAAe,CAAC,GAAG7B,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;EAC7D,MAAM,CAAC8B,WAAW,EAAEC,cAAc,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACgC,eAAe,EAAEC,kBAAkB,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACA,MAAM,CAACkC,WAAW,EAAEC,cAAc,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACoC,YAAY,EAAEC,eAAe,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACsC,YAAY,EAAEC,eAAe,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;;EAEpD;EACA,MAAMwC,gBAAgB,GAAG,GAAG,CAAC,CAAC;EAC9B,MAAMC,YAAY,GAAG,EAAE,CAAC,CAAC;EACzB,MAAM,CAACC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG3C,QAAQ,CAACwC,gBAAgB,CAAC;EAClF,MAAM,CAACI,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC8C,cAAc,EAAEC,iBAAiB,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;;EAE3D;EACA,MAAMgD,KAAK,GAAGjD,MAAM,CAAC,IAAI,CAAC;EAC1B,MAAMkD,QAAQ,GAAGlD,MAAM,CAAC,IAAIG,WAAW,CAAC;IAAEgD,OAAO,EAAE;EAAI,CAAC,CAAC,CAAC;EAC1D,MAAMC,WAAW,GAAGpD,MAAM,CAAC,IAAI,CAAC;EAChC,MAAMqD,eAAe,GAAGrD,MAAM,CAAC,IAAI,CAAC;EACpC,MAAMsD,sBAAsB,GAAGtD,MAAM,CAAC,IAAI,CAAC;;EAE3C;EACA,MAAMuD,iBAAiB,GAAGrD,WAAW,CAAEsD,YAAY,IAAK;IACtD,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,YAAY,GAAG,EAAE,CAAC;IAC7C,MAAMI,OAAO,GAAGJ,YAAY,GAAG,EAAE;IACjC,OAAO,GAAGC,OAAO,CAACI,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,OAAO,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACxF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,iBAAiB,GAAG7D,WAAW,CAAC,MAAM;IAC1C;IACA0C,uBAAuB,CAACH,gBAAgB,CAAC;IACzCO,iBAAiB,CAAC,KAAK,CAAC;;IAExB;IACA,IAAIK,eAAe,CAACW,OAAO,EAAE;MAC3BC,aAAa,CAACZ,eAAe,CAACW,OAAO,CAAC;IACxC;;IAEA;IACAX,eAAe,CAACW,OAAO,GAAGE,WAAW,CAAC,MAAM;MAC1CtB,uBAAuB,CAACuB,IAAI,IAAI;QAC9B,MAAMC,OAAO,GAAGD,IAAI,GAAG,CAAC;;QAExB;QACA,IAAIC,OAAO,KAAK1B,YAAY,EAAE;UAC5BI,oBAAoB,CAAC,IAAI,CAAC;QAC5B;;QAEA;QACA,IAAIsB,OAAO,IAAI,CAAC,EAAE;UAChBH,aAAa,CAACZ,eAAe,CAACW,OAAO,CAAC;UACtCX,eAAe,CAACW,OAAO,GAAG,IAAI;UAC9BhB,iBAAiB,CAAC,IAAI,CAAC;UACvBqB,aAAa,CAAC,CAAC;UACf,OAAO,CAAC;QACV;QAEA,OAAOD,OAAO;MAChB,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC;EACV,CAAC,EAAE,CAAC3B,gBAAgB,EAAEC,YAAY,CAAC,CAAC;;EAEpC;EACA,MAAM4B,wBAAwB,GAAGpE,WAAW,CAAC,MAAM;IACjD4C,oBAAoB,CAAC,KAAK,CAAC;EAC7B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMyB,oBAAoB,GAAGrE,WAAW,CAAC,MAAM;IAC7C;IACA4C,oBAAoB,CAAC,KAAK,CAAC;IAC3BF,uBAAuB,CAACH,gBAAgB,CAAC;IACzCO,iBAAiB,CAAC,KAAK,CAAC;;IAExB;IACA,IAAIK,eAAe,CAACW,OAAO,EAAE;MAC3BC,aAAa,CAACZ,eAAe,CAACW,OAAO,CAAC;IACxC;IAEAX,eAAe,CAACW,OAAO,GAAGE,WAAW,CAAC,MAAM;MAC1CtB,uBAAuB,CAACuB,IAAI,IAAI;QAC9B,MAAMC,OAAO,GAAGD,IAAI,GAAG,CAAC;;QAExB;QACA,IAAIC,OAAO,KAAK1B,YAAY,EAAE;UAC5BI,oBAAoB,CAAC,IAAI,CAAC;QAC5B;;QAEA;QACA,IAAIsB,OAAO,IAAI,CAAC,EAAE;UAChBH,aAAa,CAACZ,eAAe,CAACW,OAAO,CAAC;UACtCX,eAAe,CAACW,OAAO,GAAG,IAAI;UAC9BhB,iBAAiB,CAAC,IAAI,CAAC;UACvBqB,aAAa,CAAC,CAAC;UACf,OAAO,CAAC;QACV;QAEA,OAAOD,OAAO;MAChB,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC;EACV,CAAC,EAAE,CAAC3B,gBAAgB,EAAEC,YAAY,CAAC,CAAC;;EAEpC;EACA3C,SAAS,CAAC,MAAM;IACdgE,iBAAiB,CAAC,CAAC;;IAEnB;IACA,OAAO,MAAM;MACX,IAAIV,eAAe,CAACW,OAAO,EAAE;QAC3BC,aAAa,CAACZ,eAAe,CAACW,OAAO,CAAC;MACxC;IACF,CAAC;EACH,CAAC,EAAE,CAACD,iBAAiB,CAAC,CAAC;;EAEvB;EACA,MAAMS,oBAAoB,GAAGtE,WAAW,CAAC,YAAY;IACnD,IAAI;MACF;MACA,MAAMuE,gBAAgB,GAAGrE,GAAG,CAACqE,gBAAgB;MAE7C,IAAI,CAACA,gBAAgB,EAAE;QACrB3C,eAAe,CAAC,SAAS,CAAC;QAC1BE,cAAc,CAAC,yGAAyG,CAAC;QACzHE,kBAAkB,CAAC,IAAI,CAAC;QACxB,OAAO,KAAK;MACd;;MAEA;MACA;MACA,IAAI;QACF;QACA,MAAMwC,UAAU,GAAG,IAAIC,SAAS,CAAC,8DAA8D,CAAC;;QAEhG;QACA,MAAMC,iBAAiB,GAAG,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;UACzD;UACA,MAAMC,OAAO,GAAGC,UAAU,CAAC,MAAM;YAC/BP,UAAU,CAACQ,KAAK,CAAC,CAAC;YAClBH,MAAM,CAAC,IAAII,KAAK,CAAC,oBAAoB,CAAC,CAAC;UACzC,CAAC,EAAE,IAAI,CAAC;UAERT,UAAU,CAACU,MAAM,GAAG,MAAM;YACxB;YACAV,UAAU,CAACW,IAAI,CAACC,IAAI,CAACC,SAAS,CAAC;cAC7BC,aAAa,EAAEf;YACjB,CAAC,CAAC,CAAC;;YAEH;YACAQ,UAAU,CAAC,MAAM;cACfQ,YAAY,CAACT,OAAO,CAAC;cACrBN,UAAU,CAACQ,KAAK,CAAC,CAAC;cAClBJ,OAAO,CAAC,IAAI,CAAC;YACf,CAAC,EAAE,IAAI,CAAC;UACV,CAAC;UAEDJ,UAAU,CAACgB,OAAO,GAAIC,KAAK,IAAK;YAC9BF,YAAY,CAACT,OAAO,CAAC;YACrBD,MAAM,CAACY,KAAK,CAAC;UACf,CAAC;UAEDjB,UAAU,CAACkB,SAAS,GAAIC,OAAO,IAAK;YAClC,IAAI;cACF,MAAMC,IAAI,GAAGR,IAAI,CAACS,KAAK,CAACF,OAAO,CAACC,IAAI,CAAC;cACrC;cACA,IAAIA,IAAI,CAACH,KAAK,EAAE;gBACdF,YAAY,CAACT,OAAO,CAAC;gBACrBN,UAAU,CAACQ,KAAK,CAAC,CAAC;gBAClBH,MAAM,CAAC,IAAII,KAAK,CAACW,IAAI,CAACH,KAAK,CAAC,CAAC;cAC/B;YACF,CAAC,CAAC,OAAOK,CAAC,EAAE;cACV;YAAA;UAEJ,CAAC;QACH,CAAC,CAAC;;QAEF;QACA,MAAMpB,iBAAiB;QAEvB9C,eAAe,CAAC,OAAO,CAAC;QACxB,OAAO,IAAI;MACb,CAAC,CAAC,OAAO6D,KAAK,EAAE;QACdM,OAAO,CAACN,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD7D,eAAe,CAAC,SAAS,CAAC;QAC1BE,cAAc,CAAC,kGAAkG,CAAC;QAClHE,kBAAkB,CAAC,IAAI,CAAC;QACxB,OAAO,KAAK;MACd;IACF,CAAC,CAAC,OAAOyD,KAAK,EAAE;MACdM,OAAO,CAACN,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD7D,eAAe,CAAC,SAAS,CAAC;MAC1BE,cAAc,CAAC,oCAAoC,GAAG2D,KAAK,CAACE,OAAO,CAAC;MACpE3D,kBAAkB,CAAC,IAAI,CAAC;MACxB,OAAO,KAAK;IACd;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMmC,aAAa,GAAGnE,WAAW,CAAC,MAAM;IACtC0B,cAAc,CAAC,KAAK,CAAC;;IAErB;IACA,IAAIsB,QAAQ,CAACc,OAAO,EAAE;MACpBd,QAAQ,CAACc,OAAO,CAACkC,IAAI,CAAC,CAAC;IACzB;;IAEA;IACA,IAAI9C,WAAW,CAACY,OAAO,EAAE;MACvBC,aAAa,CAACb,WAAW,CAACY,OAAO,CAAC;MAClCZ,WAAW,CAACY,OAAO,GAAG,IAAI;IAC5B;;IAEA;IACA,IAAIf,KAAK,CAACe,OAAO,EAAE;MACjBf,KAAK,CAACe,OAAO,CAACkB,KAAK,CAAC,CAAC;MACrBjC,KAAK,CAACe,OAAO,GAAG,IAAI;IACtB;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMmC,cAAc,GAAGjG,WAAW,CAAC,YAAY;IAC7C,IAAI6C,cAAc,EAAE;;IAEpB;IACA,MAAMqD,aAAa,GAAG,MAAM5B,oBAAoB,CAAC,CAAC;IAClD,IAAI,CAAC4B,aAAa,EAAE;IAEpB,IAAI;MACF,MAAM3B,gBAAgB,GAAGrE,GAAG,CAACqE,gBAAgB;;MAE7C;MACA,MAAM4B,MAAM,GAAG,IAAI1B,SAAS,CAAC,8DAA8D,CAAC;;MAE5F;MACA0B,MAAM,CAACjB,MAAM,GAAG,YAAY;QAC1Ba,OAAO,CAACK,GAAG,CAAC,qBAAqB,CAAC;;QAElC;QACAD,MAAM,CAAChB,IAAI,CAACC,IAAI,CAACC,SAAS,CAAC;UACzBC,aAAa,EAAEf;QACjB,CAAC,CAAC,CAAC;;QAEH;QACA,MAAMvB,QAAQ,CAACc,OAAO,CAACuC,KAAK,CAAC,CAAC;QAC9B3E,cAAc,CAAC,IAAI,CAAC;;QAEpB;QACAwB,WAAW,CAACY,OAAO,GAAGE,WAAW,CAAC,YAAY;UAC5C,IAAI;YACF,MAAM,CAACsC,CAAC,EAAEC,IAAI,CAAC,GAAG,MAAMvD,QAAQ,CAACc,OAAO,CAAC0C,MAAM,CAAC,CAAC;YACjD,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;YAC/BD,MAAM,CAACE,iBAAiB,CAACJ,IAAI,CAAC;YAC9BE,MAAM,CAACG,SAAS,GAAG,MAAM;cACvB,IAAIT,MAAM,CAACU,UAAU,KAAKpC,SAAS,CAACqC,IAAI,EAAE;gBACxCX,MAAM,CAAChB,IAAI,CAACsB,MAAM,CAACM,MAAM,CAAC;cAC5B;YACF,CAAC;UACH,CAAC,CAAC,OAAOtB,KAAK,EAAE;YACdM,OAAO,CAACN,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;UACjD;QACF,CAAC,EAAE,IAAI,CAAC;QAER1C,KAAK,CAACe,OAAO,GAAGqC,MAAM;MACxB,CAAC;;MAED;MACAA,MAAM,CAACT,SAAS,GAAIC,OAAO,IAAK;QAC9B,IAAI;UAAA,IAAAqB,aAAA,EAAAC,qBAAA;UACF,MAAMrB,IAAI,GAAGR,IAAI,CAACS,KAAK,CAACF,OAAO,CAACC,IAAI,CAAC;UACrC,MAAMsB,KAAK,GAAG,EAAAF,aAAA,GAAApB,IAAI,CAACuB,OAAO,cAAAH,aAAA,wBAAAC,qBAAA,GAAZD,aAAA,CAAcI,YAAY,CAAC,CAAC,CAAC,cAAAH,qBAAA,uBAA7BA,qBAAA,CAA+BC,KAAK,KAAI,EAAE;UAExD,IAAIA,KAAK,CAACG,MAAM,KAAK,CAAC,EAAE;UAExB,MAAMC,gBAAgB,GAAGJ,KAAK,CAACK,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAK;YACnD,IAAI,CAACA,IAAI,CAACC,OAAO,EAAE,OAAOF,GAAG;YAC7B,MAAMG,SAAS,GAAGH,GAAG,CAACA,GAAG,CAACH,MAAM,GAAG,CAAC,CAAC;YAErC,IAAIM,SAAS,IAAIA,SAAS,CAACD,OAAO,KAAKD,IAAI,CAACC,OAAO,EAAE;cACnDC,SAAS,CAACT,KAAK,CAACU,IAAI,CAACH,IAAI,CAACA,IAAI,CAAC;YACjC,CAAC,MAAM;cACLD,GAAG,CAACI,IAAI,CAAC;gBACPF,OAAO,EAAED,IAAI,CAACC,OAAO;gBACrBR,KAAK,EAAE,CAACO,IAAI,CAACA,IAAI,CAAC;gBAClBI,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;cACpC,CAAC,CAAC;YACJ;YAEA,OAAOP,GAAG;UACZ,CAAC,EAAE,EAAE,CAAC;UAEN,IAAIF,gBAAgB,CAACD,MAAM,GAAG,CAAC,EAAE;YAC/B7F,cAAc,CAACyC,IAAI,IAAI;cACrB;cACA,MAAM+D,cAAc,GAAG,CAAC,GAAG/D,IAAI,CAAC;cAEhCqD,gBAAgB,CAACW,OAAO,CAACC,KAAK,IAAI;gBAChC,MAAMP,SAAS,GAAGK,cAAc,CAACA,cAAc,CAACX,MAAM,GAAG,CAAC,CAAC;gBAE3D,IAAIM,SAAS,IAAIA,SAAS,CAACD,OAAO,KAAKQ,KAAK,CAACR,OAAO,EAAE;kBACpD;kBACAC,SAAS,CAACT,KAAK,GAAG,CAAC,GAAGS,SAAS,CAACT,KAAK,EAAE,GAAGgB,KAAK,CAAChB,KAAK,CAAC;gBACxD,CAAC,MAAM;kBACL;kBACAc,cAAc,CAACJ,IAAI,CAACM,KAAK,CAAC;gBAC5B;cACF,CAAC,CAAC;cAEF,OAAOF,cAAc;YACvB,CAAC,CAAC;;YAEF;YACA,IAAI5E,sBAAsB,CAACU,OAAO,EAAE;cAClCV,sBAAsB,CAACU,OAAO,CAACqE,SAAS,GAAG/E,sBAAsB,CAACU,OAAO,CAACsE,YAAY;YACxF;UACF;QACF,CAAC,CAAC,OAAO3C,KAAK,EAAE;UACdM,OAAO,CAACN,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAChD;MACF,CAAC;;MAED;MACAU,MAAM,CAACX,OAAO,GAAIC,KAAK,IAAK;QAC1BM,OAAO,CAACN,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;QACxC3D,cAAc,CAAC,mBAAmB,GAAG2D,KAAK,CAACE,OAAO,CAAC;QACnD3D,kBAAkB,CAAC,IAAI,CAAC;MAC1B,CAAC;;MAED;MACAmE,MAAM,CAACkC,OAAO,GAAG,MAAM;QACrBtC,OAAO,CAACK,GAAG,CAAC,kBAAkB,CAAC;QAC/BjC,aAAa,CAAC,CAAC;MACjB,CAAC;IACH,CAAC,CAAC,OAAOsB,KAAK,EAAE;MACdM,OAAO,CAACN,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD3D,cAAc,CAAC,4BAA4B,GAAG2D,KAAK,CAACE,OAAO,CAAC;MAC5D3D,kBAAkB,CAAC,IAAI,CAAC;IAC1B;EACF,CAAC,EAAE,CAACa,cAAc,EAAEyB,oBAAoB,EAAEH,aAAa,CAAC,CAAC;;EAIzD;EACAtE,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACXsE,aAAa,CAAC,CAAC;MAEf,IAAIhB,eAAe,CAACW,OAAO,EAAE;QAC3BC,aAAa,CAACZ,eAAe,CAACW,OAAO,CAAC;MACxC;IACF,CAAC;EACH,CAAC,EAAE,CAACK,aAAa,CAAC,CAAC;;EAEnB;EACA,MAAMmE,mBAAmB,GAAGtI,WAAW,CAAC,MAAM;IAC5C,MAAMuI,SAAS,GAAGC,MAAM,CAACC,YAAY,CAAC,CAAC;IACvC,MAAMpG,YAAY,GAAGkG,SAAS,CAAC5E,QAAQ,CAAC,CAAC,CAAC+E,IAAI,CAAC,CAAC;IAEhD,IAAIrG,YAAY,EAAE;MAChBC,eAAe,CAACD,YAAY,CAAC;IAC/B;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMsG,SAAS,GAAG3I,WAAW,CAAC,YAAY;IACxC,IAAI,CAACqC,YAAY,IAAIF,YAAY,IAAIU,cAAc,EAAE;IAErD,MAAM+F,MAAM,GAAG1I,GAAG,CAAC2I,cAAc;IAEjC,IAAI,CAACD,MAAM,EAAE;MACX9G,cAAc,CAAC,qGAAqG,CAAC;MACrHE,kBAAkB,CAAC,IAAI,CAAC;MACxB;IACF;IAEAI,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACF,MAAM0G,QAAQ,GAAG,MAAMC,KAAK,CAAC,4CAA4C,EAAE;QACzEC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAE,UAAUL,MAAM;QACnC,CAAC;QACDM,IAAI,EAAE9D,IAAI,CAACC,SAAS,CAAC;UACnB8D,KAAK,EAAE,cAAc;UACrBC,QAAQ,EAAE,CACR;YACEC,IAAI,EAAE,QAAQ;YACdC,OAAO,EAAE;UACX,CAAC,EACD;YACED,IAAI,EAAE,MAAM;YACZC,OAAO,EAAE,kFAAkFjH,YAAY;UACzG,CAAC,CACF;UACDkH,MAAM,EAAE;QACV,CAAC;MACH,CAAC,CAAC;MAEF,IAAI,CAACT,QAAQ,CAACU,EAAE,EAAE;QAChB,MAAM,IAAIvE,KAAK,CAAC,cAAc6D,QAAQ,CAACW,MAAM,IAAIX,QAAQ,CAACY,UAAU,EAAE,CAAC;MACzE;MAEA,MAAMjD,MAAM,GAAGqC,QAAQ,CAACI,IAAI,CAACS,SAAS,CAAC,CAAC;MACxC,MAAMC,OAAO,GAAG,IAAIC,WAAW,CAAC,OAAO,CAAC;MACxC,IAAI9C,MAAM,GAAG,EAAE;MAEf,OAAO,IAAI,EAAE;QACX,MAAM;UAAE+C,KAAK;UAAEC;QAAK,CAAC,GAAG,MAAMtD,MAAM,CAACuD,IAAI,CAAC,CAAC;QAC3C,IAAID,IAAI,EAAE;QAEV,MAAME,KAAK,GAAGL,OAAO,CAACM,MAAM,CAACJ,KAAK,EAAE;UAAEP,MAAM,EAAE;QAAK,CAAC,CAAC;QACrD,MAAMY,KAAK,GAAGF,KAAK,CAACG,KAAK,CAAC,IAAI,CAAC,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAAC5B,IAAI,CAAC,CAAC,CAAC6B,UAAU,CAAC,OAAO,CAAC,CAAC;QAE/E,KAAK,MAAMD,IAAI,IAAIH,KAAK,EAAE;UACxB,MAAMvE,IAAI,GAAG0E,IAAI,CAACE,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;UACxC,IAAI5E,IAAI,KAAK,QAAQ,EAAE;UAEvB,IAAI;YAAA,IAAA6E,aAAA,EAAAC,cAAA,EAAAC,oBAAA;YACF,MAAMC,IAAI,GAAGxF,IAAI,CAACS,KAAK,CAACD,IAAI,CAAC;YAC7B,MAAM0D,OAAO,IAAAmB,aAAA,GAAGG,IAAI,CAACC,OAAO,cAAAJ,aAAA,wBAAAC,cAAA,GAAZD,aAAA,CAAe,CAAC,CAAC,cAAAC,cAAA,wBAAAC,oBAAA,GAAjBD,cAAA,CAAmBI,KAAK,cAAAH,oBAAA,uBAAxBA,oBAAA,CAA0BrB,OAAO;YACjD,IAAIA,OAAO,EAAE;cACXvC,MAAM,IAAIuC,OAAO;cACjBpH,cAAc,CAAC6E,MAAM,CAAC;YACxB;UACF,CAAC,CAAC,OAAOjB,CAAC,EAAE;YACVC,OAAO,CAACN,KAAK,CAAC,qBAAqB,EAAEK,CAAC,CAAC;UACzC;QACF;MACF;IACF,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdM,OAAO,CAACN,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;MACtCvD,cAAc,CAAC,SAAS,GAAGuD,KAAK,CAACE,OAAO,CAAC;IAC3C,CAAC,SAAS;MACRvD,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC,EAAE,CAACC,YAAY,EAAEF,YAAY,EAAEU,cAAc,CAAC,CAAC;;EAEhD;EACA,MAAMkI,gBAAgB,GAAG/K,WAAW,CAAC,MAAM;IACzCwB,cAAc,CAAC,EAAE,CAAC;IAClBU,cAAc,CAAC,EAAE,CAAC;IAClBI,eAAe,CAAC,EAAE,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM0I,gBAAgB,GAAGhL,WAAW,CAAC,MAAM;IACzCkC,cAAc,CAAC,EAAE,CAAC;IAClBI,eAAe,CAAC,EAAE,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM2I,UAAU,GAAGjL,WAAW,CAAC,MAAM;IACnCmE,aAAa,CAAC,CAAC;IACf,IAAI9C,MAAM,EAAEA,MAAM,CAAC,CAAC;EACtB,CAAC,EAAE,CAAC8C,aAAa,EAAE9C,MAAM,CAAC,CAAC;;EAE3B;EACA,MAAM6J,sBAAsB,GAAGlL,WAAW,CAAC,MAAM;IAC/CgC,kBAAkB,CAAC,KAAK,CAAC;EAC3B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMmJ,iBAAiB,GAAGnL,WAAW,CAAC,MAAM;IAC1C,OAAOuB,WAAW,CAAC6J,GAAG,CAACC,OAAO,IAC5B,WAAWA,OAAO,CAAC3D,OAAO,KAAK2D,OAAO,CAACnE,KAAK,CAACoE,IAAI,CAAC,GAAG,CAAC,EACxD,CAAC,CAACA,IAAI,CAAC,IAAI,CAAC;EACd,CAAC,EAAE,CAAC/J,WAAW,CAAC,CAAC;;EAEjB;EACA,MAAMgK,kBAAkB,GAAGvL,WAAW,CAAC,MAAM;IAC3C,MAAMwL,QAAQ,GAAGL,iBAAiB,CAAC,CAAC;IACpC,IAAI,CAACK,QAAQ,EAAE;IAEf,MAAMjF,IAAI,GAAG,IAAIkF,IAAI,CAAC,CAACD,QAAQ,CAAC,EAAE;MAAEE,IAAI,EAAE;IAAa,CAAC,CAAC;IACzD,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACtF,IAAI,CAAC;IACrC,MAAMuF,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACrCF,CAAC,CAACG,IAAI,GAAGN,GAAG;IACZG,CAAC,CAACI,QAAQ,GAAG,cAAc,IAAIpE,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACoE,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC3B,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM;IACzFuB,QAAQ,CAAC7C,IAAI,CAACkD,WAAW,CAACN,CAAC,CAAC;IAC5BA,CAAC,CAACO,KAAK,CAAC,CAAC;IACTN,QAAQ,CAAC7C,IAAI,CAACoD,WAAW,CAACR,CAAC,CAAC;IAC5BF,GAAG,CAACW,eAAe,CAACZ,GAAG,CAAC;EAC1B,CAAC,EAAE,CAACR,iBAAiB,CAAC,CAAC;EAEvB,oBACEhK,OAAA;IAAKqL,SAAS,EAAC,+BAA+B;IAAAC,QAAA,gBAE5CtL,OAAA;MAAQqL,SAAS,EAAC,aAAa;MAACE,OAAO,EAAEzB,UAAW;MAAC0B,KAAK,EAAC,MAAM;MAAAF,QAAA,eAC/DtL,OAAA,CAAChB,SAAS;QAAAyM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,eAGT5L,OAAA;MAAKqL,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BtL,OAAA,CAACX,cAAc;QAACgM,SAAS,EAAC;MAAY;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACzC5L,OAAA;QAAMqL,SAAS,EAAC,6BAA6B;QAAAC,QAAA,GAAC,WACnC,EAACpJ,iBAAiB,CAACZ,oBAAoB,CAAC;MAAA;QAAAmK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC,EACNtL,WAAW,iBAAIN,OAAA;QAAMqL,SAAS,EAAC,qBAAqB;QAAAC,QAAA,EAAC;MAAY;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtE,CAAC,eAEN5L,OAAA;MAAIqL,SAAS,EAAC,2BAA2B;MAAAC,QAAA,EAAC;IAAwC;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAEvF5L,OAAA;MAAKqL,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCtL,OAAA;QACEqL,SAAS,EAAE,iBAAiB/K,WAAW,GAAG,WAAW,GAAG,EAAE,EAAG;QAC7DiL,OAAO,EAAEjL,WAAW,GAAG0C,aAAa,GAAG8B,cAAe;QACtD+G,QAAQ,EAAEnK,cAAc,IAAIlB,YAAY,KAAK,SAAU;QACvDgL,KAAK,EAAElL,WAAW,GAAG,gBAAgB,GAAG,iBAAkB;QAAAgL,QAAA,GAEzDhL,WAAW,gBAAGN,OAAA,CAACd,QAAQ;UAAAuM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAG5L,OAAA,CAACf,OAAO;UAAAwM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACxCtL,WAAW,GAAG,gBAAgB,GAAG,iBAAiB;MAAA;QAAAmL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC,eAET5L,OAAA;QACEqL,SAAS,EAAC,cAAc;QACxBE,OAAO,EAAE3B,gBAAiB;QAC1BiC,QAAQ,EAAEzL,WAAW,CAAC8F,MAAM,KAAK,CAAC,IAAIxE,cAAe;QACrD8J,KAAK,EAAC,mBAAmB;QAAAF,QAAA,gBAEzBtL,OAAA,CAACZ,UAAU;UAAC0M,QAAQ,EAAC;QAAO;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,SAEjC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAET5L,OAAA;QACEqL,SAAS,EAAC,iBAAiB;QAC3BE,OAAO,EAAEnB,kBAAmB;QAC5ByB,QAAQ,EAAEzL,WAAW,CAAC8F,MAAM,KAAK,CAAC,IAAIxE,cAAe;QACrD8J,KAAK,EAAC,qBAAqB;QAAAF,QAAA,EAC5B;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAEN5L,OAAA;MAAKqL,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BtL,OAAA;QAAKqL,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjCtL,OAAA;UACEqL,SAAS,EAAC,sBAAsB;UAChCU,GAAG,EAAE9J,sBAAuB;UAC5B+J,SAAS,EAAE7E,mBAAoB;UAAAmE,QAAA,EAE9BlL,WAAW,CAAC8F,MAAM,KAAK,CAAC,gBACvBlG,OAAA;YAAKqL,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAC9B9K,YAAY,KAAK,SAAS,gBACzBR,OAAA;cAAAsL,QAAA,EAAG;YAA8D;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,gBAErE5L,OAAA;cAAAsL,QAAA,EAAG;YAAqD;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAC5D;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,GAENxL,WAAW,CAAC6J,GAAG,CAAC,CAACC,OAAO,EAAE+B,KAAK,kBAC7BjM,OAAA;YAAKqL,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BtL,OAAA;cAAMqL,SAAS,EAAC,kBAAkB;cAAAC,QAAA,GAAC,UAAQ,EAACpB,OAAO,CAAC3D,OAAO,EAAC,GAAC;YAAA;cAAAkF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpE5L,OAAA;cAAAsL,QAAA,EAAOpB,OAAO,CAACnE,KAAK,CAACoE,IAAI,CAAC,GAAG;YAAC;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA,GAFDK,KAAK;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGvC,CACN;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAEL1K,YAAY,iBACXlB,OAAA;UAAKqL,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACtCtL,OAAA;YAAKqL,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnCtL,OAAA;cAAAsL,QAAA,EAAI;YAAa;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtB5L,OAAA;cACEqL,SAAS,EAAC,oBAAoB;cAC9BE,OAAO,EAAE/D,SAAU;cACnBqE,QAAQ,EAAE,CAAC3K,YAAY,IAAIF,YAAY,IAAIU,cAAe;cAC1D8J,KAAK,EAAC,kBAAkB;cAAAF,QAAA,GAEvBtK,YAAY,gBAAGhB,OAAA,CAACJ,gBAAgB;gBAACsM,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAG5L,OAAA,CAACb,QAAQ;gBAAAsM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EAAC,kBAEhE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACN5L,OAAA;YAAKqL,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEpK;UAAY;YAAAuK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAEL,CAAC9K,WAAW,IAAIE,YAAY,kBAC3BhB,OAAA;QAAKqL,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACnCtL,OAAA;UAAKqL,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAClCtL,OAAA;YAAAsL,QAAA,EAAI;UAAW;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpB5L,OAAA;YACEqL,SAAS,EAAC,cAAc;YACxBE,OAAO,EAAE1B,gBAAiB;YAC1BgC,QAAQ,EAAE,CAAC/K,WAAW,IAAI,CAACE,YAAa;YACxCwK,KAAK,EAAC,gBAAgB;YAAAF,QAAA,gBAEtBtL,OAAA,CAACZ,UAAU;cAAC0M,QAAQ,EAAC;YAAO;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,SAEjC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN5L,OAAA;UAAKqL,SAAS,EAAC,cAAc;UAAAC,QAAA,EAC1BtK,YAAY,IAAI,CAACF,WAAW,gBAC3Bd,OAAA;YAAKqL,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCtL,OAAA,CAACJ,gBAAgB;cAACsM,IAAI,EAAE;YAAG;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9B5L,OAAA;cAAAsL,QAAA,EAAG;YAAuB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,GAEN9K;QACD;UAAA2K,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGN5L,OAAA,CAACV,MAAM;MACL6M,IAAI,EAAE3K,iBAAkB;MACxB4K,OAAO,EAAEnJ,wBAAyB;MAClC,mBAAgB,sBAAsB;MACtC,oBAAiB,4BAA4B;MAAAqI,QAAA,gBAE7CtL,OAAA,CAACN,WAAW;QAAC2M,EAAE,EAAC,sBAAsB;QAAAf,QAAA,EACnC;MAAuB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC,eACd5L,OAAA,CAACR,aAAa;QAAA8L,QAAA,eACZtL,OAAA,CAACP,iBAAiB;UAAC4M,EAAE,EAAC,4BAA4B;UAAAf,QAAA,EAAC;QAEnD;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eAChB5L,OAAA,CAACT,aAAa;QAAA+L,QAAA,gBACZtL,OAAA,CAACL,MAAM;UAAC4L,OAAO,EAAEtI,wBAAyB;UAACqJ,KAAK,EAAC,SAAS;UAAAhB,QAAA,EAAC;QAE3D;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT5L,OAAA,CAACL,MAAM;UAAC4L,OAAO,EAAErI,oBAAqB;UAACoJ,KAAK,EAAC,SAAS;UAACC,SAAS;UAAAjB,QAAA,EAAC;QAEjE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGT5L,OAAA,CAACV,MAAM;MACL6M,IAAI,EAAEzK,cAAe;MACrB,mBAAgB,sBAAsB;MACtC,oBAAiB,4BAA4B;MAAA4J,QAAA,gBAE7CtL,OAAA,CAACN,WAAW;QAAC2M,EAAE,EAAC,sBAAsB;QAAAf,QAAA,EACnC;MAAiB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eACd5L,OAAA,CAACR,aAAa;QAAA8L,QAAA,eACZtL,OAAA,CAACP,iBAAiB;UAAC4M,EAAE,EAAC,4BAA4B;UAAAf,QAAA,EAAC;QAEnD;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eAChB5L,OAAA,CAACT,aAAa;QAAA+L,QAAA,gBACZtL,OAAA,CAACL,MAAM;UAAC4L,OAAO,EAAEzB,UAAW;UAACwC,KAAK,EAAC,SAAS;UAAAhB,QAAA,EAAC;QAE7C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT5L,OAAA,CAACL,MAAM;UAAC4L,OAAO,EAAErI,oBAAqB;UAACoJ,KAAK,EAAC,SAAS;UAACC,SAAS;UAAAjB,QAAA,EAAC;QAEjE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGT5L,OAAA,CAACF,QAAQ;MACPqM,IAAI,EAAEvL,eAAgB;MACtB4L,gBAAgB,EAAE,IAAK;MACvBJ,OAAO,EAAErC,sBAAuB;MAChC0C,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAArB,QAAA,eAE3DtL,OAAA,CAACH,KAAK;QAACuM,OAAO,EAAErC,sBAAuB;QAAC6C,QAAQ,EAAC,OAAO;QAACC,EAAE,EAAE;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAxB,QAAA,EAC5E5K;MAAW;QAAA+K,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEV,CAAC;AAACzL,EAAA,CAlqBIF,sBAAsB;AAAA8M,EAAA,GAAtB9M,sBAAsB;AAoqB5B,eAAeA,sBAAsB;AAAC,IAAA8M,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}