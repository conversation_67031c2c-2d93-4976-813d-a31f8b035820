{"ast": null, "code": "import React,{useState,useRef,useEffect}from'react';import'./InterviewForm.css';import Dialog from'@mui/material/Dialog';import DialogActions from'@mui/material/DialogActions';import DialogContent from'@mui/material/DialogContent';import DialogContentText from'@mui/material/DialogContentText';import DialogTitle from'@mui/material/DialogTitle';import Button from'@mui/material/Button';import MicIcon from'@mui/icons-material/Mic';import StopIcon from'@mui/icons-material/Stop';import PlayArrowIcon from'@mui/icons-material/PlayArrow';import DeleteIcon from'@mui/icons-material/Delete';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";function InterviewForm(_ref){let{onStartSession}=_ref;// Form fields\nconst[jobTitle,setJobTitle]=useState('');const[skillset,setSkillset]=useState('');const[jobDescription,setJobDescription]=useState('');const[focusAreas,setFocusAreas]=useState([]);const[resumeFile,setResumeFile]=useState(null);// Validation states\nconst[fileError,setFileError]=useState('');const[jobDescriptionError,setJobDescriptionError]=useState('');// Recording states\nconst[isRecording,setIsRecording]=useState(false);const[recordingTime,setRecordingTime]=useState(0);const[audioBlob,setAudioBlob]=useState(null);const[audioUrl,setAudioUrl]=useState('');const mediaRecorderRef=useRef(null);const recordingTimerRef=useRef(null);// Session states\nconst[showTimeAlert,setShowTimeAlert]=useState(false);const[sessionTimeRemaining,setSessionTimeRemaining]=useState(5);// Default 5 minutes\n// Check for remaining time in localStorage\nuseEffect(()=>{const userDetails=localStorage.getItem('userDetails');if(userDetails){try{const userData=JSON.parse(userDetails);if(userData.timeRemaining){setSessionTimeRemaining(userData.timeRemaining);}}catch(error){console.error('Error parsing user data:',error);}}},[]);// Start recording voice\nconst startRecording=async()=>{try{// Reset recording state\nsetRecordingTime(0);setAudioBlob(null);setAudioUrl('');// Request microphone access\nconst stream=await navigator.mediaDevices.getUserMedia({audio:true});// Create media recorder\nconst mediaRecorder=new MediaRecorder(stream);mediaRecorderRef.current=mediaRecorder;// Set up data handling\nconst audioChunks=[];mediaRecorder.ondataavailable=event=>{if(event.data.size>0){audioChunks.push(event.data);}};// Handle recording stop\nmediaRecorder.onstop=()=>{// Create blob from chunks\nconst audioBlob=new Blob(audioChunks,{type:'audio/wav'});const audioUrl=URL.createObjectURL(audioBlob);// Update state\nsetAudioBlob(audioBlob);setAudioUrl(audioUrl);setIsRecording(false);// Stop all tracks in the stream\nstream.getTracks().forEach(track=>track.stop());};// Start recording\nmediaRecorder.start();setIsRecording(true);// Set up timer (limit to 2 minutes = 120 seconds)\nconst MAX_RECORDING_TIME=120;let seconds=0;recordingTimerRef.current=setInterval(()=>{seconds+=1;setRecordingTime(seconds);// Auto-stop after 2 minutes\nif(seconds>=MAX_RECORDING_TIME){stopRecording();}},1000);}catch(error){console.error('Error starting recording:',error);alert('Could not access microphone. Please check your permissions.');}};// Stop recording\nconst stopRecording=()=>{if(mediaRecorderRef.current&&isRecording){mediaRecorderRef.current.stop();clearInterval(recordingTimerRef.current);}};// Play recorded audio\nconst playRecording=()=>{if(audioUrl){const audio=new Audio(audioUrl);audio.play();}};// Delete recorded audio\nconst deleteRecording=()=>{if(audioUrl){URL.revokeObjectURL(audioUrl);setAudioBlob(null);setAudioUrl('');setRecordingTime(0);}};// Format recording time as MM:SS\nconst formatRecordingTime=seconds=>{const minutes=Math.floor(seconds/60);const remainingSeconds=seconds%60;return\"\".concat(minutes.toString().padStart(2,'0'),\":\").concat(remainingSeconds.toString().padStart(2,'0'));};// Validate job description word count\nconst validateJobDescription=text=>{if(!text)return true;const wordCount=text.trim().split(/\\s+/).length;if(wordCount>10000){setJobDescriptionError(\"Job description exceeds 10,000 words (current: \".concat(wordCount,\")\"));return false;}setJobDescriptionError('');return true;};// Handle form submission\nconst handleSubmit=e=>{e.preventDefault();// Validate form\nif(!jobTitle.trim()){return;// Don't submit if job title is empty\n}if(!validateJobDescription(jobDescription)){return;// Don't submit if job description is too long\n}// Show time alert before starting\nsetShowTimeAlert(true);};// Handle confirmation from time alert dialog\nconst handleTimeAlertConfirm=()=>{setShowTimeAlert(false);// Create session config\nconst config={jobTitle,skillset,jobDescription,focusAreas,resumeFile,audioBlob,sessionTimeRemaining};// Pass the configuration to the parent component\n// This will directly show the SpeechToText component from the parent\nonStartSession(config);};//   if (sessionStarted && sessionConfig) {\n//   return (\n//     <InterviewSession config={sessionConfig} onEndSession={() => setSessionStarted(false)} />\n//   );\n// }\nconst handleFocusAreaChange=area=>{if(focusAreas.includes(area)){setFocusAreas(focusAreas.filter(a=>a!==area));}else{setFocusAreas([...focusAreas,area]);}};const handleFileChange=e=>{const file=e.target.files[0];setFileError('');if(!file){setResumeFile(null);return;}// Check file type\nconst fileType=file.type;const validTypes=['application/pdf','application/vnd.openxmlformats-officedocument.wordprocessingml.document'];if(!validTypes.includes(fileType)){setFileError('Please upload a PDF or DOCX file');setResumeFile(null);e.target.value='';// Reset file input\nreturn;}// Check file size (limit to 5MB)\nif(file.size>5*1024*1024){setFileError('File size should be less than 5MB');setResumeFile(null);e.target.value='';// Reset file input\nreturn;}setResumeFile(file);};const clearResume=()=>{setResumeFile(null);setFileError('');// Reset the file input\nconst fileInput=document.getElementById('resumeUpload');if(fileInput)fileInput.value='';};return/*#__PURE__*/_jsxs(\"div\",{className:\"interview-form\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"Simulate Your Interview Session\"}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmit,children:[/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"jobTitle\",children:\"Job Title\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",id:\"jobTitle\",value:jobTitle,onChange:e=>setJobTitle(e.target.value),placeholder:\"e.g. Frontend Developer\",required:true})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"skillset\",children:\"Your Skillset\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",id:\"skillset\",value:skillset,onChange:e=>setSkillset(e.target.value),placeholder:\"Input skills separated by commas (e.g. React, JavaScript, CSS)\"}),/*#__PURE__*/_jsx(\"p\",{className:\"help-text\",children:\"Enter your skills separated by commas\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"jobDescription\",children:\"Job Description\"}),/*#__PURE__*/_jsx(\"textarea\",{id:\"jobDescription\",value:jobDescription,onChange:e=>{setJobDescription(e.target.value);validateJobDescription(e.target.value);},placeholder:\"Paste the job description here (max 10,000 words)\",rows:6,className:\"job-description-textarea\"}),jobDescriptionError&&/*#__PURE__*/_jsx(\"p\",{className:\"error-text\",children:jobDescriptionError}),/*#__PURE__*/_jsx(\"p\",{className:\"help-text\",children:\"Paste the job description to get more targeted questions (max 10,000 words)\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"Focus Areas\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"checkbox-group\",children:[/*#__PURE__*/_jsxs(\"label\",{children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:focusAreas.includes('technical'),onChange:()=>handleFocusAreaChange('technical')}),\"Technical Skills\"]}),/*#__PURE__*/_jsxs(\"label\",{children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:focusAreas.includes('behavioral'),onChange:()=>handleFocusAreaChange('behavioral')}),\"Behavioral Questions\"]}),/*#__PURE__*/_jsxs(\"label\",{children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:focusAreas.includes('problemSolving'),onChange:()=>handleFocusAreaChange('problemSolving')}),\"Problem Solving\"]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"resumeUpload\",children:\"Resume (Optional)\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"file-upload-container\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"file-input-wrapper\",children:[/*#__PURE__*/_jsx(\"button\",{type:\"button\",className:\"file-input-button\",children:\"Choose File\"}),/*#__PURE__*/_jsx(\"input\",{type:\"file\",id:\"resumeUpload\",onChange:handleFileChange,accept:\".pdf,.docx,application/pdf,application/vnd.openxmlformats-officedocument.wordprocessingml.document\",className:\"file-input\"}),/*#__PURE__*/_jsx(\"span\",{className:\"file-name\",children:resumeFile?resumeFile.name:'No file chosen'})]}),resumeFile&&/*#__PURE__*/_jsx(\"button\",{type:\"button\",className:\"clear-file\",onClick:clearResume,children:\"Remove\"})]}),fileError&&/*#__PURE__*/_jsx(\"p\",{className:\"error-text\",children:fileError}),/*#__PURE__*/_jsx(\"p\",{className:\"help-text\",children:\"Upload your resume to personalize interview questions (PDF or DOCX, max 5MB)\"}),/*#__PURE__*/_jsxs(\"p\",{className:\"help-text\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Note:\"}),\" Remove any sensitive information from your resume\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"Record Your Voice (Optional - 2 mins max)\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"voice-recorder-container\",children:[!audioUrl?/*#__PURE__*/_jsxs(\"div\",{className:\"recording-controls\",children:[/*#__PURE__*/_jsx(\"button\",{type:\"button\",className:\"record-button \".concat(isRecording?'recording':''),onClick:isRecording?stopRecording:startRecording,children:isRecording?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(StopIcon,{}),\" Stop Recording (\",formatRecordingTime(recordingTime),\")\"]}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(MicIcon,{}),\" Start Recording\"]})}),isRecording&&/*#__PURE__*/_jsxs(\"p\",{className:\"recording-indicator\",children:[\"Recording in progress... (\",formatRecordingTime(recordingTime),\")\"]})]}):/*#__PURE__*/_jsxs(\"div\",{className:\"playback-controls\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"audio-info\",children:/*#__PURE__*/_jsxs(\"span\",{children:[\"Recording complete: \",formatRecordingTime(recordingTime)]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"audio-buttons\",children:[/*#__PURE__*/_jsxs(\"button\",{type:\"button\",className:\"play-button\",onClick:playRecording,children:[/*#__PURE__*/_jsx(PlayArrowIcon,{}),\" Play\"]}),/*#__PURE__*/_jsxs(\"button\",{type:\"button\",className:\"delete-button\",onClick:deleteRecording,children:[/*#__PURE__*/_jsx(DeleteIcon,{}),\" Delete\"]})]})]}),/*#__PURE__*/_jsx(\"p\",{className:\"help-text\",children:\"Recording your voice helps the AI understand your speaking style and accent\"})]})]}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",className:\"start-button\",children:\"Start Interview\"})]}),/*#__PURE__*/_jsxs(Dialog,{open:showTimeAlert,onClose:()=>setShowTimeAlert(false),\"aria-labelledby\":\"alert-dialog-title\",\"aria-describedby\":\"alert-dialog-description\",children:[/*#__PURE__*/_jsx(DialogTitle,{id:\"alert-dialog-title\",children:\"Session Time Information\"}),/*#__PURE__*/_jsx(DialogContent,{children:/*#__PURE__*/_jsxs(DialogContentText,{id:\"alert-dialog-description\",children:[\"You have \",sessionTimeRemaining,\" minutes remaining in your session. Would you like to continue with the interview?\"]})}),/*#__PURE__*/_jsxs(DialogActions,{children:[/*#__PURE__*/_jsx(Button,{onClick:()=>setShowTimeAlert(false),color:\"primary\",children:\"Cancel\"}),/*#__PURE__*/_jsx(Button,{onClick:handleTimeAlertConfirm,color:\"primary\",autoFocus:true,children:\"Continue\"})]})]})]});}export default InterviewForm;", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "Dialog", "DialogActions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogContentText", "DialogTitle", "<PERSON><PERSON>", "MicIcon", "StopIcon", "PlayArrowIcon", "DeleteIcon", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "InterviewForm", "_ref", "onStartSession", "jobTitle", "setJobTitle", "skillset", "setSkillset", "jobDescription", "setJobDescription", "focusAreas", "setFocusA<PERSON>s", "resumeFile", "setResumeFile", "fileError", "setFileError", "jobDescriptionError", "setJobDescriptionError", "isRecording", "setIsRecording", "recordingTime", "setRecordingTime", "audioBlob", "setAudioBlob", "audioUrl", "setAudioUrl", "mediaRecorderRef", "recordingTimerRef", "showTimeAlert", "setShowTimeAlert", "sessionTimeRemaining", "setSessionTimeRemaining", "userDetails", "localStorage", "getItem", "userData", "JSON", "parse", "timeRemaining", "error", "console", "startRecording", "stream", "navigator", "mediaDevices", "getUserMedia", "audio", "mediaRecorder", "MediaRecorder", "current", "audioChunks", "ondataavailable", "event", "data", "size", "push", "onstop", "Blob", "type", "URL", "createObjectURL", "getTracks", "for<PERSON>ach", "track", "stop", "start", "MAX_RECORDING_TIME", "seconds", "setInterval", "stopRecording", "alert", "clearInterval", "playRecording", "Audio", "play", "deleteRecording", "revokeObjectURL", "formatRecordingTime", "minutes", "Math", "floor", "remainingSeconds", "concat", "toString", "padStart", "validateJobDescription", "text", "wordCount", "trim", "split", "length", "handleSubmit", "e", "preventDefault", "handleTimeAlertConfirm", "config", "handleFocusAreaChange", "area", "includes", "filter", "a", "handleFileChange", "file", "target", "files", "fileType", "validTypes", "value", "clearResume", "fileInput", "document", "getElementById", "className", "children", "onSubmit", "htmlFor", "id", "onChange", "placeholder", "required", "rows", "checked", "accept", "name", "onClick", "open", "onClose", "color", "autoFocus"], "sources": ["C:/Users/<USER>/Documents/augment-projects/interview-ai-app/interviewAI/client/src/components/InterviewForm.jsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\r\nimport './InterviewForm.css';\r\nimport Dialog from '@mui/material/Dialog';\r\nimport DialogActions from '@mui/material/DialogActions';\r\nimport DialogContent from '@mui/material/DialogContent';\r\nimport DialogContentText from '@mui/material/DialogContentText';\r\nimport DialogTitle from '@mui/material/DialogTitle';\r\nimport Button from '@mui/material/Button';\r\nimport MicIcon from '@mui/icons-material/Mic';\r\nimport StopIcon from '@mui/icons-material/Stop';\r\nimport PlayArrowIcon from '@mui/icons-material/PlayArrow';\r\nimport DeleteIcon from '@mui/icons-material/Delete';\r\n\r\nfunction InterviewForm({ onStartSession }) {\r\n  // Form fields\r\n  const [jobTitle, setJobTitle] = useState('');\r\n  const [skillset, setSkillset] = useState('');\r\n  const [jobDescription, setJobDescription] = useState('');\r\n  const [focusAreas, setFocusAreas] = useState([]);\r\n  const [resumeFile, setResumeFile] = useState(null);\r\n\r\n  // Validation states\r\n  const [fileError, setFileError] = useState('');\r\n  const [jobDescriptionError, setJobDescriptionError] = useState('');\r\n\r\n  // Recording states\r\n  const [isRecording, setIsRecording] = useState(false);\r\n  const [recordingTime, setRecordingTime] = useState(0);\r\n  const [audioBlob, setAudioBlob] = useState(null);\r\n  const [audioUrl, setAudioUrl] = useState('');\r\n  const mediaRecorderRef = useRef(null);\r\n  const recordingTimerRef = useRef(null);\r\n\r\n  // Session states\r\n  const [showTimeAlert, setShowTimeAlert] = useState(false);\r\n  const [sessionTimeRemaining, setSessionTimeRemaining] = useState(5); // Default 5 minutes\r\n\r\n  // Check for remaining time in localStorage\r\n  useEffect(() => {\r\n    const userDetails = localStorage.getItem('userDetails');\r\n    if (userDetails) {\r\n      try {\r\n        const userData = JSON.parse(userDetails);\r\n        if (userData.timeRemaining) {\r\n          setSessionTimeRemaining(userData.timeRemaining);\r\n        }\r\n      } catch (error) {\r\n        console.error('Error parsing user data:', error);\r\n      }\r\n    }\r\n  }, []);\r\n\r\n\r\n  // Start recording voice\r\n  const startRecording = async () => {\r\n    try {\r\n      // Reset recording state\r\n      setRecordingTime(0);\r\n      setAudioBlob(null);\r\n      setAudioUrl('');\r\n\r\n      // Request microphone access\r\n      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });\r\n\r\n      // Create media recorder\r\n      const mediaRecorder = new MediaRecorder(stream);\r\n      mediaRecorderRef.current = mediaRecorder;\r\n\r\n      // Set up data handling\r\n      const audioChunks = [];\r\n      mediaRecorder.ondataavailable = (event) => {\r\n        if (event.data.size > 0) {\r\n          audioChunks.push(event.data);\r\n        }\r\n      };\r\n\r\n      // Handle recording stop\r\n      mediaRecorder.onstop = () => {\r\n        // Create blob from chunks\r\n        const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });\r\n        const audioUrl = URL.createObjectURL(audioBlob);\r\n\r\n        // Update state\r\n        setAudioBlob(audioBlob);\r\n        setAudioUrl(audioUrl);\r\n        setIsRecording(false);\r\n\r\n        // Stop all tracks in the stream\r\n        stream.getTracks().forEach(track => track.stop());\r\n      };\r\n\r\n      // Start recording\r\n      mediaRecorder.start();\r\n      setIsRecording(true);\r\n\r\n      // Set up timer (limit to 2 minutes = 120 seconds)\r\n      const MAX_RECORDING_TIME = 120;\r\n      let seconds = 0;\r\n\r\n      recordingTimerRef.current = setInterval(() => {\r\n        seconds += 1;\r\n        setRecordingTime(seconds);\r\n\r\n        // Auto-stop after 2 minutes\r\n        if (seconds >= MAX_RECORDING_TIME) {\r\n          stopRecording();\r\n        }\r\n      }, 1000);\r\n    } catch (error) {\r\n      console.error('Error starting recording:', error);\r\n      alert('Could not access microphone. Please check your permissions.');\r\n    }\r\n  };\r\n\r\n  // Stop recording\r\n  const stopRecording = () => {\r\n    if (mediaRecorderRef.current && isRecording) {\r\n      mediaRecorderRef.current.stop();\r\n      clearInterval(recordingTimerRef.current);\r\n    }\r\n  };\r\n\r\n  // Play recorded audio\r\n  const playRecording = () => {\r\n    if (audioUrl) {\r\n      const audio = new Audio(audioUrl);\r\n      audio.play();\r\n    }\r\n  };\r\n\r\n  // Delete recorded audio\r\n  const deleteRecording = () => {\r\n    if (audioUrl) {\r\n      URL.revokeObjectURL(audioUrl);\r\n      setAudioBlob(null);\r\n      setAudioUrl('');\r\n      setRecordingTime(0);\r\n    }\r\n  };\r\n\r\n  // Format recording time as MM:SS\r\n  const formatRecordingTime = (seconds) => {\r\n    const minutes = Math.floor(seconds / 60);\r\n    const remainingSeconds = seconds % 60;\r\n    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;\r\n  };\r\n\r\n  // Validate job description word count\r\n  const validateJobDescription = (text) => {\r\n    if (!text) return true;\r\n\r\n    const wordCount = text.trim().split(/\\s+/).length;\r\n    if (wordCount > 10000) {\r\n      setJobDescriptionError(`Job description exceeds 10,000 words (current: ${wordCount})`);\r\n      return false;\r\n    }\r\n\r\n    setJobDescriptionError('');\r\n    return true;\r\n  };\r\n\r\n  // Handle form submission\r\n  const handleSubmit = (e) => {\r\n    e.preventDefault();\r\n\r\n    // Validate form\r\n    if (!jobTitle.trim()) {\r\n      return; // Don't submit if job title is empty\r\n    }\r\n\r\n    if (!validateJobDescription(jobDescription)) {\r\n      return; // Don't submit if job description is too long\r\n    }\r\n\r\n    // Show time alert before starting\r\n    setShowTimeAlert(true);\r\n  };\r\n\r\n  // Handle confirmation from time alert dialog\r\n  const handleTimeAlertConfirm = () => {\r\n    setShowTimeAlert(false);\r\n\r\n    // Create session config\r\n    const config = {\r\n      jobTitle,\r\n      skillset,\r\n      jobDescription,\r\n      focusAreas,\r\n      resumeFile,\r\n      audioBlob,\r\n      sessionTimeRemaining\r\n    };\r\n\r\n    // Pass the configuration to the parent component\r\n    // This will directly show the SpeechToText component from the parent\r\n    onStartSession(config);\r\n  };\r\n\r\n\r\n  //   if (sessionStarted && sessionConfig) {\r\n  //   return (\r\n  //     <InterviewSession config={sessionConfig} onEndSession={() => setSessionStarted(false)} />\r\n  //   );\r\n  // }\r\n\r\n  const handleFocusAreaChange = (area) => {\r\n    if (focusAreas.includes(area)) {\r\n      setFocusAreas(focusAreas.filter(a => a !== area));\r\n    } else {\r\n      setFocusAreas([...focusAreas, area]);\r\n    }\r\n  };\r\n\r\n  const handleFileChange = (e) => {\r\n    const file = e.target.files[0];\r\n    setFileError('');\r\n\r\n    if (!file) {\r\n      setResumeFile(null);\r\n      return;\r\n    }\r\n\r\n    // Check file type\r\n    const fileType = file.type;\r\n    const validTypes = [\r\n      'application/pdf',\r\n      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'\r\n    ];\r\n\r\n    if (!validTypes.includes(fileType)) {\r\n      setFileError('Please upload a PDF or DOCX file');\r\n      setResumeFile(null);\r\n      e.target.value = ''; // Reset file input\r\n      return;\r\n    }\r\n\r\n    // Check file size (limit to 5MB)\r\n    if (file.size > 5 * 1024 * 1024) {\r\n      setFileError('File size should be less than 5MB');\r\n      setResumeFile(null);\r\n      e.target.value = ''; // Reset file input\r\n      return;\r\n    }\r\n\r\n    setResumeFile(file);\r\n  };\r\n\r\n  const clearResume = () => {\r\n    setResumeFile(null);\r\n    setFileError('');\r\n    // Reset the file input\r\n    const fileInput = document.getElementById('resumeUpload');\r\n    if (fileInput) fileInput.value = '';\r\n  };\r\n\r\n  return (\r\n    <div className=\"interview-form\">\r\n      <h2>Simulate Your Interview Session</h2>\r\n      <form onSubmit={handleSubmit}>\r\n        <div className=\"form-group\">\r\n          <label htmlFor=\"jobTitle\">Job Title</label>\r\n          <input\r\n            type=\"text\"\r\n            id=\"jobTitle\"\r\n            value={jobTitle}\r\n            onChange={(e) => setJobTitle(e.target.value)}\r\n            placeholder=\"e.g. Frontend Developer\"\r\n            required\r\n          />\r\n        </div>\r\n\r\n        <div className=\"form-group\">\r\n          <label htmlFor=\"skillset\">Your Skillset</label>\r\n          <input\r\n            type=\"text\"\r\n            id=\"skillset\"\r\n            value={skillset}\r\n            onChange={(e) => setSkillset(e.target.value)}\r\n            placeholder=\"Input skills separated by commas (e.g. React, JavaScript, CSS)\"\r\n          />\r\n          <p className=\"help-text\">Enter your skills separated by commas</p>\r\n        </div>\r\n\r\n        <div className=\"form-group\">\r\n          <label htmlFor=\"jobDescription\">Job Description</label>\r\n          <textarea\r\n            id=\"jobDescription\"\r\n            value={jobDescription}\r\n            onChange={(e) => {\r\n              setJobDescription(e.target.value);\r\n              validateJobDescription(e.target.value);\r\n            }}\r\n            placeholder=\"Paste the job description here (max 10,000 words)\"\r\n            rows={6}\r\n            className=\"job-description-textarea\"\r\n          />\r\n          {jobDescriptionError && <p className=\"error-text\">{jobDescriptionError}</p>}\r\n          <p className=\"help-text\">Paste the job description to get more targeted questions (max 10,000 words)</p>\r\n        </div>\r\n\r\n        <div className=\"form-group\">\r\n          <label>Focus Areas</label>\r\n          <div className=\"checkbox-group\">\r\n            <label>\r\n              <input\r\n                type=\"checkbox\"\r\n                checked={focusAreas.includes('technical')}\r\n                onChange={() => handleFocusAreaChange('technical')}\r\n              />\r\n              Technical Skills\r\n            </label>\r\n            <label>\r\n              <input\r\n                type=\"checkbox\"\r\n                checked={focusAreas.includes('behavioral')}\r\n                onChange={() => handleFocusAreaChange('behavioral')}\r\n              />\r\n              Behavioral Questions\r\n            </label>\r\n            <label>\r\n              <input\r\n                type=\"checkbox\"\r\n                checked={focusAreas.includes('problemSolving')}\r\n                onChange={() => handleFocusAreaChange('problemSolving')}\r\n              />\r\n              Problem Solving\r\n            </label>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"form-group\">\r\n          <label htmlFor=\"resumeUpload\">Resume (Optional)</label>\r\n          <div className=\"file-upload-container\">\r\n            <div className=\"file-input-wrapper\">\r\n              <button type=\"button\" className=\"file-input-button\">\r\n                Choose File\r\n              </button>\r\n              <input\r\n                type=\"file\"\r\n                id=\"resumeUpload\"\r\n                onChange={handleFileChange}\r\n                accept=\".pdf,.docx,application/pdf,application/vnd.openxmlformats-officedocument.wordprocessingml.document\"\r\n                className=\"file-input\"\r\n              />\r\n              <span className=\"file-name\">\r\n                {resumeFile ? resumeFile.name : 'No file chosen'}\r\n              </span>\r\n            </div>\r\n\r\n            {resumeFile && (\r\n              <button\r\n                type=\"button\"\r\n                className=\"clear-file\"\r\n                onClick={clearResume}\r\n              >\r\n                Remove\r\n              </button>\r\n            )}\r\n          </div>\r\n          {fileError && <p className=\"error-text\">{fileError}</p>}\r\n          <p className=\"help-text\">Upload your resume to personalize interview questions (PDF or DOCX, max 5MB)</p>\r\n          <p className=\"help-text\"><strong>Note:</strong> Remove any sensitive information from your resume</p>\r\n        </div>\r\n\r\n        <div className=\"form-group\">\r\n          <label>Record Your Voice (Optional - 2 mins max)</label>\r\n          <div className=\"voice-recorder-container\">\r\n            {!audioUrl ? (\r\n              <div className=\"recording-controls\">\r\n                <button\r\n                  type=\"button\"\r\n                  className={`record-button ${isRecording ? 'recording' : ''}`}\r\n                  onClick={isRecording ? stopRecording : startRecording}\r\n                >\r\n                  {isRecording ? (\r\n                    <>\r\n                      <StopIcon /> Stop Recording ({formatRecordingTime(recordingTime)})\r\n                    </>\r\n                  ) : (\r\n                    <>\r\n                      <MicIcon /> Start Recording\r\n                    </>\r\n                  )}\r\n                </button>\r\n                {isRecording && (\r\n                  <p className=\"recording-indicator\">Recording in progress... ({formatRecordingTime(recordingTime)})</p>\r\n                )}\r\n              </div>\r\n            ) : (\r\n              <div className=\"playback-controls\">\r\n                <div className=\"audio-info\">\r\n                  <span>Recording complete: {formatRecordingTime(recordingTime)}</span>\r\n                </div>\r\n                <div className=\"audio-buttons\">\r\n                  <button type=\"button\" className=\"play-button\" onClick={playRecording}>\r\n                    <PlayArrowIcon /> Play\r\n                  </button>\r\n                  <button type=\"button\" className=\"delete-button\" onClick={deleteRecording}>\r\n                    <DeleteIcon /> Delete\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            )}\r\n            <p className=\"help-text\">Recording your voice helps the AI understand your speaking style and accent</p>\r\n          </div>\r\n        </div>\r\n\r\n        <button type=\"submit\" className=\"start-button\">Start Interview</button>\r\n      </form>\r\n\r\n      {/* Time Alert Dialog */}\r\n      <Dialog\r\n        open={showTimeAlert}\r\n        onClose={() => setShowTimeAlert(false)}\r\n        aria-labelledby=\"alert-dialog-title\"\r\n        aria-describedby=\"alert-dialog-description\"\r\n      >\r\n        <DialogTitle id=\"alert-dialog-title\">\r\n          {\"Session Time Information\"}\r\n        </DialogTitle>\r\n        <DialogContent>\r\n          <DialogContentText id=\"alert-dialog-description\">\r\n            You have {sessionTimeRemaining} minutes remaining in your session.\r\n            Would you like to continue with the interview?\r\n          </DialogContentText>\r\n        </DialogContent>\r\n        <DialogActions>\r\n          <Button onClick={() => setShowTimeAlert(false)} color=\"primary\">\r\n            Cancel\r\n          </Button>\r\n          <Button onClick={handleTimeAlertConfirm} color=\"primary\" autoFocus>\r\n            Continue\r\n          </Button>\r\n        </DialogActions>\r\n      </Dialog>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default InterviewForm;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,MAAM,CAAEC,SAAS,KAAQ,OAAO,CAC1D,MAAO,qBAAqB,CAC5B,MAAO,CAAAC,MAAM,KAAM,sBAAsB,CACzC,MAAO,CAAAC,aAAa,KAAM,6BAA6B,CACvD,MAAO,CAAAC,aAAa,KAAM,6BAA6B,CACvD,MAAO,CAAAC,iBAAiB,KAAM,iCAAiC,CAC/D,MAAO,CAAAC,WAAW,KAAM,2BAA2B,CACnD,MAAO,CAAAC,MAAM,KAAM,sBAAsB,CACzC,MAAO,CAAAC,OAAO,KAAM,yBAAyB,CAC7C,MAAO,CAAAC,QAAQ,KAAM,0BAA0B,CAC/C,MAAO,CAAAC,aAAa,KAAM,+BAA+B,CACzD,MAAO,CAAAC,UAAU,KAAM,4BAA4B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEpD,QAAS,CAAAC,aAAaA,CAAAC,IAAA,CAAqB,IAApB,CAAEC,cAAe,CAAC,CAAAD,IAAA,CACvC;AACA,KAAM,CAACE,QAAQ,CAAEC,WAAW,CAAC,CAAGvB,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACwB,QAAQ,CAAEC,WAAW,CAAC,CAAGzB,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAAC0B,cAAc,CAAEC,iBAAiB,CAAC,CAAG3B,QAAQ,CAAC,EAAE,CAAC,CACxD,KAAM,CAAC4B,UAAU,CAAEC,aAAa,CAAC,CAAG7B,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAAC8B,UAAU,CAAEC,aAAa,CAAC,CAAG/B,QAAQ,CAAC,IAAI,CAAC,CAElD;AACA,KAAM,CAACgC,SAAS,CAAEC,YAAY,CAAC,CAAGjC,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACkC,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGnC,QAAQ,CAAC,EAAE,CAAC,CAElE;AACA,KAAM,CAACoC,WAAW,CAAEC,cAAc,CAAC,CAAGrC,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAACsC,aAAa,CAAEC,gBAAgB,CAAC,CAAGvC,QAAQ,CAAC,CAAC,CAAC,CACrD,KAAM,CAACwC,SAAS,CAAEC,YAAY,CAAC,CAAGzC,QAAQ,CAAC,IAAI,CAAC,CAChD,KAAM,CAAC0C,QAAQ,CAAEC,WAAW,CAAC,CAAG3C,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAAA4C,gBAAgB,CAAG3C,MAAM,CAAC,IAAI,CAAC,CACrC,KAAM,CAAA4C,iBAAiB,CAAG5C,MAAM,CAAC,IAAI,CAAC,CAEtC;AACA,KAAM,CAAC6C,aAAa,CAAEC,gBAAgB,CAAC,CAAG/C,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAACgD,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGjD,QAAQ,CAAC,CAAC,CAAC,CAAE;AAErE;AACAE,SAAS,CAAC,IAAM,CACd,KAAM,CAAAgD,WAAW,CAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC,CACvD,GAAIF,WAAW,CAAE,CACf,GAAI,CACF,KAAM,CAAAG,QAAQ,CAAGC,IAAI,CAACC,KAAK,CAACL,WAAW,CAAC,CACxC,GAAIG,QAAQ,CAACG,aAAa,CAAE,CAC1BP,uBAAuB,CAACI,QAAQ,CAACG,aAAa,CAAC,CACjD,CACF,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAClD,CACF,CACF,CAAC,CAAE,EAAE,CAAC,CAGN;AACA,KAAM,CAAAE,cAAc,CAAG,KAAAA,CAAA,GAAY,CACjC,GAAI,CACF;AACApB,gBAAgB,CAAC,CAAC,CAAC,CACnBE,YAAY,CAAC,IAAI,CAAC,CAClBE,WAAW,CAAC,EAAE,CAAC,CAEf;AACA,KAAM,CAAAiB,MAAM,CAAG,KAAM,CAAAC,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC,CAAEC,KAAK,CAAE,IAAK,CAAC,CAAC,CAEzE;AACA,KAAM,CAAAC,aAAa,CAAG,GAAI,CAAAC,aAAa,CAACN,MAAM,CAAC,CAC/ChB,gBAAgB,CAACuB,OAAO,CAAGF,aAAa,CAExC;AACA,KAAM,CAAAG,WAAW,CAAG,EAAE,CACtBH,aAAa,CAACI,eAAe,CAAIC,KAAK,EAAK,CACzC,GAAIA,KAAK,CAACC,IAAI,CAACC,IAAI,CAAG,CAAC,CAAE,CACvBJ,WAAW,CAACK,IAAI,CAACH,KAAK,CAACC,IAAI,CAAC,CAC9B,CACF,CAAC,CAED;AACAN,aAAa,CAACS,MAAM,CAAG,IAAM,CAC3B;AACA,KAAM,CAAAlC,SAAS,CAAG,GAAI,CAAAmC,IAAI,CAACP,WAAW,CAAE,CAAEQ,IAAI,CAAE,WAAY,CAAC,CAAC,CAC9D,KAAM,CAAAlC,QAAQ,CAAGmC,GAAG,CAACC,eAAe,CAACtC,SAAS,CAAC,CAE/C;AACAC,YAAY,CAACD,SAAS,CAAC,CACvBG,WAAW,CAACD,QAAQ,CAAC,CACrBL,cAAc,CAAC,KAAK,CAAC,CAErB;AACAuB,MAAM,CAACmB,SAAS,CAAC,CAAC,CAACC,OAAO,CAACC,KAAK,EAAIA,KAAK,CAACC,IAAI,CAAC,CAAC,CAAC,CACnD,CAAC,CAED;AACAjB,aAAa,CAACkB,KAAK,CAAC,CAAC,CACrB9C,cAAc,CAAC,IAAI,CAAC,CAEpB;AACA,KAAM,CAAA+C,kBAAkB,CAAG,GAAG,CAC9B,GAAI,CAAAC,OAAO,CAAG,CAAC,CAEfxC,iBAAiB,CAACsB,OAAO,CAAGmB,WAAW,CAAC,IAAM,CAC5CD,OAAO,EAAI,CAAC,CACZ9C,gBAAgB,CAAC8C,OAAO,CAAC,CAEzB;AACA,GAAIA,OAAO,EAAID,kBAAkB,CAAE,CACjCG,aAAa,CAAC,CAAC,CACjB,CACF,CAAC,CAAE,IAAI,CAAC,CACV,CAAE,MAAO9B,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,CAAEA,KAAK,CAAC,CACjD+B,KAAK,CAAC,6DAA6D,CAAC,CACtE,CACF,CAAC,CAED;AACA,KAAM,CAAAD,aAAa,CAAGA,CAAA,GAAM,CAC1B,GAAI3C,gBAAgB,CAACuB,OAAO,EAAI/B,WAAW,CAAE,CAC3CQ,gBAAgB,CAACuB,OAAO,CAACe,IAAI,CAAC,CAAC,CAC/BO,aAAa,CAAC5C,iBAAiB,CAACsB,OAAO,CAAC,CAC1C,CACF,CAAC,CAED;AACA,KAAM,CAAAuB,aAAa,CAAGA,CAAA,GAAM,CAC1B,GAAIhD,QAAQ,CAAE,CACZ,KAAM,CAAAsB,KAAK,CAAG,GAAI,CAAA2B,KAAK,CAACjD,QAAQ,CAAC,CACjCsB,KAAK,CAAC4B,IAAI,CAAC,CAAC,CACd,CACF,CAAC,CAED;AACA,KAAM,CAAAC,eAAe,CAAGA,CAAA,GAAM,CAC5B,GAAInD,QAAQ,CAAE,CACZmC,GAAG,CAACiB,eAAe,CAACpD,QAAQ,CAAC,CAC7BD,YAAY,CAAC,IAAI,CAAC,CAClBE,WAAW,CAAC,EAAE,CAAC,CACfJ,gBAAgB,CAAC,CAAC,CAAC,CACrB,CACF,CAAC,CAED;AACA,KAAM,CAAAwD,mBAAmB,CAAIV,OAAO,EAAK,CACvC,KAAM,CAAAW,OAAO,CAAGC,IAAI,CAACC,KAAK,CAACb,OAAO,CAAG,EAAE,CAAC,CACxC,KAAM,CAAAc,gBAAgB,CAAGd,OAAO,CAAG,EAAE,CACrC,SAAAe,MAAA,CAAUJ,OAAO,CAACK,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,MAAAF,MAAA,CAAID,gBAAgB,CAACE,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,EAC/F,CAAC,CAED;AACA,KAAM,CAAAC,sBAAsB,CAAIC,IAAI,EAAK,CACvC,GAAI,CAACA,IAAI,CAAE,MAAO,KAAI,CAEtB,KAAM,CAAAC,SAAS,CAAGD,IAAI,CAACE,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,KAAK,CAAC,CAACC,MAAM,CACjD,GAAIH,SAAS,CAAG,KAAK,CAAE,CACrBtE,sBAAsB,mDAAAiE,MAAA,CAAmDK,SAAS,KAAG,CAAC,CACtF,MAAO,MAAK,CACd,CAEAtE,sBAAsB,CAAC,EAAE,CAAC,CAC1B,MAAO,KAAI,CACb,CAAC,CAED;AACA,KAAM,CAAA0E,YAAY,CAAIC,CAAC,EAAK,CAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC,CAElB;AACA,GAAI,CAACzF,QAAQ,CAACoF,IAAI,CAAC,CAAC,CAAE,CACpB,OAAQ;AACV,CAEA,GAAI,CAACH,sBAAsB,CAAC7E,cAAc,CAAC,CAAE,CAC3C,OAAQ;AACV,CAEA;AACAqB,gBAAgB,CAAC,IAAI,CAAC,CACxB,CAAC,CAED;AACA,KAAM,CAAAiE,sBAAsB,CAAGA,CAAA,GAAM,CACnCjE,gBAAgB,CAAC,KAAK,CAAC,CAEvB;AACA,KAAM,CAAAkE,MAAM,CAAG,CACb3F,QAAQ,CACRE,QAAQ,CACRE,cAAc,CACdE,UAAU,CACVE,UAAU,CACVU,SAAS,CACTQ,oBACF,CAAC,CAED;AACA;AACA3B,cAAc,CAAC4F,MAAM,CAAC,CACxB,CAAC,CAGD;AACA;AACA;AACA;AACA;AAEA,KAAM,CAAAC,qBAAqB,CAAIC,IAAI,EAAK,CACtC,GAAIvF,UAAU,CAACwF,QAAQ,CAACD,IAAI,CAAC,CAAE,CAC7BtF,aAAa,CAACD,UAAU,CAACyF,MAAM,CAACC,CAAC,EAAIA,CAAC,GAAKH,IAAI,CAAC,CAAC,CACnD,CAAC,IAAM,CACLtF,aAAa,CAAC,CAAC,GAAGD,UAAU,CAAEuF,IAAI,CAAC,CAAC,CACtC,CACF,CAAC,CAED,KAAM,CAAAI,gBAAgB,CAAIT,CAAC,EAAK,CAC9B,KAAM,CAAAU,IAAI,CAAGV,CAAC,CAACW,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAC9BzF,YAAY,CAAC,EAAE,CAAC,CAEhB,GAAI,CAACuF,IAAI,CAAE,CACTzF,aAAa,CAAC,IAAI,CAAC,CACnB,OACF,CAEA;AACA,KAAM,CAAA4F,QAAQ,CAAGH,IAAI,CAAC5C,IAAI,CAC1B,KAAM,CAAAgD,UAAU,CAAG,CACjB,iBAAiB,CACjB,yEAAyE,CAC1E,CAED,GAAI,CAACA,UAAU,CAACR,QAAQ,CAACO,QAAQ,CAAC,CAAE,CAClC1F,YAAY,CAAC,kCAAkC,CAAC,CAChDF,aAAa,CAAC,IAAI,CAAC,CACnB+E,CAAC,CAACW,MAAM,CAACI,KAAK,CAAG,EAAE,CAAE;AACrB,OACF,CAEA;AACA,GAAIL,IAAI,CAAChD,IAAI,CAAG,CAAC,CAAG,IAAI,CAAG,IAAI,CAAE,CAC/BvC,YAAY,CAAC,mCAAmC,CAAC,CACjDF,aAAa,CAAC,IAAI,CAAC,CACnB+E,CAAC,CAACW,MAAM,CAACI,KAAK,CAAG,EAAE,CAAE;AACrB,OACF,CAEA9F,aAAa,CAACyF,IAAI,CAAC,CACrB,CAAC,CAED,KAAM,CAAAM,WAAW,CAAGA,CAAA,GAAM,CACxB/F,aAAa,CAAC,IAAI,CAAC,CACnBE,YAAY,CAAC,EAAE,CAAC,CAChB;AACA,KAAM,CAAA8F,SAAS,CAAGC,QAAQ,CAACC,cAAc,CAAC,cAAc,CAAC,CACzD,GAAIF,SAAS,CAAEA,SAAS,CAACF,KAAK,CAAG,EAAE,CACrC,CAAC,CAED,mBACE7G,KAAA,QAAKkH,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BrH,IAAA,OAAAqH,QAAA,CAAI,iCAA+B,CAAI,CAAC,cACxCnH,KAAA,SAAMoH,QAAQ,CAAEvB,YAAa,CAAAsB,QAAA,eAC3BnH,KAAA,QAAKkH,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBrH,IAAA,UAAOuH,OAAO,CAAC,UAAU,CAAAF,QAAA,CAAC,WAAS,CAAO,CAAC,cAC3CrH,IAAA,UACE8D,IAAI,CAAC,MAAM,CACX0D,EAAE,CAAC,UAAU,CACbT,KAAK,CAAEvG,QAAS,CAChBiH,QAAQ,CAAGzB,CAAC,EAAKvF,WAAW,CAACuF,CAAC,CAACW,MAAM,CAACI,KAAK,CAAE,CAC7CW,WAAW,CAAC,yBAAyB,CACrCC,QAAQ,MACT,CAAC,EACC,CAAC,cAENzH,KAAA,QAAKkH,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBrH,IAAA,UAAOuH,OAAO,CAAC,UAAU,CAAAF,QAAA,CAAC,eAAa,CAAO,CAAC,cAC/CrH,IAAA,UACE8D,IAAI,CAAC,MAAM,CACX0D,EAAE,CAAC,UAAU,CACbT,KAAK,CAAErG,QAAS,CAChB+G,QAAQ,CAAGzB,CAAC,EAAKrF,WAAW,CAACqF,CAAC,CAACW,MAAM,CAACI,KAAK,CAAE,CAC7CW,WAAW,CAAC,gEAAgE,CAC7E,CAAC,cACF1H,IAAA,MAAGoH,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAC,uCAAqC,CAAG,CAAC,EAC/D,CAAC,cAENnH,KAAA,QAAKkH,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBrH,IAAA,UAAOuH,OAAO,CAAC,gBAAgB,CAAAF,QAAA,CAAC,iBAAe,CAAO,CAAC,cACvDrH,IAAA,aACEwH,EAAE,CAAC,gBAAgB,CACnBT,KAAK,CAAEnG,cAAe,CACtB6G,QAAQ,CAAGzB,CAAC,EAAK,CACfnF,iBAAiB,CAACmF,CAAC,CAACW,MAAM,CAACI,KAAK,CAAC,CACjCtB,sBAAsB,CAACO,CAAC,CAACW,MAAM,CAACI,KAAK,CAAC,CACxC,CAAE,CACFW,WAAW,CAAC,mDAAmD,CAC/DE,IAAI,CAAE,CAAE,CACRR,SAAS,CAAC,0BAA0B,CACrC,CAAC,CACDhG,mBAAmB,eAAIpB,IAAA,MAAGoH,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAEjG,mBAAmB,CAAI,CAAC,cAC3EpB,IAAA,MAAGoH,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAC,6EAA2E,CAAG,CAAC,EACrG,CAAC,cAENnH,KAAA,QAAKkH,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBrH,IAAA,UAAAqH,QAAA,CAAO,aAAW,CAAO,CAAC,cAC1BnH,KAAA,QAAKkH,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BnH,KAAA,UAAAmH,QAAA,eACErH,IAAA,UACE8D,IAAI,CAAC,UAAU,CACf+D,OAAO,CAAE/G,UAAU,CAACwF,QAAQ,CAAC,WAAW,CAAE,CAC1CmB,QAAQ,CAAEA,CAAA,GAAMrB,qBAAqB,CAAC,WAAW,CAAE,CACpD,CAAC,mBAEJ,EAAO,CAAC,cACRlG,KAAA,UAAAmH,QAAA,eACErH,IAAA,UACE8D,IAAI,CAAC,UAAU,CACf+D,OAAO,CAAE/G,UAAU,CAACwF,QAAQ,CAAC,YAAY,CAAE,CAC3CmB,QAAQ,CAAEA,CAAA,GAAMrB,qBAAqB,CAAC,YAAY,CAAE,CACrD,CAAC,uBAEJ,EAAO,CAAC,cACRlG,KAAA,UAAAmH,QAAA,eACErH,IAAA,UACE8D,IAAI,CAAC,UAAU,CACf+D,OAAO,CAAE/G,UAAU,CAACwF,QAAQ,CAAC,gBAAgB,CAAE,CAC/CmB,QAAQ,CAAEA,CAAA,GAAMrB,qBAAqB,CAAC,gBAAgB,CAAE,CACzD,CAAC,kBAEJ,EAAO,CAAC,EACL,CAAC,EACH,CAAC,cAENlG,KAAA,QAAKkH,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBrH,IAAA,UAAOuH,OAAO,CAAC,cAAc,CAAAF,QAAA,CAAC,mBAAiB,CAAO,CAAC,cACvDnH,KAAA,QAAKkH,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpCnH,KAAA,QAAKkH,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACjCrH,IAAA,WAAQ8D,IAAI,CAAC,QAAQ,CAACsD,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAAC,aAEpD,CAAQ,CAAC,cACTrH,IAAA,UACE8D,IAAI,CAAC,MAAM,CACX0D,EAAE,CAAC,cAAc,CACjBC,QAAQ,CAAEhB,gBAAiB,CAC3BqB,MAAM,CAAC,oGAAoG,CAC3GV,SAAS,CAAC,YAAY,CACvB,CAAC,cACFpH,IAAA,SAAMoH,SAAS,CAAC,WAAW,CAAAC,QAAA,CACxBrG,UAAU,CAAGA,UAAU,CAAC+G,IAAI,CAAG,gBAAgB,CAC5C,CAAC,EACJ,CAAC,CAEL/G,UAAU,eACThB,IAAA,WACE8D,IAAI,CAAC,QAAQ,CACbsD,SAAS,CAAC,YAAY,CACtBY,OAAO,CAAEhB,WAAY,CAAAK,QAAA,CACtB,QAED,CAAQ,CACT,EACE,CAAC,CACLnG,SAAS,eAAIlB,IAAA,MAAGoH,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAEnG,SAAS,CAAI,CAAC,cACvDlB,IAAA,MAAGoH,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAC,8EAA4E,CAAG,CAAC,cACzGnH,KAAA,MAAGkH,SAAS,CAAC,WAAW,CAAAC,QAAA,eAACrH,IAAA,WAAAqH,QAAA,CAAQ,OAAK,CAAQ,CAAC,qDAAkD,EAAG,CAAC,EAClG,CAAC,cAENnH,KAAA,QAAKkH,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBrH,IAAA,UAAAqH,QAAA,CAAO,2CAAyC,CAAO,CAAC,cACxDnH,KAAA,QAAKkH,SAAS,CAAC,0BAA0B,CAAAC,QAAA,EACtC,CAACzF,QAAQ,cACR1B,KAAA,QAAKkH,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACjCrH,IAAA,WACE8D,IAAI,CAAC,QAAQ,CACbsD,SAAS,kBAAA9B,MAAA,CAAmBhE,WAAW,CAAG,WAAW,CAAG,EAAE,CAAG,CAC7D0G,OAAO,CAAE1G,WAAW,CAAGmD,aAAa,CAAG5B,cAAe,CAAAwE,QAAA,CAErD/F,WAAW,cACVpB,KAAA,CAAAE,SAAA,EAAAiH,QAAA,eACErH,IAAA,CAACJ,QAAQ,GAAE,CAAC,oBAAiB,CAACqF,mBAAmB,CAACzD,aAAa,CAAC,CAAC,GACnE,EAAE,CAAC,cAEHtB,KAAA,CAAAE,SAAA,EAAAiH,QAAA,eACErH,IAAA,CAACL,OAAO,GAAE,CAAC,mBACb,EAAE,CACH,CACK,CAAC,CACR2B,WAAW,eACVpB,KAAA,MAAGkH,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EAAC,4BAA0B,CAACpC,mBAAmB,CAACzD,aAAa,CAAC,CAAC,GAAC,EAAG,CACtG,EACE,CAAC,cAENtB,KAAA,QAAKkH,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCrH,IAAA,QAAKoH,SAAS,CAAC,YAAY,CAAAC,QAAA,cACzBnH,KAAA,SAAAmH,QAAA,EAAM,sBAAoB,CAACpC,mBAAmB,CAACzD,aAAa,CAAC,EAAO,CAAC,CAClE,CAAC,cACNtB,KAAA,QAAKkH,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BnH,KAAA,WAAQ4D,IAAI,CAAC,QAAQ,CAACsD,SAAS,CAAC,aAAa,CAACY,OAAO,CAAEpD,aAAc,CAAAyC,QAAA,eACnErH,IAAA,CAACH,aAAa,GAAE,CAAC,QACnB,EAAQ,CAAC,cACTK,KAAA,WAAQ4D,IAAI,CAAC,QAAQ,CAACsD,SAAS,CAAC,eAAe,CAACY,OAAO,CAAEjD,eAAgB,CAAAsC,QAAA,eACvErH,IAAA,CAACF,UAAU,GAAE,CAAC,UAChB,EAAQ,CAAC,EACN,CAAC,EACH,CACN,cACDE,IAAA,MAAGoH,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAC,6EAA2E,CAAG,CAAC,EACrG,CAAC,EACH,CAAC,cAENrH,IAAA,WAAQ8D,IAAI,CAAC,QAAQ,CAACsD,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,iBAAe,CAAQ,CAAC,EACnE,CAAC,cAGPnH,KAAA,CAACb,MAAM,EACL4I,IAAI,CAAEjG,aAAc,CACpBkG,OAAO,CAAEA,CAAA,GAAMjG,gBAAgB,CAAC,KAAK,CAAE,CACvC,kBAAgB,oBAAoB,CACpC,mBAAiB,0BAA0B,CAAAoF,QAAA,eAE3CrH,IAAA,CAACP,WAAW,EAAC+H,EAAE,CAAC,oBAAoB,CAAAH,QAAA,CACjC,0BAA0B,CAChB,CAAC,cACdrH,IAAA,CAACT,aAAa,EAAA8H,QAAA,cACZnH,KAAA,CAACV,iBAAiB,EAACgI,EAAE,CAAC,0BAA0B,CAAAH,QAAA,EAAC,WACtC,CAACnF,oBAAoB,CAAC,oFAEjC,EAAmB,CAAC,CACP,CAAC,cAChBhC,KAAA,CAACZ,aAAa,EAAA+H,QAAA,eACZrH,IAAA,CAACN,MAAM,EAACsI,OAAO,CAAEA,CAAA,GAAM/F,gBAAgB,CAAC,KAAK,CAAE,CAACkG,KAAK,CAAC,SAAS,CAAAd,QAAA,CAAC,QAEhE,CAAQ,CAAC,cACTrH,IAAA,CAACN,MAAM,EAACsI,OAAO,CAAE9B,sBAAuB,CAACiC,KAAK,CAAC,SAAS,CAACC,SAAS,MAAAf,QAAA,CAAC,UAEnE,CAAQ,CAAC,EACI,CAAC,EACV,CAAC,EACN,CAAC,CAEV,CAEA,cAAe,CAAAhH,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}