{"ast": null, "code": "import React,{useState,useRef,useEffect,useCallback}from'react';import'./SpeechToText.css';import env from'../utils/env';import MicIcon from'@mui/icons-material/Mic';import StopIcon from'@mui/icons-material/Stop';import SendIcon from'@mui/icons-material/Send';import AccessTimeIcon from'@mui/icons-material/AccessTime';import CloseIcon from'@mui/icons-material/Close';import Dialog from'@mui/material/Dialog';import DialogActions from'@mui/material/DialogActions';import DialogContent from'@mui/material/DialogContent';import DialogContentText from'@mui/material/DialogContentText';import DialogTitle from'@mui/material/DialogTitle';import Button from'@mui/material/Button';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function SpeechToText(){const[isListening,setIsListening]=useState(false);const[transcript,setTranscript]=useState('');const[response,setResponse]=useState('');const[isLoading,setIsLoading]=useState(false);// Session timer state (15 minutes = 900 seconds)\nconst SESSION_DURATION=300;// 15 minutes in seconds\nconst WARNING_TIME=60;// Show warning when 60 seconds remain\nconst[sessionTimeRemaining,setSessionTimeRemaining]=useState(SESSION_DURATION);const[showPaymentDialog,setShowPaymentDialog]=useState(false);const[sessionExpired,setSessionExpired]=useState(false);// Session timer reference\nconst sessionTimerRef=useRef(null);// Speech recognition and text area references\nconst recognitionRef=useRef(null);const transcriptAreaRef=useRef(null);const responseAreaRef=useRef(null);// Format session time remaining (MM:SS)\nconst formatSessionTime=useCallback(totalSeconds=>{const minutes=Math.floor(totalSeconds/60);const seconds=totalSeconds%60;return\"\".concat(minutes.toString().padStart(2,'0'),\":\").concat(seconds.toString().padStart(2,'0'));},[]);// Start the session timer\nconst startSessionTimer=useCallback(()=>{// Reset session timer when starting\nsetSessionTimeRemaining(SESSION_DURATION);setSessionExpired(false);// Clear any existing interval\nif(sessionTimerRef.current){clearInterval(sessionTimerRef.current);}// Start a new interval that counts down\nsessionTimerRef.current=setInterval(()=>{setSessionTimeRemaining(prev=>{const newTime=prev-1;// Show payment dialog when 1 minute remains\nif(newTime===WARNING_TIME){setShowPaymentDialog(true);}// Session expired\nif(newTime<=0){clearInterval(sessionTimerRef.current);sessionTimerRef.current=null;setSessionExpired(true);return 0;}return newTime;});},1000);},[SESSION_DURATION,WARNING_TIME]);// Handle payment dialog close\nconst handlePaymentDialogClose=useCallback(()=>{setShowPaymentDialog(false);},[]);// Handle payment confirmation\nconst handlePaymentConfirm=useCallback(()=>{// Reset the session timer\nsetShowPaymentDialog(false);setSessionTimeRemaining(SESSION_DURATION);setSessionExpired(false);// Restart the session timer\nif(sessionTimerRef.current){clearInterval(sessionTimerRef.current);}sessionTimerRef.current=setInterval(()=>{setSessionTimeRemaining(prev=>{const newTime=prev-1;// Show payment dialog when 1 minute remains\nif(newTime===WARNING_TIME){setShowPaymentDialog(true);}// Session expired\nif(newTime<=0){clearInterval(sessionTimerRef.current);sessionTimerRef.current=null;setSessionExpired(true);return 0;}return newTime;});},1000);},[SESSION_DURATION,WARNING_TIME]);// Start session timer on component mount\nuseEffect(()=>{startSessionTimer();// Clean up session timer on unmount\nreturn()=>{if(sessionTimerRef.current){clearInterval(sessionTimerRef.current);}};},[startSessionTimer]);useEffect(()=>{// Initialize speech recognition\nconst SpeechRecognition=window.SpeechRecognition||window.webkitSpeechRecognition;if(SpeechRecognition){recognitionRef.current=new SpeechRecognition();recognitionRef.current.lang='en-US';recognitionRef.current.interimResults=true;recognitionRef.current.continuous=true;let finalTranscript=\"\";recognitionRef.current.onresult=event=>{let interimTranscript=\"\";for(let i=event.resultIndex;i<event.results.length;i++){const transcript=event.results[i][0].transcript;if(event.results[i].isFinal){finalTranscript+=transcript+\" \";}else{interimTranscript+=transcript;}}setTranscript(finalTranscript+interimTranscript);};recognitionRef.current.onerror=event=>{console.error(\"Speech recognition error\",event.error);alert(\"Error occurred: \"+event.error);setIsListening(false);};}else{alert(\"Your browser doesn't support speech recognition. Try Chrome or Edge.\");}// Cleanup function\nreturn()=>{if(recognitionRef.current){recognitionRef.current.stop();}};},[]);useEffect(()=>{// Auto-scroll transcript area when content changes\nif(transcriptAreaRef.current){transcriptAreaRef.current.scrollTop=transcriptAreaRef.current.scrollHeight;}},[transcript]);useEffect(()=>{// Auto-scroll response area when content changes\nif(responseAreaRef.current){responseAreaRef.current.scrollTop=responseAreaRef.current.scrollHeight;}},[response]);const startListening=()=>{if(!isListening&&!sessionExpired){try{// Clear the transcript when starting a new recording\nsetTranscript('');// Stop any existing recognition\nif(recognitionRef.current){try{recognitionRef.current.stop();}catch(e){// Ignore errors when stopping\n}}// Create a completely new recognition instance\nconst SpeechRecognition=window.SpeechRecognition||window.webkitSpeechRecognition;recognitionRef.current=new SpeechRecognition();recognitionRef.current.lang='en-US';recognitionRef.current.interimResults=true;recognitionRef.current.continuous=true;// Set up event handlers with a fresh finalTranscript\nlet finalTranscript=\"\";recognitionRef.current.onresult=event=>{let interimTranscript=\"\";for(let i=event.resultIndex;i<event.results.length;i++){const transcript=event.results[i][0].transcript;if(event.results[i].isFinal){finalTranscript+=transcript+\" \";}else{interimTranscript+=transcript;}}// Only update with the transcript from this recognition instance\nsetTranscript(finalTranscript+interimTranscript);};recognitionRef.current.onerror=event=>{console.error(\"Speech recognition error\",event.error);setIsListening(false);};// Start the new recognition instance\nrecognitionRef.current.start();setIsListening(true);}catch(error){console.error(\"Speech recognition error:\",error);}}};const stopListening=()=>{if(recognitionRef.current&&isListening){try{recognitionRef.current.stop();setIsListening(false);}catch(error){console.error(\"Speech recognition error:\",error);}}};const sendToGPT=async()=>{const apiKey=env.OPENAI_API_KEY;const userText=transcript.trim();console.log(\"Environment:\",process.env.NODE_ENV);console.log(\"API Key available:\",apiKey?\"Yes\":\"No\");if(!apiKey){alert(\"Please add your OpenAI API key to the .env file as REACT_APP_OPENAI_API_KEY and restart the development server.\");return;}if(!userText){alert(\"Please record or enter some text to send to GPT.\");return;}const prompt=\"In 5 lines, give only the definition and a simple example. \".concat(userText);setIsLoading(true);setResponse('');try{const response=await fetch(\"https://api.openai.com/v1/chat/completions\",{method:\"POST\",headers:{\"Content-Type\":\"application/json\",\"Authorization\":\"Bearer \".concat(apiKey)},body:JSON.stringify({model:\"gpt-4.1-nano\",messages:[{role:\"user\",content:prompt}],stream:true})});if(!response.ok||!response.body)throw new Error(\"Failed to stream response.\");const reader=response.body.getReader();const decoder=new TextDecoder(\"utf-8\");let result=\"\";while(true){const{value,done}=await reader.read();if(done)break;const chunk=decoder.decode(value,{stream:true});const lines=chunk.split(\"\\n\").filter(line=>line.trim().startsWith(\"data:\"));for(const line of lines){const data=line.replace(/^data: /,'');if(data===\"[DONE]\")break;try{var _json$choices,_json$choices$,_json$choices$$delta;const json=JSON.parse(data);const content=(_json$choices=json.choices)===null||_json$choices===void 0?void 0:(_json$choices$=_json$choices[0])===null||_json$choices$===void 0?void 0:(_json$choices$$delta=_json$choices$.delta)===null||_json$choices$$delta===void 0?void 0:_json$choices$$delta.content;if(content){result+=content;setResponse(result);}}catch(e){console.error(\"Error parsing JSON:\",e);}}}}catch(error){console.error(\"Streaming Error:\",error);setResponse(\"Error occurred: \"+error.message);}finally{if(isListening){stopListening();}setIsLoading(false);// Clear the transcript after getting the answer\nsetTranscript('');}};// Handle exit button click\nconst handleExit=useCallback(()=>{// Navigate back to home page\nwindow.location.href='/home';},[]);return/*#__PURE__*/_jsxs(\"div\",{className:\"speech-to-text\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"exit-button\",onClick:handleExit,title:\"Exit\",children:/*#__PURE__*/_jsx(CloseIcon,{})}),/*#__PURE__*/_jsxs(\"div\",{className:\"timer-container\",children:[/*#__PURE__*/_jsx(AccessTimeIcon,{className:\"timer-icon\"}),/*#__PURE__*/_jsx(\"span\",{className:\"timer-display session-timer\",children:formatSessionTime(sessionTimeRemaining)}),isListening&&/*#__PURE__*/_jsx(\"span\",{className:\"listening-indicator\",children:\"Recording...\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-container\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"answer-container\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"section-header\",children:/*#__PURE__*/_jsx(\"h3\",{children:\"AI Response\"})}),/*#__PURE__*/_jsx(\"textarea\",{ref:responseAreaRef,className:\"response-area\",value:response,readOnly:true,placeholder:\"AI answer will appear here...\",disabled:sessionExpired})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"question-container\",children:[/*#__PURE__*/_jsx(\"textarea\",{ref:transcriptAreaRef,className:\"transcript-area\",value:transcript,onChange:e=>setTranscript(e.target.value),placeholder:sessionExpired?\"Session expired. Please make a payment to continue.\":\"Type or record your question here...\",disabled:sessionExpired}),/*#__PURE__*/_jsxs(\"div\",{className:\"control-group\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"mic-button \".concat(isListening?'listening':''),onClick:isListening?stopListening:startListening,title:isListening?\"Stop Recording\":\"Start Recording\",disabled:sessionExpired,children:isListening?/*#__PURE__*/_jsx(StopIcon,{}):/*#__PURE__*/_jsx(MicIcon,{})}),/*#__PURE__*/_jsx(\"button\",{className:\"send-button\",onClick:sendToGPT,disabled:isLoading||!transcript.trim()||sessionExpired,title:\"Get Answer\",children:isLoading?\"...\":/*#__PURE__*/_jsx(SendIcon,{})})]})]})]}),/*#__PURE__*/_jsxs(Dialog,{open:showPaymentDialog,onClose:handlePaymentDialogClose,\"aria-labelledby\":\"payment-dialog-title\",\"aria-describedby\":\"payment-dialog-description\",children:[/*#__PURE__*/_jsx(DialogTitle,{id:\"payment-dialog-title\",children:\"Session Expiring Soon\"}),/*#__PURE__*/_jsx(DialogContent,{children:/*#__PURE__*/_jsx(DialogContentText,{id:\"payment-dialog-description\",children:\"Your session will expire in one minute. Would you like to make a payment to extend your session?\"})}),/*#__PURE__*/_jsxs(DialogActions,{children:[/*#__PURE__*/_jsx(Button,{onClick:handlePaymentDialogClose,color:\"primary\",children:\"Not Now\"}),/*#__PURE__*/_jsx(Button,{onClick:handlePaymentConfirm,color:\"primary\",autoFocus:true,children:\"Make Payment\"})]})]}),/*#__PURE__*/_jsxs(Dialog,{open:sessionExpired,\"aria-labelledby\":\"expired-dialog-title\",\"aria-describedby\":\"expired-dialog-description\",children:[/*#__PURE__*/_jsx(DialogTitle,{id:\"expired-dialog-title\",children:\"Session Expired\"}),/*#__PURE__*/_jsx(DialogContent,{children:/*#__PURE__*/_jsx(DialogContentText,{id:\"expired-dialog-description\",children:\"Your session has expired. Please make a payment to continue using the service.\"})}),/*#__PURE__*/_jsxs(DialogActions,{children:[/*#__PURE__*/_jsx(Button,{onClick:handleExit,color:\"primary\",children:\"Exit\"}),/*#__PURE__*/_jsx(Button,{onClick:handlePaymentConfirm,color:\"primary\",autoFocus:true,children:\"Make Payment\"})]})]})]});}export default SpeechToText;", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "useCallback", "env", "MicIcon", "StopIcon", "SendIcon", "AccessTimeIcon", "CloseIcon", "Dialog", "DialogActions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogContentText", "DialogTitle", "<PERSON><PERSON>", "jsx", "_jsx", "jsxs", "_jsxs", "SpeechToText", "isListening", "setIsListening", "transcript", "setTranscript", "response", "setResponse", "isLoading", "setIsLoading", "SESSION_DURATION", "WARNING_TIME", "sessionTimeRemaining", "setSessionTimeRemaining", "showPaymentDialog", "setShowPaymentDialog", "sessionExpired", "setSessionExpired", "sessionTimerRef", "recognitionRef", "transcriptAreaRef", "responseAreaRef", "formatSessionTime", "totalSeconds", "minutes", "Math", "floor", "seconds", "concat", "toString", "padStart", "startSessionTimer", "current", "clearInterval", "setInterval", "prev", "newTime", "handlePaymentDialogClose", "handlePaymentConfirm", "SpeechRecognition", "window", "webkitSpeechRecognition", "lang", "interimResults", "continuous", "finalTranscript", "on<PERSON>ult", "event", "interimTranscript", "i", "resultIndex", "results", "length", "isFinal", "onerror", "console", "error", "alert", "stop", "scrollTop", "scrollHeight", "startListening", "e", "start", "stopListening", "sendToGPT", "<PERSON><PERSON><PERSON><PERSON>", "OPENAI_API_KEY", "userText", "trim", "log", "process", "NODE_ENV", "prompt", "fetch", "method", "headers", "body", "JSON", "stringify", "model", "messages", "role", "content", "stream", "ok", "Error", "reader", "<PERSON><PERSON><PERSON><PERSON>", "decoder", "TextDecoder", "result", "value", "done", "read", "chunk", "decode", "lines", "split", "filter", "line", "startsWith", "data", "replace", "_json$choices", "_json$choices$", "_json$choices$$delta", "json", "parse", "choices", "delta", "message", "handleExit", "location", "href", "className", "children", "onClick", "title", "ref", "readOnly", "placeholder", "disabled", "onChange", "target", "open", "onClose", "id", "color", "autoFocus"], "sources": ["C:/Users/<USER>/Documents/augment-projects/interview-ai-app/interviewAI/client/src/components/SpeechToText.jsx"], "sourcesContent": ["import React, { useState, useRef, useEffect, useCallback } from 'react';\r\nimport './SpeechToText.css';\r\nimport env from '../utils/env';\r\nimport MicIcon from '@mui/icons-material/Mic';\r\nimport StopIcon from '@mui/icons-material/Stop';\r\nimport SendIcon from '@mui/icons-material/Send';\r\nimport AccessTimeIcon from '@mui/icons-material/AccessTime';\r\nimport CloseIcon from '@mui/icons-material/Close';\r\nimport Dialog from '@mui/material/Dialog';\r\nimport DialogActions from '@mui/material/DialogActions';\r\nimport DialogContent from '@mui/material/DialogContent';\r\nimport DialogContentText from '@mui/material/DialogContentText';\r\nimport DialogTitle from '@mui/material/DialogTitle';\r\nimport Button from '@mui/material/Button';\r\n\r\nfunction SpeechToText() {\r\n  const [isListening, setIsListening] = useState(false);\r\n  const [transcript, setTranscript] = useState('');\r\n  const [response, setResponse] = useState('');\r\n  const [isLoading, setIsLoading] = useState(false);\r\n\r\n  // Session timer state (15 minutes = 900 seconds)\r\n  const SESSION_DURATION = 300; // 15 minutes in seconds\r\n  const WARNING_TIME = 60; // Show warning when 60 seconds remain\r\n  const [sessionTimeRemaining, setSessionTimeRemaining] = useState(SESSION_DURATION);\r\n  const [showPaymentDialog, setShowPaymentDialog] = useState(false);\r\n  const [sessionExpired, setSessionExpired] = useState(false);\r\n\r\n  // Session timer reference\r\n  const sessionTimerRef = useRef(null);\r\n\r\n  // Speech recognition and text area references\r\n  const recognitionRef = useRef(null);\r\n  const transcriptAreaRef = useRef(null);\r\n  const responseAreaRef = useRef(null);\r\n\r\n  // Format session time remaining (MM:SS)\r\n  const formatSessionTime = useCallback((totalSeconds) => {\r\n    const minutes = Math.floor(totalSeconds / 60);\r\n    const seconds = totalSeconds % 60;\r\n    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\r\n  }, []);\r\n\r\n  // Start the session timer\r\n  const startSessionTimer = useCallback(() => {\r\n    // Reset session timer when starting\r\n    setSessionTimeRemaining(SESSION_DURATION);\r\n    setSessionExpired(false);\r\n\r\n    // Clear any existing interval\r\n    if (sessionTimerRef.current) {\r\n      clearInterval(sessionTimerRef.current);\r\n    }\r\n\r\n    // Start a new interval that counts down\r\n    sessionTimerRef.current = setInterval(() => {\r\n      setSessionTimeRemaining(prev => {\r\n        const newTime = prev - 1;\r\n\r\n        // Show payment dialog when 1 minute remains\r\n        if (newTime === WARNING_TIME) {\r\n          setShowPaymentDialog(true);\r\n        }\r\n\r\n        // Session expired\r\n        if (newTime <= 0) {\r\n          clearInterval(sessionTimerRef.current);\r\n          sessionTimerRef.current = null;\r\n          setSessionExpired(true);\r\n          return 0;\r\n        }\r\n\r\n        return newTime;\r\n      });\r\n    }, 1000);\r\n  }, [SESSION_DURATION, WARNING_TIME]);\r\n\r\n  // Handle payment dialog close\r\n  const handlePaymentDialogClose = useCallback(() => {\r\n    setShowPaymentDialog(false);\r\n  }, []);\r\n\r\n  // Handle payment confirmation\r\n  const handlePaymentConfirm = useCallback(() => {\r\n    // Reset the session timer\r\n    setShowPaymentDialog(false);\r\n    setSessionTimeRemaining(SESSION_DURATION);\r\n    setSessionExpired(false);\r\n\r\n    // Restart the session timer\r\n    if (sessionTimerRef.current) {\r\n      clearInterval(sessionTimerRef.current);\r\n    }\r\n\r\n    sessionTimerRef.current = setInterval(() => {\r\n      setSessionTimeRemaining(prev => {\r\n        const newTime = prev - 1;\r\n\r\n        // Show payment dialog when 1 minute remains\r\n        if (newTime === WARNING_TIME) {\r\n          setShowPaymentDialog(true);\r\n        }\r\n\r\n        // Session expired\r\n        if (newTime <= 0) {\r\n          clearInterval(sessionTimerRef.current);\r\n          sessionTimerRef.current = null;\r\n          setSessionExpired(true);\r\n          return 0;\r\n        }\r\n\r\n        return newTime;\r\n      });\r\n    }, 1000);\r\n  }, [SESSION_DURATION, WARNING_TIME]);\r\n\r\n  // Start session timer on component mount\r\n  useEffect(() => {\r\n    startSessionTimer();\r\n\r\n    // Clean up session timer on unmount\r\n    return () => {\r\n      if (sessionTimerRef.current) {\r\n        clearInterval(sessionTimerRef.current);\r\n      }\r\n    };\r\n  }, [startSessionTimer]);\r\n\r\n  useEffect(() => {\r\n    // Initialize speech recognition\r\n    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\r\n    if (SpeechRecognition) {\r\n      recognitionRef.current = new SpeechRecognition();\r\n      recognitionRef.current.lang = 'en-US';\r\n      recognitionRef.current.interimResults = true;\r\n      recognitionRef.current.continuous = true;\r\n\r\n      let finalTranscript = \"\";\r\n\r\n      recognitionRef.current.onresult = (event) => {\r\n        let interimTranscript = \"\";\r\n\r\n        for (let i = event.resultIndex; i < event.results.length; i++) {\r\n          const transcript = event.results[i][0].transcript;\r\n          if (event.results[i].isFinal) {\r\n            finalTranscript += transcript + \" \";\r\n          } else {\r\n            interimTranscript += transcript;\r\n          }\r\n        }\r\n\r\n        setTranscript(finalTranscript + interimTranscript);\r\n      };\r\n\r\n      recognitionRef.current.onerror = (event) => {\r\n        console.error(\"Speech recognition error\", event.error);\r\n        alert(\"Error occurred: \" + event.error);\r\n        setIsListening(false);\r\n      };\r\n    } else {\r\n      alert(\"Your browser doesn't support speech recognition. Try Chrome or Edge.\");\r\n    }\r\n\r\n    // Cleanup function\r\n    return () => {\r\n      if (recognitionRef.current) {\r\n        recognitionRef.current.stop();\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    // Auto-scroll transcript area when content changes\r\n    if (transcriptAreaRef.current) {\r\n      transcriptAreaRef.current.scrollTop = transcriptAreaRef.current.scrollHeight;\r\n    }\r\n  }, [transcript]);\r\n\r\n  useEffect(() => {\r\n    // Auto-scroll response area when content changes\r\n    if (responseAreaRef.current) {\r\n      responseAreaRef.current.scrollTop = responseAreaRef.current.scrollHeight;\r\n    }\r\n  }, [response]);\r\n\r\n  const startListening = () => {\r\n    if (!isListening && !sessionExpired) {\r\n      try {\r\n        // Clear the transcript when starting a new recording\r\n        setTranscript('');\r\n\r\n        // Stop any existing recognition\r\n        if (recognitionRef.current) {\r\n          try {\r\n            recognitionRef.current.stop();\r\n          } catch (e) {\r\n            // Ignore errors when stopping\r\n          }\r\n        }\r\n\r\n        // Create a completely new recognition instance\r\n        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\r\n        recognitionRef.current = new SpeechRecognition();\r\n        recognitionRef.current.lang = 'en-US';\r\n        recognitionRef.current.interimResults = true;\r\n        recognitionRef.current.continuous = true;\r\n\r\n        // Set up event handlers with a fresh finalTranscript\r\n        let finalTranscript = \"\";\r\n\r\n        recognitionRef.current.onresult = (event) => {\r\n          let interimTranscript = \"\";\r\n\r\n          for (let i = event.resultIndex; i < event.results.length; i++) {\r\n            const transcript = event.results[i][0].transcript;\r\n            if (event.results[i].isFinal) {\r\n              finalTranscript += transcript + \" \";\r\n            } else {\r\n              interimTranscript += transcript;\r\n            }\r\n          }\r\n\r\n          // Only update with the transcript from this recognition instance\r\n          setTranscript(finalTranscript + interimTranscript);\r\n        };\r\n\r\n        recognitionRef.current.onerror = (event) => {\r\n          console.error(\"Speech recognition error\", event.error);\r\n          setIsListening(false);\r\n        };\r\n\r\n        // Start the new recognition instance\r\n        recognitionRef.current.start();\r\n        setIsListening(true);\r\n      } catch (error) {\r\n        console.error(\"Speech recognition error:\", error);\r\n      }\r\n    }\r\n  };\r\n\r\n  const stopListening = () => {\r\n    if (recognitionRef.current && isListening) {\r\n      try {\r\n        recognitionRef.current.stop();\r\n        setIsListening(false);\r\n      } catch (error) {\r\n        console.error(\"Speech recognition error:\", error);\r\n      }\r\n    }\r\n  };\r\n\r\n  const sendToGPT = async () => {\r\n    const apiKey = env.OPENAI_API_KEY;\r\n    const userText = transcript.trim();\r\n\r\n    console.log(\"Environment:\", process.env.NODE_ENV);\r\n    console.log(\"API Key available:\", apiKey ? \"Yes\" : \"No\");\r\n\r\n    if (!apiKey) {\r\n      alert(\"Please add your OpenAI API key to the .env file as REACT_APP_OPENAI_API_KEY and restart the development server.\");\r\n      return;\r\n    }\r\n\r\n    if (!userText) {\r\n      alert(\"Please record or enter some text to send to GPT.\");\r\n      return;\r\n    }\r\n    const prompt = `In 5 lines, give only the definition and a simple example. ${userText}`;\r\n    setIsLoading(true);\r\n    setResponse('');\r\n\r\n    try {\r\n      const response = await fetch(\"https://api.openai.com/v1/chat/completions\", {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n          \"Authorization\": `Bearer ${apiKey}`\r\n        },\r\n        body: JSON.stringify({\r\n          model: \"gpt-4.1-nano\",\r\n          messages: [\r\n            { role: \"user\", content: prompt }\r\n          ],\r\n          stream: true\r\n        })\r\n      });\r\n\r\n      if (!response.ok || !response.body) throw new Error(\"Failed to stream response.\");\r\n\r\n      const reader = response.body.getReader();\r\n      const decoder = new TextDecoder(\"utf-8\");\r\n\r\n      let result = \"\";\r\n\r\n      while (true) {\r\n        const { value, done } = await reader.read();\r\n        if (done) break;\r\n\r\n        const chunk = decoder.decode(value, { stream: true });\r\n        const lines = chunk.split(\"\\n\").filter(line => line.trim().startsWith(\"data:\"));\r\n\r\n        for (const line of lines) {\r\n          const data = line.replace(/^data: /, '');\r\n          if (data === \"[DONE]\") break;\r\n\r\n          try {\r\n            const json = JSON.parse(data);\r\n            const content = json.choices?.[0]?.delta?.content;\r\n            if (content) {\r\n              result += content;\r\n              setResponse(result);\r\n            }\r\n          } catch (e) {\r\n            console.error(\"Error parsing JSON:\", e);\r\n          }\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Streaming Error:\", error);\r\n      setResponse(\"Error occurred: \" + error.message);\r\n    } finally {\r\n      if (isListening) {\r\n        stopListening();\r\n      }\r\n      setIsLoading(false);\r\n\r\n      // Clear the transcript after getting the answer\r\n      setTranscript('');\r\n    }\r\n  };\r\n\r\n\r\n\r\n  // Handle exit button click\r\n  const handleExit = useCallback(() => {\r\n    // Navigate back to home page\r\n    window.location.href = '/home';\r\n  }, []);\r\n\r\n  return (\r\n    <div className=\"speech-to-text\">\r\n      {/* Exit button in top right corner */}\r\n      <button className=\"exit-button\" onClick={handleExit} title=\"Exit\">\r\n        <CloseIcon />\r\n      </button>\r\n\r\n      {/* Session timer display at the top */}\r\n      <div className=\"timer-container\">\r\n        <AccessTimeIcon className=\"timer-icon\" />\r\n        <span className=\"timer-display session-timer\">\r\n          {formatSessionTime(sessionTimeRemaining)}\r\n        </span>\r\n        {isListening && <span className=\"listening-indicator\">Recording...</span>}\r\n      </div>\r\n\r\n      <div className=\"flex-container\">\r\n        {/* Answer section - maximized without clear button */}\r\n        <div className=\"answer-container\">\r\n          <div className=\"section-header\">\r\n            <h3>AI Response</h3>\r\n          </div>\r\n          <textarea\r\n            ref={responseAreaRef}\r\n            className=\"response-area\"\r\n            value={response}\r\n            readOnly\r\n            placeholder=\"AI answer will appear here...\"\r\n            disabled={sessionExpired}\r\n          />\r\n        </div>\r\n\r\n        {/* Question section - smaller with centered controls */}\r\n        <div className=\"question-container\">\r\n          <textarea\r\n            ref={transcriptAreaRef}\r\n            className=\"transcript-area\"\r\n            value={transcript}\r\n            onChange={(e) => setTranscript(e.target.value)}\r\n            placeholder={sessionExpired ? \"Session expired. Please make a payment to continue.\" : \"Type or record your question here...\"}\r\n            disabled={sessionExpired}\r\n          />\r\n\r\n          <div className=\"control-group\">\r\n            <button\r\n              className={`mic-button ${isListening ? 'listening' : ''}`}\r\n              onClick={isListening ? stopListening : startListening}\r\n              title={isListening ? \"Stop Recording\" : \"Start Recording\"}\r\n              disabled={sessionExpired}\r\n            >\r\n              {isListening ? <StopIcon /> : <MicIcon />}\r\n            </button>\r\n\r\n            <button\r\n              className=\"send-button\"\r\n              onClick={sendToGPT}\r\n              disabled={isLoading || !transcript.trim() || sessionExpired}\r\n              title=\"Get Answer\"\r\n            >\r\n              {isLoading ? \"...\" : <SendIcon />}\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Payment Dialog */}\r\n      <Dialog\r\n        open={showPaymentDialog}\r\n        onClose={handlePaymentDialogClose}\r\n        aria-labelledby=\"payment-dialog-title\"\r\n        aria-describedby=\"payment-dialog-description\"\r\n      >\r\n        <DialogTitle id=\"payment-dialog-title\">\r\n          {\"Session Expiring Soon\"}\r\n        </DialogTitle>\r\n        <DialogContent>\r\n          <DialogContentText id=\"payment-dialog-description\">\r\n            Your session will expire in one minute. Would you like to make a payment to extend your session?\r\n          </DialogContentText>\r\n        </DialogContent>\r\n        <DialogActions>\r\n          <Button onClick={handlePaymentDialogClose} color=\"primary\">\r\n            Not Now\r\n          </Button>\r\n          <Button onClick={handlePaymentConfirm} color=\"primary\" autoFocus>\r\n            Make Payment\r\n          </Button>\r\n        </DialogActions>\r\n      </Dialog>\r\n\r\n      {/* Session Expired Dialog */}\r\n      <Dialog\r\n        open={sessionExpired}\r\n        aria-labelledby=\"expired-dialog-title\"\r\n        aria-describedby=\"expired-dialog-description\"\r\n      >\r\n        <DialogTitle id=\"expired-dialog-title\">\r\n          {\"Session Expired\"}\r\n        </DialogTitle>\r\n        <DialogContent>\r\n          <DialogContentText id=\"expired-dialog-description\">\r\n            Your session has expired. Please make a payment to continue using the service.\r\n          </DialogContentText>\r\n        </DialogContent>\r\n        <DialogActions>\r\n          <Button onClick={handleExit} color=\"primary\">\r\n            Exit\r\n          </Button>\r\n          <Button onClick={handlePaymentConfirm} color=\"primary\" autoFocus>\r\n            Make Payment\r\n          </Button>\r\n        </DialogActions>\r\n      </Dialog>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default SpeechToText;\r\n\r\n\r\n\r\n\r\n\r\n\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,MAAM,CAAEC,SAAS,CAAEC,WAAW,KAAQ,OAAO,CACvE,MAAO,oBAAoB,CAC3B,MAAO,CAAAC,GAAG,KAAM,cAAc,CAC9B,MAAO,CAAAC,OAAO,KAAM,yBAAyB,CAC7C,MAAO,CAAAC,QAAQ,KAAM,0BAA0B,CAC/C,MAAO,CAAAC,QAAQ,KAAM,0BAA0B,CAC/C,MAAO,CAAAC,cAAc,KAAM,gCAAgC,CAC3D,MAAO,CAAAC,SAAS,KAAM,2BAA2B,CACjD,MAAO,CAAAC,MAAM,KAAM,sBAAsB,CACzC,MAAO,CAAAC,aAAa,KAAM,6BAA6B,CACvD,MAAO,CAAAC,aAAa,KAAM,6BAA6B,CACvD,MAAO,CAAAC,iBAAiB,KAAM,iCAAiC,CAC/D,MAAO,CAAAC,WAAW,KAAM,2BAA2B,CACnD,MAAO,CAAAC,MAAM,KAAM,sBAAsB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1C,QAAS,CAAAC,YAAYA,CAAA,CAAG,CACtB,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAGtB,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAACuB,UAAU,CAAEC,aAAa,CAAC,CAAGxB,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACyB,QAAQ,CAAEC,WAAW,CAAC,CAAG1B,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAAC2B,SAAS,CAAEC,YAAY,CAAC,CAAG5B,QAAQ,CAAC,KAAK,CAAC,CAEjD;AACA,KAAM,CAAA6B,gBAAgB,CAAG,GAAG,CAAE;AAC9B,KAAM,CAAAC,YAAY,CAAG,EAAE,CAAE;AACzB,KAAM,CAACC,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGhC,QAAQ,CAAC6B,gBAAgB,CAAC,CAClF,KAAM,CAACI,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGlC,QAAQ,CAAC,KAAK,CAAC,CACjE,KAAM,CAACmC,cAAc,CAAEC,iBAAiB,CAAC,CAAGpC,QAAQ,CAAC,KAAK,CAAC,CAE3D;AACA,KAAM,CAAAqC,eAAe,CAAGpC,MAAM,CAAC,IAAI,CAAC,CAEpC;AACA,KAAM,CAAAqC,cAAc,CAAGrC,MAAM,CAAC,IAAI,CAAC,CACnC,KAAM,CAAAsC,iBAAiB,CAAGtC,MAAM,CAAC,IAAI,CAAC,CACtC,KAAM,CAAAuC,eAAe,CAAGvC,MAAM,CAAC,IAAI,CAAC,CAEpC;AACA,KAAM,CAAAwC,iBAAiB,CAAGtC,WAAW,CAAEuC,YAAY,EAAK,CACtD,KAAM,CAAAC,OAAO,CAAGC,IAAI,CAACC,KAAK,CAACH,YAAY,CAAG,EAAE,CAAC,CAC7C,KAAM,CAAAI,OAAO,CAAGJ,YAAY,CAAG,EAAE,CACjC,SAAAK,MAAA,CAAUJ,OAAO,CAACK,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,MAAAF,MAAA,CAAID,OAAO,CAACE,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,EACtF,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAC,iBAAiB,CAAG/C,WAAW,CAAC,IAAM,CAC1C;AACA6B,uBAAuB,CAACH,gBAAgB,CAAC,CACzCO,iBAAiB,CAAC,KAAK,CAAC,CAExB;AACA,GAAIC,eAAe,CAACc,OAAO,CAAE,CAC3BC,aAAa,CAACf,eAAe,CAACc,OAAO,CAAC,CACxC,CAEA;AACAd,eAAe,CAACc,OAAO,CAAGE,WAAW,CAAC,IAAM,CAC1CrB,uBAAuB,CAACsB,IAAI,EAAI,CAC9B,KAAM,CAAAC,OAAO,CAAGD,IAAI,CAAG,CAAC,CAExB;AACA,GAAIC,OAAO,GAAKzB,YAAY,CAAE,CAC5BI,oBAAoB,CAAC,IAAI,CAAC,CAC5B,CAEA;AACA,GAAIqB,OAAO,EAAI,CAAC,CAAE,CAChBH,aAAa,CAACf,eAAe,CAACc,OAAO,CAAC,CACtCd,eAAe,CAACc,OAAO,CAAG,IAAI,CAC9Bf,iBAAiB,CAAC,IAAI,CAAC,CACvB,MAAO,EAAC,CACV,CAEA,MAAO,CAAAmB,OAAO,CAChB,CAAC,CAAC,CACJ,CAAC,CAAE,IAAI,CAAC,CACV,CAAC,CAAE,CAAC1B,gBAAgB,CAAEC,YAAY,CAAC,CAAC,CAEpC;AACA,KAAM,CAAA0B,wBAAwB,CAAGrD,WAAW,CAAC,IAAM,CACjD+B,oBAAoB,CAAC,KAAK,CAAC,CAC7B,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAuB,oBAAoB,CAAGtD,WAAW,CAAC,IAAM,CAC7C;AACA+B,oBAAoB,CAAC,KAAK,CAAC,CAC3BF,uBAAuB,CAACH,gBAAgB,CAAC,CACzCO,iBAAiB,CAAC,KAAK,CAAC,CAExB;AACA,GAAIC,eAAe,CAACc,OAAO,CAAE,CAC3BC,aAAa,CAACf,eAAe,CAACc,OAAO,CAAC,CACxC,CAEAd,eAAe,CAACc,OAAO,CAAGE,WAAW,CAAC,IAAM,CAC1CrB,uBAAuB,CAACsB,IAAI,EAAI,CAC9B,KAAM,CAAAC,OAAO,CAAGD,IAAI,CAAG,CAAC,CAExB;AACA,GAAIC,OAAO,GAAKzB,YAAY,CAAE,CAC5BI,oBAAoB,CAAC,IAAI,CAAC,CAC5B,CAEA;AACA,GAAIqB,OAAO,EAAI,CAAC,CAAE,CAChBH,aAAa,CAACf,eAAe,CAACc,OAAO,CAAC,CACtCd,eAAe,CAACc,OAAO,CAAG,IAAI,CAC9Bf,iBAAiB,CAAC,IAAI,CAAC,CACvB,MAAO,EAAC,CACV,CAEA,MAAO,CAAAmB,OAAO,CAChB,CAAC,CAAC,CACJ,CAAC,CAAE,IAAI,CAAC,CACV,CAAC,CAAE,CAAC1B,gBAAgB,CAAEC,YAAY,CAAC,CAAC,CAEpC;AACA5B,SAAS,CAAC,IAAM,CACdgD,iBAAiB,CAAC,CAAC,CAEnB;AACA,MAAO,IAAM,CACX,GAAIb,eAAe,CAACc,OAAO,CAAE,CAC3BC,aAAa,CAACf,eAAe,CAACc,OAAO,CAAC,CACxC,CACF,CAAC,CACH,CAAC,CAAE,CAACD,iBAAiB,CAAC,CAAC,CAEvBhD,SAAS,CAAC,IAAM,CACd;AACA,KAAM,CAAAwD,iBAAiB,CAAGC,MAAM,CAACD,iBAAiB,EAAIC,MAAM,CAACC,uBAAuB,CACpF,GAAIF,iBAAiB,CAAE,CACrBpB,cAAc,CAACa,OAAO,CAAG,GAAI,CAAAO,iBAAiB,CAAC,CAAC,CAChDpB,cAAc,CAACa,OAAO,CAACU,IAAI,CAAG,OAAO,CACrCvB,cAAc,CAACa,OAAO,CAACW,cAAc,CAAG,IAAI,CAC5CxB,cAAc,CAACa,OAAO,CAACY,UAAU,CAAG,IAAI,CAExC,GAAI,CAAAC,eAAe,CAAG,EAAE,CAExB1B,cAAc,CAACa,OAAO,CAACc,QAAQ,CAAIC,KAAK,EAAK,CAC3C,GAAI,CAAAC,iBAAiB,CAAG,EAAE,CAE1B,IAAK,GAAI,CAAAC,CAAC,CAAGF,KAAK,CAACG,WAAW,CAAED,CAAC,CAAGF,KAAK,CAACI,OAAO,CAACC,MAAM,CAAEH,CAAC,EAAE,CAAE,CAC7D,KAAM,CAAA7C,UAAU,CAAG2C,KAAK,CAACI,OAAO,CAACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC7C,UAAU,CACjD,GAAI2C,KAAK,CAACI,OAAO,CAACF,CAAC,CAAC,CAACI,OAAO,CAAE,CAC5BR,eAAe,EAAIzC,UAAU,CAAG,GAAG,CACrC,CAAC,IAAM,CACL4C,iBAAiB,EAAI5C,UAAU,CACjC,CACF,CAEAC,aAAa,CAACwC,eAAe,CAAGG,iBAAiB,CAAC,CACpD,CAAC,CAED7B,cAAc,CAACa,OAAO,CAACsB,OAAO,CAAIP,KAAK,EAAK,CAC1CQ,OAAO,CAACC,KAAK,CAAC,0BAA0B,CAAET,KAAK,CAACS,KAAK,CAAC,CACtDC,KAAK,CAAC,kBAAkB,CAAGV,KAAK,CAACS,KAAK,CAAC,CACvCrD,cAAc,CAAC,KAAK,CAAC,CACvB,CAAC,CACH,CAAC,IAAM,CACLsD,KAAK,CAAC,sEAAsE,CAAC,CAC/E,CAEA;AACA,MAAO,IAAM,CACX,GAAItC,cAAc,CAACa,OAAO,CAAE,CAC1Bb,cAAc,CAACa,OAAO,CAAC0B,IAAI,CAAC,CAAC,CAC/B,CACF,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN3E,SAAS,CAAC,IAAM,CACd;AACA,GAAIqC,iBAAiB,CAACY,OAAO,CAAE,CAC7BZ,iBAAiB,CAACY,OAAO,CAAC2B,SAAS,CAAGvC,iBAAiB,CAACY,OAAO,CAAC4B,YAAY,CAC9E,CACF,CAAC,CAAE,CAACxD,UAAU,CAAC,CAAC,CAEhBrB,SAAS,CAAC,IAAM,CACd;AACA,GAAIsC,eAAe,CAACW,OAAO,CAAE,CAC3BX,eAAe,CAACW,OAAO,CAAC2B,SAAS,CAAGtC,eAAe,CAACW,OAAO,CAAC4B,YAAY,CAC1E,CACF,CAAC,CAAE,CAACtD,QAAQ,CAAC,CAAC,CAEd,KAAM,CAAAuD,cAAc,CAAGA,CAAA,GAAM,CAC3B,GAAI,CAAC3D,WAAW,EAAI,CAACc,cAAc,CAAE,CACnC,GAAI,CACF;AACAX,aAAa,CAAC,EAAE,CAAC,CAEjB;AACA,GAAIc,cAAc,CAACa,OAAO,CAAE,CAC1B,GAAI,CACFb,cAAc,CAACa,OAAO,CAAC0B,IAAI,CAAC,CAAC,CAC/B,CAAE,MAAOI,CAAC,CAAE,CACV;AAAA,CAEJ,CAEA;AACA,KAAM,CAAAvB,iBAAiB,CAAGC,MAAM,CAACD,iBAAiB,EAAIC,MAAM,CAACC,uBAAuB,CACpFtB,cAAc,CAACa,OAAO,CAAG,GAAI,CAAAO,iBAAiB,CAAC,CAAC,CAChDpB,cAAc,CAACa,OAAO,CAACU,IAAI,CAAG,OAAO,CACrCvB,cAAc,CAACa,OAAO,CAACW,cAAc,CAAG,IAAI,CAC5CxB,cAAc,CAACa,OAAO,CAACY,UAAU,CAAG,IAAI,CAExC;AACA,GAAI,CAAAC,eAAe,CAAG,EAAE,CAExB1B,cAAc,CAACa,OAAO,CAACc,QAAQ,CAAIC,KAAK,EAAK,CAC3C,GAAI,CAAAC,iBAAiB,CAAG,EAAE,CAE1B,IAAK,GAAI,CAAAC,CAAC,CAAGF,KAAK,CAACG,WAAW,CAAED,CAAC,CAAGF,KAAK,CAACI,OAAO,CAACC,MAAM,CAAEH,CAAC,EAAE,CAAE,CAC7D,KAAM,CAAA7C,UAAU,CAAG2C,KAAK,CAACI,OAAO,CAACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC7C,UAAU,CACjD,GAAI2C,KAAK,CAACI,OAAO,CAACF,CAAC,CAAC,CAACI,OAAO,CAAE,CAC5BR,eAAe,EAAIzC,UAAU,CAAG,GAAG,CACrC,CAAC,IAAM,CACL4C,iBAAiB,EAAI5C,UAAU,CACjC,CACF,CAEA;AACAC,aAAa,CAACwC,eAAe,CAAGG,iBAAiB,CAAC,CACpD,CAAC,CAED7B,cAAc,CAACa,OAAO,CAACsB,OAAO,CAAIP,KAAK,EAAK,CAC1CQ,OAAO,CAACC,KAAK,CAAC,0BAA0B,CAAET,KAAK,CAACS,KAAK,CAAC,CACtDrD,cAAc,CAAC,KAAK,CAAC,CACvB,CAAC,CAED;AACAgB,cAAc,CAACa,OAAO,CAAC+B,KAAK,CAAC,CAAC,CAC9B5D,cAAc,CAAC,IAAI,CAAC,CACtB,CAAE,MAAOqD,KAAK,CAAE,CACdD,OAAO,CAACC,KAAK,CAAC,2BAA2B,CAAEA,KAAK,CAAC,CACnD,CACF,CACF,CAAC,CAED,KAAM,CAAAQ,aAAa,CAAGA,CAAA,GAAM,CAC1B,GAAI7C,cAAc,CAACa,OAAO,EAAI9B,WAAW,CAAE,CACzC,GAAI,CACFiB,cAAc,CAACa,OAAO,CAAC0B,IAAI,CAAC,CAAC,CAC7BvD,cAAc,CAAC,KAAK,CAAC,CACvB,CAAE,MAAOqD,KAAK,CAAE,CACdD,OAAO,CAACC,KAAK,CAAC,2BAA2B,CAAEA,KAAK,CAAC,CACnD,CACF,CACF,CAAC,CAED,KAAM,CAAAS,SAAS,CAAG,KAAAA,CAAA,GAAY,CAC5B,KAAM,CAAAC,MAAM,CAAGjF,GAAG,CAACkF,cAAc,CACjC,KAAM,CAAAC,QAAQ,CAAGhE,UAAU,CAACiE,IAAI,CAAC,CAAC,CAElCd,OAAO,CAACe,GAAG,CAAC,cAAc,CAAEC,OAAO,CAACtF,GAAG,CAACuF,QAAQ,CAAC,CACjDjB,OAAO,CAACe,GAAG,CAAC,oBAAoB,CAAEJ,MAAM,CAAG,KAAK,CAAG,IAAI,CAAC,CAExD,GAAI,CAACA,MAAM,CAAE,CACXT,KAAK,CAAC,iHAAiH,CAAC,CACxH,OACF,CAEA,GAAI,CAACW,QAAQ,CAAE,CACbX,KAAK,CAAC,kDAAkD,CAAC,CACzD,OACF,CACA,KAAM,CAAAgB,MAAM,+DAAA7C,MAAA,CAAiEwC,QAAQ,CAAE,CACvF3D,YAAY,CAAC,IAAI,CAAC,CAClBF,WAAW,CAAC,EAAE,CAAC,CAEf,GAAI,CACF,KAAM,CAAAD,QAAQ,CAAG,KAAM,CAAAoE,KAAK,CAAC,4CAA4C,CAAE,CACzEC,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,CACP,cAAc,CAAE,kBAAkB,CAClC,eAAe,WAAAhD,MAAA,CAAYsC,MAAM,CACnC,CAAC,CACDW,IAAI,CAAEC,IAAI,CAACC,SAAS,CAAC,CACnBC,KAAK,CAAE,cAAc,CACrBC,QAAQ,CAAE,CACR,CAAEC,IAAI,CAAE,MAAM,CAAEC,OAAO,CAAEV,MAAO,CAAC,CAClC,CACDW,MAAM,CAAE,IACV,CAAC,CACH,CAAC,CAAC,CAEF,GAAI,CAAC9E,QAAQ,CAAC+E,EAAE,EAAI,CAAC/E,QAAQ,CAACuE,IAAI,CAAE,KAAM,IAAI,CAAAS,KAAK,CAAC,4BAA4B,CAAC,CAEjF,KAAM,CAAAC,MAAM,CAAGjF,QAAQ,CAACuE,IAAI,CAACW,SAAS,CAAC,CAAC,CACxC,KAAM,CAAAC,OAAO,CAAG,GAAI,CAAAC,WAAW,CAAC,OAAO,CAAC,CAExC,GAAI,CAAAC,MAAM,CAAG,EAAE,CAEf,MAAO,IAAI,CAAE,CACX,KAAM,CAAEC,KAAK,CAAEC,IAAK,CAAC,CAAG,KAAM,CAAAN,MAAM,CAACO,IAAI,CAAC,CAAC,CAC3C,GAAID,IAAI,CAAE,MAEV,KAAM,CAAAE,KAAK,CAAGN,OAAO,CAACO,MAAM,CAACJ,KAAK,CAAE,CAAER,MAAM,CAAE,IAAK,CAAC,CAAC,CACrD,KAAM,CAAAa,KAAK,CAAGF,KAAK,CAACG,KAAK,CAAC,IAAI,CAAC,CAACC,MAAM,CAACC,IAAI,EAAIA,IAAI,CAAC/B,IAAI,CAAC,CAAC,CAACgC,UAAU,CAAC,OAAO,CAAC,CAAC,CAE/E,IAAK,KAAM,CAAAD,IAAI,GAAI,CAAAH,KAAK,CAAE,CACxB,KAAM,CAAAK,IAAI,CAAGF,IAAI,CAACG,OAAO,CAAC,SAAS,CAAE,EAAE,CAAC,CACxC,GAAID,IAAI,GAAK,QAAQ,CAAE,MAEvB,GAAI,KAAAE,aAAA,CAAAC,cAAA,CAAAC,oBAAA,CACF,KAAM,CAAAC,IAAI,CAAG7B,IAAI,CAAC8B,KAAK,CAACN,IAAI,CAAC,CAC7B,KAAM,CAAAnB,OAAO,EAAAqB,aAAA,CAAGG,IAAI,CAACE,OAAO,UAAAL,aAAA,kBAAAC,cAAA,CAAZD,aAAA,CAAe,CAAC,CAAC,UAAAC,cAAA,kBAAAC,oBAAA,CAAjBD,cAAA,CAAmBK,KAAK,UAAAJ,oBAAA,iBAAxBA,oBAAA,CAA0BvB,OAAO,CACjD,GAAIA,OAAO,CAAE,CACXQ,MAAM,EAAIR,OAAO,CACjB5E,WAAW,CAACoF,MAAM,CAAC,CACrB,CACF,CAAE,MAAO7B,CAAC,CAAE,CACVP,OAAO,CAACC,KAAK,CAAC,qBAAqB,CAAEM,CAAC,CAAC,CACzC,CACF,CACF,CACF,CAAE,MAAON,KAAK,CAAE,CACdD,OAAO,CAACC,KAAK,CAAC,kBAAkB,CAAEA,KAAK,CAAC,CACxCjD,WAAW,CAAC,kBAAkB,CAAGiD,KAAK,CAACuD,OAAO,CAAC,CACjD,CAAC,OAAS,CACR,GAAI7G,WAAW,CAAE,CACf8D,aAAa,CAAC,CAAC,CACjB,CACAvD,YAAY,CAAC,KAAK,CAAC,CAEnB;AACAJ,aAAa,CAAC,EAAE,CAAC,CACnB,CACF,CAAC,CAID;AACA,KAAM,CAAA2G,UAAU,CAAGhI,WAAW,CAAC,IAAM,CACnC;AACAwD,MAAM,CAACyE,QAAQ,CAACC,IAAI,CAAG,OAAO,CAChC,CAAC,CAAE,EAAE,CAAC,CAEN,mBACElH,KAAA,QAAKmH,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAE7BtH,IAAA,WAAQqH,SAAS,CAAC,aAAa,CAACE,OAAO,CAAEL,UAAW,CAACM,KAAK,CAAC,MAAM,CAAAF,QAAA,cAC/DtH,IAAA,CAACR,SAAS,GAAE,CAAC,CACP,CAAC,cAGTU,KAAA,QAAKmH,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BtH,IAAA,CAACT,cAAc,EAAC8H,SAAS,CAAC,YAAY,CAAE,CAAC,cACzCrH,IAAA,SAAMqH,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAC1C9F,iBAAiB,CAACV,oBAAoB,CAAC,CACpC,CAAC,CACNV,WAAW,eAAIJ,IAAA,SAAMqH,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAC,cAAY,CAAM,CAAC,EACtE,CAAC,cAENpH,KAAA,QAAKmH,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAE7BpH,KAAA,QAAKmH,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BtH,IAAA,QAAKqH,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC7BtH,IAAA,OAAAsH,QAAA,CAAI,aAAW,CAAI,CAAC,CACjB,CAAC,cACNtH,IAAA,aACEyH,GAAG,CAAElG,eAAgB,CACrB8F,SAAS,CAAC,eAAe,CACzBvB,KAAK,CAAEtF,QAAS,CAChBkH,QAAQ,MACRC,WAAW,CAAC,+BAA+B,CAC3CC,QAAQ,CAAE1G,cAAe,CAC1B,CAAC,EACC,CAAC,cAGNhB,KAAA,QAAKmH,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACjCtH,IAAA,aACEyH,GAAG,CAAEnG,iBAAkB,CACvB+F,SAAS,CAAC,iBAAiB,CAC3BvB,KAAK,CAAExF,UAAW,CAClBuH,QAAQ,CAAG7D,CAAC,EAAKzD,aAAa,CAACyD,CAAC,CAAC8D,MAAM,CAAChC,KAAK,CAAE,CAC/C6B,WAAW,CAAEzG,cAAc,CAAG,qDAAqD,CAAG,sCAAuC,CAC7H0G,QAAQ,CAAE1G,cAAe,CAC1B,CAAC,cAEFhB,KAAA,QAAKmH,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BtH,IAAA,WACEqH,SAAS,eAAAvF,MAAA,CAAgB1B,WAAW,CAAG,WAAW,CAAG,EAAE,CAAG,CAC1DmH,OAAO,CAAEnH,WAAW,CAAG8D,aAAa,CAAGH,cAAe,CACtDyD,KAAK,CAAEpH,WAAW,CAAG,gBAAgB,CAAG,iBAAkB,CAC1DwH,QAAQ,CAAE1G,cAAe,CAAAoG,QAAA,CAExBlH,WAAW,cAAGJ,IAAA,CAACX,QAAQ,GAAE,CAAC,cAAGW,IAAA,CAACZ,OAAO,GAAE,CAAC,CACnC,CAAC,cAETY,IAAA,WACEqH,SAAS,CAAC,aAAa,CACvBE,OAAO,CAAEpD,SAAU,CACnByD,QAAQ,CAAElH,SAAS,EAAI,CAACJ,UAAU,CAACiE,IAAI,CAAC,CAAC,EAAIrD,cAAe,CAC5DsG,KAAK,CAAC,YAAY,CAAAF,QAAA,CAEjB5G,SAAS,CAAG,KAAK,cAAGV,IAAA,CAACV,QAAQ,GAAE,CAAC,CAC3B,CAAC,EACN,CAAC,EACH,CAAC,EACH,CAAC,cAGNY,KAAA,CAACT,MAAM,EACLsI,IAAI,CAAE/G,iBAAkB,CACxBgH,OAAO,CAAEzF,wBAAyB,CAClC,kBAAgB,sBAAsB,CACtC,mBAAiB,4BAA4B,CAAA+E,QAAA,eAE7CtH,IAAA,CAACH,WAAW,EAACoI,EAAE,CAAC,sBAAsB,CAAAX,QAAA,CACnC,uBAAuB,CACb,CAAC,cACdtH,IAAA,CAACL,aAAa,EAAA2H,QAAA,cACZtH,IAAA,CAACJ,iBAAiB,EAACqI,EAAE,CAAC,4BAA4B,CAAAX,QAAA,CAAC,kGAEnD,CAAmB,CAAC,CACP,CAAC,cAChBpH,KAAA,CAACR,aAAa,EAAA4H,QAAA,eACZtH,IAAA,CAACF,MAAM,EAACyH,OAAO,CAAEhF,wBAAyB,CAAC2F,KAAK,CAAC,SAAS,CAAAZ,QAAA,CAAC,SAE3D,CAAQ,CAAC,cACTtH,IAAA,CAACF,MAAM,EAACyH,OAAO,CAAE/E,oBAAqB,CAAC0F,KAAK,CAAC,SAAS,CAACC,SAAS,MAAAb,QAAA,CAAC,cAEjE,CAAQ,CAAC,EACI,CAAC,EACV,CAAC,cAGTpH,KAAA,CAACT,MAAM,EACLsI,IAAI,CAAE7G,cAAe,CACrB,kBAAgB,sBAAsB,CACtC,mBAAiB,4BAA4B,CAAAoG,QAAA,eAE7CtH,IAAA,CAACH,WAAW,EAACoI,EAAE,CAAC,sBAAsB,CAAAX,QAAA,CACnC,iBAAiB,CACP,CAAC,cACdtH,IAAA,CAACL,aAAa,EAAA2H,QAAA,cACZtH,IAAA,CAACJ,iBAAiB,EAACqI,EAAE,CAAC,4BAA4B,CAAAX,QAAA,CAAC,gFAEnD,CAAmB,CAAC,CACP,CAAC,cAChBpH,KAAA,CAACR,aAAa,EAAA4H,QAAA,eACZtH,IAAA,CAACF,MAAM,EAACyH,OAAO,CAAEL,UAAW,CAACgB,KAAK,CAAC,SAAS,CAAAZ,QAAA,CAAC,MAE7C,CAAQ,CAAC,cACTtH,IAAA,CAACF,MAAM,EAACyH,OAAO,CAAE/E,oBAAqB,CAAC0F,KAAK,CAAC,SAAS,CAACC,SAAS,MAAAb,QAAA,CAAC,cAEjE,CAAQ,CAAC,EACI,CAAC,EACV,CAAC,EACN,CAAC,CAEV,CAEA,cAAe,CAAAnH,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}