// Simple utility to access environment variables
const env = {
  OPENAI_API_KEY: process.env.REACT_APP_OPENAI_API_KEY,
  GOOGLE_SPEECH_API_KEY: process.env.REACT_APP_GOOGLE_SPEECH_API_KEY,
  DEEPGRAM_API_KEY: process.env.REACT_APP_DEEPGRAM_API_KEY,
  REACT_APP_API_URL: process.env.REACT_APP_API_URL,

  // Add other environment variables as needed
  isDevelopment: process.env.NODE_ENV === 'development',
  isProduction: process.env.NODE_ENV === 'production',

  // Helper method to check if a key exists
  hasKey: function(key) {
    return !!this[key];
  },

  // Helper to check if Google Speech-to-Text is configured
  hasGoogleSpeechAPI: function() {
    return !!this.GOOGLE_SPEECH_API_KEY;
  },

  // Helper to check if Deepgram API is configured
  hasDeepgramAPI: function() {
    return !!this.DEEPGRAM_API_KEY;
  },

  // Helper to check if OpenAI API is configured
  hasOpenAIAPI: function() {
    return !!this.OPENAI_API_KEY;
  },

  // Get a sample API key for testing (only in development)
  getSampleDeepgramKey: function() {
    if (this.isDevelopment) {
      return '3e94fc5a6e60d63146576d3bd188a8737cfeb360'; // This is a placeholder key for development only
    }
    return null;
  }
};

export default env;
