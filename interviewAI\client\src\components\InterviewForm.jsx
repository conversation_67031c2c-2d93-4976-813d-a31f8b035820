import React, { useState, useRef, useEffect } from 'react';
import './InterviewForm.css';
import SpeechToText from './SpeechToText';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import Button from '@mui/material/Button';
import MicIcon from '@mui/icons-material/Mic';
import StopIcon from '@mui/icons-material/Stop';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import DeleteIcon from '@mui/icons-material/Delete';

function InterviewForm({ onStartSession }) {
  // Form fields
  const [jobTitle, setJobTitle] = useState('');
  const [skillset, setSkillset] = useState('');
  const [jobDescription, setJobDescription] = useState('');
  const [focusAreas, setFocusAreas] = useState([]);
  const [resumeFile, setResumeFile] = useState(null);

  // Validation states
  const [fileError, setFileError] = useState('');
  const [jobDescriptionError, setJobDescriptionError] = useState('');

  // Recording states
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [audioBlob, setAudioBlob] = useState(null);
  const [audioUrl, setAudioUrl] = useState('');
  const mediaRecorderRef = useRef(null);
  const recordingTimerRef = useRef(null);

  // Session states
  const [redirectToSpeech, setRedirectToSpeechToText] = useState(false);
  const [showTimeAlert, setShowTimeAlert] = useState(false);
  const [sessionTimeRemaining, setSessionTimeRemaining] = useState(5); // Default 5 minutes

  // Check for remaining time in localStorage
  useEffect(() => {
    const userDetails = localStorage.getItem('userDetails');
    if (userDetails) {
      try {
        const userData = JSON.parse(userDetails);
        if (userData.timeRemaining) {
          setSessionTimeRemaining(userData.timeRemaining);
        }
      } catch (error) {
        console.error('Error parsing user data:', error);
      }
    }
  }, []);


  // Start recording voice
  const startRecording = async () => {
    try {
      // Reset recording state
      setRecordingTime(0);
      setAudioBlob(null);
      setAudioUrl('');

      // Request microphone access
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

      // Create media recorder
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;

      // Set up data handling
      const audioChunks = [];
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunks.push(event.data);
        }
      };

      // Handle recording stop
      mediaRecorder.onstop = () => {
        // Create blob from chunks
        const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
        const audioUrl = URL.createObjectURL(audioBlob);

        // Update state
        setAudioBlob(audioBlob);
        setAudioUrl(audioUrl);
        setIsRecording(false);

        // Stop all tracks in the stream
        stream.getTracks().forEach(track => track.stop());
      };

      // Start recording
      mediaRecorder.start();
      setIsRecording(true);

      // Set up timer (limit to 2 minutes = 120 seconds)
      const MAX_RECORDING_TIME = 120;
      let seconds = 0;

      recordingTimerRef.current = setInterval(() => {
        seconds += 1;
        setRecordingTime(seconds);

        // Auto-stop after 2 minutes
        if (seconds >= MAX_RECORDING_TIME) {
          stopRecording();
        }
      }, 1000);
    } catch (error) {
      console.error('Error starting recording:', error);
      alert('Could not access microphone. Please check your permissions.');
    }
  };

  // Stop recording
  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      clearInterval(recordingTimerRef.current);
    }
  };

  // Play recorded audio
  const playRecording = () => {
    if (audioUrl) {
      const audio = new Audio(audioUrl);
      audio.play();
    }
  };

  // Delete recorded audio
  const deleteRecording = () => {
    if (audioUrl) {
      URL.revokeObjectURL(audioUrl);
      setAudioBlob(null);
      setAudioUrl('');
      setRecordingTime(0);
    }
  };

  // Format recording time as MM:SS
  const formatRecordingTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // Validate job description word count
  const validateJobDescription = (text) => {
    if (!text) return true;

    const wordCount = text.trim().split(/\s+/).length;
    if (wordCount > 10000) {
      setJobDescriptionError(`Job description exceeds 10,000 words (current: ${wordCount})`);
      return false;
    }

    setJobDescriptionError('');
    return true;
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();

    // Validate form
    if (!jobTitle.trim()) {
      return; // Don't submit if job title is empty
    }

    if (!validateJobDescription(jobDescription)) {
      return; // Don't submit if job description is too long
    }

    // Show time alert before starting
    setShowTimeAlert(true);
  };

  // Handle confirmation from time alert dialog
  const handleTimeAlertConfirm = () => {
    setShowTimeAlert(false);

    // Create session config
    const config = {
      jobTitle,
      skillset,
      jobDescription,
      focusAreas,
      resumeFile,
      audioBlob,
      sessionTimeRemaining
    };

    // Pass the configuration to the parent component
    onStartSession(config);

    // Redirect to speech-to-text component
    setRedirectToSpeechToText(true);
  };

    if (redirectToSpeech) {
    return <SpeechToText />;
  }


  //   if (sessionStarted && sessionConfig) {
  //   return (
  //     <InterviewSession config={sessionConfig} onEndSession={() => setSessionStarted(false)} />
  //   );
  // }

  const handleFocusAreaChange = (area) => {
    if (focusAreas.includes(area)) {
      setFocusAreas(focusAreas.filter(a => a !== area));
    } else {
      setFocusAreas([...focusAreas, area]);
    }
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    setFileError('');

    if (!file) {
      setResumeFile(null);
      return;
    }

    // Check file type
    const fileType = file.type;
    const validTypes = [
      'application/pdf',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];

    if (!validTypes.includes(fileType)) {
      setFileError('Please upload a PDF or DOCX file');
      setResumeFile(null);
      e.target.value = ''; // Reset file input
      return;
    }

    // Check file size (limit to 5MB)
    if (file.size > 5 * 1024 * 1024) {
      setFileError('File size should be less than 5MB');
      setResumeFile(null);
      e.target.value = ''; // Reset file input
      return;
    }

    setResumeFile(file);
  };

  const clearResume = () => {
    setResumeFile(null);
    setFileError('');
    // Reset the file input
    const fileInput = document.getElementById('resumeUpload');
    if (fileInput) fileInput.value = '';
  };

  return (
    <div className="interview-form">
      <h2>Simulate Your Interview Session</h2>
      <form onSubmit={handleSubmit}>
        <div className="form-group">
          <label htmlFor="jobTitle">Job Title</label>
          <input
            type="text"
            id="jobTitle"
            value={jobTitle}
            onChange={(e) => setJobTitle(e.target.value)}
            placeholder="e.g. Frontend Developer"
            required
          />
        </div>

        <div className="form-group">
          <label htmlFor="skillset">Your Skillset</label>
          <input
            type="text"
            id="skillset"
            value={skillset}
            onChange={(e) => setSkillset(e.target.value)}
            placeholder="Input skills separated by commas (e.g. React, JavaScript, CSS)"
          />
          <p className="help-text">Enter your skills separated by commas</p>
        </div>

        <div className="form-group">
          <label htmlFor="jobDescription">Job Description</label>
          <textarea
            id="jobDescription"
            value={jobDescription}
            onChange={(e) => {
              setJobDescription(e.target.value);
              validateJobDescription(e.target.value);
            }}
            placeholder="Paste the job description here (max 10,000 words)"
            rows={6}
            className="job-description-textarea"
          />
          {jobDescriptionError && <p className="error-text">{jobDescriptionError}</p>}
          <p className="help-text">Paste the job description to get more targeted questions (max 10,000 words)</p>
        </div>

        <div className="form-group">
          <label>Focus Areas</label>
          <div className="checkbox-group">
            <label>
              <input
                type="checkbox"
                checked={focusAreas.includes('technical')}
                onChange={() => handleFocusAreaChange('technical')}
              />
              Technical Skills
            </label>
            <label>
              <input
                type="checkbox"
                checked={focusAreas.includes('behavioral')}
                onChange={() => handleFocusAreaChange('behavioral')}
              />
              Behavioral Questions
            </label>
            <label>
              <input
                type="checkbox"
                checked={focusAreas.includes('problemSolving')}
                onChange={() => handleFocusAreaChange('problemSolving')}
              />
              Problem Solving
            </label>
          </div>
        </div>

        <div className="form-group">
          <label htmlFor="resumeUpload">Resume (Optional)</label>
          <div className="file-upload-container">
            <div className="file-input-wrapper">
              <button type="button" className="file-input-button">
                Choose File
              </button>
              <input
                type="file"
                id="resumeUpload"
                onChange={handleFileChange}
                accept=".pdf,.docx,application/pdf,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                className="file-input"
              />
              <span className="file-name">
                {resumeFile ? resumeFile.name : 'No file chosen'}
              </span>
            </div>

            {resumeFile && (
              <button
                type="button"
                className="clear-file"
                onClick={clearResume}
              >
                Remove
              </button>
            )}
          </div>
          {fileError && <p className="error-text">{fileError}</p>}
          <p className="help-text">Upload your resume to personalize interview questions (PDF or DOCX, max 5MB)</p>
          <p className="help-text"><strong>Note:</strong> Remove any sensitive information from your resume</p>
        </div>

        <div className="form-group">
          <label>Record Your Voice (Optional - 2 mins max)</label>
          <div className="voice-recorder-container">
            {!audioUrl ? (
              <div className="recording-controls">
                <button
                  type="button"
                  className={`record-button ${isRecording ? 'recording' : ''}`}
                  onClick={isRecording ? stopRecording : startRecording}
                >
                  {isRecording ? (
                    <>
                      <StopIcon /> Stop Recording ({formatRecordingTime(recordingTime)})
                    </>
                  ) : (
                    <>
                      <MicIcon /> Start Recording
                    </>
                  )}
                </button>
                {isRecording && (
                  <p className="recording-indicator">Recording in progress... ({formatRecordingTime(recordingTime)})</p>
                )}
              </div>
            ) : (
              <div className="playback-controls">
                <div className="audio-info">
                  <span>Recording complete: {formatRecordingTime(recordingTime)}</span>
                </div>
                <div className="audio-buttons">
                  <button type="button" className="play-button" onClick={playRecording}>
                    <PlayArrowIcon /> Play
                  </button>
                  <button type="button" className="delete-button" onClick={deleteRecording}>
                    <DeleteIcon /> Delete
                  </button>
                </div>
              </div>
            )}
            <p className="help-text">Recording your voice helps the AI understand your speaking style and accent</p>
          </div>
        </div>

        <button type="submit" className="start-button">Start Interview</button>
      </form>

      {/* Time Alert Dialog */}
      <Dialog
        open={showTimeAlert}
        onClose={() => setShowTimeAlert(false)}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">
          {"Session Time Information"}
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            You have {sessionTimeRemaining} minutes remaining in your session.
            Would you like to continue with the interview?
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowTimeAlert(false)} color="primary">
            Cancel
          </Button>
          <Button onClick={handleTimeAlertConfirm} color="primary" autoFocus>
            Continue
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
}

export default InterviewForm;
