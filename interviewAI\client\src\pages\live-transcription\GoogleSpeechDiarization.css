.google-speech-diarization {
  max-width: 900px;
  margin: 0 auto;
  padding: 24px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.page-header {
  margin-bottom: 24px;
  text-align: center;
}

.page-header h1 {
  margin: 0 0 8px;
  color: #333;
  font-size: 28px;
}

.description {
  color: #666;
  font-size: 16px;
  margin: 0 0 12px;
}

.feature-badges {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 12px;
}

.feature-badge {
  background-color: #e8f0fe;
  color: #1a73e8;
  font-size: 12px;
  font-weight: 500;
  padding: 4px 10px;
  border-radius: 16px;
  display: inline-flex;
  align-items: center;
  box-shadow: 0 1px 2px rgba(0,0,0,0.05);
  border: 1px solid #d2e3fc;
}

.feature-badge.highlight {
  background-color: #fce8e6;
  color: #ea4335;
  border-color: #fad2cf;
  font-weight: 600;
  animation: pulse-badge 2s infinite;
}

@keyframes pulse-badge {
  0% {
    box-shadow: 0 0 0 0 rgba(234, 67, 53, 0.4);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(234, 67, 53, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(234, 67, 53, 0);
  }
}

/* API Status */
.api-status {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 12px 16px;
  margin-bottom: 20px;
}

.api-indicator {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #555;
}

.api-indicator .status-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 8px;
  background-color: #9e9e9e;
}

.api-indicator.valid .status-dot {
  background-color: #34a853;
}

.api-indicator.invalid .status-dot {
  background-color: #ea4335;
}

.test-api-button {
  padding: 8px 12px;
  background-color: #4285f4;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 13px;
  cursor: pointer;
}

.test-api-button:hover {
  background-color: #3367d6;
}

.test-api-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.api-buttons {
  display: flex;
  gap: 10px;
}

.verify-link {
  display: inline-block;
  padding: 8px 12px;
  background-color: #fbbc05;
  color: #333;
  text-decoration: none;
  border-radius: 4px;
  font-size: 13px;
  transition: background-color 0.2s;
}

.verify-link:hover {
  background-color: #f0b400;
  text-decoration: none;
  color: #333;
}

.api-instructions {
  background-color: #e8f0fe;
  border: 1px solid #d2e3fc;
  border-radius: 4px;
  padding: 16px 20px;
  margin-bottom: 20px;
}

.api-instructions h3 {
  margin-top: 0;
  font-size: 16px;
  color: #1a73e8;
  margin-bottom: 12px;
}

.api-instructions ol {
  padding-left: 20px;
  margin-bottom: 16px;
}

.api-instructions li {
  margin-bottom: 8px;
  font-size: 14px;
}

.api-instructions a {
  color: #1a73e8;
  text-decoration: none;
}

.api-instructions a:hover {
  text-decoration: underline;
}

.api-instructions p {
  margin-bottom: 0;
  font-size: 14px;
}

.api-instructions code {
  background-color: #f1f3f4;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: monospace;
  word-break: break-all;
}

.troubleshooting-tips {
  margin-top: 16px;
  padding: 12px 16px;
  background-color: #fff8e1;
  border: 1px solid #ffecb3;
  border-radius: 4px;
}

.troubleshooting-tips h4 {
  margin-top: 0;
  margin-bottom: 8px;
  font-size: 15px;
  color: #b06000;
}

.troubleshooting-tips ul {
  margin: 0;
  padding-left: 20px;
}

.troubleshooting-tips li {
  margin-bottom: 6px;
  font-size: 14px;
  color: #5f6368;
}

/* Controls */
.controls-container {
  margin-bottom: 24px;
}

.recording-controls {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.control-button {
  padding: 10px 16px;
  border: none;
  border-radius: 4px;
  background-color: #4285f4;
  color: white;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.control-button:hover {
  background-color: #3367d6;
}

.control-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.control-button.recording {
  background-color: #ea4335;
  animation: pulse-bg 1.5s infinite;
}

.control-button.tab-audio {
  background-color: #34a853;
}

.control-button.tab-audio:hover {
  background-color: #2d9249;
}

.control-button.tab-audio.recording {
  background-color: #fbbc05;
  color: #333;
}

@keyframes pulse-bg {
  0% { background-color: #ea4335; }
  50% { background-color: #c0392b; }
  100% { background-color: #ea4335; }
}

.processing-indicator {
  background-color: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-left: 4px solid #fbbc05;
  padding: 12px 16px;
  margin-bottom: 16px;
  border-radius: 4px;
  font-size: 14px;
  color: #555;
}

.error-message {
  background-color: #fdecea;
  border: 1px solid #fdcdc9;
  border-left: 4px solid #ea4335;
  padding: 12px 16px;
  margin-bottom: 16px;
  border-radius: 4px;
  font-size: 14px;
  color: #d93025;
}

/* Transcript */
.transcript-container {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.transcript-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
}

.transcript-header h2 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.download-button {
  padding: 8px 12px;
  background-color: #4285f4;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 13px;
  cursor: pointer;
}

.download-button:hover {
  background-color: #3367d6;
}

.transcript-content {
  padding: 16px;
  max-height: 500px;
  overflow-y: auto;
}

.empty-state {
  color: #999;
  text-align: center;
  font-style: italic;
  padding: 40px 0;
}

.transcript-segments {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.transcript-segment {
  padding: 12px 16px;
  border-radius: 8px;
  background-color: #f9f9f9;
  border-left: 3px solid #ddd;
}

.transcript-segment.speaker-1 {
  border-left-color: #4285f4;
  background-color: #e8f0fe;
}

.transcript-segment.speaker-2 {
  border-left-color: #34a853;
  background-color: #e6f4ea;
}

.transcript-segment.interviewer {
  border-left-color: #4285f4;
  background-color: #e8f0fe;
  position: relative;
}

.transcript-segment.interviewer::before {
  content: '';
  position: absolute;
  left: -10px;
  top: 50%;
  transform: translateY(-50%);
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #4285f4;
  animation: pulse-dot 1.5s cubic-bezier(0.455, 0.03, 0.515, 0.955) -0.4s infinite;
}

.transcript-segment.candidate {
  border-left-color: #34a853;
  background-color: #e6f4ea;
  position: relative;
}

.transcript-segment.candidate::before {
  content: '';
  position: absolute;
  left: -10px;
  top: 50%;
  transform: translateY(-50%);
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #34a853;
  animation: pulse-dot 1.5s cubic-bezier(0.455, 0.03, 0.515, 0.955) -0.4s infinite;
}

.transcript-segment.system {
  border-left-color: #fbbc05;
  background-color: #fff8e1;
}

/* Live transcription segment styling */
.transcript-segment[class*="is-live"] {
  position: relative;
  animation: fade-in 0.3s ease-in-out;
  border-style: dashed;
}

.transcript-segment[class*="is-live"]::after {
  content: '• Live';
  position: absolute;
  right: 10px;
  top: 10px;
  font-size: 12px;
  color: #ea4335;
  font-weight: bold;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes fade-in {
  from { opacity: 0.7; }
  to { opacity: 1; }
}

@keyframes pulse-dot {
  0% {
    transform: translateY(-50%) scale(0.8);
  }
  50% {
    transform: translateY(-50%) scale(1.2);
  }
  100% {
    transform: translateY(-50%) scale(0.8);
  }
}

.segment-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.timestamp {
  font-size: 12px;
  color: #777;
  margin-right: 8px;
}

.speaker-label {
  font-weight: 600;
  font-size: 14px;
  color: #555;
  padding: 4px 10px;
  border-radius: 16px;
  background-color: #f1f3f4;
  display: inline-flex;
  align-items: center;
  box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.transcript-segment.speaker-1 .speaker-label {
  background-color: #d2e3fc;
  color: #1a73e8;
}

.transcript-segment.speaker-2 .speaker-label {
  background-color: #ceead6;
  color: #137333;
}

.transcript-segment.interviewer .speaker-label {
  background-color: #d2e3fc;
  color: #1a73e8;
  position: relative;
  padding-left: 12px;
}

.transcript-segment.candidate .speaker-label {
  background-color: #ceead6;
  color: #137333;
  position: relative;
  padding-left: 12px;
}

.transcript-segment.system .speaker-label {
  background-color: #fff3c4;
  color: #b06000;
}

.segment-text {
  font-size: 15px;
  line-height: 1.5;
  color: #333;
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .google-speech-diarization {
    padding: 16px;
  }

  .recording-controls {
    flex-direction: column;
  }

  .transcript-content {
    max-height: 400px;
  }
}
