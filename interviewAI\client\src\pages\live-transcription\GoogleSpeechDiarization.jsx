import React, { useState, useEffect, useRef } from 'react';
import './GoogleSpeechDiarization.css';
import env from '../../utils/env';

function GoogleSpeechDiarization() {
  // State for recording and transcription
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [transcriptSegments, setTranscriptSegments] = useState([]);
  const [errorMessage, setErrorMessage] = useState('');
  const [audioSource, setAudioSource] = useState('microphone'); // 'microphone' or 'tab'
  const [apiKeyStatus, setApiKeyStatus] = useState('unknown'); // 'unknown', 'valid', 'invalid'
  const [isTestingApiKey, setIsTestingApiKey] = useState(false);

  // Refs for audio handling
  const mediaRecorderRef = useRef(null);
  const audioChunksRef = useRef([]);
  const mediaStreamRef = useRef(null);

  // Clean up function for audio resources
  const cleanupAudio = () => {
    if (mediaRecorderRef.current) {
      if (mediaRecorderRef.current.state !== 'inactive') {
        mediaRecorderRef.current.stop();
      }
      mediaRecorderRef.current = null;
    }

    if (mediaStreamRef.current) {
      mediaStreamRef.current.getTracks().forEach(track => track.stop());
      mediaStreamRef.current = null;
    }

    audioChunksRef.current = [];
  };

  // Clean up on component unmount
  useEffect(() => {
    return () => {
      cleanupAudio();
    };
  }, []);

  // Test the Google Speech API key on component mount
  useEffect(() => {
    testApiKey();
  }, []);

  // Function to test if the API key is valid
  const testApiKey = async () => {
    try {
      setIsTestingApiKey(true);
      setErrorMessage('');

      // Get API key from environment variables
      const apiKey = env.GOOGLE_SPEECH_API_KEY;
      if (!apiKey) {
        throw new Error("Google Speech API key not found. Please add it to your .env file as REACT_APP_GOOGLE_SPEECH_API_KEY");
      }

      // Create a minimal audio sample for testing
      // This is a very short, empty audio buffer encoded as base64
      const testAudioContent = "UklGRiQAAABXQVZFZm10IBAAAAABAAEARKwAAIhYAQACABAAZGF0YQAAAAA=";

      // Make a simple request to the Google Speech API with minimal audio data
      const response = await fetch(`https://speech.googleapis.com/v1p1beta1/speech:recognize?key=${apiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          config: {
            encoding: 'LINEAR16',
            sampleRateHertz: 44100, // Updated to match WAV header
            languageCode: 'en-US',
          },
          audio: {
            content: testAudioContent
          }
        })
      });

      const data = await response.json();

      // Check if the response contains an error
      if (data.error) {
        // If the error is about invalid audio, that means the API key is valid
        // but our test audio is too small (which is expected)
        if (data.error.message && data.error.message.includes('audio')) {
          console.log("Google Speech API key is valid (audio error is expected)");
          setApiKeyStatus('valid');
        } else {
          // Other errors likely mean the API key is invalid
          console.error("Google Speech API key validation failed:", data.error);
          setApiKeyStatus('invalid');
          setErrorMessage(`API Key Error: ${data.error.message || 'Invalid API key'}`);
        }
      } else {
        // If no error, the key is definitely valid
        console.log("Google Speech API key is valid");
        setApiKeyStatus('valid');
      }
    } catch (error) {
      console.error("Error testing API key:", error);

      // Check for specific network errors
      if (error.message === 'Failed to fetch') {
        setErrorMessage(`Network error: Could not connect to Google API. This could be due to:
          1. No internet connection
          2. CORS restrictions in your browser
          3. A firewall blocking the request

          Try using the API key directly in your application instead.`);
      } else {
        setErrorMessage(`Error testing API key: ${error.message}`);
      }

      setApiKeyStatus('unknown'); // Set to unknown instead of invalid for network errors
    } finally {
      setIsTestingApiKey(false);
    }
  };

  // Start recording from microphone
  const startMicrophoneRecording = async () => {
    try {
      cleanupAudio();
      setErrorMessage('');
      setAudioSource('microphone');

      // Get microphone stream
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      mediaStreamRef.current = stream;

      // Create media recorder
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;

      // Set up data handling
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      // Handle recording stop
      mediaRecorder.onstop = () => {
        processAudioForTranscription();
      };

      // Start recording
      mediaRecorder.start(1000); // Collect data every second
      setIsRecording(true);

      console.log("Microphone recording started");
    } catch (error) {
      console.error("Error starting microphone recording:", error);
      setErrorMessage(`Failed to start microphone: ${error.message}`);
    }
  };

  // Start recording from tab audio
  const startTabAudioRecording = async () => {
    try {
      cleanupAudio();
      setErrorMessage('');
      setAudioSource('tab');

      // Request screen sharing with audio
      const stream = await navigator.mediaDevices.getDisplayMedia({
        video: true,
        audio: true
      });

      // Check if audio is included
      const audioTracks = stream.getAudioTracks();
      if (audioTracks.length === 0) {
        throw new Error("No audio track found. Please make sure to select 'Share audio' when sharing the tab.");
      }

      mediaStreamRef.current = stream;

      // Create media recorder
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;

      // Set up data handling
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      // Handle recording stop
      mediaRecorder.onstop = () => {
        processAudioForTranscription();
      };

      // Handle stream ending (user stops sharing)
      stream.getVideoTracks()[0].onended = () => {
        stopRecording();
      };

      // Start recording
      mediaRecorder.start(1000); // Collect data every second
      setIsRecording(true);

      console.log("Tab audio recording started");
    } catch (error) {
      console.error("Error starting tab audio recording:", error);
      setErrorMessage(`Failed to capture tab audio: ${error.message}`);
    }
  };

  // Stop recording
  const stopRecording = () => {
    if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {
      mediaRecorderRef.current.stop();
    }
    setIsRecording(false);
  };

  // Process recorded audio and send to Google Speech-to-Text API
  const processAudioForTranscription = async () => {
    if (audioChunksRef.current.length === 0) {
      console.log("No audio data to process");
      return;
    }

    try {
      setIsProcessing(true);

      // Create audio blob from chunks
      const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });
      console.log("Audio blob created:", audioBlob.size, "bytes");

      // Log audio details for debugging
      const audioUrl = URL.createObjectURL(audioBlob);
      console.log("Audio URL for debugging:", audioUrl);

      // Convert blob to base64
      const reader = new FileReader();
      reader.readAsDataURL(audioBlob);

      reader.onloadend = async () => {
        // Get base64 data (remove the data URL prefix)
        const base64Audio = reader.result.split(',')[1];
        console.log("Base64 audio length:", base64Audio.length);

        // Get API key from environment variables
        const apiKey = env.GOOGLE_SPEECH_API_KEY;
        if (!apiKey) {
          throw new Error("Google Speech API key not found. Please add it to your .env file as REACT_APP_GOOGLE_SPEECH_API_KEY");
        }
        console.log("Using Google Speech API key:", apiKey);

        // Prepare request to Google Speech-to-Text API
        console.log("Sending request to Google Speech API...");
        const response = await fetch(`https://speech.googleapis.com/v1p1beta1/speech:recognize?key=${apiKey}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            config: {
              encoding: 'WEBM_OPUS',
              // Don't specify sampleRateHertz to let Google detect it automatically
              languageCode: 'en-US',
              enableAutomaticPunctuation: true,
              enableSpeakerDiarization: true,
              diarizationSpeakerCount: 2,
              model: 'default',
              // Add additional settings for better results
              useEnhanced: true,
              metadata: {
                interactionType: 'DISCUSSION',
                microphoneDistance: 'NEARFIELD',
                recordingDeviceType: audioSource === 'microphone' ? 'PC_MIC' : 'OTHER'
              }
            },
            audio: {
              content: base64Audio
            }
          })
        });

        console.log("Response status:", response.status);

        if (!response.ok) {
          const errorData = await response.json();
          console.error("API Error details:", errorData);
          throw new Error(`Google API error: ${errorData.error?.message || response.statusText}`);
        }

        const data = await response.json();
        console.log("Received transcription data:", data);

        // Add a default transcript if no speaker diarization is available
        if (!data.results || data.results.length === 0) {
          setTranscriptSegments(prev => [
            ...prev,
            {
              speaker: audioSource === 'microphone' ? 'Candidate' : 'Interviewer',
              text: "No speech detected. Please try speaking louder or check your microphone.",
              timestamp: new Date()
            }
          ]);
        } else {
          processTranscriptionResponse(data);
        }

        // Clear audio chunks for next recording
        audioChunksRef.current = [];
        setIsProcessing(false);
      };

      reader.onerror = (error) => {
        console.error("FileReader error:", error);
        throw new Error(`Error reading audio data: ${error}`);
      };

    } catch (error) {
      console.error("Error processing audio:", error);
      setErrorMessage(`Error processing audio: ${error.message}`);

      // Add error to transcript for user visibility
      setTranscriptSegments(prev => [
        ...prev,
        {
          speaker: 'System',
          text: `Error: ${error.message}. Please try again.`,
          timestamp: new Date()
        }
      ]);

      setIsProcessing(false);
    }
  };

  // Process the response from Google Speech-to-Text API
  const processTranscriptionResponse = (data) => {
    if (!data.results || data.results.length === 0) {
      console.log("No transcription results returned");
      return;
    }

    try {
      console.log("Processing transcription results:", data.results);

      // First, try to get a complete transcript
      let fullTranscript = '';
      data.results.forEach(result => {
        if (result.alternatives && result.alternatives[0]) {
          fullTranscript += result.alternatives[0].transcript + ' ';
        }
      });

      fullTranscript = fullTranscript.trim();
      console.log("Full transcript:", fullTranscript);

      // Check if we have any words with speaker tags
      let hasWordLevelDiarization = false;
      let diarizedWords = [];

      // Look through all results for word-level speaker diarization
      data.results.forEach(result => {
        if (result.alternatives && result.alternatives[0] && result.alternatives[0].words) {
          const words = result.alternatives[0].words;
          if (words.length > 0 && words[0].speakerTag) {
            hasWordLevelDiarization = true;
            diarizedWords = diarizedWords.concat(words);
          }
        }
      });

      if (hasWordLevelDiarization && diarizedWords.length > 0) {
        console.log("Found word-level diarization with", diarizedWords.length, "words");

        // Group words by speaker
        let currentSpeaker = null;
        let currentText = '';
        let segments = [];

        diarizedWords.forEach(word => {
          const speakerTag = word.speakerTag || 0;

          // If speaker changed or this is the first word
          if (speakerTag !== currentSpeaker) {
            // Save previous segment if it exists
            if (currentText) {
              // Map speaker tags to meaningful names
              let speakerName;
              if (currentSpeaker === 1) {
                speakerName = audioSource === 'tab' ? 'Interviewer' : 'Speaker 1';
              } else if (currentSpeaker === 2) {
                speakerName = audioSource === 'microphone' ? 'Candidate' : 'Speaker 2';
              } else {
                speakerName = `Speaker ${currentSpeaker}`;
              }

              segments.push({
                speaker: speakerName,
                text: currentText.trim(),
                timestamp: new Date()
              });
            }

            // Start new segment
            currentSpeaker = speakerTag;
            currentText = word.word + ' ';
          } else {
            // Continue current segment
            currentText += word.word + ' ';
          }
        });

        // Add the last segment
        if (currentText) {
          // Map speaker tags to meaningful names
          let speakerName;
          if (currentSpeaker === 1) {
            speakerName = audioSource === 'tab' ? 'Interviewer' : 'Speaker 1';
          } else if (currentSpeaker === 2) {
            speakerName = audioSource === 'microphone' ? 'Candidate' : 'Speaker 2';
          } else {
            speakerName = `Speaker ${currentSpeaker}`;
          }

          segments.push({
            speaker: speakerName,
            text: currentText.trim(),
            timestamp: new Date()
          });
        }

        console.log("Created segments:", segments);

        // Update transcript segments
        setTranscriptSegments(prev => [...prev, ...segments]);
      } else {
        // No speaker diarization, add as single segment based on audio source
        console.log("No word-level diarization found, using audio source for speaker identification");

        if (fullTranscript) {
          setTranscriptSegments(prev => [
            ...prev,
            {
              speaker: audioSource === 'microphone' ? 'Candidate' : 'Interviewer',
              text: fullTranscript,
              timestamp: new Date()
            }
          ]);
        } else {
          console.log("No transcript text found in the response");
        }
      }
    } catch (error) {
      console.error("Error processing transcription response:", error);
      setErrorMessage(`Error processing transcription: ${error.message}`);

      // Add error to transcript for user visibility
      setTranscriptSegments(prev => [
        ...prev,
        {
          speaker: 'System',
          text: `Error processing speech: ${error.message}`,
          timestamp: new Date()
        }
      ]);
    }
  };

  // Download transcript as text file
  const downloadTranscript = () => {
    if (transcriptSegments.length === 0) return;

    let content = "Conversation Transcript:\n\n";
    transcriptSegments.forEach(segment => {
      content += `[${segment.timestamp.toLocaleTimeString()}] ${segment.speaker}: ${segment.text}\n`;
    });

    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'google_transcript.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="google-speech-diarization">
      <div className="page-header">
        <h1>Google Speech API with Speaker Diarization</h1>
        <p className="description">
          This page uses Google's Speech-to-Text API to transcribe audio with speaker diarization.
        </p>
      </div>

      <div className="api-status">
        <div className={`api-indicator ${apiKeyStatus}`}>
          <span className="status-dot"></span>
          {isTestingApiKey ? 'Testing API key...' :
            apiKeyStatus === 'valid' ? 'API Key: Valid' :
            apiKeyStatus === 'invalid' ? 'API Key: Invalid' :
            'API Key: Not verified'}
        </div>
        <div className="api-buttons">
          <button
            className="test-api-button"
            onClick={testApiKey}
            disabled={isTestingApiKey}
          >
            Test API Key
          </button>
          <a
            href={`https://console.cloud.google.com/apis/credentials`}
            target="_blank"
            rel="noopener noreferrer"
            className="verify-link"
          >
            Verify in Google Console
          </a>
        </div>
      </div>

      {apiKeyStatus === 'unknown' && (
        <div className="api-instructions">
          <h3>How to Verify Your API Key Manually:</h3>
          <ol>
            <li>Go to the <a href="https://console.cloud.google.com/apis/credentials" target="_blank" rel="noopener noreferrer">Google Cloud Console</a></li>
            <li>Select your project</li>
            <li>Go to "APIs & Services" → "Credentials"</li>
            <li>Check that your API key exists and is not restricted in a way that prevents browser usage</li>
            <li>Make sure the Speech-to-Text API is enabled for your project</li>
          </ol>
          <p>Your current API key is: <code>{env.GOOGLE_SPEECH_API_KEY}</code></p>
        </div>
      )}

      <div className="controls-container">
        <div className="recording-controls">
          <button
            className={`control-button ${isRecording && audioSource === 'microphone' ? 'recording' : ''}`}
            onClick={isRecording ? stopRecording : startMicrophoneRecording}
            disabled={isProcessing || isTestingApiKey}
          >
            {isRecording && audioSource === 'microphone'
              ? 'Stop Recording'
              : 'Start Microphone Recording'}
          </button>

          <button
            className={`control-button tab-audio ${isRecording && audioSource === 'tab' ? 'recording' : ''}`}
            onClick={isRecording ? stopRecording : startTabAudioRecording}
            disabled={isProcessing || isTestingApiKey}
          >
            {isRecording && audioSource === 'tab'
              ? 'Stop Tab Recording'
              : 'Start Tab Audio Recording'}
          </button>
        </div>

        {isProcessing && (
          <div className="processing-indicator">
            Processing audio... This may take a moment.
          </div>
        )}

        {errorMessage && (
          <div className="error-message">
            {errorMessage}
          </div>
        )}
      </div>

      <div className="transcript-container">
        <div className="transcript-header">
          <h2>Transcript with Speaker Diarization</h2>
          {transcriptSegments.length > 0 && (
            <button
              className="download-button"
              onClick={downloadTranscript}
            >
              Download Transcript
            </button>
          )}
        </div>

        <div className="transcript-content">
          {transcriptSegments.length === 0 ? (
            <div className="empty-state">
              No transcription yet. Start recording to see results.
            </div>
          ) : (
            <div className="transcript-segments">
              {transcriptSegments.map((segment, index) => (
                <div
                  key={index}
                  className={`transcript-segment ${segment.speaker.toLowerCase().replace(' ', '-')}`}
                >
                  <div className="segment-header">
                    <span className="timestamp">{segment.timestamp.toLocaleTimeString()}</span>
                    <span className="speaker-label">{segment.speaker}</span>
                  </div>
                  <div className="segment-text">{segment.text}</div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default GoogleSpeechDiarization;
