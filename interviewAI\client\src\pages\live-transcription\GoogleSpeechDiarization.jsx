import React, { useState, useEffect, useRef } from 'react';
import './GoogleSpeechDiarization.css';
import env from '../../utils/env';

function GoogleSpeechDiarization() {
  // State for recording and transcription
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [transcriptSegments, setTranscriptSegments] = useState([]);
  const [errorMessage, setErrorMessage] = useState('');
  const [audioSource, setAudioSource] = useState('microphone'); // 'microphone' or 'tab'

  // Refs for audio handling
  const mediaRecorderRef = useRef(null);
  const audioChunksRef = useRef([]);
  const mediaStreamRef = useRef(null);

  // Clean up function for audio resources
  const cleanupAudio = () => {
    if (mediaRecorderRef.current) {
      if (mediaRecorderRef.current.state !== 'inactive') {
        mediaRecorderRef.current.stop();
      }
      mediaRecorderRef.current = null;
    }

    if (mediaStreamRef.current) {
      mediaStreamRef.current.getTracks().forEach(track => track.stop());
      mediaStreamRef.current = null;
    }

    audioChunksRef.current = [];
  };

  // Clean up on component unmount
  useEffect(() => {
    return () => {
      cleanupAudio();
    };
  }, []);

  // Start recording from microphone
  const startMicrophoneRecording = async () => {
    try {
      cleanupAudio();
      setErrorMessage('');
      setAudioSource('microphone');

      // Get microphone stream
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      mediaStreamRef.current = stream;

      // Create media recorder
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;

      // Set up data handling
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      // Handle recording stop
      mediaRecorder.onstop = () => {
        processAudioForTranscription();
      };

      // Start recording
      mediaRecorder.start(1000); // Collect data every second
      setIsRecording(true);

      console.log("Microphone recording started");
    } catch (error) {
      console.error("Error starting microphone recording:", error);
      setErrorMessage(`Failed to start microphone: ${error.message}`);
    }
  };

  // Start recording from tab audio
  const startTabAudioRecording = async () => {
    try {
      cleanupAudio();
      setErrorMessage('');
      setAudioSource('tab');

      // Request screen sharing with audio
      const stream = await navigator.mediaDevices.getDisplayMedia({
        video: true,
        audio: true
      });

      // Check if audio is included
      const audioTracks = stream.getAudioTracks();
      if (audioTracks.length === 0) {
        throw new Error("No audio track found. Please make sure to select 'Share audio' when sharing the tab.");
      }

      mediaStreamRef.current = stream;

      // Create media recorder
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;

      // Set up data handling
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      // Handle recording stop
      mediaRecorder.onstop = () => {
        processAudioForTranscription();
      };

      // Handle stream ending (user stops sharing)
      stream.getVideoTracks()[0].onended = () => {
        stopRecording();
      };

      // Start recording
      mediaRecorder.start(1000); // Collect data every second
      setIsRecording(true);

      console.log("Tab audio recording started");
    } catch (error) {
      console.error("Error starting tab audio recording:", error);
      setErrorMessage(`Failed to capture tab audio: ${error.message}`);
    }
  };

  // Stop recording
  const stopRecording = () => {
    if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {
      mediaRecorderRef.current.stop();
    }
    setIsRecording(false);
  };

  // Process recorded audio and send to Google Speech-to-Text API
  const processAudioForTranscription = async () => {
    if (audioChunksRef.current.length === 0) {
      console.log("No audio data to process");
      return;
    }

    try {
      setIsProcessing(true);

      // Create audio blob from chunks
      const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });

      // Convert blob to base64
      const reader = new FileReader();
      reader.readAsDataURL(audioBlob);

      reader.onloadend = async () => {
        // Get base64 data (remove the data URL prefix)
        const base64Audio = reader.result.split(',')[1];

        // Get API key from environment variables
        const apiKey = env.REACT_APP_GOOGLE_SPEECH_API_KEY;
        if (!apiKey) {
          throw new Error("Google Speech API key not found. Please add it to your .env file as REACT_APP_GOOGLE_SPEECH_API_KEY");
        }
        console.log("Using Google Speech API key:", apiKey);

        // Prepare request to Google Speech-to-Text API
        const response = await fetch(`https://speech.googleapis.com/v1p1beta1/speech:recognize?key=${apiKey}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            config: {
              encoding: 'WEBM_OPUS',
              sampleRateHertz: 48000,
              languageCode: 'en-US',
              enableAutomaticPunctuation: true,
              enableSpeakerDiarization: true,
              diarizationSpeakerCount: 2,
              model: 'default'
            },
            audio: {
              content: base64Audio
            }
          })
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(`Google API error: ${errorData.error?.message || response.statusText}`);
        }

        const data = await response.json();
        processTranscriptionResponse(data);

        // Clear audio chunks for next recording
        audioChunksRef.current = [];
        setIsProcessing(false);
      };

      reader.onerror = (error) => {
        throw new Error(`Error reading audio data: ${error}`);
      };

    } catch (error) {
      console.error("Error processing audio:", error);
      setErrorMessage(`Error processing audio: ${error.message}`);
      setIsProcessing(false);
    }
  };

  // Process the response from Google Speech-to-Text API
  const processTranscriptionResponse = (data) => {
    if (!data.results || data.results.length === 0) {
      console.log("No transcription results returned");
      return;
    }

    try {
      // Get the result with diarization
      const result = data.results[data.results.length - 1];

      if (!result.alternatives || !result.alternatives[0]) {
        console.log("No alternatives in result");
        return;
      }

      const alternative = result.alternatives[0];
      const transcript = alternative.transcript;

      // Process speaker diarization if available
      if (alternative.words && alternative.words.length > 0) {
        let currentSpeaker = null;
        let currentText = '';
        let segments = [];

        // Group words by speaker
        alternative.words.forEach(word => {
          const speakerTag = word.speakerTag || 0;

          // If speaker changed or this is the first word
          if (speakerTag !== currentSpeaker) {
            // Save previous segment if it exists
            if (currentText) {
              segments.push({
                speaker: currentSpeaker === 1 ? 'Speaker 1' : 'Speaker 2',
                text: currentText.trim(),
                timestamp: new Date()
              });
            }

            // Start new segment
            currentSpeaker = speakerTag;
            currentText = word.word + ' ';
          } else {
            // Continue current segment
            currentText += word.word + ' ';
          }
        });

        // Add the last segment
        if (currentText) {
          segments.push({
            speaker: currentSpeaker === 1 ? 'Speaker 1' : 'Speaker 2',
            text: currentText.trim(),
            timestamp: new Date()
          });
        }

        // Update transcript segments
        setTranscriptSegments(prev => [...prev, ...segments]);
      } else {
        // No speaker diarization, add as single segment
        setTranscriptSegments(prev => [
          ...prev,
          {
            speaker: audioSource === 'microphone' ? 'Candidate' : 'Interviewer',
            text: transcript,
            timestamp: new Date()
          }
        ]);
      }
    } catch (error) {
      console.error("Error processing transcription response:", error);
      setErrorMessage(`Error processing transcription: ${error.message}`);
    }
  };

  // Download transcript as text file
  const downloadTranscript = () => {
    if (transcriptSegments.length === 0) return;

    let content = "Conversation Transcript:\n\n";
    transcriptSegments.forEach(segment => {
      content += `[${segment.timestamp.toLocaleTimeString()}] ${segment.speaker}: ${segment.text}\n`;
    });

    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'google_transcript.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="google-speech-diarization">
      <div className="page-header">
        <h1>Google Speech API with Speaker Diarization</h1>
        <p className="description">
          This page uses Google's Speech-to-Text API to transcribe audio with speaker diarization.
        </p>
      </div>

      <div className="controls-container">
        <div className="recording-controls">
          <button
            className={`control-button ${isRecording && audioSource === 'microphone' ? 'recording' : ''}`}
            onClick={isRecording ? stopRecording : startMicrophoneRecording}
            disabled={isProcessing}
          >
            {isRecording && audioSource === 'microphone'
              ? 'Stop Recording'
              : 'Start Microphone Recording'}
          </button>

          <button
            className={`control-button tab-audio ${isRecording && audioSource === 'tab' ? 'recording' : ''}`}
            onClick={isRecording ? stopRecording : startTabAudioRecording}
            disabled={isProcessing}
          >
            {isRecording && audioSource === 'tab'
              ? 'Stop Tab Recording'
              : 'Start Tab Audio Recording'}
          </button>
        </div>

        {isProcessing && (
          <div className="processing-indicator">
            Processing audio... This may take a moment.
          </div>
        )}

        {errorMessage && (
          <div className="error-message">
            {errorMessage}
          </div>
        )}
      </div>

      <div className="transcript-container">
        <div className="transcript-header">
          <h2>Transcript with Speaker Diarization</h2>
          {transcriptSegments.length > 0 && (
            <button
              className="download-button"
              onClick={downloadTranscript}
            >
              Download Transcript
            </button>
          )}
        </div>

        <div className="transcript-content">
          {transcriptSegments.length === 0 ? (
            <div className="empty-state">
              No transcription yet. Start recording to see results.
            </div>
          ) : (
            <div className="transcript-segments">
              {transcriptSegments.map((segment, index) => (
                <div
                  key={index}
                  className={`transcript-segment ${segment.speaker.toLowerCase().replace(' ', '-')}`}
                >
                  <div className="segment-header">
                    <span className="timestamp">{segment.timestamp.toLocaleTimeString()}</span>
                    <span className="speaker-label">{segment.speaker}</span>
                  </div>
                  <div className="segment-text">{segment.text}</div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default GoogleSpeechDiarization;
