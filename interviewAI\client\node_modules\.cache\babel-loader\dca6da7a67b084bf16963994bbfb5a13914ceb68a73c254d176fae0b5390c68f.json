{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\interview-ai-app\\\\interviewAI\\\\client\\\\src\\\\pages\\\\admin-home\\\\index.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport Header from '../../components/Header';\nimport InterviewForm from '../../components/InterviewForm';\nimport InterviewSession from '../../components/InterviewSession';\nimport SpeechToText from '../../components/SpeechToText';\nimport VoiceTranscriber from '../../components/VoiceTranscriber';\nimport MeetingTranscriber from '../../components/MeetingTranscriber';\nimport './admin-home.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction AdminHomeScreen() {\n  _s();\n  const [sessionActive, setSessionActive] = useState(false);\n  const [sessionConfig, setSessionConfig] = useState(null);\n  const [showSpeechToText, setShowSpeechToText] = useState(false);\n  const [showVoiceTranscriber, setShowVoiceTranscriber] = useState(false);\n  const [showMeetingTranscriber, setShowMeetingTranscriber] = useState(false);\n  const startSession = config => {\n    setSessionConfig(config);\n    setSessionActive(true);\n    setShowSpeechToText(false);\n    setShowVoiceTranscriber(false);\n    setShowMeetingTranscriber(false);\n  };\n  const endSession = () => {\n    setSessionActive(false);\n    setSessionConfig(null);\n  };\n  const toggleSpeechToText = () => {\n    setShowSpeechToText(!showSpeechToText);\n    if (!showSpeechToText) {\n      setSessionActive(false);\n      setShowVoiceTranscriber(false);\n      setShowMeetingTranscriber(false);\n    }\n  };\n  const toggleVoiceTranscriber = () => {\n    setShowVoiceTranscriber(!showVoiceTranscriber);\n    if (!showVoiceTranscriber) {\n      setSessionActive(false);\n      setShowSpeechToText(false);\n      setShowMeetingTranscriber(false);\n    }\n  };\n  const toggleMeetingTranscriber = () => {\n    setShowMeetingTranscriber(!showMeetingTranscriber);\n    if (!showMeetingTranscriber) {\n      setSessionActive(false);\n      setShowSpeechToText(false);\n      setShowVoiceTranscriber(false);\n    }\n  };\n  const handleBackFromVoiceTranscriber = () => {\n    setShowVoiceTranscriber(false);\n  };\n  const handleBackFromMeetingTranscriber = () => {\n    setShowMeetingTranscriber(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"App\",\n    children: [!showVoiceTranscriber && !showMeetingTranscriber && /*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 60\n    }, this), !showVoiceTranscriber && !showMeetingTranscriber && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"nav-buttons\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => {\n          setShowSpeechToText(false);\n          setShowVoiceTranscriber(false);\n          setShowMeetingTranscriber(false);\n          setSessionActive(false);\n        },\n        className: !sessionActive && !showSpeechToText && !showVoiceTranscriber && !showMeetingTranscriber ? 'active' : '',\n        children: \"Home\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: toggleSpeechToText,\n        className: showSpeechToText ? 'active' : '',\n        children: \"Interview AI Tool\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: toggleVoiceTranscriber,\n        className: showVoiceTranscriber ? 'active' : '',\n        children: \"Voice Transcriber\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: toggleMeetingTranscriber,\n        className: showMeetingTranscriber ? 'active' : '',\n        children: \"Meeting Transcriber\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: showVoiceTranscriber || showMeetingTranscriber || showSpeechToText ? 'fullscreen' : '',\n      children: showSpeechToText ? /*#__PURE__*/_jsxDEV(SpeechToText, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 11\n      }, this) : showVoiceTranscriber ? /*#__PURE__*/_jsxDEV(VoiceTranscriber, {\n        onBack: handleBackFromVoiceTranscriber\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 11\n      }, this) : showMeetingTranscriber ? /*#__PURE__*/_jsxDEV(MeetingTranscriber, {\n        onBack: handleBackFromMeetingTranscriber\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 11\n      }, this) : !sessionActive ? /*#__PURE__*/_jsxDEV(InterviewForm, {\n        onStartSession: startSession\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(InterviewSession, {\n        config: sessionConfig,\n        onEndSession: endSession\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 66,\n    columnNumber: 5\n  }, this);\n}\n_s(AdminHomeScreen, \"DdAdbycGLs+jriKEaDbfKGesndY=\");\n_c = AdminHomeScreen;\nexport default AdminHomeScreen;\nvar _c;\n$RefreshReg$(_c, \"AdminHomeScreen\");", "map": {"version": 3, "names": ["React", "useState", "Header", "InterviewForm", "InterviewSession", "SpeechToText", "VoiceTranscriber", "MeetingTranscriber", "jsxDEV", "_jsxDEV", "AdminHomeScreen", "_s", "sessionActive", "setSessionActive", "sessionConfig", "setSessionConfig", "showSpeechToText", "setShowSpeechToText", "showVoiceTranscriber", "setShowVoiceTranscriber", "showMeetingTranscriber", "setShowMeetingTranscriber", "startSession", "config", "endSession", "toggleSpeechToText", "toggleVoiceTranscriber", "toggleMeetingTranscriber", "handleBackFromVoiceTranscriber", "handleBackFromMeetingTranscriber", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onBack", "onStartSession", "onEndSession", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/interview-ai-app/interviewAI/client/src/pages/admin-home/index.jsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport Header from '../../components/Header';\r\nimport InterviewForm from '../../components/InterviewForm';\r\nimport InterviewSession from '../../components/InterviewSession';\r\nimport SpeechToText from '../../components/SpeechToText';\r\nimport VoiceTranscriber from '../../components/VoiceTranscriber';\r\nimport MeetingTranscriber from '../../components/MeetingTranscriber';\r\nimport './admin-home.css';\r\n\r\nfunction AdminHomeScreen() {\r\n  const [sessionActive, setSessionActive] = useState(false);\r\n  const [sessionConfig, setSessionConfig] = useState(null);\r\n  const [showSpeechToText, setShowSpeechToText] = useState(false);\r\n  const [showVoiceTranscriber, setShowVoiceTranscriber] = useState(false);\r\n  const [showMeetingTranscriber, setShowMeetingTranscriber] = useState(false);\r\n\r\n  const startSession = (config) => {\r\n    setSessionConfig(config);\r\n    setSessionActive(true);\r\n    setShowSpeechToText(false);\r\n    setShowVoiceTranscriber(false);\r\n    setShowMeetingTranscriber(false);\r\n  };\r\n\r\n  const endSession = () => {\r\n    setSessionActive(false);\r\n    setSessionConfig(null);\r\n  };\r\n\r\n  const toggleSpeechToText = () => {\r\n    setShowSpeechToText(!showSpeechToText);\r\n    if (!showSpeechToText) {\r\n      setSessionActive(false);\r\n      setShowVoiceTranscriber(false);\r\n      setShowMeetingTranscriber(false);\r\n    }\r\n  };\r\n\r\n  const toggleVoiceTranscriber = () => {\r\n    setShowVoiceTranscriber(!showVoiceTranscriber);\r\n    if (!showVoiceTranscriber) {\r\n      setSessionActive(false);\r\n      setShowSpeechToText(false);\r\n      setShowMeetingTranscriber(false);\r\n    }\r\n  };\r\n\r\n  const toggleMeetingTranscriber = () => {\r\n    setShowMeetingTranscriber(!showMeetingTranscriber);\r\n    if (!showMeetingTranscriber) {\r\n      setSessionActive(false);\r\n      setShowSpeechToText(false);\r\n      setShowVoiceTranscriber(false);\r\n    }\r\n  };\r\n\r\n  const handleBackFromVoiceTranscriber = () => {\r\n    setShowVoiceTranscriber(false);\r\n  };\r\n\r\n  const handleBackFromMeetingTranscriber = () => {\r\n    setShowMeetingTranscriber(false);\r\n  };\r\n\r\n  return (\r\n    <div className=\"App\">\r\n      {!showVoiceTranscriber && !showMeetingTranscriber && <Header />}\r\n      \r\n      {!showVoiceTranscriber && !showMeetingTranscriber && (\r\n        <div className=\"nav-buttons\">\r\n          <button \r\n            onClick={() => {\r\n              setShowSpeechToText(false);\r\n              setShowVoiceTranscriber(false);\r\n              setShowMeetingTranscriber(false);\r\n              setSessionActive(false);\r\n            }}\r\n            className={!sessionActive && !showSpeechToText && !showVoiceTranscriber && !showMeetingTranscriber ? 'active' : ''}\r\n          >\r\n            Home\r\n          </button>\r\n          <button \r\n            onClick={toggleSpeechToText}\r\n            className={showSpeechToText ? 'active' : ''}\r\n          >\r\n            Interview AI Tool\r\n          </button>\r\n          <button \r\n            onClick={toggleVoiceTranscriber}\r\n            className={showVoiceTranscriber ? 'active' : ''}\r\n          >\r\n            Voice Transcriber\r\n          </button>\r\n          <button \r\n            onClick={toggleMeetingTranscriber}\r\n            className={showMeetingTranscriber ? 'active' : ''}\r\n          >\r\n            Meeting Transcriber\r\n          </button>\r\n        </div>\r\n      )}\r\n      \r\n      <main className={showVoiceTranscriber || showMeetingTranscriber || showSpeechToText ? 'fullscreen' : ''}>\r\n        {showSpeechToText ? (\r\n          <SpeechToText />\r\n        ) : showVoiceTranscriber ? (\r\n          <VoiceTranscriber onBack={handleBackFromVoiceTranscriber} />\r\n        ) : showMeetingTranscriber ? (\r\n          <MeetingTranscriber onBack={handleBackFromMeetingTranscriber} />\r\n        ) : !sessionActive ? (\r\n          <InterviewForm onStartSession={startSession} />\r\n        ) : (\r\n          <InterviewSession config={sessionConfig} onEndSession={endSession} />\r\n        )}\r\n      </main>\r\n    </div>\r\n  );\r\n}\r\nexport default AdminHomeScreen;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,aAAa,MAAM,gCAAgC;AAC1D,OAAOC,gBAAgB,MAAM,mCAAmC;AAChE,OAAOC,YAAY,MAAM,+BAA+B;AACxD,OAAOC,gBAAgB,MAAM,mCAAmC;AAChE,OAAOC,kBAAkB,MAAM,qCAAqC;AACpE,OAAO,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,SAASC,eAAeA,CAAA,EAAG;EAAAC,EAAA;EACzB,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACa,aAAa,EAAEC,gBAAgB,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACe,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACiB,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACmB,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EAE3E,MAAMqB,YAAY,GAAIC,MAAM,IAAK;IAC/BR,gBAAgB,CAACQ,MAAM,CAAC;IACxBV,gBAAgB,CAAC,IAAI,CAAC;IACtBI,mBAAmB,CAAC,KAAK,CAAC;IAC1BE,uBAAuB,CAAC,KAAK,CAAC;IAC9BE,yBAAyB,CAAC,KAAK,CAAC;EAClC,CAAC;EAED,MAAMG,UAAU,GAAGA,CAAA,KAAM;IACvBX,gBAAgB,CAAC,KAAK,CAAC;IACvBE,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAMU,kBAAkB,GAAGA,CAAA,KAAM;IAC/BR,mBAAmB,CAAC,CAACD,gBAAgB,CAAC;IACtC,IAAI,CAACA,gBAAgB,EAAE;MACrBH,gBAAgB,CAAC,KAAK,CAAC;MACvBM,uBAAuB,CAAC,KAAK,CAAC;MAC9BE,yBAAyB,CAAC,KAAK,CAAC;IAClC;EACF,CAAC;EAED,MAAMK,sBAAsB,GAAGA,CAAA,KAAM;IACnCP,uBAAuB,CAAC,CAACD,oBAAoB,CAAC;IAC9C,IAAI,CAACA,oBAAoB,EAAE;MACzBL,gBAAgB,CAAC,KAAK,CAAC;MACvBI,mBAAmB,CAAC,KAAK,CAAC;MAC1BI,yBAAyB,CAAC,KAAK,CAAC;IAClC;EACF,CAAC;EAED,MAAMM,wBAAwB,GAAGA,CAAA,KAAM;IACrCN,yBAAyB,CAAC,CAACD,sBAAsB,CAAC;IAClD,IAAI,CAACA,sBAAsB,EAAE;MAC3BP,gBAAgB,CAAC,KAAK,CAAC;MACvBI,mBAAmB,CAAC,KAAK,CAAC;MAC1BE,uBAAuB,CAAC,KAAK,CAAC;IAChC;EACF,CAAC;EAED,MAAMS,8BAA8B,GAAGA,CAAA,KAAM;IAC3CT,uBAAuB,CAAC,KAAK,CAAC;EAChC,CAAC;EAED,MAAMU,gCAAgC,GAAGA,CAAA,KAAM;IAC7CR,yBAAyB,CAAC,KAAK,CAAC;EAClC,CAAC;EAED,oBACEZ,OAAA;IAAKqB,SAAS,EAAC,KAAK;IAAAC,QAAA,GACjB,CAACb,oBAAoB,IAAI,CAACE,sBAAsB,iBAAIX,OAAA,CAACP,MAAM;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAE9D,CAACjB,oBAAoB,IAAI,CAACE,sBAAsB,iBAC/CX,OAAA;MAAKqB,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BtB,OAAA;QACE2B,OAAO,EAAEA,CAAA,KAAM;UACbnB,mBAAmB,CAAC,KAAK,CAAC;UAC1BE,uBAAuB,CAAC,KAAK,CAAC;UAC9BE,yBAAyB,CAAC,KAAK,CAAC;UAChCR,gBAAgB,CAAC,KAAK,CAAC;QACzB,CAAE;QACFiB,SAAS,EAAE,CAAClB,aAAa,IAAI,CAACI,gBAAgB,IAAI,CAACE,oBAAoB,IAAI,CAACE,sBAAsB,GAAG,QAAQ,GAAG,EAAG;QAAAW,QAAA,EACpH;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT1B,OAAA;QACE2B,OAAO,EAAEX,kBAAmB;QAC5BK,SAAS,EAAEd,gBAAgB,GAAG,QAAQ,GAAG,EAAG;QAAAe,QAAA,EAC7C;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT1B,OAAA;QACE2B,OAAO,EAAEV,sBAAuB;QAChCI,SAAS,EAAEZ,oBAAoB,GAAG,QAAQ,GAAG,EAAG;QAAAa,QAAA,EACjD;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT1B,OAAA;QACE2B,OAAO,EAAET,wBAAyB;QAClCG,SAAS,EAAEV,sBAAsB,GAAG,QAAQ,GAAG,EAAG;QAAAW,QAAA,EACnD;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN,eAED1B,OAAA;MAAMqB,SAAS,EAAEZ,oBAAoB,IAAIE,sBAAsB,IAAIJ,gBAAgB,GAAG,YAAY,GAAG,EAAG;MAAAe,QAAA,EACrGf,gBAAgB,gBACfP,OAAA,CAACJ,YAAY;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,GACdjB,oBAAoB,gBACtBT,OAAA,CAACH,gBAAgB;QAAC+B,MAAM,EAAET;MAA+B;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,GAC1Df,sBAAsB,gBACxBX,OAAA,CAACF,kBAAkB;QAAC8B,MAAM,EAAER;MAAiC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,GAC9D,CAACvB,aAAa,gBAChBH,OAAA,CAACN,aAAa;QAACmC,cAAc,EAAEhB;MAAa;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAE/C1B,OAAA,CAACL,gBAAgB;QAACmB,MAAM,EAAET,aAAc;QAACyB,YAAY,EAAEf;MAAW;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IACrE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAACxB,EAAA,CA5GQD,eAAe;AAAA8B,EAAA,GAAf9B,eAAe;AA6GxB,eAAeA,eAAe;AAAC,IAAA8B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}