{"ast": null, "code": "import React,{useState}from\"react\";import{useNavigate}from\"react-router-dom\";import{<PERSON><PERSON>,TextField,Button}from\"@mui/material\";import{signupApiCall}from\"../../utils/Apicalls\";import SpinnerShow from\"../../components/Spinner\";import\"./signup.css\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function SignupPage(){const navigate=useNavigate();const[firstName,setFirstName]=useState(\"\");const[userName,setUserName]=useState(\"\");const[mobile,setMobile]=useState(\"\");const[loading,setLoading]=useState(false);const[msgType,setMsgType]=useState(\"\");const[error,setError]=useState(\"\");const validateForm=()=>{if(!firstName.trim()){setError(\"Please enter your name\");return false;}if(!userName.trim()){setError(\"Please enter your email\");return false;}const emailRegex=/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;if(!emailRegex.test(userName)){setError(\"Please enter a valid email address\");return false;}// mobile is optional, no validation\nreturn true;};const onSubmit=async()=>{if(!validateForm()){return;}setLoading(true);setError(\"\");try{const result=await signupApiCall({firstName:firstName,email:userName,mobile:mobile});setLoading(false);if(result!==null&&result!==void 0&&result.status){setMsgType(\"redirectLogin\");setTimeout(()=>{navigate(\"/login\");},3000);}else{setError((result===null||result===void 0?void 0:result.message)||\"Failed to create account. Please try again.\");}}catch(err){setLoading(false);setError(\"An error occurred while creating your account. Please try again later.\");console.error(\"Signup error:\",err);}};const handleLoginClick=e=>{e.preventDefault();navigate(\"/login\");};return/*#__PURE__*/_jsx(\"div\",{className:\"login-container\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"screen\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"screen__content\",children:[loading&&/*#__PURE__*/_jsx(\"div\",{style:{marginTop:20},children:/*#__PURE__*/_jsx(SpinnerShow,{})}),error&&!loading&&/*#__PURE__*/_jsx(\"div\",{style:{padding:\"20px 30px 0\"},children:/*#__PURE__*/_jsx(Alert,{severity:\"error\",children:error})}),!loading&&!msgType&&/*#__PURE__*/_jsxs(\"div\",{className:\"login\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"signup-title\",children:\"Create Your Account\"}),/*#__PURE__*/_jsx(\"div\",{className:\"login__field\",children:/*#__PURE__*/_jsx(TextField,{id:\"outlined-basic\",label:\"Name\",variant:\"outlined\",value:firstName,onChange:e=>setFirstName(e.target.value),placeholder:\"Enter your full name\",className:\"w-100\",fullWidth:true})}),/*#__PURE__*/_jsx(\"div\",{className:\"login__field\",children:/*#__PURE__*/_jsx(TextField,{id:\"outlined-basic\",label:\"Email\",variant:\"outlined\",value:userName,onChange:e=>setUserName(e.target.value),placeholder:\"Enter your email\",className:\"w-100\",fullWidth:true,type:\"email\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"login__field\",children:/*#__PURE__*/_jsx(TextField,{id:\"outlined-basic\",label:\"Mobile (optional)\",variant:\"outlined\",value:mobile,onChange:e=>setMobile(e.target.value),placeholder:\"Enter your mobile number\",className:\"w-100\",fullWidth:true,type:\"tel\"})}),/*#__PURE__*/_jsxs(\"button\",{className:\"button login__submit\",onClick:onSubmit,children:[/*#__PURE__*/_jsx(\"span\",{className:\"button__text\",children:\"Create Account\"}),/*#__PURE__*/_jsx(\"i\",{className:\"button__icon fas fa-chevron-right\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"login-link\",children:[\"Already have an account? \",/*#__PURE__*/_jsx(\"button\",{className:\"text-button\",onClick:handleLoginClick,children:\"Log in\"})]})]}),!loading&&msgType===\"redirectLogin\"&&/*#__PURE__*/_jsxs(\"div\",{className:\"login\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Account Created Successfully!\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Please check your email for a temporary password to login.\"}),/*#__PURE__*/_jsx(Button,{variant:\"contained\",onClick:()=>navigate(\"/login\"),style:{marginTop:20},children:\"Go to Login\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"screen__background\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"screen__background__shape screen__background__shape4\"}),/*#__PURE__*/_jsx(\"span\",{className:\"screen__background__shape screen__background__shape3\"}),/*#__PURE__*/_jsx(\"span\",{className:\"screen__background__shape screen__background__shape2\"}),/*#__PURE__*/_jsx(\"span\",{className:\"screen__background__shape screen__background__shape1\"})]})]})});}export default SignupPage;", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "<PERSON><PERSON>", "TextField", "<PERSON><PERSON>", "signupApiCall", "SpinnerShow", "jsx", "_jsx", "jsxs", "_jsxs", "SignupPage", "navigate", "firstName", "setFirstName", "userName", "setUserName", "mobile", "setMobile", "loading", "setLoading", "msgType", "setMsgType", "error", "setError", "validateForm", "trim", "emailRegex", "test", "onSubmit", "result", "email", "status", "setTimeout", "message", "err", "console", "handleLoginClick", "e", "preventDefault", "className", "children", "style", "marginTop", "padding", "severity", "id", "label", "variant", "value", "onChange", "target", "placeholder", "fullWidth", "type", "onClick"], "sources": ["C:/Users/<USER>/Documents/augment-projects/interview-ai-app/interviewAI/client/src/pages/signup/SignupPage.jsx"], "sourcesContent": ["import React, { useState } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport { <PERSON><PERSON>, TextField, Button } from \"@mui/material\";\nimport { signupApiCall } from \"../../utils/Apicalls\";\nimport SpinnerShow from \"../../components/Spinner\";\nimport \"./signup.css\";\n\nfunction SignupPage() {\n  const navigate = useNavigate();\n  const [firstName, setFirstName] = useState(\"\");\n  const [userName, setUserName] = useState(\"\");\n  const [mobile, setMobile] = useState(\"\");\n  const [loading, setLoading] = useState(false);\n  const [msgType, setMsgType] = useState(\"\");\n  const [error, setError] = useState(\"\");\n\n  const validateForm = () => {\n    if (!firstName.trim()) {\n      setError(\"Please enter your name\");\n      return false;\n    }\n    if (!userName.trim()) {\n      setError(\"Please enter your email\");\n      return false;\n    }\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    if (!emailRegex.test(userName)) {\n      setError(\"Please enter a valid email address\");\n      return false;\n    }\n    // mobile is optional, no validation\n    return true;\n  };\n\n  const onSubmit = async () => {\n    if (!validateForm()) {\n      return;\n    }\n    setLoading(true);\n    setError(\"\");\n    try {\n      const result = await signupApiCall({\n        firstName: firstName,\n        email: userName,\n        mobile: mobile,\n      });\n      setLoading(false);\n      if (result?.status) {\n        setMsgType(\"redirectLogin\");\n        setTimeout(() => {\n          navigate(\"/login\");\n        }, 3000);\n      } else {\n        setError(result?.message || \"Failed to create account. Please try again.\");\n      }\n    } catch (err) {\n      setLoading(false);\n      setError(\"An error occurred while creating your account. Please try again later.\");\n      console.error(\"Signup error:\", err);\n    }\n  };\n\n  const handleLoginClick = (e) => {\n    e.preventDefault();\n    navigate(\"/login\");\n  };\n\n  return (\n    <div className=\"login-container\">\n      <div className=\"screen\">\n        <div className=\"screen__content\">\n          {loading && (\n            <div style={{ marginTop: 20 }}>\n              <SpinnerShow />\n            </div>\n          )}\n          {error && !loading && (\n            <div style={{ padding: \"20px 30px 0\" }}>\n              <Alert severity=\"error\">{error}</Alert>\n            </div>\n          )}\n          {!loading && !msgType && (\n            <div className=\"login\">\n              <h2 className=\"signup-title\">Create Your Account</h2>\n              <div className=\"login__field\">\n                <TextField\n                  id=\"outlined-basic\"\n                  label=\"Name\"\n                  variant=\"outlined\"\n                  value={firstName}\n                  onChange={(e) => setFirstName(e.target.value)}\n                  placeholder=\"Enter your full name\"\n                  className=\"w-100\"\n                  fullWidth\n                />\n              </div>\n              <div className=\"login__field\">\n                <TextField\n                  id=\"outlined-basic\"\n                  label=\"Email\"\n                  variant=\"outlined\"\n                  value={userName}\n                  onChange={(e) => setUserName(e.target.value)}\n                  placeholder=\"Enter your email\"\n                  className=\"w-100\"\n                  fullWidth\n                  type=\"email\"\n                />\n              </div>\n              <div className=\"login__field\">\n                <TextField\n                  id=\"outlined-basic\"\n                  label=\"Mobile (optional)\"\n                  variant=\"outlined\"\n                  value={mobile}\n                  onChange={(e) => setMobile(e.target.value)}\n                  placeholder=\"Enter your mobile number\"\n                  className=\"w-100\"\n                  fullWidth\n                  type=\"tel\"\n                />\n              </div>\n              <button className=\"button login__submit\" onClick={onSubmit}>\n                <span className=\"button__text\">Create Account</span>\n                <i className=\"button__icon fas fa-chevron-right\"></i>\n              </button>\n              <div className=\"login-link\">\n                Already have an account? <button className=\"text-button\" onClick={handleLoginClick}>Log in</button>\n              </div>\n            </div>\n          )}\n          {!loading && msgType === \"redirectLogin\" && (\n            <div className=\"login\">\n              <h3>Account Created Successfully!</h3>\n              <p>Please check your email for a temporary password to login.</p>\n              <Button variant=\"contained\" onClick={() => navigate(\"/login\")} style={{ marginTop: 20 }}>\n                Go to Login\n              </Button>\n            </div>\n          )}\n        </div>\n        <div className=\"screen__background\">\n          <span className=\"screen__background__shape screen__background__shape4\"></span>\n          <span className=\"screen__background__shape screen__background__shape3\"></span>\n          <span className=\"screen__background__shape screen__background__shape2\"></span>\n          <span className=\"screen__background__shape screen__background__shape1\"></span>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default SignupPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OAASC,KAAK,CAAEC,SAAS,CAAEC,MAAM,KAAQ,eAAe,CACxD,OAASC,aAAa,KAAQ,sBAAsB,CACpD,MAAO,CAAAC,WAAW,KAAM,0BAA0B,CAClD,MAAO,cAAc,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEtB,QAAS,CAAAC,UAAUA,CAAA,CAAG,CACpB,KAAM,CAAAC,QAAQ,CAAGX,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACY,SAAS,CAAEC,YAAY,CAAC,CAAGd,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACe,QAAQ,CAAEC,WAAW,CAAC,CAAGhB,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACiB,MAAM,CAAEC,SAAS,CAAC,CAAGlB,QAAQ,CAAC,EAAE,CAAC,CACxC,KAAM,CAACmB,OAAO,CAAEC,UAAU,CAAC,CAAGpB,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACqB,OAAO,CAAEC,UAAU,CAAC,CAAGtB,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAACuB,KAAK,CAAEC,QAAQ,CAAC,CAAGxB,QAAQ,CAAC,EAAE,CAAC,CAEtC,KAAM,CAAAyB,YAAY,CAAGA,CAAA,GAAM,CACzB,GAAI,CAACZ,SAAS,CAACa,IAAI,CAAC,CAAC,CAAE,CACrBF,QAAQ,CAAC,wBAAwB,CAAC,CAClC,MAAO,MAAK,CACd,CACA,GAAI,CAACT,QAAQ,CAACW,IAAI,CAAC,CAAC,CAAE,CACpBF,QAAQ,CAAC,yBAAyB,CAAC,CACnC,MAAO,MAAK,CACd,CACA,KAAM,CAAAG,UAAU,CAAG,4BAA4B,CAC/C,GAAI,CAACA,UAAU,CAACC,IAAI,CAACb,QAAQ,CAAC,CAAE,CAC9BS,QAAQ,CAAC,oCAAoC,CAAC,CAC9C,MAAO,MAAK,CACd,CACA;AACA,MAAO,KAAI,CACb,CAAC,CAED,KAAM,CAAAK,QAAQ,CAAG,KAAAA,CAAA,GAAY,CAC3B,GAAI,CAACJ,YAAY,CAAC,CAAC,CAAE,CACnB,OACF,CACAL,UAAU,CAAC,IAAI,CAAC,CAChBI,QAAQ,CAAC,EAAE,CAAC,CACZ,GAAI,CACF,KAAM,CAAAM,MAAM,CAAG,KAAM,CAAAzB,aAAa,CAAC,CACjCQ,SAAS,CAAEA,SAAS,CACpBkB,KAAK,CAAEhB,QAAQ,CACfE,MAAM,CAAEA,MACV,CAAC,CAAC,CACFG,UAAU,CAAC,KAAK,CAAC,CACjB,GAAIU,MAAM,SAANA,MAAM,WAANA,MAAM,CAAEE,MAAM,CAAE,CAClBV,UAAU,CAAC,eAAe,CAAC,CAC3BW,UAAU,CAAC,IAAM,CACfrB,QAAQ,CAAC,QAAQ,CAAC,CACpB,CAAC,CAAE,IAAI,CAAC,CACV,CAAC,IAAM,CACLY,QAAQ,CAAC,CAAAM,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAEI,OAAO,GAAI,6CAA6C,CAAC,CAC5E,CACF,CAAE,MAAOC,GAAG,CAAE,CACZf,UAAU,CAAC,KAAK,CAAC,CACjBI,QAAQ,CAAC,wEAAwE,CAAC,CAClFY,OAAO,CAACb,KAAK,CAAC,eAAe,CAAEY,GAAG,CAAC,CACrC,CACF,CAAC,CAED,KAAM,CAAAE,gBAAgB,CAAIC,CAAC,EAAK,CAC9BA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClB3B,QAAQ,CAAC,QAAQ,CAAC,CACpB,CAAC,CAED,mBACEJ,IAAA,QAAKgC,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9B/B,KAAA,QAAK8B,SAAS,CAAC,QAAQ,CAAAC,QAAA,eACrB/B,KAAA,QAAK8B,SAAS,CAAC,iBAAiB,CAAAC,QAAA,EAC7BtB,OAAO,eACNX,IAAA,QAAKkC,KAAK,CAAE,CAAEC,SAAS,CAAE,EAAG,CAAE,CAAAF,QAAA,cAC5BjC,IAAA,CAACF,WAAW,GAAE,CAAC,CACZ,CACN,CACAiB,KAAK,EAAI,CAACJ,OAAO,eAChBX,IAAA,QAAKkC,KAAK,CAAE,CAAEE,OAAO,CAAE,aAAc,CAAE,CAAAH,QAAA,cACrCjC,IAAA,CAACN,KAAK,EAAC2C,QAAQ,CAAC,OAAO,CAAAJ,QAAA,CAAElB,KAAK,CAAQ,CAAC,CACpC,CACN,CACA,CAACJ,OAAO,EAAI,CAACE,OAAO,eACnBX,KAAA,QAAK8B,SAAS,CAAC,OAAO,CAAAC,QAAA,eACpBjC,IAAA,OAAIgC,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,qBAAmB,CAAI,CAAC,cACrDjC,IAAA,QAAKgC,SAAS,CAAC,cAAc,CAAAC,QAAA,cAC3BjC,IAAA,CAACL,SAAS,EACR2C,EAAE,CAAC,gBAAgB,CACnBC,KAAK,CAAC,MAAM,CACZC,OAAO,CAAC,UAAU,CAClBC,KAAK,CAAEpC,SAAU,CACjBqC,QAAQ,CAAGZ,CAAC,EAAKxB,YAAY,CAACwB,CAAC,CAACa,MAAM,CAACF,KAAK,CAAE,CAC9CG,WAAW,CAAC,sBAAsB,CAClCZ,SAAS,CAAC,OAAO,CACjBa,SAAS,MACV,CAAC,CACC,CAAC,cACN7C,IAAA,QAAKgC,SAAS,CAAC,cAAc,CAAAC,QAAA,cAC3BjC,IAAA,CAACL,SAAS,EACR2C,EAAE,CAAC,gBAAgB,CACnBC,KAAK,CAAC,OAAO,CACbC,OAAO,CAAC,UAAU,CAClBC,KAAK,CAAElC,QAAS,CAChBmC,QAAQ,CAAGZ,CAAC,EAAKtB,WAAW,CAACsB,CAAC,CAACa,MAAM,CAACF,KAAK,CAAE,CAC7CG,WAAW,CAAC,kBAAkB,CAC9BZ,SAAS,CAAC,OAAO,CACjBa,SAAS,MACTC,IAAI,CAAC,OAAO,CACb,CAAC,CACC,CAAC,cACN9C,IAAA,QAAKgC,SAAS,CAAC,cAAc,CAAAC,QAAA,cAC3BjC,IAAA,CAACL,SAAS,EACR2C,EAAE,CAAC,gBAAgB,CACnBC,KAAK,CAAC,mBAAmB,CACzBC,OAAO,CAAC,UAAU,CAClBC,KAAK,CAAEhC,MAAO,CACdiC,QAAQ,CAAGZ,CAAC,EAAKpB,SAAS,CAACoB,CAAC,CAACa,MAAM,CAACF,KAAK,CAAE,CAC3CG,WAAW,CAAC,0BAA0B,CACtCZ,SAAS,CAAC,OAAO,CACjBa,SAAS,MACTC,IAAI,CAAC,KAAK,CACX,CAAC,CACC,CAAC,cACN5C,KAAA,WAAQ8B,SAAS,CAAC,sBAAsB,CAACe,OAAO,CAAE1B,QAAS,CAAAY,QAAA,eACzDjC,IAAA,SAAMgC,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,gBAAc,CAAM,CAAC,cACpDjC,IAAA,MAAGgC,SAAS,CAAC,mCAAmC,CAAI,CAAC,EAC/C,CAAC,cACT9B,KAAA,QAAK8B,SAAS,CAAC,YAAY,CAAAC,QAAA,EAAC,2BACD,cAAAjC,IAAA,WAAQgC,SAAS,CAAC,aAAa,CAACe,OAAO,CAAElB,gBAAiB,CAAAI,QAAA,CAAC,QAAM,CAAQ,CAAC,EAChG,CAAC,EACH,CACN,CACA,CAACtB,OAAO,EAAIE,OAAO,GAAK,eAAe,eACtCX,KAAA,QAAK8B,SAAS,CAAC,OAAO,CAAAC,QAAA,eACpBjC,IAAA,OAAAiC,QAAA,CAAI,+BAA6B,CAAI,CAAC,cACtCjC,IAAA,MAAAiC,QAAA,CAAG,4DAA0D,CAAG,CAAC,cACjEjC,IAAA,CAACJ,MAAM,EAAC4C,OAAO,CAAC,WAAW,CAACO,OAAO,CAAEA,CAAA,GAAM3C,QAAQ,CAAC,QAAQ,CAAE,CAAC8B,KAAK,CAAE,CAAEC,SAAS,CAAE,EAAG,CAAE,CAAAF,QAAA,CAAC,aAEzF,CAAQ,CAAC,EACN,CACN,EACE,CAAC,cACN/B,KAAA,QAAK8B,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACjCjC,IAAA,SAAMgC,SAAS,CAAC,sDAAsD,CAAO,CAAC,cAC9EhC,IAAA,SAAMgC,SAAS,CAAC,sDAAsD,CAAO,CAAC,cAC9EhC,IAAA,SAAMgC,SAAS,CAAC,sDAAsD,CAAO,CAAC,cAC9EhC,IAAA,SAAMgC,SAAS,CAAC,sDAAsD,CAAO,CAAC,EAC3E,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAEA,cAAe,CAAA7B,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}