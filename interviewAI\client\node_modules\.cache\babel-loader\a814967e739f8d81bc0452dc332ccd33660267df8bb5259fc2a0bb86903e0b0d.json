{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\interview-ai-app\\\\interviewAI\\\\client\\\\src\\\\components\\\\InterviewAssistant.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect, useCallback } from 'react';\nimport './InterviewAssistant.css';\nimport env from '../utils/env';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction InterviewAssistant() {\n  _s();\n  const [isListening, setIsListening] = useState(false);\n  const [transcriptMessages, setTranscriptMessages] = useState([]);\n  const [currentTranscript, setCurrentTranscript] = useState('');\n  const [response, setResponse] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [autoSubmit, setAutoSubmit] = useState(false); // Toggle for auto-submission\n  const [previousTranscript, setPreviousTranscript] = useState(''); // Track previous transcript for comparison\n\n  // Timer state\n  const [timerSeconds, setTimerSeconds] = useState(0);\n  const timerIntervalRef = useRef(null);\n  const pauseTimerRef = useRef(null); // Timer for detecting speech pauses\n\n  const recognitionRef = useRef(null);\n  const transcriptAreaRef = useRef(null);\n  const responseAreaRef = useRef(null);\n\n  // Format seconds to MM:SS\n  const formatTime = useCallback(totalSeconds => {\n    const minutes = Math.floor(totalSeconds / 60);\n    const seconds = totalSeconds % 60;\n    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\n  }, []);\n\n  // Start the timer\n  const startTimer = useCallback(() => {\n    // Reset timer when starting\n    setTimerSeconds(0);\n\n    // Clear any existing interval\n    if (timerIntervalRef.current) {\n      clearInterval(timerIntervalRef.current);\n    }\n\n    // Start a new interval\n    timerIntervalRef.current = setInterval(() => {\n      setTimerSeconds(prev => prev + 1);\n    }, 1000);\n  }, []);\n\n  // Stop the timer\n  const stopTimer = useCallback(() => {\n    if (timerIntervalRef.current) {\n      clearInterval(timerIntervalRef.current);\n      timerIntervalRef.current = null;\n    }\n  }, []);\n\n  // Function to check for speech pause and auto-submit\n  const checkForSpeechPause = useCallback(() => {\n    // Clear any existing pause timer\n    if (pauseTimerRef.current) {\n      clearTimeout(pauseTimerRef.current);\n    }\n\n    // Set a new pause timer\n    pauseTimerRef.current = setTimeout(() => {\n      console.log(\"Checking auto-submit conditions:\", {\n        autoSubmit,\n        hasText: !!currentTranscript.trim(),\n        isListening,\n        isLoading\n      });\n\n      // Only auto-submit if:\n      // 1. Auto-submit is enabled\n      // 2. We have a non-empty transcript\n      // 3. We're currently listening\n      // 4. We're not already loading a response\n      if (autoSubmit && currentTranscript.trim() && isListening && !isLoading) {\n        console.log(\"Auto-submit conditions met, sending to GPT\");\n\n        // Call the submit button click directly\n        const submitButton = document.querySelector('.submit-button');\n        if (submitButton && !submitButton.disabled) {\n          submitButton.click();\n        }\n      }\n    }, 2000); // 2 second pause detection\n  }, [autoSubmit, currentTranscript, isListening, isLoading]);\n\n  // Clean up timer on unmount\n  useEffect(() => {\n    return () => {\n      if (timerIntervalRef.current) {\n        clearInterval(timerIntervalRef.current);\n      }\n      if (pauseTimerRef.current) {\n        clearTimeout(pauseTimerRef.current);\n      }\n    };\n  }, []);\n  useEffect(() => {\n    // Initialize speech recognition\n    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n    if (SpeechRecognition) {\n      recognitionRef.current = new SpeechRecognition();\n      recognitionRef.current.lang = 'en-US';\n      recognitionRef.current.interimResults = true;\n      recognitionRef.current.continuous = true;\n      let finalTranscript = \"\";\n      recognitionRef.current.onresult = event => {\n        let interimTranscript = \"\";\n\n        // Store previous transcript for comparison\n        setPreviousTranscript(finalTranscript + interimTranscript);\n        for (let i = event.resultIndex; i < event.results.length; i++) {\n          const transcript = event.results[i][0].transcript;\n          if (event.results[i].isFinal) {\n            finalTranscript += transcript + \" \";\n          } else {\n            interimTranscript += transcript;\n          }\n        }\n        const newTranscript = finalTranscript + interimTranscript;\n        setCurrentTranscript(newTranscript);\n\n        // Check for pause if transcript has changed\n        if (newTranscript !== previousTranscript) {\n          checkForSpeechPause();\n        }\n      };\n      recognitionRef.current.onerror = event => {\n        console.error(\"Speech recognition error\", event.error);\n        alert(\"Error occurred: \" + event.error);\n        setIsListening(false);\n        stopTimer();\n      };\n    } else {\n      alert(\"Your browser doesn't support speech recognition. Try Chrome or Edge.\");\n    }\n\n    // Cleanup function\n    return () => {\n      if (recognitionRef.current) {\n        recognitionRef.current.stop();\n      }\n    };\n  }, [stopTimer, checkForSpeechPause, previousTranscript]);\n  useEffect(() => {\n    // Auto-scroll transcript area when content changes\n    if (transcriptAreaRef.current) {\n      transcriptAreaRef.current.scrollTop = transcriptAreaRef.current.scrollHeight;\n    }\n  }, [transcriptMessages, currentTranscript]);\n  useEffect(() => {\n    // Auto-scroll response area when content changes\n    if (responseAreaRef.current) {\n      responseAreaRef.current.scrollTop = responseAreaRef.current.scrollHeight;\n    }\n  }, [response]);\n\n  // Debug effect for auto-submit mode\n  useEffect(() => {\n    console.log(`Auto-submit mode ${autoSubmit ? 'enabled' : 'disabled'}`);\n  }, [autoSubmit]);\n  const startListening = useCallback(() => {\n    if (recognitionRef.current && !isListening) {\n      try {\n        recognitionRef.current.start();\n        setIsListening(true);\n        startTimer(); // Start the timer when listening begins\n      } catch (error) {\n        console.error(\"Speech recognition error:\", error);\n        // If recognition is already running, stop it first then restart\n        if (error.message.includes(\"already started\")) {\n          recognitionRef.current.stop();\n          setTimeout(() => {\n            recognitionRef.current.start();\n            setIsListening(true);\n            startTimer(); // Start the timer when listening begins\n          }, 100);\n        }\n      }\n    }\n  }, [isListening, startTimer]);\n  const stopListening = useCallback(() => {\n    if (recognitionRef.current && isListening) {\n      try {\n        recognitionRef.current.stop();\n        setIsListening(false);\n        stopTimer(); // Stop the timer when listening ends\n      } catch (error) {\n        console.error(\"Speech recognition error:\", error);\n      }\n    }\n  }, [isListening, stopTimer]);\n\n  // Send transcript to GPT for response\n  const sendToGPT = useCallback(async () => {\n    const apiKey = env.OPENAI_API_KEY;\n    const userText = currentTranscript.trim();\n    if (!apiKey) {\n      alert(\"Please add your OpenAI API key to the .env file as REACT_APP_OPENAI_API_KEY and restart the development server.\");\n      return;\n    }\n    if (!userText) {\n      alert(\"Please record or enter some text to send to GPT.\");\n      return;\n    }\n\n    // Add the current transcript to the messages array\n    setTranscriptMessages(prev => [...prev, {\n      text: userText,\n      timestamp: new Date(),\n      time: timerSeconds\n    }]);\n\n    // Clear the current transcript input\n    setCurrentTranscript('');\n    setIsLoading(true);\n    setResponse('');\n    try {\n      console.log(\"Sending to GPT API:\", userText);\n      const response = await fetch(\"https://api.openai.com/v1/chat/completions\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          \"Authorization\": `Bearer ${apiKey}`\n        },\n        body: JSON.stringify({\n          model: \"gpt-4.1-nano\",\n          messages: [{\n            role: \"system\",\n            content: \"You are Sensei, an AI interview assistant. Provide detailed, well-structured responses to interview questions. Format your answers with bullet points for clarity. Keep responses concise but comprehensive.\"\n          }, {\n            role: \"user\",\n            content: userText\n          }],\n          stream: true\n        })\n      });\n      if (!response.ok || !response.body) throw new Error(\"Failed to stream response.\");\n      const reader = response.body.getReader();\n      const decoder = new TextDecoder(\"utf-8\");\n      let result = \"\";\n      while (true) {\n        const {\n          value,\n          done\n        } = await reader.read();\n        if (done) break;\n        const chunk = decoder.decode(value, {\n          stream: true\n        });\n        const lines = chunk.split(\"\\n\").filter(line => line.trim().startsWith(\"data:\"));\n        for (const line of lines) {\n          const data = line.replace(/^data: /, '');\n          if (data === \"[DONE]\") break;\n          try {\n            var _json$choices, _json$choices$, _json$choices$$delta;\n            const json = JSON.parse(data);\n            const content = (_json$choices = json.choices) === null || _json$choices === void 0 ? void 0 : (_json$choices$ = _json$choices[0]) === null || _json$choices$ === void 0 ? void 0 : (_json$choices$$delta = _json$choices$.delta) === null || _json$choices$$delta === void 0 ? void 0 : _json$choices$$delta.content;\n            if (content) {\n              result += content;\n              setResponse(result);\n            }\n          } catch (e) {\n            console.error(\"Error parsing JSON:\", e);\n          }\n        }\n      }\n    } catch (error) {\n      console.error(\"Streaming Error:\", error);\n      setResponse(\"Error occurred: \" + error.message);\n    } finally {\n      setIsLoading(false);\n    }\n  }, [currentTranscript, timerSeconds]);\n  const clearTranscript = () => {\n    setCurrentTranscript('');\n    setTranscriptMessages([]);\n  };\n  const clearResponse = () => {\n    setResponse('');\n  };\n\n  // Handle end interview with confirmation popup\n  const [showEndConfirmation, setShowEndConfirmation] = useState(false);\n  const handleEndInterview = () => {\n    setShowEndConfirmation(true);\n  };\n  const confirmEndInterview = () => {\n    // Here you would handle the actual end interview logic\n    // For example, redirect to a summary page or reset the state\n    setShowEndConfirmation(false);\n    clearTranscript();\n    clearResponse();\n    // You could also redirect to another page\n    // window.location.href = '/';\n  };\n  const cancelEndInterview = () => {\n    setShowEndConfirmation(false);\n  };\n  const generateQuestion = () => {\n    // This would generate a sample interview question\n    const questions = [\"Tell me about yourself.\", \"What are your greatest strengths?\", \"What do you consider to be your weaknesses?\", \"Why do you want this job?\", \"Where do you see yourself in five years?\", \"Why should we hire you?\", \"What is your greatest professional achievement?\", \"Tell me about a challenge or conflict you've faced at work, and how you dealt with it.\", \"Tell me about a time you demonstrated leadership skills.\", \"What's your management style?\", \"How do you handle stress and pressure?\", \"What are your salary expectations?\", \"What do you like to do outside of work?\", \"What are your career goals?\", \"Why are you leaving your current job?\", \"How do you prioritize your work?\", \"What are you passionate about?\", \"What makes you unique?\", \"What should I know that's not on your resume?\", \"What would your first 30, 60, or 90 days look like in this role?\"];\n    const randomQuestion = questions[Math.floor(Math.random() * questions.length)];\n\n    // Add the generated question directly to the transcript messages\n    setTranscriptMessages(prev => [...prev, {\n      text: randomQuestion,\n      timestamp: new Date(),\n      time: timerSeconds\n    }]);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"interview-assistant\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"interview-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"transcription-panel\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"panel-content\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: transcriptAreaRef,\n            className: \"transcript-content\",\n            children: [transcriptMessages.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"transcript-messages\",\n              children: transcriptMessages.map((msg, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"transcript-message\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"timestamp\",\n                  children: formatTime(msg.time)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 378,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"message-text\",\n                  children: msg.text\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 379,\n                  columnNumber: 23\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 377,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"empty-state\",\n              children: \"Your interview questions will appear here\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 17\n            }, this), currentTranscript && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"transcript-message current\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"timestamp\",\n                children: formatTime(timerSeconds)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 389,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"message-text\",\n                children: currentTranscript\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"panel-footer\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-label\",\n            children: \"Interviewer\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"generate-button\",\n            onClick: generateQuestion,\n            children: \"Generate\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 396,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"response-panel\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"panel-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Answers\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"panel-description\",\n            children: \"Displayed here are the intelligent responses generated by Sensei. Whenever the interviewer concludes a question, provides a tailored answer for you.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 410,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 408,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"panel-content\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: responseAreaRef,\n            className: \"response-content\",\n            children: response ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"response-message\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"message-text\",\n                children: response\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 423,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"empty-state\",\n              children: \"InterviewAssistant's responses will appear here\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 426,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"panel-footer\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-label\",\n            children: \"InterviewAssistant's\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 432,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 431,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 407,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 360,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"controls-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"timer-display\",\n        children: isListening ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"recording-dot\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 441,\n            columnNumber: 15\n          }, this), formatTime(timerSeconds)]\n        }, void 0, true) : formatTime(timerSeconds)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 438,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"limit-indicator\",\n        children: \"15-minute limit\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 449,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"input-controls\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          className: \"text-input\",\n          value: currentTranscript,\n          onChange: e => setCurrentTranscript(e.target.value),\n          placeholder: \"Type your question here...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 452,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"submit-button\",\n          onClick: sendToGPT,\n          disabled: isLoading || !currentTranscript.trim(),\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"arrow-icon\",\n            children: \"\\u2191\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 465,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 460,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 451,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"action-buttons\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: `toggle-button ${autoSubmit ? 'active' : ''}`,\n          onClick: () => setAutoSubmit(prev => !prev),\n          title: autoSubmit ? \"Auto-submit enabled\" : \"Auto-submit disabled\",\n          \"data-active\": autoSubmit,\n          children: autoSubmit ? \"Auto\" : \"Manual\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 470,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `mic-button ${isListening ? 'active' : ''}`,\n          onClick: isListening ? stopListening : startListening,\n          children: isListening ? \"Stop\" : \"Start\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 479,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"settings-button\",\n          children: \"\\u2699\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 486,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"end-button\",\n          onClick: handleEndInterview,\n          children: \"End\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 492,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 469,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 437,\n      columnNumber: 7\n    }, this), showEndConfirmation && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"End interview confirmation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 506,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 505,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-content\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"This action cannot be undone, and you will need to start a new interview to continue.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 509,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 508,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-footer\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"cancel-button\",\n            onClick: cancelEndInterview,\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 512,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"confirm-button\",\n            onClick: confirmEndInterview,\n            children: \"OK\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 515,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 511,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 504,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 503,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 359,\n    columnNumber: 5\n  }, this);\n}\n_s(InterviewAssistant, \"AxwLB5aKsWuGw5DRZyQ5Nsdok3M=\");\n_c = InterviewAssistant;\nexport default InterviewAssistant;\nvar _c;\n$RefreshReg$(_c, \"InterviewAssistant\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "useCallback", "env", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "InterviewAssistant", "_s", "isListening", "setIsListening", "transcriptMessages", "setTranscriptMessages", "currentTranscript", "setCurrentTranscript", "response", "setResponse", "isLoading", "setIsLoading", "autoSubmit", "setAutoSubmit", "previousTranscript", "setPreviousTranscript", "timerSeconds", "setTimerSeconds", "timerIntervalRef", "pauseTimerRef", "recognitionRef", "transcriptAreaRef", "responseAreaRef", "formatTime", "totalSeconds", "minutes", "Math", "floor", "seconds", "toString", "padStart", "startTimer", "current", "clearInterval", "setInterval", "prev", "stopTimer", "checkForSpeechPause", "clearTimeout", "setTimeout", "console", "log", "hasText", "trim", "submitButton", "document", "querySelector", "disabled", "click", "SpeechRecognition", "window", "webkitSpeechRecognition", "lang", "interimResults", "continuous", "finalTranscript", "on<PERSON>ult", "event", "interimTranscript", "i", "resultIndex", "results", "length", "transcript", "isFinal", "newTranscript", "onerror", "error", "alert", "stop", "scrollTop", "scrollHeight", "startListening", "start", "message", "includes", "stopListening", "sendToGPT", "<PERSON><PERSON><PERSON><PERSON>", "OPENAI_API_KEY", "userText", "text", "timestamp", "Date", "time", "fetch", "method", "headers", "body", "JSON", "stringify", "model", "messages", "role", "content", "stream", "ok", "Error", "reader", "<PERSON><PERSON><PERSON><PERSON>", "decoder", "TextDecoder", "result", "value", "done", "read", "chunk", "decode", "lines", "split", "filter", "line", "startsWith", "data", "replace", "_json$choices", "_json$choices$", "_json$choices$$delta", "json", "parse", "choices", "delta", "e", "clearTranscript", "clearResponse", "showEndConfirmation", "setShowEndConfirmation", "handleEndInterview", "confirmEndInterview", "cancelEndInterview", "generateQuestion", "questions", "randomQuestion", "random", "className", "children", "ref", "map", "msg", "index", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "type", "onChange", "target", "placeholder", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/interview-ai-app/interviewAI/client/src/components/InterviewAssistant.jsx"], "sourcesContent": ["import React, { useState, useRef, useEffect, useCallback } from 'react';\nimport './InterviewAssistant.css';\nimport env from '../utils/env';\n\nfunction InterviewAssistant() {\n  const [isListening, setIsListening] = useState(false);\n  const [transcriptMessages, setTranscriptMessages] = useState([]);\n  const [currentTranscript, setCurrentTranscript] = useState('');\n  const [response, setResponse] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [autoSubmit, setAutoSubmit] = useState(false); // Toggle for auto-submission\n  const [previousTranscript, setPreviousTranscript] = useState(''); // Track previous transcript for comparison\n\n  // Timer state\n  const [timerSeconds, setTimerSeconds] = useState(0);\n  const timerIntervalRef = useRef(null);\n  const pauseTimerRef = useRef(null); // Timer for detecting speech pauses\n\n  const recognitionRef = useRef(null);\n  const transcriptAreaRef = useRef(null);\n  const responseAreaRef = useRef(null);\n\n  // Format seconds to MM:SS\n  const formatTime = useCallback((totalSeconds) => {\n    const minutes = Math.floor(totalSeconds / 60);\n    const seconds = totalSeconds % 60;\n    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\n  }, []);\n\n  // Start the timer\n  const startTimer = useCallback(() => {\n    // Reset timer when starting\n    setTimerSeconds(0);\n\n    // Clear any existing interval\n    if (timerIntervalRef.current) {\n      clearInterval(timerIntervalRef.current);\n    }\n\n    // Start a new interval\n    timerIntervalRef.current = setInterval(() => {\n      setTimerSeconds(prev => prev + 1);\n    }, 1000);\n  }, []);\n\n  // Stop the timer\n  const stopTimer = useCallback(() => {\n    if (timerIntervalRef.current) {\n      clearInterval(timerIntervalRef.current);\n      timerIntervalRef.current = null;\n    }\n  }, []);\n\n  // Function to check for speech pause and auto-submit\n  const checkForSpeechPause = useCallback(() => {\n    // Clear any existing pause timer\n    if (pauseTimerRef.current) {\n      clearTimeout(pauseTimerRef.current);\n    }\n\n    // Set a new pause timer\n    pauseTimerRef.current = setTimeout(() => {\n      console.log(\"Checking auto-submit conditions:\", {\n        autoSubmit,\n        hasText: !!currentTranscript.trim(),\n        isListening,\n        isLoading\n      });\n\n      // Only auto-submit if:\n      // 1. Auto-submit is enabled\n      // 2. We have a non-empty transcript\n      // 3. We're currently listening\n      // 4. We're not already loading a response\n      if (autoSubmit &&\n          currentTranscript.trim() &&\n          isListening &&\n          !isLoading) {\n\n        console.log(\"Auto-submit conditions met, sending to GPT\");\n\n        // Call the submit button click directly\n        const submitButton = document.querySelector('.submit-button');\n        if (submitButton && !submitButton.disabled) {\n          submitButton.click();\n        }\n      }\n    }, 2000); // 2 second pause detection\n  }, [autoSubmit, currentTranscript, isListening, isLoading]);\n\n  // Clean up timer on unmount\n  useEffect(() => {\n    return () => {\n      if (timerIntervalRef.current) {\n        clearInterval(timerIntervalRef.current);\n      }\n      if (pauseTimerRef.current) {\n        clearTimeout(pauseTimerRef.current);\n      }\n    };\n  }, []);\n\n  useEffect(() => {\n    // Initialize speech recognition\n    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n    if (SpeechRecognition) {\n      recognitionRef.current = new SpeechRecognition();\n      recognitionRef.current.lang = 'en-US';\n      recognitionRef.current.interimResults = true;\n      recognitionRef.current.continuous = true;\n\n      let finalTranscript = \"\";\n\n      recognitionRef.current.onresult = (event) => {\n        let interimTranscript = \"\";\n\n        // Store previous transcript for comparison\n        setPreviousTranscript(finalTranscript + interimTranscript);\n\n        for (let i = event.resultIndex; i < event.results.length; i++) {\n          const transcript = event.results[i][0].transcript;\n          if (event.results[i].isFinal) {\n            finalTranscript += transcript + \" \";\n          } else {\n            interimTranscript += transcript;\n          }\n        }\n\n        const newTranscript = finalTranscript + interimTranscript;\n        setCurrentTranscript(newTranscript);\n\n        // Check for pause if transcript has changed\n        if (newTranscript !== previousTranscript) {\n          checkForSpeechPause();\n        }\n      };\n\n      recognitionRef.current.onerror = (event) => {\n        console.error(\"Speech recognition error\", event.error);\n        alert(\"Error occurred: \" + event.error);\n        setIsListening(false);\n        stopTimer();\n      };\n    } else {\n      alert(\"Your browser doesn't support speech recognition. Try Chrome or Edge.\");\n    }\n\n    // Cleanup function\n    return () => {\n      if (recognitionRef.current) {\n        recognitionRef.current.stop();\n      }\n    };\n  }, [stopTimer, checkForSpeechPause, previousTranscript]);\n\n  useEffect(() => {\n    // Auto-scroll transcript area when content changes\n    if (transcriptAreaRef.current) {\n      transcriptAreaRef.current.scrollTop = transcriptAreaRef.current.scrollHeight;\n    }\n  }, [transcriptMessages, currentTranscript]);\n\n  useEffect(() => {\n    // Auto-scroll response area when content changes\n    if (responseAreaRef.current) {\n      responseAreaRef.current.scrollTop = responseAreaRef.current.scrollHeight;\n    }\n  }, [response]);\n\n  // Debug effect for auto-submit mode\n  useEffect(() => {\n    console.log(`Auto-submit mode ${autoSubmit ? 'enabled' : 'disabled'}`);\n  }, [autoSubmit]);\n\n  const startListening = useCallback(() => {\n    if (recognitionRef.current && !isListening) {\n      try {\n        recognitionRef.current.start();\n        setIsListening(true);\n        startTimer(); // Start the timer when listening begins\n      } catch (error) {\n        console.error(\"Speech recognition error:\", error);\n        // If recognition is already running, stop it first then restart\n        if (error.message.includes(\"already started\")) {\n          recognitionRef.current.stop();\n          setTimeout(() => {\n            recognitionRef.current.start();\n            setIsListening(true);\n            startTimer(); // Start the timer when listening begins\n          }, 100);\n        }\n      }\n    }\n  }, [isListening, startTimer]);\n\n  const stopListening = useCallback(() => {\n    if (recognitionRef.current && isListening) {\n      try {\n        recognitionRef.current.stop();\n        setIsListening(false);\n        stopTimer(); // Stop the timer when listening ends\n      } catch (error) {\n        console.error(\"Speech recognition error:\", error);\n      }\n    }\n  }, [isListening, stopTimer]);\n\n  // Send transcript to GPT for response\n  const sendToGPT = useCallback(async () => {\n    const apiKey = env.OPENAI_API_KEY;\n    const userText = currentTranscript.trim();\n\n    if (!apiKey) {\n      alert(\"Please add your OpenAI API key to the .env file as REACT_APP_OPENAI_API_KEY and restart the development server.\");\n      return;\n    }\n\n    if (!userText) {\n      alert(\"Please record or enter some text to send to GPT.\");\n      return;\n    }\n\n    // Add the current transcript to the messages array\n    setTranscriptMessages(prev => [\n      ...prev,\n      { text: userText, timestamp: new Date(), time: timerSeconds }\n    ]);\n\n    // Clear the current transcript input\n    setCurrentTranscript('');\n\n    setIsLoading(true);\n    setResponse('');\n\n    try {\n      console.log(\"Sending to GPT API:\", userText);\n      const response = await fetch(\"https://api.openai.com/v1/chat/completions\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          \"Authorization\": `Bearer ${apiKey}`\n        },\n        body: JSON.stringify({\n          model: \"gpt-4.1-nano\",\n          messages: [\n            {\n              role: \"system\",\n              content: \"You are Sensei, an AI interview assistant. Provide detailed, well-structured responses to interview questions. Format your answers with bullet points for clarity. Keep responses concise but comprehensive.\"\n            },\n            { role: \"user\", content: userText }\n          ],\n          stream: true\n        })\n      });\n\n      if (!response.ok || !response.body) throw new Error(\"Failed to stream response.\");\n\n      const reader = response.body.getReader();\n      const decoder = new TextDecoder(\"utf-8\");\n\n      let result = \"\";\n\n      while (true) {\n        const { value, done } = await reader.read();\n        if (done) break;\n\n        const chunk = decoder.decode(value, { stream: true });\n        const lines = chunk.split(\"\\n\").filter(line => line.trim().startsWith(\"data:\"));\n\n        for (const line of lines) {\n          const data = line.replace(/^data: /, '');\n          if (data === \"[DONE]\") break;\n\n          try {\n            const json = JSON.parse(data);\n            const content = json.choices?.[0]?.delta?.content;\n            if (content) {\n              result += content;\n              setResponse(result);\n            }\n          } catch (e) {\n            console.error(\"Error parsing JSON:\", e);\n          }\n        }\n      }\n    } catch (error) {\n      console.error(\"Streaming Error:\", error);\n      setResponse(\"Error occurred: \" + error.message);\n    } finally {\n      setIsLoading(false);\n    }\n  }, [currentTranscript, timerSeconds]);\n\n  const clearTranscript = () => {\n    setCurrentTranscript('');\n    setTranscriptMessages([]);\n  };\n\n  const clearResponse = () => {\n    setResponse('');\n  };\n\n  // Handle end interview with confirmation popup\n  const [showEndConfirmation, setShowEndConfirmation] = useState(false);\n\n  const handleEndInterview = () => {\n    setShowEndConfirmation(true);\n  };\n\n  const confirmEndInterview = () => {\n    // Here you would handle the actual end interview logic\n    // For example, redirect to a summary page or reset the state\n    setShowEndConfirmation(false);\n    clearTranscript();\n    clearResponse();\n    // You could also redirect to another page\n    // window.location.href = '/';\n  };\n\n  const cancelEndInterview = () => {\n    setShowEndConfirmation(false);\n  };\n\n  const generateQuestion = () => {\n    // This would generate a sample interview question\n    const questions = [\n      \"Tell me about yourself.\",\n      \"What are your greatest strengths?\",\n      \"What do you consider to be your weaknesses?\",\n      \"Why do you want this job?\",\n      \"Where do you see yourself in five years?\",\n      \"Why should we hire you?\",\n      \"What is your greatest professional achievement?\",\n      \"Tell me about a challenge or conflict you've faced at work, and how you dealt with it.\",\n      \"Tell me about a time you demonstrated leadership skills.\",\n      \"What's your management style?\",\n      \"How do you handle stress and pressure?\",\n      \"What are your salary expectations?\",\n      \"What do you like to do outside of work?\",\n      \"What are your career goals?\",\n      \"Why are you leaving your current job?\",\n      \"How do you prioritize your work?\",\n      \"What are you passionate about?\",\n      \"What makes you unique?\",\n      \"What should I know that's not on your resume?\",\n      \"What would your first 30, 60, or 90 days look like in this role?\"\n    ];\n\n    const randomQuestion = questions[Math.floor(Math.random() * questions.length)];\n\n    // Add the generated question directly to the transcript messages\n    setTranscriptMessages(prev => [\n      ...prev,\n      { text: randomQuestion, timestamp: new Date(), time: timerSeconds }\n    ]);\n  };\n\n  return (\n    <div className=\"interview-assistant\">\n      <div className=\"interview-container\">\n        <div className=\"transcription-panel\">\n          {/* <div className=\"panel-header\">\n            <h3>Transcription Messages</h3>\n            <p className=\"panel-description\">\n              Interviewer questions will be transcribed in this section for your review and response.\n            </p>\n          </div> */}\n\n          <div className=\"panel-content\">\n            <div\n              ref={transcriptAreaRef}\n              className=\"transcript-content\"\n            >\n              {transcriptMessages.length > 0 ? (\n                <div className=\"transcript-messages\">\n                  {transcriptMessages.map((msg, index) => (\n                    <div key={index} className=\"transcript-message\">\n                      <div className=\"timestamp\">{formatTime(msg.time)}</div>\n                      <div className=\"message-text\">{msg.text}</div>\n                    </div>\n                  ))}\n                </div>\n              ) : (\n                <div className=\"empty-state\">Your interview questions will appear here</div>\n              )}\n\n              {currentTranscript && (\n                <div className=\"transcript-message current\">\n                  <div className=\"timestamp\">{formatTime(timerSeconds)}</div>\n                  <div className=\"message-text\">{currentTranscript}</div>\n                </div>\n              )}\n            </div>\n          </div>\n\n          <div className=\"panel-footer\">\n            <div className=\"user-label\">Interviewer</div>\n            <button\n              className=\"generate-button\"\n              onClick={generateQuestion}\n            >\n              Generate\n            </button>\n          </div>\n        </div>\n\n        <div className=\"response-panel\">\n          <div className=\"panel-header\">\n            <h3>Answers</h3>\n            <p className=\"panel-description\">\n              Displayed here are the intelligent responses generated by Sensei. Whenever the interviewer concludes a question,\n              provides a tailored answer for you.\n            </p>\n          </div>\n\n          <div className=\"panel-content\">\n            <div\n              ref={responseAreaRef}\n              className=\"response-content\"\n            >\n              {response ? (\n                <div className=\"response-message\">\n                  <div className=\"message-text\">{response}</div>\n                </div>\n              ) : (\n                <div className=\"empty-state\">InterviewAssistant's responses will appear here</div>\n              )}\n            </div>\n          </div>\n\n          <div className=\"panel-footer\">\n            <div className=\"user-label\">InterviewAssistant's</div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"controls-container\">\n        <div className=\"timer-display\">\n          {isListening ? (\n            <>\n              <span className=\"recording-dot\"></span>\n              {formatTime(timerSeconds)}\n            </>\n          ) : (\n            formatTime(timerSeconds)\n          )}\n        </div>\n\n        <div className=\"limit-indicator\">15-minute limit</div>\n\n        <div className=\"input-controls\">\n          <input\n            type=\"text\"\n            className=\"text-input\"\n            value={currentTranscript}\n            onChange={(e) => setCurrentTranscript(e.target.value)}\n            placeholder=\"Type your question here...\"\n          />\n\n          <button\n            className=\"submit-button\"\n            onClick={sendToGPT}\n            disabled={isLoading || !currentTranscript.trim()}\n          >\n            <span className=\"arrow-icon\">↑</span>\n          </button>\n        </div>\n\n        <div className=\"action-buttons\">\n          <button\n            className={`toggle-button ${autoSubmit ? 'active' : ''}`}\n            onClick={() => setAutoSubmit(prev => !prev)}\n            title={autoSubmit ? \"Auto-submit enabled\" : \"Auto-submit disabled\"}\n            data-active={autoSubmit}\n          >\n            {autoSubmit ? \"Auto\" : \"Manual\"}\n          </button>\n\n          <button\n            className={`mic-button ${isListening ? 'active' : ''}`}\n            onClick={isListening ? stopListening : startListening}\n          >\n            {isListening ? \"Stop\" : \"Start\"}\n          </button>\n\n          <button\n            className=\"settings-button\"\n          >\n            ⚙️\n          </button>\n\n          <button\n            className=\"end-button\"\n            onClick={handleEndInterview}\n          >\n            End\n          </button>\n        </div>\n      </div>\n\n      {/* End Interview Confirmation Modal */}\n      {showEndConfirmation && (\n        <div className=\"modal-overlay\">\n          <div className=\"modal-container\">\n            <div className=\"modal-header\">\n              <h3>End interview confirmation</h3>\n            </div>\n            <div className=\"modal-content\">\n              <p>This action cannot be undone, and you will need to start a new interview to continue.</p>\n            </div>\n            <div className=\"modal-footer\">\n              <button className=\"cancel-button\" onClick={cancelEndInterview}>\n                Cancel\n              </button>\n              <button className=\"confirm-button\" onClick={confirmEndInterview}>\n                OK\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n\nexport default InterviewAssistant;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AACvE,OAAO,0BAA0B;AACjC,OAAOC,GAAG,MAAM,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/B,SAASC,kBAAkBA,CAAA,EAAG;EAAAC,EAAA;EAC5B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACa,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAACe,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACiB,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACmB,SAAS,EAAEC,YAAY,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACqB,UAAU,EAAEC,aAAa,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACrD,MAAM,CAACuB,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;;EAElE;EACA,MAAM,CAACyB,YAAY,EAAEC,eAAe,CAAC,GAAG1B,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM2B,gBAAgB,GAAG1B,MAAM,CAAC,IAAI,CAAC;EACrC,MAAM2B,aAAa,GAAG3B,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;;EAEpC,MAAM4B,cAAc,GAAG5B,MAAM,CAAC,IAAI,CAAC;EACnC,MAAM6B,iBAAiB,GAAG7B,MAAM,CAAC,IAAI,CAAC;EACtC,MAAM8B,eAAe,GAAG9B,MAAM,CAAC,IAAI,CAAC;;EAEpC;EACA,MAAM+B,UAAU,GAAG7B,WAAW,CAAE8B,YAAY,IAAK;IAC/C,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,YAAY,GAAG,EAAE,CAAC;IAC7C,MAAMI,OAAO,GAAGJ,YAAY,GAAG,EAAE;IACjC,OAAO,GAAGC,OAAO,CAACI,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,OAAO,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACxF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,UAAU,GAAGrC,WAAW,CAAC,MAAM;IACnC;IACAuB,eAAe,CAAC,CAAC,CAAC;;IAElB;IACA,IAAIC,gBAAgB,CAACc,OAAO,EAAE;MAC5BC,aAAa,CAACf,gBAAgB,CAACc,OAAO,CAAC;IACzC;;IAEA;IACAd,gBAAgB,CAACc,OAAO,GAAGE,WAAW,CAAC,MAAM;MAC3CjB,eAAe,CAACkB,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IACnC,CAAC,EAAE,IAAI,CAAC;EACV,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,SAAS,GAAG1C,WAAW,CAAC,MAAM;IAClC,IAAIwB,gBAAgB,CAACc,OAAO,EAAE;MAC5BC,aAAa,CAACf,gBAAgB,CAACc,OAAO,CAAC;MACvCd,gBAAgB,CAACc,OAAO,GAAG,IAAI;IACjC;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMK,mBAAmB,GAAG3C,WAAW,CAAC,MAAM;IAC5C;IACA,IAAIyB,aAAa,CAACa,OAAO,EAAE;MACzBM,YAAY,CAACnB,aAAa,CAACa,OAAO,CAAC;IACrC;;IAEA;IACAb,aAAa,CAACa,OAAO,GAAGO,UAAU,CAAC,MAAM;MACvCC,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE;QAC9C7B,UAAU;QACV8B,OAAO,EAAE,CAAC,CAACpC,iBAAiB,CAACqC,IAAI,CAAC,CAAC;QACnCzC,WAAW;QACXQ;MACF,CAAC,CAAC;;MAEF;MACA;MACA;MACA;MACA;MACA,IAAIE,UAAU,IACVN,iBAAiB,CAACqC,IAAI,CAAC,CAAC,IACxBzC,WAAW,IACX,CAACQ,SAAS,EAAE;QAEd8B,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;;QAEzD;QACA,MAAMG,YAAY,GAAGC,QAAQ,CAACC,aAAa,CAAC,gBAAgB,CAAC;QAC7D,IAAIF,YAAY,IAAI,CAACA,YAAY,CAACG,QAAQ,EAAE;UAC1CH,YAAY,CAACI,KAAK,CAAC,CAAC;QACtB;MACF;IACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;EACZ,CAAC,EAAE,CAACpC,UAAU,EAAEN,iBAAiB,EAAEJ,WAAW,EAAEQ,SAAS,CAAC,CAAC;;EAE3D;EACAjB,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX,IAAIyB,gBAAgB,CAACc,OAAO,EAAE;QAC5BC,aAAa,CAACf,gBAAgB,CAACc,OAAO,CAAC;MACzC;MACA,IAAIb,aAAa,CAACa,OAAO,EAAE;QACzBM,YAAY,CAACnB,aAAa,CAACa,OAAO,CAAC;MACrC;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAENvC,SAAS,CAAC,MAAM;IACd;IACA,MAAMwD,iBAAiB,GAAGC,MAAM,CAACD,iBAAiB,IAAIC,MAAM,CAACC,uBAAuB;IACpF,IAAIF,iBAAiB,EAAE;MACrB7B,cAAc,CAACY,OAAO,GAAG,IAAIiB,iBAAiB,CAAC,CAAC;MAChD7B,cAAc,CAACY,OAAO,CAACoB,IAAI,GAAG,OAAO;MACrChC,cAAc,CAACY,OAAO,CAACqB,cAAc,GAAG,IAAI;MAC5CjC,cAAc,CAACY,OAAO,CAACsB,UAAU,GAAG,IAAI;MAExC,IAAIC,eAAe,GAAG,EAAE;MAExBnC,cAAc,CAACY,OAAO,CAACwB,QAAQ,GAAIC,KAAK,IAAK;QAC3C,IAAIC,iBAAiB,GAAG,EAAE;;QAE1B;QACA3C,qBAAqB,CAACwC,eAAe,GAAGG,iBAAiB,CAAC;QAE1D,KAAK,IAAIC,CAAC,GAAGF,KAAK,CAACG,WAAW,EAAED,CAAC,GAAGF,KAAK,CAACI,OAAO,CAACC,MAAM,EAAEH,CAAC,EAAE,EAAE;UAC7D,MAAMI,UAAU,GAAGN,KAAK,CAACI,OAAO,CAACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAACI,UAAU;UACjD,IAAIN,KAAK,CAACI,OAAO,CAACF,CAAC,CAAC,CAACK,OAAO,EAAE;YAC5BT,eAAe,IAAIQ,UAAU,GAAG,GAAG;UACrC,CAAC,MAAM;YACLL,iBAAiB,IAAIK,UAAU;UACjC;QACF;QAEA,MAAME,aAAa,GAAGV,eAAe,GAAGG,iBAAiB;QACzDnD,oBAAoB,CAAC0D,aAAa,CAAC;;QAEnC;QACA,IAAIA,aAAa,KAAKnD,kBAAkB,EAAE;UACxCuB,mBAAmB,CAAC,CAAC;QACvB;MACF,CAAC;MAEDjB,cAAc,CAACY,OAAO,CAACkC,OAAO,GAAIT,KAAK,IAAK;QAC1CjB,OAAO,CAAC2B,KAAK,CAAC,0BAA0B,EAAEV,KAAK,CAACU,KAAK,CAAC;QACtDC,KAAK,CAAC,kBAAkB,GAAGX,KAAK,CAACU,KAAK,CAAC;QACvChE,cAAc,CAAC,KAAK,CAAC;QACrBiC,SAAS,CAAC,CAAC;MACb,CAAC;IACH,CAAC,MAAM;MACLgC,KAAK,CAAC,sEAAsE,CAAC;IAC/E;;IAEA;IACA,OAAO,MAAM;MACX,IAAIhD,cAAc,CAACY,OAAO,EAAE;QAC1BZ,cAAc,CAACY,OAAO,CAACqC,IAAI,CAAC,CAAC;MAC/B;IACF,CAAC;EACH,CAAC,EAAE,CAACjC,SAAS,EAAEC,mBAAmB,EAAEvB,kBAAkB,CAAC,CAAC;EAExDrB,SAAS,CAAC,MAAM;IACd;IACA,IAAI4B,iBAAiB,CAACW,OAAO,EAAE;MAC7BX,iBAAiB,CAACW,OAAO,CAACsC,SAAS,GAAGjD,iBAAiB,CAACW,OAAO,CAACuC,YAAY;IAC9E;EACF,CAAC,EAAE,CAACnE,kBAAkB,EAAEE,iBAAiB,CAAC,CAAC;EAE3Cb,SAAS,CAAC,MAAM;IACd;IACA,IAAI6B,eAAe,CAACU,OAAO,EAAE;MAC3BV,eAAe,CAACU,OAAO,CAACsC,SAAS,GAAGhD,eAAe,CAACU,OAAO,CAACuC,YAAY;IAC1E;EACF,CAAC,EAAE,CAAC/D,QAAQ,CAAC,CAAC;;EAEd;EACAf,SAAS,CAAC,MAAM;IACd+C,OAAO,CAACC,GAAG,CAAC,oBAAoB7B,UAAU,GAAG,SAAS,GAAG,UAAU,EAAE,CAAC;EACxE,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;EAEhB,MAAM4D,cAAc,GAAG9E,WAAW,CAAC,MAAM;IACvC,IAAI0B,cAAc,CAACY,OAAO,IAAI,CAAC9B,WAAW,EAAE;MAC1C,IAAI;QACFkB,cAAc,CAACY,OAAO,CAACyC,KAAK,CAAC,CAAC;QAC9BtE,cAAc,CAAC,IAAI,CAAC;QACpB4B,UAAU,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,OAAOoC,KAAK,EAAE;QACd3B,OAAO,CAAC2B,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD;QACA,IAAIA,KAAK,CAACO,OAAO,CAACC,QAAQ,CAAC,iBAAiB,CAAC,EAAE;UAC7CvD,cAAc,CAACY,OAAO,CAACqC,IAAI,CAAC,CAAC;UAC7B9B,UAAU,CAAC,MAAM;YACfnB,cAAc,CAACY,OAAO,CAACyC,KAAK,CAAC,CAAC;YAC9BtE,cAAc,CAAC,IAAI,CAAC;YACpB4B,UAAU,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,EAAE,GAAG,CAAC;QACT;MACF;IACF;EACF,CAAC,EAAE,CAAC7B,WAAW,EAAE6B,UAAU,CAAC,CAAC;EAE7B,MAAM6C,aAAa,GAAGlF,WAAW,CAAC,MAAM;IACtC,IAAI0B,cAAc,CAACY,OAAO,IAAI9B,WAAW,EAAE;MACzC,IAAI;QACFkB,cAAc,CAACY,OAAO,CAACqC,IAAI,CAAC,CAAC;QAC7BlE,cAAc,CAAC,KAAK,CAAC;QACrBiC,SAAS,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,OAAO+B,KAAK,EAAE;QACd3B,OAAO,CAAC2B,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACnD;IACF;EACF,CAAC,EAAE,CAACjE,WAAW,EAAEkC,SAAS,CAAC,CAAC;;EAE5B;EACA,MAAMyC,SAAS,GAAGnF,WAAW,CAAC,YAAY;IACxC,MAAMoF,MAAM,GAAGnF,GAAG,CAACoF,cAAc;IACjC,MAAMC,QAAQ,GAAG1E,iBAAiB,CAACqC,IAAI,CAAC,CAAC;IAEzC,IAAI,CAACmC,MAAM,EAAE;MACXV,KAAK,CAAC,iHAAiH,CAAC;MACxH;IACF;IAEA,IAAI,CAACY,QAAQ,EAAE;MACbZ,KAAK,CAAC,kDAAkD,CAAC;MACzD;IACF;;IAEA;IACA/D,qBAAqB,CAAC8B,IAAI,IAAI,CAC5B,GAAGA,IAAI,EACP;MAAE8C,IAAI,EAAED,QAAQ;MAAEE,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;MAAEC,IAAI,EAAEpE;IAAa,CAAC,CAC9D,CAAC;;IAEF;IACAT,oBAAoB,CAAC,EAAE,CAAC;IAExBI,YAAY,CAAC,IAAI,CAAC;IAClBF,WAAW,CAAC,EAAE,CAAC;IAEf,IAAI;MACF+B,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEuC,QAAQ,CAAC;MAC5C,MAAMxE,QAAQ,GAAG,MAAM6E,KAAK,CAAC,4CAA4C,EAAE;QACzEC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAE,UAAUT,MAAM;QACnC,CAAC;QACDU,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBC,KAAK,EAAE,cAAc;UACrBC,QAAQ,EAAE,CACR;YACEC,IAAI,EAAE,QAAQ;YACdC,OAAO,EAAE;UACX,CAAC,EACD;YAAED,IAAI,EAAE,MAAM;YAAEC,OAAO,EAAEd;UAAS,CAAC,CACpC;UACDe,MAAM,EAAE;QACV,CAAC;MACH,CAAC,CAAC;MAEF,IAAI,CAACvF,QAAQ,CAACwF,EAAE,IAAI,CAACxF,QAAQ,CAACgF,IAAI,EAAE,MAAM,IAAIS,KAAK,CAAC,4BAA4B,CAAC;MAEjF,MAAMC,MAAM,GAAG1F,QAAQ,CAACgF,IAAI,CAACW,SAAS,CAAC,CAAC;MACxC,MAAMC,OAAO,GAAG,IAAIC,WAAW,CAAC,OAAO,CAAC;MAExC,IAAIC,MAAM,GAAG,EAAE;MAEf,OAAO,IAAI,EAAE;QACX,MAAM;UAAEC,KAAK;UAAEC;QAAK,CAAC,GAAG,MAAMN,MAAM,CAACO,IAAI,CAAC,CAAC;QAC3C,IAAID,IAAI,EAAE;QAEV,MAAME,KAAK,GAAGN,OAAO,CAACO,MAAM,CAACJ,KAAK,EAAE;UAAER,MAAM,EAAE;QAAK,CAAC,CAAC;QACrD,MAAMa,KAAK,GAAGF,KAAK,CAACG,KAAK,CAAC,IAAI,CAAC,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACpE,IAAI,CAAC,CAAC,CAACqE,UAAU,CAAC,OAAO,CAAC,CAAC;QAE/E,KAAK,MAAMD,IAAI,IAAIH,KAAK,EAAE;UACxB,MAAMK,IAAI,GAAGF,IAAI,CAACG,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;UACxC,IAAID,IAAI,KAAK,QAAQ,EAAE;UAEvB,IAAI;YAAA,IAAAE,aAAA,EAAAC,cAAA,EAAAC,oBAAA;YACF,MAAMC,IAAI,GAAG7B,IAAI,CAAC8B,KAAK,CAACN,IAAI,CAAC;YAC7B,MAAMnB,OAAO,IAAAqB,aAAA,GAAGG,IAAI,CAACE,OAAO,cAAAL,aAAA,wBAAAC,cAAA,GAAZD,aAAA,CAAe,CAAC,CAAC,cAAAC,cAAA,wBAAAC,oBAAA,GAAjBD,cAAA,CAAmBK,KAAK,cAAAJ,oBAAA,uBAAxBA,oBAAA,CAA0BvB,OAAO;YACjD,IAAIA,OAAO,EAAE;cACXQ,MAAM,IAAIR,OAAO;cACjBrF,WAAW,CAAC6F,MAAM,CAAC;YACrB;UACF,CAAC,CAAC,OAAOoB,CAAC,EAAE;YACVlF,OAAO,CAAC2B,KAAK,CAAC,qBAAqB,EAAEuD,CAAC,CAAC;UACzC;QACF;MACF;IACF,CAAC,CAAC,OAAOvD,KAAK,EAAE;MACd3B,OAAO,CAAC2B,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MACxC1D,WAAW,CAAC,kBAAkB,GAAG0D,KAAK,CAACO,OAAO,CAAC;IACjD,CAAC,SAAS;MACR/D,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,CAACL,iBAAiB,EAAEU,YAAY,CAAC,CAAC;EAErC,MAAM2G,eAAe,GAAGA,CAAA,KAAM;IAC5BpH,oBAAoB,CAAC,EAAE,CAAC;IACxBF,qBAAqB,CAAC,EAAE,CAAC;EAC3B,CAAC;EAED,MAAMuH,aAAa,GAAGA,CAAA,KAAM;IAC1BnH,WAAW,CAAC,EAAE,CAAC;EACjB,CAAC;;EAED;EACA,MAAM,CAACoH,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGvI,QAAQ,CAAC,KAAK,CAAC;EAErE,MAAMwI,kBAAkB,GAAGA,CAAA,KAAM;IAC/BD,sBAAsB,CAAC,IAAI,CAAC;EAC9B,CAAC;EAED,MAAME,mBAAmB,GAAGA,CAAA,KAAM;IAChC;IACA;IACAF,sBAAsB,CAAC,KAAK,CAAC;IAC7BH,eAAe,CAAC,CAAC;IACjBC,aAAa,CAAC,CAAC;IACf;IACA;EACF,CAAC;EAED,MAAMK,kBAAkB,GAAGA,CAAA,KAAM;IAC/BH,sBAAsB,CAAC,KAAK,CAAC;EAC/B,CAAC;EAED,MAAMI,gBAAgB,GAAGA,CAAA,KAAM;IAC7B;IACA,MAAMC,SAAS,GAAG,CAChB,yBAAyB,EACzB,mCAAmC,EACnC,6CAA6C,EAC7C,2BAA2B,EAC3B,0CAA0C,EAC1C,yBAAyB,EACzB,iDAAiD,EACjD,wFAAwF,EACxF,0DAA0D,EAC1D,+BAA+B,EAC/B,wCAAwC,EACxC,oCAAoC,EACpC,yCAAyC,EACzC,6BAA6B,EAC7B,uCAAuC,EACvC,kCAAkC,EAClC,gCAAgC,EAChC,wBAAwB,EACxB,+CAA+C,EAC/C,kEAAkE,CACnE;IAED,MAAMC,cAAc,GAAGD,SAAS,CAACzG,IAAI,CAACC,KAAK,CAACD,IAAI,CAAC2G,MAAM,CAAC,CAAC,GAAGF,SAAS,CAACrE,MAAM,CAAC,CAAC;;IAE9E;IACAzD,qBAAqB,CAAC8B,IAAI,IAAI,CAC5B,GAAGA,IAAI,EACP;MAAE8C,IAAI,EAAEmD,cAAc;MAAElD,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;MAAEC,IAAI,EAAEpE;IAAa,CAAC,CACpE,CAAC;EACJ,CAAC;EAED,oBACEnB,OAAA;IAAKyI,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAClC1I,OAAA;MAAKyI,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClC1I,OAAA;QAAKyI,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAQlC1I,OAAA;UAAKyI,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5B1I,OAAA;YACE2I,GAAG,EAAEnH,iBAAkB;YACvBiH,SAAS,EAAC,oBAAoB;YAAAC,QAAA,GAE7BnI,kBAAkB,CAAC0D,MAAM,GAAG,CAAC,gBAC5BjE,OAAA;cAAKyI,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EACjCnI,kBAAkB,CAACqI,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBACjC9I,OAAA;gBAAiByI,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBAC7C1I,OAAA;kBAAKyI,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAEhH,UAAU,CAACmH,GAAG,CAACtD,IAAI;gBAAC;kBAAAwD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACvDlJ,OAAA;kBAAKyI,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAEG,GAAG,CAACzD;gBAAI;kBAAA2D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA,GAFtCJ,KAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,gBAENlJ,OAAA;cAAKyI,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAyC;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAC5E,EAEAzI,iBAAiB,iBAChBT,OAAA;cAAKyI,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBACzC1I,OAAA;gBAAKyI,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAEhH,UAAU,CAACP,YAAY;cAAC;gBAAA4H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC3DlJ,OAAA;gBAAKyI,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAEjI;cAAiB;gBAAAsI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlJ,OAAA;UAAKyI,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B1I,OAAA;YAAKyI,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAW;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC7ClJ,OAAA;YACEyI,SAAS,EAAC,iBAAiB;YAC3BU,OAAO,EAAEd,gBAAiB;YAAAK,QAAA,EAC3B;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENlJ,OAAA;QAAKyI,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B1I,OAAA;UAAKyI,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B1I,OAAA;YAAA0I,QAAA,EAAI;UAAO;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChBlJ,OAAA;YAAGyI,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAC;UAGjC;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENlJ,OAAA;UAAKyI,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5B1I,OAAA;YACE2I,GAAG,EAAElH,eAAgB;YACrBgH,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAE3B/H,QAAQ,gBACPX,OAAA;cAAKyI,SAAS,EAAC,kBAAkB;cAAAC,QAAA,eAC/B1I,OAAA;gBAAKyI,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAE/H;cAAQ;gBAAAoI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,gBAENlJ,OAAA;cAAKyI,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAA+C;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAClF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlJ,OAAA;UAAKyI,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3B1I,OAAA;YAAKyI,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAoB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENlJ,OAAA;MAAKyI,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBACjC1I,OAAA;QAAKyI,SAAS,EAAC,eAAe;QAAAC,QAAA,EAC3BrI,WAAW,gBACVL,OAAA,CAAAE,SAAA;UAAAwI,QAAA,gBACE1I,OAAA;YAAMyI,SAAS,EAAC;UAAe;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EACtCxH,UAAU,CAACP,YAAY,CAAC;QAAA,eACzB,CAAC,GAEHO,UAAU,CAACP,YAAY;MACxB;QAAA4H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENlJ,OAAA;QAAKyI,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAC;MAAe;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAEtDlJ,OAAA;QAAKyI,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B1I,OAAA;UACEoJ,IAAI,EAAC,MAAM;UACXX,SAAS,EAAC,YAAY;UACtB/B,KAAK,EAAEjG,iBAAkB;UACzB4I,QAAQ,EAAGxB,CAAC,IAAKnH,oBAAoB,CAACmH,CAAC,CAACyB,MAAM,CAAC5C,KAAK,CAAE;UACtD6C,WAAW,EAAC;QAA4B;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eAEFlJ,OAAA;UACEyI,SAAS,EAAC,eAAe;UACzBU,OAAO,EAAEnE,SAAU;UACnB9B,QAAQ,EAAErC,SAAS,IAAI,CAACJ,iBAAiB,CAACqC,IAAI,CAAC,CAAE;UAAA4F,QAAA,eAEjD1I,OAAA;YAAMyI,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAC;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENlJ,OAAA;QAAKyI,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B1I,OAAA;UACEyI,SAAS,EAAE,iBAAiB1H,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;UACzDoI,OAAO,EAAEA,CAAA,KAAMnI,aAAa,CAACsB,IAAI,IAAI,CAACA,IAAI,CAAE;UAC5CkH,KAAK,EAAEzI,UAAU,GAAG,qBAAqB,GAAG,sBAAuB;UACnE,eAAaA,UAAW;UAAA2H,QAAA,EAEvB3H,UAAU,GAAG,MAAM,GAAG;QAAQ;UAAAgI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eAETlJ,OAAA;UACEyI,SAAS,EAAE,cAAcpI,WAAW,GAAG,QAAQ,GAAG,EAAE,EAAG;UACvD8I,OAAO,EAAE9I,WAAW,GAAG0E,aAAa,GAAGJ,cAAe;UAAA+D,QAAA,EAErDrI,WAAW,GAAG,MAAM,GAAG;QAAO;UAAA0I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eAETlJ,OAAA;UACEyI,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAC5B;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETlJ,OAAA;UACEyI,SAAS,EAAC,YAAY;UACtBU,OAAO,EAAEjB,kBAAmB;UAAAQ,QAAA,EAC7B;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLlB,mBAAmB,iBAClBhI,OAAA;MAAKyI,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC5B1I,OAAA;QAAKyI,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9B1I,OAAA;UAAKyI,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3B1I,OAAA;YAAA0I,QAAA,EAAI;UAA0B;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,eACNlJ,OAAA;UAAKyI,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5B1I,OAAA;YAAA0I,QAAA,EAAG;UAAqF;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzF,CAAC,eACNlJ,OAAA;UAAKyI,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B1I,OAAA;YAAQyI,SAAS,EAAC,eAAe;YAACU,OAAO,EAAEf,kBAAmB;YAAAM,QAAA,EAAC;UAE/D;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTlJ,OAAA;YAAQyI,SAAS,EAAC,gBAAgB;YAACU,OAAO,EAAEhB,mBAAoB;YAAAO,QAAA,EAAC;UAEjE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAAC9I,EAAA,CAvgBQD,kBAAkB;AAAAsJ,EAAA,GAAlBtJ,kBAAkB;AAygB3B,eAAeA,kBAAkB;AAAC,IAAAsJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}