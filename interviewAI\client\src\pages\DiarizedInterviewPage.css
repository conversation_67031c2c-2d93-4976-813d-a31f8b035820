.diarized-interview-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  background-color: #f8f9fa;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.page-header {
  padding: 20px;
  background-color: white;
  border-bottom: 1px solid #e9ecef;
  text-align: center;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #333;
}

.description {
  color: #666;
  margin: 10px 0 0;
  font-size: 14px;
}

.interview-container {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.transcription-panel,
.response-panel {
  display: flex;
  flex-direction: column;
  flex: 1;
  border-right: 1px solid #e9ecef;
  background-color: white;
  overflow: hidden;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #e9ecef;
  background-color: #f8f9fa;
}

.panel-header h2 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.panel-description {
  margin: 5px 0 0;
  font-size: 12px;
  color: #666;
  font-style: italic;
}

.auto-submit-toggle {
  display: flex;
  align-items: center;
}

.auto-submit-toggle label {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #555;
  cursor: pointer;
}

.auto-submit-toggle input[type="checkbox"] {
  margin-right: 8px;
}

.manual-submit {
  padding: 15px;
  border-top: 1px solid #e9ecef;
  display: flex;
  justify-content: flex-end;
}

.submit-button {
  background-color: #4285f4;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.submit-button:hover {
  background-color: #3367d6;
}

.submit-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.response-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.response-message {
  background-color: #f0f8f4;
  padding: 16px;
  border-radius: 8px;
  border-left: 3px solid #34a853;
  margin-bottom: 16px;
  animation: fade-in 0.5s ease-out;
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message-text {
  font-size: 15px;
  line-height: 1.6;
  color: #333;
  white-space: pre-wrap;
}

.empty-state {
  color: #999;
  text-align: center;
  font-style: italic;
  padding: 40px 20px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .interview-container {
    flex-direction: column;
  }

  .transcription-panel,
  .response-panel {
    flex: none;
  }

  .transcription-panel {
    height: 60%;
  }

  .response-panel {
    height: 40%;
    border-right: none;
    border-top: 1px solid #e9ecef;
  }
}
