{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\interview-ai-app\\\\interviewAI\\\\client\\\\src\\\\components\\\\AutomatedInterview.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect, useCallback } from 'react';\nimport './AutomatedInterview.css';\nimport env from '../utils/env';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction AutomatedInterview() {\n  _s();\n  // Main states\n  const [step, setStep] = useState('configure'); // configure, connect, interview\n  const [isConnected, setIsConnected] = useState(false);\n  const [isListening, setIsListening] = useState(false);\n  const [isRecording, setIsRecording] = useState(false);\n  const [transcriptMessages, setTranscriptMessages] = useState([]);\n  const [currentTranscript, setCurrentTranscript] = useState('');\n  const [response, setResponse] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [usingGoogleAPI, setUsingGoogleAPI] = useState(false);\n\n  // Configuration states\n  const [specialization, setSpecialization] = useState('software-engineering');\n  const [language, setLanguage] = useState('english');\n\n  // Timer state\n  const [timerSeconds, setTimerSeconds] = useState(0);\n  const timerIntervalRef = useRef(null);\n  const pauseTimerRef = useRef(null); // Timer for detecting speech pauses\n\n  // Refs\n  const recognitionRef = useRef(null);\n  const transcriptAreaRef = useRef(null);\n  const responseAreaRef = useRef(null);\n  const mediaStreamRef = useRef(null); // For screen sharing stream\n\n  // Format seconds to MM:SS\n  const formatTime = useCallback(totalSeconds => {\n    const minutes = Math.floor(totalSeconds / 60);\n    const seconds = totalSeconds % 60;\n    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\n  }, []);\n\n  // Start the timer\n  const startTimer = useCallback(() => {\n    // Reset timer when starting\n    setTimerSeconds(0);\n\n    // Clear any existing interval\n    if (timerIntervalRef.current) {\n      clearInterval(timerIntervalRef.current);\n    }\n\n    // Start a new interval\n    timerIntervalRef.current = setInterval(() => {\n      setTimerSeconds(prev => prev + 1);\n    }, 1000);\n  }, []);\n\n  // Stop the timer\n  const stopTimer = useCallback(() => {\n    if (timerIntervalRef.current) {\n      clearInterval(timerIntervalRef.current);\n      timerIntervalRef.current = null;\n    }\n  }, []);\n\n  // Send transcript to GPT for response\n  const sendToGPT = useCallback(async text => {\n    console.log(\"sendToGPT called with text:\", text);\n    const apiKey = env.OPENAI_API_KEY;\n    const userText = text || currentTranscript.trim();\n    if (!apiKey) {\n      alert(\"Please add your OpenAI API key to the .env file as REACT_APP_OPENAI_API_KEY and restart the development server.\");\n      return;\n    }\n    if (!userText) {\n      console.warn(\"No transcript to send.\");\n      return;\n    }\n    console.log(\"Processing transcript:\", userText);\n\n    // Note: We're not adding to transcriptMessages here anymore\n    // That's now handled in the checkForSpeechPause function\n    // to avoid duplicate messages\n\n    // Clear the current transcript input if it matches what we're sending\n    // This prevents sending the same text twice\n    if (currentTranscript.trim() === userText) {\n      console.log(\"Clearing current transcript\");\n      setCurrentTranscript('');\n    }\n    setIsLoading(true);\n    setResponse('');\n    try {\n      const response = await fetch(\"https://api.openai.com/v1/chat/completions\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          \"Authorization\": `Bearer ${apiKey}`\n        },\n        body: JSON.stringify({\n          model: \"gpt-4.1-nano\",\n          messages: [{\n            role: \"system\",\n            content: `You are an AI interview assistant for ${specialization} interviews. Provide detailed, well-structured responses to interview questions. Format your answers with bullet points for clarity. Keep responses concise but comprehensive.`\n          }, {\n            role: \"user\",\n            content: userText\n          }],\n          stream: true\n        })\n      });\n      if (!response.ok || !response.body) throw new Error(\"Failed to stream response.\");\n      const reader = response.body.getReader();\n      const decoder = new TextDecoder(\"utf-8\");\n      let result = \"\";\n      while (true) {\n        const {\n          value,\n          done\n        } = await reader.read();\n        if (done) break;\n        const chunk = decoder.decode(value, {\n          stream: true\n        });\n        const lines = chunk.split(\"\\n\").filter(line => line.trim().startsWith(\"data:\"));\n        for (const line of lines) {\n          const data = line.replace(/^data: /, '');\n          if (data === \"[DONE]\") break;\n          try {\n            var _json$choices, _json$choices$, _json$choices$$delta;\n            const json = JSON.parse(data);\n            const content = (_json$choices = json.choices) === null || _json$choices === void 0 ? void 0 : (_json$choices$ = _json$choices[0]) === null || _json$choices$ === void 0 ? void 0 : (_json$choices$$delta = _json$choices$.delta) === null || _json$choices$$delta === void 0 ? void 0 : _json$choices$$delta.content;\n            if (content) {\n              result += content;\n              setResponse(result);\n            }\n          } catch (e) {\n            console.error(\"Error parsing JSON:\", e);\n          }\n        }\n      }\n    } catch (error) {\n      console.error(\"Streaming Error:\", error);\n      setResponse(\"Error occurred: \" + error.message);\n    } finally {\n      setIsLoading(false);\n    }\n  }, [currentTranscript, specialization]); // timerSeconds is not needed here\n\n  // Function to check for speech pause and auto-submit\n  const checkForSpeechPause = useCallback(() => {\n    console.log(\"Checking for speech pause...\");\n\n    // Clear any existing pause timer\n    if (pauseTimerRef.current) {\n      clearTimeout(pauseTimerRef.current);\n      console.log(\"Cleared existing pause timer\");\n    }\n\n    // Set a new pause timer\n    pauseTimerRef.current = setTimeout(() => {\n      console.log(\"Pause timer triggered\");\n\n      // Only auto-submit if:\n      // 1. We have a non-empty transcript\n      // 2. We're currently listening\n      // 3. We're not already loading a response\n      const userText = currentTranscript.trim();\n      console.log(\"Checking conditions for auto-submit:\", {\n        hasText: !!userText,\n        isListening,\n        notLoading: !isLoading\n      });\n      if (userText && isListening && !isLoading) {\n        console.log(\"Auto-submitting transcript:\", userText);\n\n        // Add the transcript to the messages array\n        setTranscriptMessages(prev => {\n          const newMessages = [...prev, {\n            text: userText,\n            timestamp: new Date(),\n            time: timerSeconds\n          }];\n          console.log(\"Updated transcript messages:\", newMessages);\n          return newMessages;\n        });\n\n        // Clear the current transcript\n        setCurrentTranscript(\"\");\n\n        // Send to GPT for response\n        sendToGPT(userText);\n      }\n    }, 2000); // 2 second pause detection (increased from 1.5s for better reliability)\n  }, [currentTranscript, isListening, isLoading, sendToGPT, timerSeconds]);\n\n  // Clean up timer on unmount\n  useEffect(() => {\n    return () => {\n      if (timerIntervalRef.current) {\n        clearInterval(timerIntervalRef.current);\n      }\n      if (pauseTimerRef.current) {\n        clearTimeout(pauseTimerRef.current);\n      }\n    };\n  }, []);\n\n  // Initialize speech recognition\n  useEffect(() => {\n    try {\n      console.log(\"Initializing speech recognition...\");\n\n      // Use the browser's built-in SpeechRecognition API directly for better reliability\n      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n      if (!SpeechRecognition) {\n        throw new Error(\"Speech recognition is not supported in this browser\");\n      }\n\n      // Create a new recognition instance\n      recognitionRef.current = new SpeechRecognition();\n\n      // Configure the recognition\n      recognitionRef.current.lang = language === 'english' ? 'en-US' : language === 'spanish' ? 'es-ES' : language === 'french' ? 'fr-FR' : language === 'german' ? 'de-DE' : language === 'chinese' ? 'zh-CN' : language === 'japanese' ? 'ja-JP' : 'en-US';\n      recognitionRef.current.continuous = true;\n      recognitionRef.current.interimResults = true;\n\n      // Check if Google API is available\n      const hasGoogleAPI = env.hasGoogleSpeechAPI();\n      setUsingGoogleAPI(hasGoogleAPI);\n      console.log(`Using ${hasGoogleAPI ? 'Google Speech-to-Text API Avaialable' : 'Web Speech API'}`);\n\n      // Reset the transcript\n      let finalTranscript = \"\";\n      console.log(\"Setting up speech recognition event handlers...\");\n      recognitionRef.current.onresult = event => {\n        console.log(\"Speech recognition result received:\", event);\n        let interimTranscript = \"\";\n        for (let i = event.resultIndex; i < event.results.length; i++) {\n          const transcript = event.results[i][0].transcript;\n          const confidence = event.results[i][0].confidence;\n          console.log(`Transcript: \"${transcript}\" (Confidence: ${confidence.toFixed(2)})`);\n          if (event.results[i].isFinal) {\n            finalTranscript += transcript + \" \";\n            console.log(\"Final transcript updated:\", finalTranscript);\n          } else {\n            interimTranscript += transcript;\n            console.log(\"Interim transcript updated:\", interimTranscript);\n          }\n        }\n        const newTranscript = finalTranscript + interimTranscript;\n        console.log(\"Setting current transcript to:\", newTranscript);\n\n        // Force UI update with the new transcript - use a callback to ensure state is updated\n        setCurrentTranscript(prev => {\n          console.log(`Updating transcript from \"${prev}\" to \"${newTranscript}\"`);\n\n          // Force a DOM update by dispatching a custom event\n          window.dispatchEvent(new CustomEvent('transcriptUpdated', {\n            detail: {\n              transcript: newTranscript\n            }\n          }));\n          return newTranscript;\n        });\n\n        // Also update the DOM directly as a fallback\n        const transcriptElement = document.querySelector('.message-text .active-transcript');\n        if (transcriptElement) {\n          transcriptElement.textContent = newTranscript;\n        }\n\n        // Check for pause if transcript has changed\n        if (newTranscript.trim() !== \"\") {\n          console.log(\"Transcript not empty, checking for speech pause\");\n          checkForSpeechPause();\n        }\n      };\n      recognitionRef.current.onerror = event => {\n        console.error(\"Speech recognition error\", event.error, event);\n        if (event.error !== 'no-speech') {\n          console.error(\"Critical speech recognition error:\", event.error);\n          alert(\"Error occurred: \" + event.error);\n          setIsListening(false);\n          stopTimer();\n        } else {\n          console.log(\"No speech detected, continuing to listen\");\n        }\n      };\n      recognitionRef.current.onend = () => {\n        console.log(\"Speech recognition ended\");\n        // Restart recognition if we're still supposed to be listening\n        if (isListening) {\n          console.log(\"Still listening, attempting to restart recognition\");\n          try {\n            recognitionRef.current.start();\n            console.log(\"Recognition restarted successfully\");\n          } catch (error) {\n            console.error(\"Failed to restart recognition:\", error);\n          }\n        } else {\n          console.log(\"Not listening anymore, not restarting recognition\");\n        }\n      };\n\n      // We're not using MediaRecorder anymore to avoid errors\n      console.log(\"Using built-in speech recognition without MediaRecorder\");\n    } catch (error) {\n      console.error(\"Failed to initialize speech recognition:\", error);\n      alert(\"Your browser doesn't support speech recognition. Try Chrome or Edge.\");\n    }\n\n    // Cleanup function\n    return () => {\n      if (recognitionRef.current) {\n        try {\n          recognitionRef.current.stop();\n        } catch (error) {\n          console.error(\"Error stopping recognition:\", error);\n        }\n      }\n    };\n  }, [checkForSpeechPause, isListening, stopTimer, language, usingGoogleAPI]);\n\n  // Auto-scroll transcript area when content changes\n  useEffect(() => {\n    if (transcriptAreaRef.current) {\n      transcriptAreaRef.current.scrollTop = transcriptAreaRef.current.scrollHeight;\n    }\n  }, [transcriptMessages, currentTranscript]);\n\n  // Auto-scroll response area when content changes\n  useEffect(() => {\n    if (responseAreaRef.current) {\n      responseAreaRef.current.scrollTop = responseAreaRef.current.scrollHeight;\n    }\n  }, [response]);\n\n  // Start listening for speech\n  const startListening = useCallback(() => {\n    console.log(\"Starting speech recognition...\");\n    if (recognitionRef.current && !isListening) {\n      try {\n        console.log(\"Calling recognition.start()\");\n        recognitionRef.current.start();\n        console.log(\"Recognition started successfully\");\n        setIsListening(true);\n        startTimer(); // Start the timer when listening begins\n      } catch (error) {\n        console.error(\"Speech recognition error:\", error);\n        // If recognition is already running, stop it first then restart\n        if (error.message.includes(\"already started\")) {\n          console.log(\"Recognition already started, stopping and restarting\");\n          recognitionRef.current.stop();\n          setTimeout(() => {\n            console.log(\"Restarting recognition after stop\");\n            recognitionRef.current.start();\n            setIsListening(true);\n            startTimer(); // Start the timer when listening begins\n          }, 100);\n        }\n      }\n    } else {\n      console.log(\"Cannot start listening:\", recognitionRef.current ? \"Already listening\" : \"No recognition object\");\n    }\n  }, [isListening, startTimer]);\n\n  // Test speech recognition with a simulated result\n  const testSpeechRecognition = useCallback(() => {\n    console.log(\"Testing speech recognition with simulated result\");\n\n    // Create a simulated SpeechRecognitionEvent\n    const simulatedEvent = {\n      resultIndex: 0,\n      results: [{\n        0: {\n          transcript: \"This is a simulated speech recognition result.\",\n          confidence: 0.9\n        },\n        isFinal: true,\n        length: 1\n      }]\n    };\n\n    // If we have a recognition object, manually trigger its onresult handler\n    if (recognitionRef.current && recognitionRef.current.onresult) {\n      console.log(\"Manually triggering onresult handler with simulated event\");\n      recognitionRef.current.onresult(simulatedEvent);\n    } else {\n      console.log(\"Cannot test speech recognition: No recognition object or onresult handler\");\n      // Directly set the transcript as a fallback\n      setCurrentTranscript(\"This is a simulated speech recognition result.\");\n    }\n  }, []);\n\n  // Stop listening for speech\n  const stopListening = useCallback(() => {\n    if (recognitionRef.current && isListening) {\n      try {\n        recognitionRef.current.stop();\n        setIsListening(false);\n        stopTimer(); // Stop the timer when listening ends\n      } catch (error) {\n        console.error(\"Speech recognition error:\", error);\n      }\n    }\n  }, [isListening, stopTimer]);\n\n  // This section was removed to fix the duplicate declaration error\n\n  // Request screen sharing - simplified approach\n  const requestScreenSharing = async () => {\n    try {\n      console.log(\"Requesting screen sharing...\");\n\n      // First, get screen sharing permission\n      const displayStream = await navigator.mediaDevices.getDisplayMedia({\n        video: true,\n        audio: false // Don't request audio from screen to avoid conflicts\n      });\n      console.log(\"Screen sharing access granted\");\n      console.log(\"Screen tracks:\", displayStream.getTracks().map(t => `${t.kind} (${t.label})`));\n\n      // Store the display stream\n      mediaStreamRef.current = displayStream;\n\n      // Handle the case when user stops sharing\n      displayStream.getVideoTracks()[0].onended = () => {\n        console.log(\"User stopped screen sharing\");\n        setIsConnected(false);\n        stopRecording();\n      };\n\n      // Set connected state and move to interview step\n      setIsConnected(true);\n      setStep('interview');\n\n      // Start listening for speech - this is separate from recording\n      startListening();\n\n      // We don't need to start recording for speech recognition to work\n      // This avoids the MediaRecorder error\n      console.log(\"Speech recognition started without MediaRecorder\");\n    } catch (error) {\n      console.error(\"Error sharing screen:\", error);\n      alert(\"Failed to share screen: \" + error.message);\n    }\n  };\n\n  // We're not using MediaRecorder anymore to avoid errors\n  // Speech recognition works without recording\n\n  // Stop screen sharing and clean up\n  const stopRecording = () => {\n    console.log(\"Stopping screen sharing...\");\n\n    // We're not using MediaRecorder anymore, but we'll keep the function name for compatibility\n    setIsRecording(false);\n\n    // Safely stop all tracks in the media stream\n    if (mediaStreamRef.current) {\n      try {\n        console.log(\"Stopping all media tracks\");\n        mediaStreamRef.current.getTracks().forEach(track => {\n          try {\n            track.stop();\n            console.log(`Stopped ${track.kind} track: ${track.label}`);\n          } catch (trackError) {\n            console.error(`Error stopping ${track.kind} track:`, trackError);\n          }\n        });\n      } catch (streamError) {\n        console.error(\"Error stopping media stream tracks:\", streamError);\n      }\n\n      // Clear the reference\n      mediaStreamRef.current = null;\n    }\n\n    // Also stop listening\n    stopListening();\n    console.log(\"Screen sharing stopped\");\n  };\n\n  // End the interview\n  const endInterview = () => {\n    stopRecording();\n    stopListening();\n    setStep('configure');\n    setIsConnected(false);\n    setTranscriptMessages([]);\n    setCurrentTranscript('');\n    setResponse('');\n  };\n\n  // Render different steps\n  const renderConfigureStep = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"configure-step\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Configure AI\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 520,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"config-form\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Specialization\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 523,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: specialization,\n          onChange: e => setSpecialization(e.target.value),\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"software-engineering\",\n            children: \"Software Engineering\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 528,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"data-science\",\n            children: \"Data Science\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 529,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"product-management\",\n            children: \"Product Management\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 530,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"marketing\",\n            children: \"Marketing\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 531,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"sales\",\n            children: \"Sales\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 532,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"customer-service\",\n            children: \"Customer Service\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 533,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 524,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 522,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Language\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 538,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: language,\n          onChange: e => setLanguage(e.target.value),\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"english\",\n            children: \"English\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 543,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"spanish\",\n            children: \"Spanish\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 544,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"french\",\n            children: \"French\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 545,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"german\",\n            children: \"German\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 546,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"chinese\",\n            children: \"Chinese\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 547,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"japanese\",\n            children: \"Japanese\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 548,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 539,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 537,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 521,\n      columnNumber: 7\n    }, this), env.hasGoogleSpeechAPI() ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"api-status success\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"icon\",\n        children: \"\\u2713\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 555,\n        columnNumber: 11\n      }, this), \"Google Speech-to-Text API is configured and will be used for better voice recognition\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 554,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"api-status warning\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"icon\",\n        children: \"\\u2139\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 560,\n        columnNumber: 11\n      }, this), \"Google Speech-to-Text API is not configured. The app will use the browser's built-in speech recognition, which may be less accurate.\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 559,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"connect-button\",\n      onClick: () => setStep('connect'),\n      children: \"Next\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 565,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 519,\n    columnNumber: 5\n  }, this);\n  const renderConnectStep = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"connect-step\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Connect to Interview\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 576,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: \"Share your screen to start the interview process.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 577,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: \"Make sure you have the interview window open before proceeding.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 578,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"share-screen-button\",\n      onClick: requestScreenSharing,\n      children: \"Share Screen\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 580,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"back-button\",\n      onClick: () => setStep('configure'),\n      children: \"Back\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 587,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 575,\n    columnNumber: 5\n  }, this);\n  const renderInterviewStep = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"interview-step\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"interview-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"transcription-panel\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"panel-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Interviewer\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 601,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"connection-status\",\n            children: isConnected ? /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"connected\",\n              children: \"Connected\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 604,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"disconnected\",\n              children: \"Disconnected\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 606,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 602,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 600,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"panel-content\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: transcriptAreaRef,\n            className: \"transcript-content\",\n            children: [transcriptMessages.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"transcript-messages\",\n              children: transcriptMessages.map((msg, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"transcript-message\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"timestamp\",\n                  children: formatTime(msg.time)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 620,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"message-text\",\n                  children: msg.text\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 621,\n                  columnNumber: 23\n                }, this)]\n              }, `msg-${index}-${msg.time}`, true, {\n                fileName: _jsxFileName,\n                lineNumber: 619,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 617,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"empty-state\",\n              children: \"Interviewer questions will appear here automatically\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 626,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"transcript-message current\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"timestamp\",\n                children: formatTime(timerSeconds)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 631,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"message-text\",\n                children: currentTranscript ? /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"active-transcript\",\n                  children: currentTranscript\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 634,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"placeholder\",\n                  children: \"Waiting for speech...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 635,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 632,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 630,\n              columnNumber: 15\n            }, this), process.env.NODE_ENV === 'development' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"debug-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [\"Current transcript length: \", (currentTranscript === null || currentTranscript === void 0 ? void 0 : currentTranscript.length) || 0]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 643,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [\"Transcript messages: \", transcriptMessages.length]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 644,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [\"Is listening: \", isListening ? 'Yes' : 'No']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 645,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [\"Is recording: \", isRecording ? 'Yes' : 'No']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 646,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [\"Using Google API: \", usingGoogleAPI ? 'Yes' : 'No']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 647,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 642,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 612,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 611,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 599,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"response-panel\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"panel-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"AI Assistant\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 656,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 655,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"panel-content\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: responseAreaRef,\n            className: \"response-content\",\n            children: response ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"response-message\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"message-text\",\n                children: response\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 666,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 665,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"empty-state\",\n              children: \"AI responses will appear here automatically\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 669,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 660,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 659,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 654,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 598,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"controls-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"status-indicators\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"timer-display\",\n          children: isListening ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"recording-dot\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 681,\n              columnNumber: 17\n            }, this), formatTime(timerSeconds)]\n          }, void 0, true) : formatTime(timerSeconds)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 678,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"status-label\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: isListening ? \"Listening...\" : \"Paused\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 690,\n            columnNumber: 13\n          }, this), usingGoogleAPI && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"api-badge google\",\n            children: \"Google Speech API\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 692,\n            columnNumber: 15\n          }, this), !usingGoogleAPI && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"api-badge browser\",\n            children: \"Browser Speech API\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 695,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 689,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 677,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"action-buttons\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: `mic-button ${isListening ? 'active' : ''}`,\n          onClick: isListening ? stopListening : startListening,\n          children: isListening ? \"Pause\" : \"Resume\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 701,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"stop-button\",\n          onClick: endInterview,\n          children: \"Stop Recording\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 708,\n          columnNumber: 11\n        }, this), process.env.NODE_ENV === 'development' && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"test-button\",\n            onClick: () => {\n              const testText = \"This is a test transcript message. If you can see this, the transcript display is working correctly.\";\n              console.log(\"Adding test transcript:\", testText);\n              setTranscriptMessages(prev => [...prev, {\n                text: testText,\n                timestamp: new Date(),\n                time: timerSeconds\n              }]);\n            },\n            children: \"Add Test Message\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 718,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"test-button speech\",\n            onClick: testSpeechRecognition,\n            children: \"Test Speech Recognition\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 732,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 700,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 676,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 597,\n    columnNumber: 5\n  }, this);\n\n  // Render the appropriate step\n  const renderStep = () => {\n    switch (step) {\n      case 'configure':\n        return renderConfigureStep();\n      case 'connect':\n        return renderConnectStep();\n      case 'interview':\n        return renderInterviewStep();\n      default:\n        return renderConfigureStep();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"automated-interview\",\n    children: renderStep()\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 760,\n    columnNumber: 5\n  }, this);\n}\n_s(AutomatedInterview, \"2F8euyQt7zCLDMv1EypPpO6MmZM=\");\n_c = AutomatedInterview;\nexport default AutomatedInterview;\nvar _c;\n$RefreshReg$(_c, \"AutomatedInterview\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "useCallback", "env", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AutomatedInterview", "_s", "step", "setStep", "isConnected", "setIsConnected", "isListening", "setIsListening", "isRecording", "setIsRecording", "transcriptMessages", "setTranscriptMessages", "currentTranscript", "setCurrentTranscript", "response", "setResponse", "isLoading", "setIsLoading", "usingGoogleAPI", "setUsingGoogleAPI", "specialization", "setSpecialization", "language", "setLanguage", "timerSeconds", "setTimerSeconds", "timerIntervalRef", "pauseTimerRef", "recognitionRef", "transcriptAreaRef", "responseAreaRef", "mediaStreamRef", "formatTime", "totalSeconds", "minutes", "Math", "floor", "seconds", "toString", "padStart", "startTimer", "current", "clearInterval", "setInterval", "prev", "stopTimer", "sendToGPT", "text", "console", "log", "<PERSON><PERSON><PERSON><PERSON>", "OPENAI_API_KEY", "userText", "trim", "alert", "warn", "fetch", "method", "headers", "body", "JSON", "stringify", "model", "messages", "role", "content", "stream", "ok", "Error", "reader", "<PERSON><PERSON><PERSON><PERSON>", "decoder", "TextDecoder", "result", "value", "done", "read", "chunk", "decode", "lines", "split", "filter", "line", "startsWith", "data", "replace", "_json$choices", "_json$choices$", "_json$choices$$delta", "json", "parse", "choices", "delta", "e", "error", "message", "checkForSpeechPause", "clearTimeout", "setTimeout", "hasText", "notLoading", "newMessages", "timestamp", "Date", "time", "SpeechRecognition", "window", "webkitSpeechRecognition", "lang", "continuous", "interimResults", "hasGoogleAPI", "hasGoogleSpeechAPI", "finalTranscript", "on<PERSON>ult", "event", "interimTranscript", "i", "resultIndex", "results", "length", "transcript", "confidence", "toFixed", "isFinal", "newTranscript", "dispatchEvent", "CustomEvent", "detail", "transcriptElement", "document", "querySelector", "textContent", "onerror", "onend", "start", "stop", "scrollTop", "scrollHeight", "startListening", "includes", "testSpeechRecognition", "simulatedEvent", "stopListening", "requestScreenSharing", "displayStream", "navigator", "mediaDevices", "getDisplayMedia", "video", "audio", "getTracks", "map", "t", "kind", "label", "getVideoTracks", "onended", "stopRecording", "for<PERSON>ach", "track", "trackError", "streamError", "endInterview", "renderConfigureStep", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onChange", "target", "onClick", "renderConnectStep", "renderInterviewStep", "ref", "msg", "index", "process", "NODE_ENV", "testText", "renderStep", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/interview-ai-app/interviewAI/client/src/components/AutomatedInterview.jsx"], "sourcesContent": ["import React, { useState, useRef, useEffect, useCallback } from 'react';\nimport './AutomatedInterview.css';\nimport env from '../utils/env';\n\nfunction AutomatedInterview() {\n  // Main states\n  const [step, setStep] = useState('configure'); // configure, connect, interview\n  const [isConnected, setIsConnected] = useState(false);\n  const [isListening, setIsListening] = useState(false);\n  const [isRecording, setIsRecording] = useState(false);\n  const [transcriptMessages, setTranscriptMessages] = useState([]);\n  const [currentTranscript, setCurrentTranscript] = useState('');\n  const [response, setResponse] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [usingGoogleAPI, setUsingGoogleAPI] = useState(false);\n\n  // Configuration states\n  const [specialization, setSpecialization] = useState('software-engineering');\n  const [language, setLanguage] = useState('english');\n\n  // Timer state\n  const [timerSeconds, setTimerSeconds] = useState(0);\n  const timerIntervalRef = useRef(null);\n  const pauseTimerRef = useRef(null); // Timer for detecting speech pauses\n\n  // Refs\n  const recognitionRef = useRef(null);\n  const transcriptAreaRef = useRef(null);\n  const responseAreaRef = useRef(null);\n  const mediaStreamRef = useRef(null); // For screen sharing stream\n\n  // Format seconds to MM:SS\n  const formatTime = useCallback((totalSeconds) => {\n    const minutes = Math.floor(totalSeconds / 60);\n    const seconds = totalSeconds % 60;\n    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\n  }, []);\n\n  // Start the timer\n  const startTimer = useCallback(() => {\n    // Reset timer when starting\n    setTimerSeconds(0);\n\n    // Clear any existing interval\n    if (timerIntervalRef.current) {\n      clearInterval(timerIntervalRef.current);\n    }\n\n    // Start a new interval\n    timerIntervalRef.current = setInterval(() => {\n      setTimerSeconds(prev => prev + 1);\n    }, 1000);\n  }, []);\n\n  // Stop the timer\n  const stopTimer = useCallback(() => {\n    if (timerIntervalRef.current) {\n      clearInterval(timerIntervalRef.current);\n      timerIntervalRef.current = null;\n    }\n  }, []);\n\n  // Send transcript to GPT for response\n  const sendToGPT = useCallback(async (text) => {\n    console.log(\"sendToGPT called with text:\", text);\n\n    const apiKey = env.OPENAI_API_KEY;\n    const userText = text || currentTranscript.trim();\n\n    if (!apiKey) {\n      alert(\"Please add your OpenAI API key to the .env file as REACT_APP_OPENAI_API_KEY and restart the development server.\");\n      return;\n    }\n\n    if (!userText) {\n      console.warn(\"No transcript to send.\");\n      return;\n    }\n\n    console.log(\"Processing transcript:\", userText);\n\n    // Note: We're not adding to transcriptMessages here anymore\n    // That's now handled in the checkForSpeechPause function\n    // to avoid duplicate messages\n\n    // Clear the current transcript input if it matches what we're sending\n    // This prevents sending the same text twice\n    if (currentTranscript.trim() === userText) {\n      console.log(\"Clearing current transcript\");\n      setCurrentTranscript('');\n    }\n\n    setIsLoading(true);\n    setResponse('');\n\n    try {\n      const response = await fetch(\"https://api.openai.com/v1/chat/completions\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          \"Authorization\": `Bearer ${apiKey}`\n        },\n        body: JSON.stringify({\n          model: \"gpt-4.1-nano\",\n          messages: [\n            {\n              role: \"system\",\n              content: `You are an AI interview assistant for ${specialization} interviews. Provide detailed, well-structured responses to interview questions. Format your answers with bullet points for clarity. Keep responses concise but comprehensive.`\n            },\n            { role: \"user\", content: userText }\n          ],\n          stream: true\n        })\n      });\n\n      if (!response.ok || !response.body) throw new Error(\"Failed to stream response.\");\n\n      const reader = response.body.getReader();\n      const decoder = new TextDecoder(\"utf-8\");\n\n      let result = \"\";\n\n      while (true) {\n        const { value, done } = await reader.read();\n        if (done) break;\n\n        const chunk = decoder.decode(value, { stream: true });\n        const lines = chunk.split(\"\\n\").filter(line => line.trim().startsWith(\"data:\"));\n\n        for (const line of lines) {\n          const data = line.replace(/^data: /, '');\n          if (data === \"[DONE]\") break;\n\n          try {\n            const json = JSON.parse(data);\n            const content = json.choices?.[0]?.delta?.content;\n            if (content) {\n              result += content;\n              setResponse(result);\n            }\n          } catch (e) {\n            console.error(\"Error parsing JSON:\", e);\n          }\n        }\n      }\n    } catch (error) {\n      console.error(\"Streaming Error:\", error);\n      setResponse(\"Error occurred: \" + error.message);\n    } finally {\n      setIsLoading(false);\n    }\n  }, [currentTranscript, specialization]); // timerSeconds is not needed here\n\n  // Function to check for speech pause and auto-submit\n  const checkForSpeechPause = useCallback(() => {\n    console.log(\"Checking for speech pause...\");\n\n    // Clear any existing pause timer\n    if (pauseTimerRef.current) {\n      clearTimeout(pauseTimerRef.current);\n      console.log(\"Cleared existing pause timer\");\n    }\n\n    // Set a new pause timer\n    pauseTimerRef.current = setTimeout(() => {\n      console.log(\"Pause timer triggered\");\n\n      // Only auto-submit if:\n      // 1. We have a non-empty transcript\n      // 2. We're currently listening\n      // 3. We're not already loading a response\n      const userText = currentTranscript.trim();\n\n      console.log(\"Checking conditions for auto-submit:\", {\n        hasText: !!userText,\n        isListening,\n        notLoading: !isLoading\n      });\n\n      if (userText && isListening && !isLoading) {\n        console.log(\"Auto-submitting transcript:\", userText);\n\n        // Add the transcript to the messages array\n        setTranscriptMessages(prev => {\n          const newMessages = [\n            ...prev,\n            { text: userText, timestamp: new Date(), time: timerSeconds }\n          ];\n          console.log(\"Updated transcript messages:\", newMessages);\n          return newMessages;\n        });\n\n        // Clear the current transcript\n        setCurrentTranscript(\"\");\n\n        // Send to GPT for response\n        sendToGPT(userText);\n      }\n    }, 2000); // 2 second pause detection (increased from 1.5s for better reliability)\n  }, [currentTranscript, isListening, isLoading, sendToGPT, timerSeconds]);\n\n  // Clean up timer on unmount\n  useEffect(() => {\n    return () => {\n      if (timerIntervalRef.current) {\n        clearInterval(timerIntervalRef.current);\n      }\n      if (pauseTimerRef.current) {\n        clearTimeout(pauseTimerRef.current);\n      }\n    };\n  }, []);\n\n  // Initialize speech recognition\n  useEffect(() => {\n    try {\n      console.log(\"Initializing speech recognition...\");\n\n      // Use the browser's built-in SpeechRecognition API directly for better reliability\n      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n\n      if (!SpeechRecognition) {\n        throw new Error(\"Speech recognition is not supported in this browser\");\n      }\n\n      // Create a new recognition instance\n      recognitionRef.current = new SpeechRecognition();\n\n      // Configure the recognition\n      recognitionRef.current.lang = language === 'english' ? 'en-US' :\n                                   language === 'spanish' ? 'es-ES' :\n                                   language === 'french' ? 'fr-FR' :\n                                   language === 'german' ? 'de-DE' :\n                                   language === 'chinese' ? 'zh-CN' :\n                                   language === 'japanese' ? 'ja-JP' : 'en-US';\n      recognitionRef.current.continuous = true;\n      recognitionRef.current.interimResults = true;\n\n      // Check if Google API is available\n      const hasGoogleAPI = env.hasGoogleSpeechAPI();\n      setUsingGoogleAPI(hasGoogleAPI);\n      console.log(`Using ${hasGoogleAPI ? 'Google Speech-to-Text API Avaialable' : 'Web Speech API'}`);\n\n      // Reset the transcript\n      let finalTranscript = \"\";\n\n      console.log(\"Setting up speech recognition event handlers...\");\n\n      recognitionRef.current.onresult = (event) => {\n        console.log(\"Speech recognition result received:\", event);\n        let interimTranscript = \"\";\n\n        for (let i = event.resultIndex; i < event.results.length; i++) {\n          const transcript = event.results[i][0].transcript;\n          const confidence = event.results[i][0].confidence;\n          console.log(`Transcript: \"${transcript}\" (Confidence: ${confidence.toFixed(2)})`);\n\n          if (event.results[i].isFinal) {\n            finalTranscript += transcript + \" \";\n            console.log(\"Final transcript updated:\", finalTranscript);\n          } else {\n            interimTranscript += transcript;\n            console.log(\"Interim transcript updated:\", interimTranscript);\n          }\n        }\n\n        const newTranscript = finalTranscript + interimTranscript;\n        console.log(\"Setting current transcript to:\", newTranscript);\n\n        // Force UI update with the new transcript - use a callback to ensure state is updated\n        setCurrentTranscript(prev => {\n          console.log(`Updating transcript from \"${prev}\" to \"${newTranscript}\"`);\n\n          // Force a DOM update by dispatching a custom event\n          window.dispatchEvent(new CustomEvent('transcriptUpdated', {\n            detail: { transcript: newTranscript }\n          }));\n\n          return newTranscript;\n        });\n\n        // Also update the DOM directly as a fallback\n        const transcriptElement = document.querySelector('.message-text .active-transcript');\n        if (transcriptElement) {\n          transcriptElement.textContent = newTranscript;\n        }\n\n        // Check for pause if transcript has changed\n        if (newTranscript.trim() !== \"\") {\n          console.log(\"Transcript not empty, checking for speech pause\");\n          checkForSpeechPause();\n        }\n      };\n\n      recognitionRef.current.onerror = (event) => {\n        console.error(\"Speech recognition error\", event.error, event);\n        if (event.error !== 'no-speech') {\n          console.error(\"Critical speech recognition error:\", event.error);\n          alert(\"Error occurred: \" + event.error);\n          setIsListening(false);\n          stopTimer();\n        } else {\n          console.log(\"No speech detected, continuing to listen\");\n        }\n      };\n\n      recognitionRef.current.onend = () => {\n        console.log(\"Speech recognition ended\");\n        // Restart recognition if we're still supposed to be listening\n        if (isListening) {\n          console.log(\"Still listening, attempting to restart recognition\");\n          try {\n            recognitionRef.current.start();\n            console.log(\"Recognition restarted successfully\");\n          } catch (error) {\n            console.error(\"Failed to restart recognition:\", error);\n          }\n        } else {\n          console.log(\"Not listening anymore, not restarting recognition\");\n        }\n      };\n\n      // We're not using MediaRecorder anymore to avoid errors\n      console.log(\"Using built-in speech recognition without MediaRecorder\");\n\n    } catch (error) {\n      console.error(\"Failed to initialize speech recognition:\", error);\n      alert(\"Your browser doesn't support speech recognition. Try Chrome or Edge.\");\n    }\n\n    // Cleanup function\n    return () => {\n      if (recognitionRef.current) {\n        try {\n          recognitionRef.current.stop();\n        } catch (error) {\n          console.error(\"Error stopping recognition:\", error);\n        }\n      }\n    };\n  }, [checkForSpeechPause, isListening, stopTimer, language, usingGoogleAPI]);\n\n  // Auto-scroll transcript area when content changes\n  useEffect(() => {\n    if (transcriptAreaRef.current) {\n      transcriptAreaRef.current.scrollTop = transcriptAreaRef.current.scrollHeight;\n    }\n  }, [transcriptMessages, currentTranscript]);\n\n  // Auto-scroll response area when content changes\n  useEffect(() => {\n    if (responseAreaRef.current) {\n      responseAreaRef.current.scrollTop = responseAreaRef.current.scrollHeight;\n    }\n  }, [response]);\n\n  // Start listening for speech\n  const startListening = useCallback(() => {\n    console.log(\"Starting speech recognition...\");\n    if (recognitionRef.current && !isListening) {\n      try {\n        console.log(\"Calling recognition.start()\");\n        recognitionRef.current.start();\n        console.log(\"Recognition started successfully\");\n        setIsListening(true);\n        startTimer(); // Start the timer when listening begins\n      } catch (error) {\n        console.error(\"Speech recognition error:\", error);\n        // If recognition is already running, stop it first then restart\n        if (error.message.includes(\"already started\")) {\n          console.log(\"Recognition already started, stopping and restarting\");\n          recognitionRef.current.stop();\n          setTimeout(() => {\n            console.log(\"Restarting recognition after stop\");\n            recognitionRef.current.start();\n            setIsListening(true);\n            startTimer(); // Start the timer when listening begins\n          }, 100);\n        }\n      }\n    } else {\n      console.log(\"Cannot start listening:\",\n        recognitionRef.current ? \"Already listening\" : \"No recognition object\");\n    }\n  }, [isListening, startTimer]);\n\n  // Test speech recognition with a simulated result\n  const testSpeechRecognition = useCallback(() => {\n    console.log(\"Testing speech recognition with simulated result\");\n\n    // Create a simulated SpeechRecognitionEvent\n    const simulatedEvent = {\n      resultIndex: 0,\n      results: [\n        {\n          0: { transcript: \"This is a simulated speech recognition result.\", confidence: 0.9 },\n          isFinal: true,\n          length: 1\n        }\n      ]\n    };\n\n    // If we have a recognition object, manually trigger its onresult handler\n    if (recognitionRef.current && recognitionRef.current.onresult) {\n      console.log(\"Manually triggering onresult handler with simulated event\");\n      recognitionRef.current.onresult(simulatedEvent);\n    } else {\n      console.log(\"Cannot test speech recognition: No recognition object or onresult handler\");\n      // Directly set the transcript as a fallback\n      setCurrentTranscript(\"This is a simulated speech recognition result.\");\n    }\n  }, []);\n\n  // Stop listening for speech\n  const stopListening = useCallback(() => {\n    if (recognitionRef.current && isListening) {\n      try {\n        recognitionRef.current.stop();\n        setIsListening(false);\n        stopTimer(); // Stop the timer when listening ends\n      } catch (error) {\n        console.error(\"Speech recognition error:\", error);\n      }\n    }\n  }, [isListening, stopTimer]);\n\n  // This section was removed to fix the duplicate declaration error\n\n  // Request screen sharing - simplified approach\n  const requestScreenSharing = async () => {\n    try {\n      console.log(\"Requesting screen sharing...\");\n\n      // First, get screen sharing permission\n      const displayStream = await navigator.mediaDevices.getDisplayMedia({\n        video: true,\n        audio: false // Don't request audio from screen to avoid conflicts\n      });\n\n      console.log(\"Screen sharing access granted\");\n      console.log(\"Screen tracks:\", displayStream.getTracks().map(t => `${t.kind} (${t.label})`));\n\n      // Store the display stream\n      mediaStreamRef.current = displayStream;\n\n      // Handle the case when user stops sharing\n      displayStream.getVideoTracks()[0].onended = () => {\n        console.log(\"User stopped screen sharing\");\n        setIsConnected(false);\n        stopRecording();\n      };\n\n      // Set connected state and move to interview step\n      setIsConnected(true);\n      setStep('interview');\n\n      // Start listening for speech - this is separate from recording\n      startListening();\n\n      // We don't need to start recording for speech recognition to work\n      // This avoids the MediaRecorder error\n      console.log(\"Speech recognition started without MediaRecorder\");\n\n    } catch (error) {\n      console.error(\"Error sharing screen:\", error);\n      alert(\"Failed to share screen: \" + error.message);\n    }\n  };\n\n  // We're not using MediaRecorder anymore to avoid errors\n  // Speech recognition works without recording\n\n  // Stop screen sharing and clean up\n  const stopRecording = () => {\n    console.log(\"Stopping screen sharing...\");\n\n    // We're not using MediaRecorder anymore, but we'll keep the function name for compatibility\n    setIsRecording(false);\n\n    // Safely stop all tracks in the media stream\n    if (mediaStreamRef.current) {\n      try {\n        console.log(\"Stopping all media tracks\");\n        mediaStreamRef.current.getTracks().forEach(track => {\n          try {\n            track.stop();\n            console.log(`Stopped ${track.kind} track: ${track.label}`);\n          } catch (trackError) {\n            console.error(`Error stopping ${track.kind} track:`, trackError);\n          }\n        });\n      } catch (streamError) {\n        console.error(\"Error stopping media stream tracks:\", streamError);\n      }\n\n      // Clear the reference\n      mediaStreamRef.current = null;\n    }\n\n    // Also stop listening\n    stopListening();\n\n    console.log(\"Screen sharing stopped\");\n  };\n\n  // End the interview\n  const endInterview = () => {\n    stopRecording();\n    stopListening();\n    setStep('configure');\n    setIsConnected(false);\n    setTranscriptMessages([]);\n    setCurrentTranscript('');\n    setResponse('');\n  };\n\n  // Render different steps\n  const renderConfigureStep = () => (\n    <div className=\"configure-step\">\n      <h2>Configure AI</h2>\n      <div className=\"config-form\">\n        <div className=\"form-group\">\n          <label>Specialization</label>\n          <select\n            value={specialization}\n            onChange={(e) => setSpecialization(e.target.value)}\n          >\n            <option value=\"software-engineering\">Software Engineering</option>\n            <option value=\"data-science\">Data Science</option>\n            <option value=\"product-management\">Product Management</option>\n            <option value=\"marketing\">Marketing</option>\n            <option value=\"sales\">Sales</option>\n            <option value=\"customer-service\">Customer Service</option>\n          </select>\n        </div>\n\n        <div className=\"form-group\">\n          <label>Language</label>\n          <select\n            value={language}\n            onChange={(e) => setLanguage(e.target.value)}\n          >\n            <option value=\"english\">English</option>\n            <option value=\"spanish\">Spanish</option>\n            <option value=\"french\">French</option>\n            <option value=\"german\">German</option>\n            <option value=\"chinese\">Chinese</option>\n            <option value=\"japanese\">Japanese</option>\n          </select>\n        </div>\n      </div>\n\n      {env.hasGoogleSpeechAPI() ? (\n        <div className=\"api-status success\">\n          <span className=\"icon\">✓</span>\n          Google Speech-to-Text API is configured and will be used for better voice recognition\n        </div>\n      ) : (\n        <div className=\"api-status warning\">\n          <span className=\"icon\">ℹ</span>\n          Google Speech-to-Text API is not configured. The app will use the browser's built-in speech recognition, which may be less accurate.\n        </div>\n      )}\n\n      <button\n        className=\"connect-button\"\n        onClick={() => setStep('connect')}\n      >\n        Next\n      </button>\n    </div>\n  );\n\n  const renderConnectStep = () => (\n    <div className=\"connect-step\">\n      <h2>Connect to Interview</h2>\n      <p>Share your screen to start the interview process.</p>\n      <p>Make sure you have the interview window open before proceeding.</p>\n\n      <button\n        className=\"share-screen-button\"\n        onClick={requestScreenSharing}\n      >\n        Share Screen\n      </button>\n\n      <button\n        className=\"back-button\"\n        onClick={() => setStep('configure')}\n      >\n        Back\n      </button>\n    </div>\n  );\n\n  const renderInterviewStep = () => (\n    <div className=\"interview-step\">\n      <div className=\"interview-container\">\n        <div className=\"transcription-panel\">\n          <div className=\"panel-header\">\n            <h3>Interviewer</h3>\n            <div className=\"connection-status\">\n              {isConnected ? (\n                <span className=\"connected\">Connected</span>\n              ) : (\n                <span className=\"disconnected\">Disconnected</span>\n              )}\n            </div>\n          </div>\n\n          <div className=\"panel-content\">\n            <div\n              ref={transcriptAreaRef}\n              className=\"transcript-content\"\n            >\n              {transcriptMessages.length > 0 ? (\n                <div className=\"transcript-messages\">\n                  {transcriptMessages.map((msg, index) => (\n                    <div key={`msg-${index}-${msg.time}`} className=\"transcript-message\">\n                      <div className=\"timestamp\">{formatTime(msg.time)}</div>\n                      <div className=\"message-text\">{msg.text}</div>\n                    </div>\n                  ))}\n                </div>\n              ) : (\n                <div className=\"empty-state\">Interviewer questions will appear here automatically</div>\n              )}\n\n              {/* Always show current transcript area, even if empty */}\n              <div className=\"transcript-message current\">\n                <div className=\"timestamp\">{formatTime(timerSeconds)}</div>\n                <div className=\"message-text\">\n                  {currentTranscript ?\n                    <span className=\"active-transcript\">{currentTranscript}</span> :\n                    <span className=\"placeholder\">Waiting for speech...</span>\n                  }\n                </div>\n              </div>\n\n              {/* Debug info - only shown in development */}\n              {process.env.NODE_ENV === 'development' && (\n                <div className=\"debug-info\">\n                  <div>Current transcript length: {currentTranscript?.length || 0}</div>\n                  <div>Transcript messages: {transcriptMessages.length}</div>\n                  <div>Is listening: {isListening ? 'Yes' : 'No'}</div>\n                  <div>Is recording: {isRecording ? 'Yes' : 'No'}</div>\n                  <div>Using Google API: {usingGoogleAPI ? 'Yes' : 'No'}</div>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n\n        <div className=\"response-panel\">\n          <div className=\"panel-header\">\n            <h3>AI Assistant</h3>\n          </div>\n\n          <div className=\"panel-content\">\n            <div\n              ref={responseAreaRef}\n              className=\"response-content\"\n            >\n              {response ? (\n                <div className=\"response-message\">\n                  <div className=\"message-text\">{response}</div>\n                </div>\n              ) : (\n                <div className=\"empty-state\">AI responses will appear here automatically</div>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"controls-container\">\n        <div className=\"status-indicators\">\n          <div className=\"timer-display\">\n            {isListening ? (\n              <>\n                <span className=\"recording-dot\"></span>\n                {formatTime(timerSeconds)}\n              </>\n            ) : (\n              formatTime(timerSeconds)\n            )}\n          </div>\n\n          <div className=\"status-label\">\n            <span>{isListening ? \"Listening...\" : \"Paused\"}</span>\n            {usingGoogleAPI && (\n              <span className=\"api-badge google\">Google Speech API</span>\n            )}\n            {!usingGoogleAPI && (\n              <span className=\"api-badge browser\">Browser Speech API</span>\n            )}\n          </div>\n        </div>\n\n        <div className=\"action-buttons\">\n          <button\n            className={`mic-button ${isListening ? 'active' : ''}`}\n            onClick={isListening ? stopListening : startListening}\n          >\n            {isListening ? \"Pause\" : \"Resume\"}\n          </button>\n\n          <button\n            className=\"stop-button\"\n            onClick={endInterview}\n          >\n            Stop Recording\n          </button>\n\n          {/* Test buttons for debugging - only shown in development */}\n          {process.env.NODE_ENV === 'development' && (\n            <>\n              <button\n                className=\"test-button\"\n                onClick={() => {\n                  const testText = \"This is a test transcript message. If you can see this, the transcript display is working correctly.\";\n                  console.log(\"Adding test transcript:\", testText);\n                  setTranscriptMessages(prev => [\n                    ...prev,\n                    { text: testText, timestamp: new Date(), time: timerSeconds }\n                  ]);\n                }}\n              >\n                Add Test Message\n              </button>\n\n              <button\n                className=\"test-button speech\"\n                onClick={testSpeechRecognition}\n              >\n                Test Speech Recognition\n              </button>\n            </>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n\n  // Render the appropriate step\n  const renderStep = () => {\n    switch (step) {\n      case 'configure':\n        return renderConfigureStep();\n      case 'connect':\n        return renderConnectStep();\n      case 'interview':\n        return renderInterviewStep();\n      default:\n        return renderConfigureStep();\n    }\n  };\n\n  return (\n    <div className=\"automated-interview\">\n      {renderStep()}\n    </div>\n  );\n}\n\nexport default AutomatedInterview;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AACvE,OAAO,0BAA0B;AACjC,OAAOC,GAAG,MAAM,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/B,SAASC,kBAAkBA,CAAA,EAAG;EAAAC,EAAA;EAC5B;EACA,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGZ,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACa,WAAW,EAAEC,cAAc,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACe,WAAW,EAAEC,cAAc,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACiB,WAAW,EAAEC,cAAc,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACmB,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAACqB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACuB,QAAQ,EAAEC,WAAW,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACyB,SAAS,EAAEC,YAAY,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC2B,cAAc,EAAEC,iBAAiB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;;EAE3D;EACA,MAAM,CAAC6B,cAAc,EAAEC,iBAAiB,CAAC,GAAG9B,QAAQ,CAAC,sBAAsB,CAAC;EAC5E,MAAM,CAAC+B,QAAQ,EAAEC,WAAW,CAAC,GAAGhC,QAAQ,CAAC,SAAS,CAAC;;EAEnD;EACA,MAAM,CAACiC,YAAY,EAAEC,eAAe,CAAC,GAAGlC,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAMmC,gBAAgB,GAAGlC,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMmC,aAAa,GAAGnC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;;EAEpC;EACA,MAAMoC,cAAc,GAAGpC,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMqC,iBAAiB,GAAGrC,MAAM,CAAC,IAAI,CAAC;EACtC,MAAMsC,eAAe,GAAGtC,MAAM,CAAC,IAAI,CAAC;EACpC,MAAMuC,cAAc,GAAGvC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;;EAErC;EACA,MAAMwC,UAAU,GAAGtC,WAAW,CAAEuC,YAAY,IAAK;IAC/C,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,YAAY,GAAG,EAAE,CAAC;IAC7C,MAAMI,OAAO,GAAGJ,YAAY,GAAG,EAAE;IACjC,OAAO,GAAGC,OAAO,CAACI,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,OAAO,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACxF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,UAAU,GAAG9C,WAAW,CAAC,MAAM;IACnC;IACA+B,eAAe,CAAC,CAAC,CAAC;;IAElB;IACA,IAAIC,gBAAgB,CAACe,OAAO,EAAE;MAC5BC,aAAa,CAAChB,gBAAgB,CAACe,OAAO,CAAC;IACzC;;IAEA;IACAf,gBAAgB,CAACe,OAAO,GAAGE,WAAW,CAAC,MAAM;MAC3ClB,eAAe,CAACmB,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IACnC,CAAC,EAAE,IAAI,CAAC;EACV,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,SAAS,GAAGnD,WAAW,CAAC,MAAM;IAClC,IAAIgC,gBAAgB,CAACe,OAAO,EAAE;MAC5BC,aAAa,CAAChB,gBAAgB,CAACe,OAAO,CAAC;MACvCf,gBAAgB,CAACe,OAAO,GAAG,IAAI;IACjC;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMK,SAAS,GAAGpD,WAAW,CAAC,MAAOqD,IAAI,IAAK;IAC5CC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEF,IAAI,CAAC;IAEhD,MAAMG,MAAM,GAAGvD,GAAG,CAACwD,cAAc;IACjC,MAAMC,QAAQ,GAAGL,IAAI,IAAInC,iBAAiB,CAACyC,IAAI,CAAC,CAAC;IAEjD,IAAI,CAACH,MAAM,EAAE;MACXI,KAAK,CAAC,iHAAiH,CAAC;MACxH;IACF;IAEA,IAAI,CAACF,QAAQ,EAAE;MACbJ,OAAO,CAACO,IAAI,CAAC,wBAAwB,CAAC;MACtC;IACF;IAEAP,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEG,QAAQ,CAAC;;IAE/C;IACA;IACA;;IAEA;IACA;IACA,IAAIxC,iBAAiB,CAACyC,IAAI,CAAC,CAAC,KAAKD,QAAQ,EAAE;MACzCJ,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;MAC1CpC,oBAAoB,CAAC,EAAE,CAAC;IAC1B;IAEAI,YAAY,CAAC,IAAI,CAAC;IAClBF,WAAW,CAAC,EAAE,CAAC;IAEf,IAAI;MACF,MAAMD,QAAQ,GAAG,MAAM0C,KAAK,CAAC,4CAA4C,EAAE;QACzEC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAE,UAAUR,MAAM;QACnC,CAAC;QACDS,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBC,KAAK,EAAE,cAAc;UACrBC,QAAQ,EAAE,CACR;YACEC,IAAI,EAAE,QAAQ;YACdC,OAAO,EAAE,yCAAyC7C,cAAc;UAClE,CAAC,EACD;YAAE4C,IAAI,EAAE,MAAM;YAAEC,OAAO,EAAEb;UAAS,CAAC,CACpC;UACDc,MAAM,EAAE;QACV,CAAC;MACH,CAAC,CAAC;MAEF,IAAI,CAACpD,QAAQ,CAACqD,EAAE,IAAI,CAACrD,QAAQ,CAAC6C,IAAI,EAAE,MAAM,IAAIS,KAAK,CAAC,4BAA4B,CAAC;MAEjF,MAAMC,MAAM,GAAGvD,QAAQ,CAAC6C,IAAI,CAACW,SAAS,CAAC,CAAC;MACxC,MAAMC,OAAO,GAAG,IAAIC,WAAW,CAAC,OAAO,CAAC;MAExC,IAAIC,MAAM,GAAG,EAAE;MAEf,OAAO,IAAI,EAAE;QACX,MAAM;UAAEC,KAAK;UAAEC;QAAK,CAAC,GAAG,MAAMN,MAAM,CAACO,IAAI,CAAC,CAAC;QAC3C,IAAID,IAAI,EAAE;QAEV,MAAME,KAAK,GAAGN,OAAO,CAACO,MAAM,CAACJ,KAAK,EAAE;UAAER,MAAM,EAAE;QAAK,CAAC,CAAC;QACrD,MAAMa,KAAK,GAAGF,KAAK,CAACG,KAAK,CAAC,IAAI,CAAC,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAAC7B,IAAI,CAAC,CAAC,CAAC8B,UAAU,CAAC,OAAO,CAAC,CAAC;QAE/E,KAAK,MAAMD,IAAI,IAAIH,KAAK,EAAE;UACxB,MAAMK,IAAI,GAAGF,IAAI,CAACG,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;UACxC,IAAID,IAAI,KAAK,QAAQ,EAAE;UAEvB,IAAI;YAAA,IAAAE,aAAA,EAAAC,cAAA,EAAAC,oBAAA;YACF,MAAMC,IAAI,GAAG7B,IAAI,CAAC8B,KAAK,CAACN,IAAI,CAAC;YAC7B,MAAMnB,OAAO,IAAAqB,aAAA,GAAGG,IAAI,CAACE,OAAO,cAAAL,aAAA,wBAAAC,cAAA,GAAZD,aAAA,CAAe,CAAC,CAAC,cAAAC,cAAA,wBAAAC,oBAAA,GAAjBD,cAAA,CAAmBK,KAAK,cAAAJ,oBAAA,uBAAxBA,oBAAA,CAA0BvB,OAAO;YACjD,IAAIA,OAAO,EAAE;cACXQ,MAAM,IAAIR,OAAO;cACjBlD,WAAW,CAAC0D,MAAM,CAAC;YACrB;UACF,CAAC,CAAC,OAAOoB,CAAC,EAAE;YACV7C,OAAO,CAAC8C,KAAK,CAAC,qBAAqB,EAAED,CAAC,CAAC;UACzC;QACF;MACF;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd9C,OAAO,CAAC8C,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MACxC/E,WAAW,CAAC,kBAAkB,GAAG+E,KAAK,CAACC,OAAO,CAAC;IACjD,CAAC,SAAS;MACR9E,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,CAACL,iBAAiB,EAAEQ,cAAc,CAAC,CAAC,CAAC,CAAC;;EAEzC;EACA,MAAM4E,mBAAmB,GAAGtG,WAAW,CAAC,MAAM;IAC5CsD,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;;IAE3C;IACA,IAAItB,aAAa,CAACc,OAAO,EAAE;MACzBwD,YAAY,CAACtE,aAAa,CAACc,OAAO,CAAC;MACnCO,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;IAC7C;;IAEA;IACAtB,aAAa,CAACc,OAAO,GAAGyD,UAAU,CAAC,MAAM;MACvClD,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;;MAEpC;MACA;MACA;MACA;MACA,MAAMG,QAAQ,GAAGxC,iBAAiB,CAACyC,IAAI,CAAC,CAAC;MAEzCL,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE;QAClDkD,OAAO,EAAE,CAAC,CAAC/C,QAAQ;QACnB9C,WAAW;QACX8F,UAAU,EAAE,CAACpF;MACf,CAAC,CAAC;MAEF,IAAIoC,QAAQ,IAAI9C,WAAW,IAAI,CAACU,SAAS,EAAE;QACzCgC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEG,QAAQ,CAAC;;QAEpD;QACAzC,qBAAqB,CAACiC,IAAI,IAAI;UAC5B,MAAMyD,WAAW,GAAG,CAClB,GAAGzD,IAAI,EACP;YAAEG,IAAI,EAAEK,QAAQ;YAAEkD,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;YAAEC,IAAI,EAAEhF;UAAa,CAAC,CAC9D;UACDwB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEoD,WAAW,CAAC;UACxD,OAAOA,WAAW;QACpB,CAAC,CAAC;;QAEF;QACAxF,oBAAoB,CAAC,EAAE,CAAC;;QAExB;QACAiC,SAAS,CAACM,QAAQ,CAAC;MACrB;IACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;EACZ,CAAC,EAAE,CAACxC,iBAAiB,EAAEN,WAAW,EAAEU,SAAS,EAAE8B,SAAS,EAAEtB,YAAY,CAAC,CAAC;;EAExE;EACA/B,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX,IAAIiC,gBAAgB,CAACe,OAAO,EAAE;QAC5BC,aAAa,CAAChB,gBAAgB,CAACe,OAAO,CAAC;MACzC;MACA,IAAId,aAAa,CAACc,OAAO,EAAE;QACzBwD,YAAY,CAACtE,aAAa,CAACc,OAAO,CAAC;MACrC;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAhD,SAAS,CAAC,MAAM;IACd,IAAI;MACFuD,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;;MAEjD;MACA,MAAMwD,iBAAiB,GAAGC,MAAM,CAACD,iBAAiB,IAAIC,MAAM,CAACC,uBAAuB;MAEpF,IAAI,CAACF,iBAAiB,EAAE;QACtB,MAAM,IAAIrC,KAAK,CAAC,qDAAqD,CAAC;MACxE;;MAEA;MACAxC,cAAc,CAACa,OAAO,GAAG,IAAIgE,iBAAiB,CAAC,CAAC;;MAEhD;MACA7E,cAAc,CAACa,OAAO,CAACmE,IAAI,GAAGtF,QAAQ,KAAK,SAAS,GAAG,OAAO,GACjCA,QAAQ,KAAK,SAAS,GAAG,OAAO,GAChCA,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAC/BA,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAC/BA,QAAQ,KAAK,SAAS,GAAG,OAAO,GAChCA,QAAQ,KAAK,UAAU,GAAG,OAAO,GAAG,OAAO;MACxEM,cAAc,CAACa,OAAO,CAACoE,UAAU,GAAG,IAAI;MACxCjF,cAAc,CAACa,OAAO,CAACqE,cAAc,GAAG,IAAI;;MAE5C;MACA,MAAMC,YAAY,GAAGpH,GAAG,CAACqH,kBAAkB,CAAC,CAAC;MAC7C7F,iBAAiB,CAAC4F,YAAY,CAAC;MAC/B/D,OAAO,CAACC,GAAG,CAAC,SAAS8D,YAAY,GAAG,sCAAsC,GAAG,gBAAgB,EAAE,CAAC;;MAEhG;MACA,IAAIE,eAAe,GAAG,EAAE;MAExBjE,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;MAE9DrB,cAAc,CAACa,OAAO,CAACyE,QAAQ,GAAIC,KAAK,IAAK;QAC3CnE,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEkE,KAAK,CAAC;QACzD,IAAIC,iBAAiB,GAAG,EAAE;QAE1B,KAAK,IAAIC,CAAC,GAAGF,KAAK,CAACG,WAAW,EAAED,CAAC,GAAGF,KAAK,CAACI,OAAO,CAACC,MAAM,EAAEH,CAAC,EAAE,EAAE;UAC7D,MAAMI,UAAU,GAAGN,KAAK,CAACI,OAAO,CAACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAACI,UAAU;UACjD,MAAMC,UAAU,GAAGP,KAAK,CAACI,OAAO,CAACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAACK,UAAU;UACjD1E,OAAO,CAACC,GAAG,CAAC,gBAAgBwE,UAAU,kBAAkBC,UAAU,CAACC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;UAEjF,IAAIR,KAAK,CAACI,OAAO,CAACF,CAAC,CAAC,CAACO,OAAO,EAAE;YAC5BX,eAAe,IAAIQ,UAAU,GAAG,GAAG;YACnCzE,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEgE,eAAe,CAAC;UAC3D,CAAC,MAAM;YACLG,iBAAiB,IAAIK,UAAU;YAC/BzE,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEmE,iBAAiB,CAAC;UAC/D;QACF;QAEA,MAAMS,aAAa,GAAGZ,eAAe,GAAGG,iBAAiB;QACzDpE,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE4E,aAAa,CAAC;;QAE5D;QACAhH,oBAAoB,CAAC+B,IAAI,IAAI;UAC3BI,OAAO,CAACC,GAAG,CAAC,6BAA6BL,IAAI,SAASiF,aAAa,GAAG,CAAC;;UAEvE;UACAnB,MAAM,CAACoB,aAAa,CAAC,IAAIC,WAAW,CAAC,mBAAmB,EAAE;YACxDC,MAAM,EAAE;cAAEP,UAAU,EAAEI;YAAc;UACtC,CAAC,CAAC,CAAC;UAEH,OAAOA,aAAa;QACtB,CAAC,CAAC;;QAEF;QACA,MAAMI,iBAAiB,GAAGC,QAAQ,CAACC,aAAa,CAAC,kCAAkC,CAAC;QACpF,IAAIF,iBAAiB,EAAE;UACrBA,iBAAiB,CAACG,WAAW,GAAGP,aAAa;QAC/C;;QAEA;QACA,IAAIA,aAAa,CAACxE,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;UAC/BL,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;UAC9D+C,mBAAmB,CAAC,CAAC;QACvB;MACF,CAAC;MAEDpE,cAAc,CAACa,OAAO,CAAC4F,OAAO,GAAIlB,KAAK,IAAK;QAC1CnE,OAAO,CAAC8C,KAAK,CAAC,0BAA0B,EAAEqB,KAAK,CAACrB,KAAK,EAAEqB,KAAK,CAAC;QAC7D,IAAIA,KAAK,CAACrB,KAAK,KAAK,WAAW,EAAE;UAC/B9C,OAAO,CAAC8C,KAAK,CAAC,oCAAoC,EAAEqB,KAAK,CAACrB,KAAK,CAAC;UAChExC,KAAK,CAAC,kBAAkB,GAAG6D,KAAK,CAACrB,KAAK,CAAC;UACvCvF,cAAc,CAAC,KAAK,CAAC;UACrBsC,SAAS,CAAC,CAAC;QACb,CAAC,MAAM;UACLG,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;QACzD;MACF,CAAC;MAEDrB,cAAc,CAACa,OAAO,CAAC6F,KAAK,GAAG,MAAM;QACnCtF,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;QACvC;QACA,IAAI3C,WAAW,EAAE;UACf0C,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;UACjE,IAAI;YACFrB,cAAc,CAACa,OAAO,CAAC8F,KAAK,CAAC,CAAC;YAC9BvF,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;UACnD,CAAC,CAAC,OAAO6C,KAAK,EAAE;YACd9C,OAAO,CAAC8C,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;UACxD;QACF,CAAC,MAAM;UACL9C,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;QAClE;MACF,CAAC;;MAED;MACAD,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;IAExE,CAAC,CAAC,OAAO6C,KAAK,EAAE;MACd9C,OAAO,CAAC8C,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChExC,KAAK,CAAC,sEAAsE,CAAC;IAC/E;;IAEA;IACA,OAAO,MAAM;MACX,IAAI1B,cAAc,CAACa,OAAO,EAAE;QAC1B,IAAI;UACFb,cAAc,CAACa,OAAO,CAAC+F,IAAI,CAAC,CAAC;QAC/B,CAAC,CAAC,OAAO1C,KAAK,EAAE;UACd9C,OAAO,CAAC8C,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACrD;MACF;IACF,CAAC;EACH,CAAC,EAAE,CAACE,mBAAmB,EAAE1F,WAAW,EAAEuC,SAAS,EAAEvB,QAAQ,EAAEJ,cAAc,CAAC,CAAC;;EAE3E;EACAzB,SAAS,CAAC,MAAM;IACd,IAAIoC,iBAAiB,CAACY,OAAO,EAAE;MAC7BZ,iBAAiB,CAACY,OAAO,CAACgG,SAAS,GAAG5G,iBAAiB,CAACY,OAAO,CAACiG,YAAY;IAC9E;EACF,CAAC,EAAE,CAAChI,kBAAkB,EAAEE,iBAAiB,CAAC,CAAC;;EAE3C;EACAnB,SAAS,CAAC,MAAM;IACd,IAAIqC,eAAe,CAACW,OAAO,EAAE;MAC3BX,eAAe,CAACW,OAAO,CAACgG,SAAS,GAAG3G,eAAe,CAACW,OAAO,CAACiG,YAAY;IAC1E;EACF,CAAC,EAAE,CAAC5H,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAM6H,cAAc,GAAGjJ,WAAW,CAAC,MAAM;IACvCsD,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;IAC7C,IAAIrB,cAAc,CAACa,OAAO,IAAI,CAACnC,WAAW,EAAE;MAC1C,IAAI;QACF0C,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;QAC1CrB,cAAc,CAACa,OAAO,CAAC8F,KAAK,CAAC,CAAC;QAC9BvF,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;QAC/C1C,cAAc,CAAC,IAAI,CAAC;QACpBiC,UAAU,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,OAAOsD,KAAK,EAAE;QACd9C,OAAO,CAAC8C,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD;QACA,IAAIA,KAAK,CAACC,OAAO,CAAC6C,QAAQ,CAAC,iBAAiB,CAAC,EAAE;UAC7C5F,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;UACnErB,cAAc,CAACa,OAAO,CAAC+F,IAAI,CAAC,CAAC;UAC7BtC,UAAU,CAAC,MAAM;YACflD,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;YAChDrB,cAAc,CAACa,OAAO,CAAC8F,KAAK,CAAC,CAAC;YAC9BhI,cAAc,CAAC,IAAI,CAAC;YACpBiC,UAAU,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,EAAE,GAAG,CAAC;QACT;MACF;IACF,CAAC,MAAM;MACLQ,OAAO,CAACC,GAAG,CAAC,yBAAyB,EACnCrB,cAAc,CAACa,OAAO,GAAG,mBAAmB,GAAG,uBAAuB,CAAC;IAC3E;EACF,CAAC,EAAE,CAACnC,WAAW,EAAEkC,UAAU,CAAC,CAAC;;EAE7B;EACA,MAAMqG,qBAAqB,GAAGnJ,WAAW,CAAC,MAAM;IAC9CsD,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;;IAE/D;IACA,MAAM6F,cAAc,GAAG;MACrBxB,WAAW,EAAE,CAAC;MACdC,OAAO,EAAE,CACP;QACE,CAAC,EAAE;UAAEE,UAAU,EAAE,gDAAgD;UAAEC,UAAU,EAAE;QAAI,CAAC;QACpFE,OAAO,EAAE,IAAI;QACbJ,MAAM,EAAE;MACV,CAAC;IAEL,CAAC;;IAED;IACA,IAAI5F,cAAc,CAACa,OAAO,IAAIb,cAAc,CAACa,OAAO,CAACyE,QAAQ,EAAE;MAC7DlE,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;MACxErB,cAAc,CAACa,OAAO,CAACyE,QAAQ,CAAC4B,cAAc,CAAC;IACjD,CAAC,MAAM;MACL9F,OAAO,CAACC,GAAG,CAAC,2EAA2E,CAAC;MACxF;MACApC,oBAAoB,CAAC,gDAAgD,CAAC;IACxE;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMkI,aAAa,GAAGrJ,WAAW,CAAC,MAAM;IACtC,IAAIkC,cAAc,CAACa,OAAO,IAAInC,WAAW,EAAE;MACzC,IAAI;QACFsB,cAAc,CAACa,OAAO,CAAC+F,IAAI,CAAC,CAAC;QAC7BjI,cAAc,CAAC,KAAK,CAAC;QACrBsC,SAAS,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,OAAOiD,KAAK,EAAE;QACd9C,OAAO,CAAC8C,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACnD;IACF;EACF,CAAC,EAAE,CAACxF,WAAW,EAAEuC,SAAS,CAAC,CAAC;;EAE5B;;EAEA;EACA,MAAMmG,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACFhG,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;;MAE3C;MACA,MAAMgG,aAAa,GAAG,MAAMC,SAAS,CAACC,YAAY,CAACC,eAAe,CAAC;QACjEC,KAAK,EAAE,IAAI;QACXC,KAAK,EAAE,KAAK,CAAC;MACf,CAAC,CAAC;MAEFtG,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC5CD,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEgG,aAAa,CAACM,SAAS,CAAC,CAAC,CAACC,GAAG,CAACC,CAAC,IAAI,GAAGA,CAAC,CAACC,IAAI,KAAKD,CAAC,CAACE,KAAK,GAAG,CAAC,CAAC;;MAE3F;MACA5H,cAAc,CAACU,OAAO,GAAGwG,aAAa;;MAEtC;MACAA,aAAa,CAACW,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,OAAO,GAAG,MAAM;QAChD7G,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;QAC1C5C,cAAc,CAAC,KAAK,CAAC;QACrByJ,aAAa,CAAC,CAAC;MACjB,CAAC;;MAED;MACAzJ,cAAc,CAAC,IAAI,CAAC;MACpBF,OAAO,CAAC,WAAW,CAAC;;MAEpB;MACAwI,cAAc,CAAC,CAAC;;MAEhB;MACA;MACA3F,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;IAEjE,CAAC,CAAC,OAAO6C,KAAK,EAAE;MACd9C,OAAO,CAAC8C,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CxC,KAAK,CAAC,0BAA0B,GAAGwC,KAAK,CAACC,OAAO,CAAC;IACnD;EACF,CAAC;;EAED;EACA;;EAEA;EACA,MAAM+D,aAAa,GAAGA,CAAA,KAAM;IAC1B9G,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;;IAEzC;IACAxC,cAAc,CAAC,KAAK,CAAC;;IAErB;IACA,IAAIsB,cAAc,CAACU,OAAO,EAAE;MAC1B,IAAI;QACFO,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;QACxClB,cAAc,CAACU,OAAO,CAAC8G,SAAS,CAAC,CAAC,CAACQ,OAAO,CAACC,KAAK,IAAI;UAClD,IAAI;YACFA,KAAK,CAACxB,IAAI,CAAC,CAAC;YACZxF,OAAO,CAACC,GAAG,CAAC,WAAW+G,KAAK,CAACN,IAAI,WAAWM,KAAK,CAACL,KAAK,EAAE,CAAC;UAC5D,CAAC,CAAC,OAAOM,UAAU,EAAE;YACnBjH,OAAO,CAAC8C,KAAK,CAAC,kBAAkBkE,KAAK,CAACN,IAAI,SAAS,EAAEO,UAAU,CAAC;UAClE;QACF,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOC,WAAW,EAAE;QACpBlH,OAAO,CAAC8C,KAAK,CAAC,qCAAqC,EAAEoE,WAAW,CAAC;MACnE;;MAEA;MACAnI,cAAc,CAACU,OAAO,GAAG,IAAI;IAC/B;;IAEA;IACAsG,aAAa,CAAC,CAAC;IAEf/F,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;EACvC,CAAC;;EAED;EACA,MAAMkH,YAAY,GAAGA,CAAA,KAAM;IACzBL,aAAa,CAAC,CAAC;IACff,aAAa,CAAC,CAAC;IACf5I,OAAO,CAAC,WAAW,CAAC;IACpBE,cAAc,CAAC,KAAK,CAAC;IACrBM,qBAAqB,CAAC,EAAE,CAAC;IACzBE,oBAAoB,CAAC,EAAE,CAAC;IACxBE,WAAW,CAAC,EAAE,CAAC;EACjB,CAAC;;EAED;EACA,MAAMqJ,mBAAmB,GAAGA,CAAA,kBAC1BvK,OAAA;IAAKwK,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC7BzK,OAAA;MAAAyK,QAAA,EAAI;IAAY;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACrB7K,OAAA;MAAKwK,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BzK,OAAA;QAAKwK,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBzK,OAAA;UAAAyK,QAAA,EAAO;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC7B7K,OAAA;UACE6E,KAAK,EAAEtD,cAAe;UACtBuJ,QAAQ,EAAG9E,CAAC,IAAKxE,iBAAiB,CAACwE,CAAC,CAAC+E,MAAM,CAAClG,KAAK,CAAE;UAAA4F,QAAA,gBAEnDzK,OAAA;YAAQ6E,KAAK,EAAC,sBAAsB;YAAA4F,QAAA,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAClE7K,OAAA;YAAQ6E,KAAK,EAAC,cAAc;YAAA4F,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAClD7K,OAAA;YAAQ6E,KAAK,EAAC,oBAAoB;YAAA4F,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC9D7K,OAAA;YAAQ6E,KAAK,EAAC,WAAW;YAAA4F,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC5C7K,OAAA;YAAQ6E,KAAK,EAAC,OAAO;YAAA4F,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpC7K,OAAA;YAAQ6E,KAAK,EAAC,kBAAkB;YAAA4F,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEN7K,OAAA;QAAKwK,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBzK,OAAA;UAAAyK,QAAA,EAAO;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACvB7K,OAAA;UACE6E,KAAK,EAAEpD,QAAS;UAChBqJ,QAAQ,EAAG9E,CAAC,IAAKtE,WAAW,CAACsE,CAAC,CAAC+E,MAAM,CAAClG,KAAK,CAAE;UAAA4F,QAAA,gBAE7CzK,OAAA;YAAQ6E,KAAK,EAAC,SAAS;YAAA4F,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACxC7K,OAAA;YAAQ6E,KAAK,EAAC,SAAS;YAAA4F,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACxC7K,OAAA;YAAQ6E,KAAK,EAAC,QAAQ;YAAA4F,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtC7K,OAAA;YAAQ6E,KAAK,EAAC,QAAQ;YAAA4F,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtC7K,OAAA;YAAQ6E,KAAK,EAAC,SAAS;YAAA4F,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACxC7K,OAAA;YAAQ6E,KAAK,EAAC,UAAU;YAAA4F,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAEL/K,GAAG,CAACqH,kBAAkB,CAAC,CAAC,gBACvBnH,OAAA;MAAKwK,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBACjCzK,OAAA;QAAMwK,SAAS,EAAC,MAAM;QAAAC,QAAA,EAAC;MAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,yFAEjC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,gBAEN7K,OAAA;MAAKwK,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBACjCzK,OAAA;QAAMwK,SAAS,EAAC,MAAM;QAAAC,QAAA,EAAC;MAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,wIAEjC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CACN,eAED7K,OAAA;MACEwK,SAAS,EAAC,gBAAgB;MAC1BQ,OAAO,EAAEA,CAAA,KAAM1K,OAAO,CAAC,SAAS,CAAE;MAAAmK,QAAA,EACnC;IAED;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CACN;EAED,MAAMI,iBAAiB,GAAGA,CAAA,kBACxBjL,OAAA;IAAKwK,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3BzK,OAAA;MAAAyK,QAAA,EAAI;IAAoB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC7B7K,OAAA;MAAAyK,QAAA,EAAG;IAAiD;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eACxD7K,OAAA;MAAAyK,QAAA,EAAG;IAA+D;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eAEtE7K,OAAA;MACEwK,SAAS,EAAC,qBAAqB;MAC/BQ,OAAO,EAAE7B,oBAAqB;MAAAsB,QAAA,EAC/B;IAED;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAET7K,OAAA;MACEwK,SAAS,EAAC,aAAa;MACvBQ,OAAO,EAAEA,CAAA,KAAM1K,OAAO,CAAC,WAAW,CAAE;MAAAmK,QAAA,EACrC;IAED;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CACN;EAED,MAAMK,mBAAmB,GAAGA,CAAA,kBAC1BlL,OAAA;IAAKwK,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC7BzK,OAAA;MAAKwK,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClCzK,OAAA;QAAKwK,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAClCzK,OAAA;UAAKwK,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BzK,OAAA;YAAAyK,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpB7K,OAAA;YAAKwK,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAC/BlK,WAAW,gBACVP,OAAA;cAAMwK,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,gBAE5C7K,OAAA;cAAMwK,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAClD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN7K,OAAA;UAAKwK,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5BzK,OAAA;YACEmL,GAAG,EAAEnJ,iBAAkB;YACvBwI,SAAS,EAAC,oBAAoB;YAAAC,QAAA,GAE7B5J,kBAAkB,CAAC8G,MAAM,GAAG,CAAC,gBAC5B3H,OAAA;cAAKwK,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EACjC5J,kBAAkB,CAAC8I,GAAG,CAAC,CAACyB,GAAG,EAAEC,KAAK,kBACjCrL,OAAA;gBAAsCwK,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBAClEzK,OAAA;kBAAKwK,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAEtI,UAAU,CAACiJ,GAAG,CAACzE,IAAI;gBAAC;kBAAA+D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACvD7K,OAAA;kBAAKwK,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAEW,GAAG,CAAClI;gBAAI;kBAAAwH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA,GAFtC,OAAOQ,KAAK,IAAID,GAAG,CAACzE,IAAI,EAAE;gBAAA+D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAG/B,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,gBAEN7K,OAAA;cAAKwK,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAoD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACvF,eAGD7K,OAAA;cAAKwK,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBACzCzK,OAAA;gBAAKwK,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAEtI,UAAU,CAACR,YAAY;cAAC;gBAAA+I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC3D7K,OAAA;gBAAKwK,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAC1B1J,iBAAiB,gBAChBf,OAAA;kBAAMwK,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,EAAE1J;gBAAiB;kBAAA2J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,gBAC9D7K,OAAA;kBAAMwK,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAqB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEzD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAGLS,OAAO,CAACxL,GAAG,CAACyL,QAAQ,KAAK,aAAa,iBACrCvL,OAAA;cAAKwK,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBzK,OAAA;gBAAAyK,QAAA,GAAK,6BAA2B,EAAC,CAAA1J,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAE4G,MAAM,KAAI,CAAC;cAAA;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtE7K,OAAA;gBAAAyK,QAAA,GAAK,uBAAqB,EAAC5J,kBAAkB,CAAC8G,MAAM;cAAA;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC3D7K,OAAA;gBAAAyK,QAAA,GAAK,gBAAc,EAAChK,WAAW,GAAG,KAAK,GAAG,IAAI;cAAA;gBAAAiK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrD7K,OAAA;gBAAAyK,QAAA,GAAK,gBAAc,EAAC9J,WAAW,GAAG,KAAK,GAAG,IAAI;cAAA;gBAAA+J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrD7K,OAAA;gBAAAyK,QAAA,GAAK,oBAAkB,EAACpJ,cAAc,GAAG,KAAK,GAAG,IAAI;cAAA;gBAAAqJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN7K,OAAA;QAAKwK,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BzK,OAAA;UAAKwK,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BzK,OAAA;YAAAyK,QAAA,EAAI;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,eAEN7K,OAAA;UAAKwK,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5BzK,OAAA;YACEmL,GAAG,EAAElJ,eAAgB;YACrBuI,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAE3BxJ,QAAQ,gBACPjB,OAAA;cAAKwK,SAAS,EAAC,kBAAkB;cAAAC,QAAA,eAC/BzK,OAAA;gBAAKwK,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAExJ;cAAQ;gBAAAyJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,gBAEN7K,OAAA;cAAKwK,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAA2C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAC9E;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN7K,OAAA;MAAKwK,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBACjCzK,OAAA;QAAKwK,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCzK,OAAA;UAAKwK,SAAS,EAAC,eAAe;UAAAC,QAAA,EAC3BhK,WAAW,gBACVT,OAAA,CAAAE,SAAA;YAAAuK,QAAA,gBACEzK,OAAA;cAAMwK,SAAS,EAAC;YAAe;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACtC1I,UAAU,CAACR,YAAY,CAAC;UAAA,eACzB,CAAC,GAEHQ,UAAU,CAACR,YAAY;QACxB;UAAA+I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEN7K,OAAA;UAAKwK,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BzK,OAAA;YAAAyK,QAAA,EAAOhK,WAAW,GAAG,cAAc,GAAG;UAAQ;YAAAiK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EACrDxJ,cAAc,iBACbrB,OAAA;YAAMwK,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAC3D,EACA,CAACxJ,cAAc,iBACdrB,OAAA;YAAMwK,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAC7D;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN7K,OAAA;QAAKwK,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BzK,OAAA;UACEwK,SAAS,EAAE,cAAc/J,WAAW,GAAG,QAAQ,GAAG,EAAE,EAAG;UACvDuK,OAAO,EAAEvK,WAAW,GAAGyI,aAAa,GAAGJ,cAAe;UAAA2B,QAAA,EAErDhK,WAAW,GAAG,OAAO,GAAG;QAAQ;UAAAiK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eAET7K,OAAA;UACEwK,SAAS,EAAC,aAAa;UACvBQ,OAAO,EAAEV,YAAa;UAAAG,QAAA,EACvB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EAGRS,OAAO,CAACxL,GAAG,CAACyL,QAAQ,KAAK,aAAa,iBACrCvL,OAAA,CAAAE,SAAA;UAAAuK,QAAA,gBACEzK,OAAA;YACEwK,SAAS,EAAC,aAAa;YACvBQ,OAAO,EAAEA,CAAA,KAAM;cACb,MAAMQ,QAAQ,GAAG,sGAAsG;cACvHrI,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEoI,QAAQ,CAAC;cAChD1K,qBAAqB,CAACiC,IAAI,IAAI,CAC5B,GAAGA,IAAI,EACP;gBAAEG,IAAI,EAAEsI,QAAQ;gBAAE/E,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;gBAAEC,IAAI,EAAEhF;cAAa,CAAC,CAC9D,CAAC;YACJ,CAAE;YAAA8I,QAAA,EACH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAET7K,OAAA;YACEwK,SAAS,EAAC,oBAAoB;YAC9BQ,OAAO,EAAEhC,qBAAsB;YAAAyB,QAAA,EAChC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,eACT,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;;EAED;EACA,MAAMY,UAAU,GAAGA,CAAA,KAAM;IACvB,QAAQpL,IAAI;MACV,KAAK,WAAW;QACd,OAAOkK,mBAAmB,CAAC,CAAC;MAC9B,KAAK,SAAS;QACZ,OAAOU,iBAAiB,CAAC,CAAC;MAC5B,KAAK,WAAW;QACd,OAAOC,mBAAmB,CAAC,CAAC;MAC9B;QACE,OAAOX,mBAAmB,CAAC,CAAC;IAChC;EACF,CAAC;EAED,oBACEvK,OAAA;IAAKwK,SAAS,EAAC,qBAAqB;IAAAC,QAAA,EACjCgB,UAAU,CAAC;EAAC;IAAAf,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEV;AAACzK,EAAA,CAvvBQD,kBAAkB;AAAAuL,EAAA,GAAlBvL,kBAAkB;AAyvB3B,eAAeA,kBAAkB;AAAC,IAAAuL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}