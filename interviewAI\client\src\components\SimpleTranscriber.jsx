import React, { useState, useEffect, useRef } from 'react';
import './SimpleTranscriber.css';

function SimpleTranscriber({ onTranscriptChange }) {
  const [transcript, setTranscript] = useState('');
  const [isListening, setIsListening] = useState(false);
  const recognitionRef = useRef(null);
  const finalTranscriptRef = useRef('');

  // Initialize speech recognition
  useEffect(() => {
    // Browser compatibility
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    
    if (!SpeechRecognition) {
      console.error("Speech recognition not supported in this browser");
      return;
    }
    
    // Create recognition instance
    recognitionRef.current = new SpeechRecognition();
    
    // Configure
    recognitionRef.current.continuous = true;
    recognitionRef.current.interimResults = true;
    recognitionRef.current.lang = 'en-US';
    
    // Set up event handlers
    recognitionRef.current.onresult = (event) => {
      let interimTranscript = '';
      
      for (let i = event.resultIndex; i < event.results.length; i++) {
        const result = event.results[i];
        const text = result[0].transcript;
        
        if (result.isFinal) {
          finalTranscriptRef.current += text + ' ';
        } else {
          interimTranscript += text;
        }
      }
      
      const fullTranscript = finalTranscriptRef.current + interimTranscript;
      setTranscript(fullTranscript);
      
      // Notify parent component
      if (onTranscriptChange) {
        onTranscriptChange(fullTranscript);
      }
    };
    
    recognitionRef.current.onerror = (event) => {
      console.error('Speech recognition error:', event.error);
      if (event.error !== 'no-speech') {
        setIsListening(false);
      }
    };
    
    recognitionRef.current.onend = () => {
      // Restart if still supposed to be listening
      if (isListening) {
        try {
          recognitionRef.current.start();
        } catch (error) {
          console.error('Failed to restart recognition:', error);
        }
      }
    };
    
    // Clean up
    return () => {
      if (recognitionRef.current) {
        try {
          recognitionRef.current.stop();
        } catch (error) {
          console.error('Error stopping recognition:', error);
        }
      }
    };
  }, [isListening, onTranscriptChange]);
  
  // Start listening
  const startListening = () => {
    if (recognitionRef.current && !isListening) {
      finalTranscriptRef.current = ''; // Reset transcript
      setTranscript('');
      
      try {
        recognitionRef.current.start();
        setIsListening(true);
      } catch (error) {
        console.error('Error starting recognition:', error);
        
        // Handle "already started" error
        if (error.message.includes('already started')) {
          recognitionRef.current.stop();
          setTimeout(() => {
            recognitionRef.current.start();
            setIsListening(true);
          }, 100);
        }
      }
    }
  };
  
  // Stop listening
  const stopListening = () => {
    if (recognitionRef.current && isListening) {
      recognitionRef.current.stop();
      setIsListening(false);
    }
  };
  
  return (
    <div className="simple-transcriber">
      <div className="transcript-display">
        {transcript ? (
          <p className="transcript-text">{transcript}</p>
        ) : (
          <p className="placeholder">Waiting for speech...</p>
        )}
      </div>
      
      <div className="controls">
        <button 
          className={`control-button ${isListening ? 'listening' : ''}`}
          onClick={isListening ? stopListening : startListening}
        >
          {isListening ? 'Stop' : 'Start'} Listening
        </button>
        
        {isListening && (
          <div className="status">
            <span className="listening-indicator"></span>
            Listening...
          </div>
        )}
      </div>
    </div>
  );
}

export default SimpleTranscriber;
