import { useState, useEffect, useRef } from 'react';
import './SimpleTranscriber.css';

function SimpleTranscriber({ onTranscriptChange }) {
  const [transcript, setTranscript] = useState('');
  const [isListening, setIsListening] = useState(false);
  const recognitionRef = useRef(null);
  const finalTranscriptRef = useRef('');

  // Initialize speech recognition
  useEffect(() => {
    // Browser compatibility
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;

    if (!SpeechRecognition) {
      console.error("Speech recognition not supported in this browser");
      return;
    }

    // Create recognition instance
    recognitionRef.current = new SpeechRecognition();

    // Configure
    recognitionRef.current.continuous = true;
    recognitionRef.current.interimResults = true;
    recognitionRef.current.lang = 'en-US';

    // Set up event handlers
    recognitionRef.current.onresult = (event) => {
      let interimTranscript = '';

      for (let i = event.resultIndex; i < event.results.length; i++) {
        const result = event.results[i];
        const text = result[0].transcript;

        if (result.isFinal) {
          finalTranscriptRef.current += text + ' ';
        } else {
          interimTranscript += text;
        }
      }

      const fullTranscript = finalTranscriptRef.current + interimTranscript;
      setTranscript(fullTranscript);

      // Notify parent component
      if (onTranscriptChange) {
        onTranscriptChange(fullTranscript);
      }
    };

    recognitionRef.current.onerror = (event) => {
      console.error('Speech recognition error:', event.error);

      // Handle different error types
      switch (event.error) {
        case 'no-speech':
          // This is a common error that doesn't need to stop the recognition
          console.log('No speech detected, continuing to listen');
          break;

        case 'aborted':
          // When aborted, we should try to restart after a short delay
          console.log('Recognition aborted, attempting to restart');
          setTimeout(() => {
            if (isListening) {
              try {
                recognitionRef.current.start();
                console.log('Recognition restarted after abort');
              } catch (error) {
                console.error('Failed to restart after abort:', error);
                setIsListening(false);
              }
            }
          }, 300);
          break;

        default:
          // For other errors, we should stop listening
          console.error('Critical recognition error:', event.error);
          setIsListening(false);
      }
    };

    recognitionRef.current.onend = () => {
      console.log('Speech recognition ended');

      // Restart if still supposed to be listening
      if (isListening) {
        console.log('Still listening, attempting to restart recognition');

        // Add a small delay before restarting to avoid rapid restart loops
        setTimeout(() => {
          if (isListening && recognitionRef.current) {
            try {
              recognitionRef.current.start();
              console.log('Recognition restarted successfully');
            } catch (error) {
              console.error('Failed to restart recognition:', error);

              // If we get an "already started" error, stop and try again
              if (error.message && error.message.includes('already started')) {
                try {
                  recognitionRef.current.stop();
                  setTimeout(() => {
                    if (isListening && recognitionRef.current) {
                      recognitionRef.current.start();
                      console.log('Recognition restarted after stopping');
                    }
                  }, 300);
                } catch (stopError) {
                  console.error('Error stopping recognition:', stopError);
                  setIsListening(false);
                }
              } else {
                setIsListening(false);
              }
            }
          }
        }, 300);
      } else {
        console.log('Not listening anymore, not restarting recognition');
      }
    };

    // Clean up
    return () => {
      if (recognitionRef.current) {
        try {
          recognitionRef.current.stop();
        } catch (error) {
          console.error('Error stopping recognition:', error);
        }
      }
    };
  }, [isListening, onTranscriptChange]);

  // Start listening with improved error handling
  const startListening = () => {
    console.log('Starting speech recognition...');

    if (!recognitionRef.current) {
      console.error('Speech recognition not initialized');
      alert('Speech recognition is not available in your browser. Please try using Chrome or Edge.');
      return;
    }

    if (isListening) {
      console.log('Already listening, no need to start again');
      return;
    }

    // Reset transcript
    finalTranscriptRef.current = '';
    setTranscript('');

    try {
      console.log('Calling recognition.start()');
      recognitionRef.current.start();
      console.log('Recognition started successfully');
      setIsListening(true);

      // Add visual feedback
      const transcriptDisplay = document.querySelector('.transcript-display');
      if (transcriptDisplay) {
        transcriptDisplay.classList.add('listening');
      }

    } catch (error) {
      console.error('Error starting recognition:', error);

      // Handle "already started" error
      if (error.message && error.message.includes('already started')) {
        console.log('Recognition already started, stopping and restarting');

        try {
          recognitionRef.current.stop();

          // Add a small delay before restarting
          setTimeout(() => {
            try {
              console.log('Restarting recognition after stop');
              recognitionRef.current.start();
              setIsListening(true);

              // Add visual feedback
              const transcriptDisplay = document.querySelector('.transcript-display');
              if (transcriptDisplay) {
                transcriptDisplay.classList.add('listening');
              }
            } catch (restartError) {
              console.error('Failed to restart recognition:', restartError);
              alert('Failed to start speech recognition. Please refresh the page and try again.');
            }
          }, 300);
        } catch (stopError) {
          console.error('Error stopping recognition:', stopError);
        }
      } else {
        // For other errors, show a user-friendly message
        alert('Failed to start speech recognition: ' + error.message);
      }
    }
  };

  // Stop listening with improved error handling
  const stopListening = () => {
    console.log('Stopping speech recognition...');

    if (!recognitionRef.current) {
      console.log('No recognition object to stop');
      setIsListening(false);
      return;
    }

    if (!isListening) {
      console.log('Not listening, no need to stop');
      return;
    }

    try {
      console.log('Calling recognition.stop()');
      recognitionRef.current.stop();
      console.log('Recognition stopped successfully');
      setIsListening(false);

      // Remove visual feedback
      const transcriptDisplay = document.querySelector('.transcript-display');
      if (transcriptDisplay) {
        transcriptDisplay.classList.remove('listening');
      }

    } catch (error) {
      console.error('Error stopping recognition:', error);

      // Force the state to be updated even if there was an error
      setIsListening(false);

      // If we can't stop normally, try to recreate the recognition object
      try {
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        recognitionRef.current = new SpeechRecognition();
        recognitionRef.current.continuous = true;
        recognitionRef.current.interimResults = true;
        recognitionRef.current.lang = 'en-US';
        console.log('Recognition object recreated after stop error');
      } catch (recreateError) {
        console.error('Failed to recreate recognition object:', recreateError);
      }
    }
  };

  // Check if speech recognition is supported
  const isSpeechRecognitionSupported = !!(window.SpeechRecognition || window.webkitSpeechRecognition);

  // State for tab audio capture
  const [isCapturingTabAudio, setIsCapturingTabAudio] = useState(false);

  // Function to start capturing tab audio
  const startTabAudioCapture = async () => {
    try {
      console.log("Starting tab audio capture...");

      // Request screen sharing with audio
      const stream = await navigator.mediaDevices.getDisplayMedia({
        video: true,
        audio: true // This is critical - we need to capture audio
      });

      // Check if audio is included
      const audioTracks = stream.getAudioTracks();
      if (audioTracks.length === 0) {
        alert("No audio track found. Please make sure to select 'Share audio' when sharing the tab.");
        stream.getTracks().forEach(track => track.stop());
        return;
      }

      console.log("Tab audio capture started with tracks:",
        stream.getTracks().map(t => `${t.kind} (${t.label})`));

      // Stop any existing recognition
      if (recognitionRef.current && isListening) {
        try {
          recognitionRef.current.stop();
        } catch (error) {
          console.error("Error stopping existing recognition:", error);
        }
      }

      // Create a new recognition instance for the tab audio
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      recognitionRef.current = new SpeechRecognition();
      recognitionRef.current.continuous = true;
      recognitionRef.current.interimResults = true;
      recognitionRef.current.lang = 'en-US';

      // Reset transcript
      finalTranscriptRef.current = '';
      setTranscript('');

      // Set up event handlers
      recognitionRef.current.onresult = (event) => {
        let interimTranscript = '';

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const result = event.results[i];
          const text = result[0].transcript;

          if (result.isFinal) {
            finalTranscriptRef.current += text + ' ';
          } else {
            interimTranscript += text;
          }
        }

        const fullTranscript = finalTranscriptRef.current + interimTranscript;
        setTranscript(fullTranscript);

        // Notify parent component
        if (onTranscriptChange) {
          onTranscriptChange(fullTranscript);
        }
      };

      // Start recognition with the tab audio stream
      recognitionRef.current.start();
      setIsListening(true);
      setIsCapturingTabAudio(true);

      // Handle stream ending (user stops sharing)
      stream.getVideoTracks()[0].onended = () => {
        console.log("Tab sharing ended");
        stopTabAudioCapture();
      };

      // Store the stream for cleanup
      window.tabAudioStream = stream;

    } catch (error) {
      console.error("Error capturing tab audio:", error);
      alert("Failed to capture tab audio: " + error.message);
    }
  };

  // Function to stop capturing tab audio
  const stopTabAudioCapture = () => {
    console.log("Stopping tab audio capture...");

    // Stop the stream
    if (window.tabAudioStream) {
      window.tabAudioStream.getTracks().forEach(track => {
        track.stop();
        console.log(`Stopped ${track.kind} track: ${track.label}`);
      });
      window.tabAudioStream = null;
    }

    // Stop recognition
    if (recognitionRef.current && isListening) {
      try {
        recognitionRef.current.stop();
      } catch (error) {
        console.error("Error stopping recognition:", error);
      }
    }

    setIsListening(false);
    setIsCapturingTabAudio(false);
  };

  return (
    <div className="simple-transcriber">
      {!isSpeechRecognitionSupported && (
        <div className="browser-warning">
          <p>
            <strong>Warning:</strong> Your browser doesn't support speech recognition.
            Please use Chrome, Edge, or another Chromium-based browser for this feature to work.
          </p>
        </div>
      )}

      <div className="transcript-display">
        {transcript ? (
          <p className="transcript-text">{transcript}</p>
        ) : (
          <p className="placeholder">Waiting for speech or audio...</p>
        )}
      </div>

      <div className="controls">
        <button
          className={`control-button ${isListening && !isCapturingTabAudio ? 'listening' : ''}`}
          onClick={isListening ? stopListening : startListening}
          disabled={!isSpeechRecognitionSupported || isCapturingTabAudio}
          title="Capture microphone audio"
        >
          {isListening && !isCapturingTabAudio ? 'Stop' : 'Start'} Mic Listening
        </button>

        <button
          className={`control-button tab-audio ${isCapturingTabAudio ? 'capturing' : ''}`}
          onClick={isCapturingTabAudio ? stopTabAudioCapture : startTabAudioCapture}
          disabled={!isSpeechRecognitionSupported}
          title="Capture audio from another tab (e.g., YouTube)"
        >
          {isCapturingTabAudio ? 'Stop' : 'Start'} Tab Audio Capture
        </button>

        {isListening && (
          <div className="status">
            <span className="listening-indicator"></span>
            {isCapturingTabAudio ? 'Capturing Tab Audio...' : 'Listening to Microphone...'}
          </div>
        )}
      </div>

      {/* Add debug info */}
      {process.env.NODE_ENV === 'development' && (
        <div className="debug-info">
          <p>Speech Recognition Supported: {isSpeechRecognitionSupported ? 'Yes' : 'No'}</p>
          <p>Is Listening: {isListening ? 'Yes' : 'No'}</p>
          <p>Transcript Length: {transcript.length}</p>
        </div>
      )}
    </div>
  );
}

export default SimpleTranscriber;
