import React, { useState, useRef, useEffect, useCallback } from 'react';
import './SpeechToText.css';
import env from '../utils/env';
import MicIcon from '@mui/icons-material/Mic';
import StopIcon from '@mui/icons-material/Stop';
import SendIcon from '@mui/icons-material/Send';
import DeleteIcon from '@mui/icons-material/Delete';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import CloseIcon from '@mui/icons-material/Close';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import Button from '@mui/material/Button';

function SpeechToText({ onBack }) {
  const [isListening, setIsListening] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [response, setResponse] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Session timer state (15 minutes = 900 seconds)
  const SESSION_DURATION = 300; // 15 minutes in seconds
  const WARNING_TIME = 60; // Show warning when 60 seconds remain
  const [sessionTimeRemaining, setSessionTimeRemaining] = useState(SESSION_DURATION);
  const [showPaymentDialog, setShowPaymentDialog] = useState(false);
  const [sessionExpired, setSessionExpired] = useState(false);

  // Session timer reference
  const sessionTimerRef = useRef(null);

  // Speech recognition and text area references
  const recognitionRef = useRef(null);
  const transcriptAreaRef = useRef(null);
  const responseAreaRef = useRef(null);

  // Format session time remaining (MM:SS)
  const formatSessionTime = useCallback((totalSeconds) => {
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }, []);

  // Start the session timer
  const startSessionTimer = useCallback(() => {
    // Reset session timer when starting
    setSessionTimeRemaining(SESSION_DURATION);
    setSessionExpired(false);

    // Clear any existing interval
    if (sessionTimerRef.current) {
      clearInterval(sessionTimerRef.current);
    }

    // Start a new interval that counts down
    sessionTimerRef.current = setInterval(() => {
      setSessionTimeRemaining(prev => {
        const newTime = prev - 1;

        // Show payment dialog when 1 minute remains
        if (newTime === WARNING_TIME) {
          setShowPaymentDialog(true);
        }

        // Session expired
        if (newTime <= 0) {
          clearInterval(sessionTimerRef.current);
          sessionTimerRef.current = null;
          setSessionExpired(true);
          return 0;
        }

        return newTime;
      });
    }, 1000);
  }, [SESSION_DURATION, WARNING_TIME]);

  // Handle payment dialog close
  const handlePaymentDialogClose = useCallback(() => {
    setShowPaymentDialog(false);
  }, []);

  // Handle payment confirmation
  const handlePaymentConfirm = useCallback(() => {
    // Instead of resetting timer, navigate to plans page
    window.location.href = '/plans';
  }, []);

  // Start session timer on component mount
  useEffect(() => {
    startSessionTimer();

    // Clean up session timer on unmount
    return () => {
      if (sessionTimerRef.current) {
        clearInterval(sessionTimerRef.current);
      }
    };
  }, [startSessionTimer]);

  // Store finalTranscript in a ref to access it from event handlers
  const finalTranscriptRef = useRef("");

  useEffect(() => {
    // Initialize speech recognition
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    if (SpeechRecognition) {
      recognitionRef.current = new SpeechRecognition();
      recognitionRef.current.lang = 'en-US';
      recognitionRef.current.interimResults = true;
      recognitionRef.current.continuous = true;

      // Initialize finalTranscript from the current transcript value
      finalTranscriptRef.current = transcript;

      recognitionRef.current.onresult = (event) => {
        let interimTranscript = "";

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const transcript = event.results[i][0].transcript;
          if (event.results[i].isFinal) {
            finalTranscriptRef.current += transcript + " ";
          } else {
            interimTranscript += transcript;
          }
        }

        setTranscript(finalTranscriptRef.current + interimTranscript);
      };

      recognitionRef.current.onerror = (event) => {
        console.error("Speech recognition error", event.error);
        alert("Error occurred: " + event.error);
        setIsListening(false);
      };
    } else {
      alert("Your browser doesn't support speech recognition. Try Chrome or Edge.");
    }

    // Cleanup function
    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.stop();
      }
    };
  }, [transcript]);

  useEffect(() => {
    // Auto-scroll transcript area when content changes
    if (transcriptAreaRef.current) {
      transcriptAreaRef.current.scrollTop = transcriptAreaRef.current.scrollHeight;
    }

    // If we're not listening, keep finalTranscriptRef in sync with transcript
    // This ensures consistency when the transcript is changed by other means
    if (!isListening) {
      finalTranscriptRef.current = transcript;
    }
  }, [transcript, isListening]);

  useEffect(() => {
    // Auto-scroll response area when content changes
    if (responseAreaRef.current) {
      responseAreaRef.current.scrollTop = responseAreaRef.current.scrollHeight;
    }
  }, [response]);

  const startListening = () => {
    if (recognitionRef.current && !isListening && !sessionExpired) {
      try {
        // Clear the transcript when starting a new recording
        setTranscript('');

        // Reset the finalTranscript reference
        finalTranscriptRef.current = "";

        // Reinitialize speech recognition to clear its state
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        recognitionRef.current = new SpeechRecognition();
        recognitionRef.current.lang = 'en-US';
        recognitionRef.current.interimResults = true;
        recognitionRef.current.continuous = true;

        // Set up event handlers with the reset finalTranscriptRef
        recognitionRef.current.onresult = (event) => {
          let interimTranscript = "";

          for (let i = event.resultIndex; i < event.results.length; i++) {
            const transcript = event.results[i][0].transcript;
            if (event.results[i].isFinal) {
              finalTranscriptRef.current += transcript + " ";
            } else {
              interimTranscript += transcript;
            }
          }

          setTranscript(finalTranscriptRef.current + interimTranscript);
        };

        recognitionRef.current.onerror = (event) => {
          console.error("Speech recognition error", event.error);
          alert("Error occurred: " + event.error);
          setIsListening(false);
        };

        // Start the recognition
        recognitionRef.current.start();
        setIsListening(true);
      } catch (error) {
        console.error("Speech recognition error:", error);
        // If recognition is already running, stop it first then restart
        if (error.message.includes("already started")) {
          recognitionRef.current.stop();
          setTimeout(() => {
            startListening(); // Call the function recursively to restart properly
          }, 100);
        }
      }
    }
  };

  const stopListening = () => {
    if (recognitionRef.current && isListening) {
      try {
        recognitionRef.current.stop();
        setIsListening(false);

        // When stopping, make sure the finalTranscriptRef matches the current transcript
        // This ensures consistency if the user has manually edited the text
        finalTranscriptRef.current = transcript;
      } catch (error) {
        console.error("Speech recognition error:", error);
      }
    }
  };

  const sendToGPT = async () => {
    const apiKey = env.OPENAI_API_KEY;
    const userText = transcript.trim();

    console.log("Environment:", process.env.NODE_ENV);
    console.log("API Key available:", apiKey ? "Yes" : "No");

    if (!apiKey) {
      alert("Please add your OpenAI API key to the .env file as REACT_APP_OPENAI_API_KEY and restart the development server.");
      return;
    }

    if (!userText) {
      alert("Please record or enter some text to send to GPT.");
      return;
    }
    const prompt = `In 5 lines, give only the definition and a simple example. ${userText}`;
    setIsLoading(true);
    setResponse('');

    try {
      const response = await fetch("https://api.openai.com/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${apiKey}`
        },
        body: JSON.stringify({
          model: "gpt-4.1-nano",
          messages: [
            { role: "user", content: prompt }
          ],
          stream: true
        })
      });

      if (!response.ok || !response.body) throw new Error("Failed to stream response.");

      const reader = response.body.getReader();
      const decoder = new TextDecoder("utf-8");

      let result = "";

      while (true) {
        const { value, done } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value, { stream: true });
        const lines = chunk.split("\n").filter(line => line.trim().startsWith("data:"));

        for (const line of lines) {
          const data = line.replace(/^data: /, '');
          if (data === "[DONE]") break;

          try {
            const json = JSON.parse(data);
            const content = json.choices?.[0]?.delta?.content;
            if (content) {
              result += content;
              setResponse(result);
            }
          } catch (e) {
            console.error("Error parsing JSON:", e);
          }
        }
      }
    } catch (error) {
      console.error("Streaming Error:", error);
      setResponse("Error occurred: " + error.message);
    } finally {
      if (isListening) {
        stopListening();
      }
      setIsLoading(false);

      // Clear the transcript after getting the answer
      setTranscript('');
      // Also reset the finalTranscript reference
      finalTranscriptRef.current = "";
    }
  };

  const clearTranscript = () => {
    // Clear the transcript state
    setTranscript('');

    // Reset the finalTranscript reference
    finalTranscriptRef.current = "";

    // Reset the speech recognition if it's active
    if (recognitionRef.current) {
      // We need to stop and restart recognition to clear its internal state
      const wasListening = isListening;

      try {
        // Only stop if currently listening
        if (wasListening) {
          recognitionRef.current.stop();
          setIsListening(false);
        }

        // Wait a moment to ensure recognition has fully stopped
        setTimeout(() => {
          // Reinitialize speech recognition to clear its state
          const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
          recognitionRef.current = new SpeechRecognition();
          recognitionRef.current.lang = 'en-US';
          recognitionRef.current.interimResults = true;
          recognitionRef.current.continuous = true;

          // Set up event handlers with a fresh finalTranscript
          recognitionRef.current.onresult = (event) => {
            let interimTranscript = "";

            for (let i = event.resultIndex; i < event.results.length; i++) {
              const transcript = event.results[i][0].transcript;
              if (event.results[i].isFinal) {
                finalTranscriptRef.current += transcript + " ";
              } else {
                interimTranscript += transcript;
              }
            }

            setTranscript(finalTranscriptRef.current + interimTranscript);
          };

          recognitionRef.current.onerror = (event) => {
            console.error("Speech recognition error", event.error);
            setIsListening(false);
          };

          // Restart recognition if it was active
          if (wasListening) {
            try {
              recognitionRef.current.start();
              setIsListening(true);
            } catch (error) {
              console.error("Failed to restart recognition:", error);
            }
          }
        }, 200); // Add a small delay to ensure recognition has fully stopped
      } catch (error) {
        console.error("Error during transcript clearing:", error);
      }
    }
  };

  const clearResponse = () => {
    setResponse('');
  };

  // Handle exit button click
  const handleExit = useCallback(() => {
    // Use the onBack prop if provided, otherwise navigate to home
    if (onBack) {
      onBack();
    } else {
      window.location.href = '/home';
    }
  }, [onBack]);

  return (
    <div className="speech-to-text">
      {/* Exit button in top right corner */}
      <button className="exit-button" onClick={handleExit} title="Exit">
        <CloseIcon />
      </button>

      {/* Session timer display at the top right corner */}
      <div className="timer-container">
        <AccessTimeIcon className="timer-icon" />
        <span className="timer-display session-timer">
          {formatSessionTime(sessionTimeRemaining)}
        </span>
        {isListening && <span className="listening-indicator">Recording...</span>}
      </div>

      <div className="flex-container">
        {/* Answer section - larger and with side clear button */}
        <div className="answer-container">
          <div className="section-header">
            <h3>AI Response</h3>
            <button
              className="clear-button side-button"
              onClick={clearResponse}
              disabled={!response || sessionExpired}
              title="Clear Response"
            >
              <DeleteIcon fontSize="small" />
            </button>
          </div>
          <textarea
            ref={responseAreaRef}
            className="response-area"
            value={response}
            readOnly
            placeholder="AI answer will appear here..."
            disabled={sessionExpired}
          />
        </div>

        {/* Question section - smaller with side controls */}
        <div className="question-container">
          <div className="section-header">
            <div className="control-group">
              <button
                className={`mic-button ${isListening ? 'listening' : ''}`}
                onClick={isListening ? stopListening : startListening}
                title={isListening ? "Stop Recording" : "Start Recording"}
                disabled={sessionExpired}
              >
                {isListening ? <StopIcon /> : <MicIcon />}
              </button>

              <button
                className="send-button"
                onClick={sendToGPT}
                disabled={isLoading || !transcript.trim() || sessionExpired}
                title="Get Answer"
              >
                {isLoading ? "..." : <SendIcon />}
              </button>

              <button
                className="clear-button side-button"
                onClick={clearTranscript}
                disabled={!transcript || sessionExpired}
                title="Clear Question"
              >
                <DeleteIcon fontSize="small" />
              </button>
            </div>
          </div>
          <textarea
            ref={transcriptAreaRef}
            className="transcript-area"
            value={transcript}
            onKeyDown={(e) => {
              // Special handling for Backspace key during active listening
              if (e.key === 'Backspace' && isListening) {
                // Temporarily stop listening to handle the backspace properly
                if (recognitionRef.current) {
                  recognitionRef.current.stop();

                  // We'll restart in the onChange handler
                  // This prevents conflicts between backspace and ongoing recognition
                }
              }
            }}
            onChange={(e) => {
              // Update the transcript state
              const newValue = e.target.value;
              setTranscript(newValue);

              // If we're listening, we need to handle the change specially
              if (isListening) {
                // Update finalTranscriptRef and restart recognition
                finalTranscriptRef.current = newValue;

                // We'll restart recognition after a short delay
                // This is handled by the useEffect that watches isListening
                setIsListening(false);

                // Restart listening after a short delay
                setTimeout(() => {
                  startListening();
                }, 200);
              }
            }}
            placeholder={sessionExpired ? "Session expired. Please make a payment to continue." : "Type or record your question here..."}
            disabled={sessionExpired}
          />
        </div>
      </div>

      {/* Payment Dialog */}
      <Dialog
        open={showPaymentDialog}
        onClose={handlePaymentDialogClose}
        aria-labelledby="payment-dialog-title"
        aria-describedby="payment-dialog-description"
      >
        <DialogTitle id="payment-dialog-title">
          {"Session Expiring Soon"}
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="payment-dialog-description">
            Your session will expire in one minute. Would you like to make a payment to extend your session?
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handlePaymentDialogClose} color="primary">
            Not Now
          </Button>
          <Button onClick={handlePaymentConfirm} color="primary" autoFocus>
            Make Payment
          </Button>
        </DialogActions>
      </Dialog>

      {/* Session Expired Dialog */}
      <Dialog
        open={sessionExpired}
        aria-labelledby="expired-dialog-title"
        aria-describedby="expired-dialog-description"
      >
        <DialogTitle id="expired-dialog-title">
          {"Session Expired"}
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="expired-dialog-description">
            Your session has expired. Please make a payment to continue using the service.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleExit} color="primary">
            Exit
          </Button>
          <Button onClick={handlePaymentConfirm} color="primary" autoFocus>
            Make Payment
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
}

export default SpeechToText;






