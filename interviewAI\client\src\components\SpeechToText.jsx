import React, { useState, useRef, useEffect, useCallback } from 'react';
import './SpeechToText.css';
import env from '../utils/env';
import MicIcon from '@mui/icons-material/Mic';
import StopIcon from '@mui/icons-material/Stop';
import SendIcon from '@mui/icons-material/Send';
import DeleteIcon from '@mui/icons-material/Delete';
import AccessTimeIcon from '@mui/icons-material/AccessTime';

function SpeechToText() {
  const [isListening, setIsListening] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [response, setResponse] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Timer state
  const [timerSeconds, setTimerSeconds] = useState(0);
  const timerIntervalRef = useRef(null);

  const recognitionRef = useRef(null);
  const transcriptAreaRef = useRef(null);
  const responseAreaRef = useRef(null);

  // Format seconds to MM:SS
  const formatTime = useCallback((totalSeconds) => {
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }, []);

  // Start the timer
  const startTimer = useCallback(() => {
    // Reset timer when starting
    setTimerSeconds(0);

    // Clear any existing interval
    if (timerIntervalRef.current) {
      clearInterval(timerIntervalRef.current);
    }

    // Start a new interval
    timerIntervalRef.current = setInterval(() => {
      setTimerSeconds(prev => prev + 1);
    }, 1000);
  }, []);

  // Stop the timer
  const stopTimer = useCallback(() => {
    if (timerIntervalRef.current) {
      clearInterval(timerIntervalRef.current);
      timerIntervalRef.current = null;
    }
  }, []);

  // Clean up timer on unmount
  useEffect(() => {
    return () => {
      if (timerIntervalRef.current) {
        clearInterval(timerIntervalRef.current);
      }
    };
  }, []);

  useEffect(() => {
    // Initialize speech recognition
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    if (SpeechRecognition) {
      recognitionRef.current = new SpeechRecognition();
      recognitionRef.current.lang = 'en-US';
      recognitionRef.current.interimResults = true;
      recognitionRef.current.continuous = true;

      let finalTranscript = "";

      recognitionRef.current.onresult = (event) => {
        let interimTranscript = "";

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const transcript = event.results[i][0].transcript;
          if (event.results[i].isFinal) {
            finalTranscript += transcript + " ";
          } else {
            interimTranscript += transcript;
          }
        }

        setTranscript(finalTranscript + interimTranscript);
      };

      recognitionRef.current.onerror = (event) => {
        console.error("Speech recognition error", event.error);
        alert("Error occurred: " + event.error);
        setIsListening(false);
      };
    } else {
      alert("Your browser doesn't support speech recognition. Try Chrome or Edge.");
    }

    // Cleanup function
    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.stop();
      }
    };
  }, []);

  useEffect(() => {
    // Auto-scroll transcript area when content changes
    if (transcriptAreaRef.current) {
      transcriptAreaRef.current.scrollTop = transcriptAreaRef.current.scrollHeight;
    }
  }, [transcript]);

  useEffect(() => {
    // Auto-scroll response area when content changes
    if (responseAreaRef.current) {
      responseAreaRef.current.scrollTop = responseAreaRef.current.scrollHeight;
    }
  }, [response]);

  const startListening = () => {
    if (recognitionRef.current && !isListening) {
      try {
        recognitionRef.current.start();
        setIsListening(true);
        startTimer(); // Start the timer when listening begins
      } catch (error) {
        console.error("Speech recognition error:", error);
        // If recognition is already running, stop it first then restart
        if (error.message.includes("already started")) {
          recognitionRef.current.stop();
          setTimeout(() => {
            recognitionRef.current.start();
            setIsListening(true);
            startTimer(); // Start the timer when listening begins
          }, 100);
        }
      }
    }
  };

  const stopListening = () => {
    if (recognitionRef.current && isListening) {
      try {
        recognitionRef.current.stop();
        setIsListening(false);
        stopTimer(); // Stop the timer when listening ends
      } catch (error) {
        console.error("Speech recognition error:", error);
      }
    }
  };

  const sendToGPT = async () => {
    const apiKey = env.OPENAI_API_KEY;
    const userText = transcript.trim();

    console.log("Environment:", process.env.NODE_ENV);
    console.log("API Key available:", apiKey ? "Yes" : "No");

    if (!apiKey) {
      alert("Please add your OpenAI API key to the .env file as REACT_APP_OPENAI_API_KEY and restart the development server.");
      return;
    }

    if (!userText) {
      alert("Please record or enter some text to send to GPT.");
      return;
    }
    const prompt = `In 5 lines, give only the definition and a simple example. ${userText}`;
    setIsLoading(true);
    setResponse('');

    // Store the current transcript before clearing it
    const currentTranscript = transcript;

    try {
      const response = await fetch("https://api.openai.com/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${apiKey}`
        },
        body: JSON.stringify({
          model: "gpt-4.1-nano",
          messages: [
            { role: "user", content: prompt }
          ],
          stream: true
        })
      });

      if (!response.ok || !response.body) throw new Error("Failed to stream response.");

      const reader = response.body.getReader();
      const decoder = new TextDecoder("utf-8");

      let result = "";

      while (true) {
        const { value, done } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value, { stream: true });
        const lines = chunk.split("\n").filter(line => line.trim().startsWith("data:"));

        for (const line of lines) {
          const data = line.replace(/^data: /, '');
          if (data === "[DONE]") break;

          try {
            const json = JSON.parse(data);
            const content = json.choices?.[0]?.delta?.content;
            if (content) {
              result += content;
              setResponse(result);
            }
          } catch (e) {
            console.error("Error parsing JSON:", e);
          }
        }
      }
    } catch (error) {
      console.error("Streaming Error:", error);
      setResponse("Error occurred: " + error.message);
    } finally {
      if (isListening) {
        stopListening();
      }
      setIsLoading(false);

      // Clear the transcript after getting the answer
      setTranscript('');
    }
  };

  const clearTranscript = () => {
    setTranscript('');

    // Reset the finalTranscript in the speech recognition handler
    if (recognitionRef.current) {
      // We need to stop and restart recognition to clear its internal state
      const wasListening = isListening;

      try {
        // Only stop if currently listening
        if (wasListening) {
          recognitionRef.current.stop();
          setIsListening(false);
        }

        // Wait a moment to ensure recognition has fully stopped
        setTimeout(() => {
          // Reinitialize speech recognition to clear its state
          const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
          recognitionRef.current = new SpeechRecognition();
          recognitionRef.current.lang = 'en-US';
          recognitionRef.current.interimResults = true;
          recognitionRef.current.continuous = true;

          // Set up event handlers with a fresh finalTranscript
          let finalTranscript = "";

          recognitionRef.current.onresult = (event) => {
            let interimTranscript = "";

            for (let i = event.resultIndex; i < event.results.length; i++) {
              const transcript = event.results[i][0].transcript;
              if (event.results[i].isFinal) {
                finalTranscript += transcript + " ";
              } else {
                interimTranscript += transcript;
              }
            }

            setTranscript(finalTranscript + interimTranscript);
          };

          recognitionRef.current.onerror = (event) => {
            console.error("Speech recognition error", event.error);
            setIsListening(false);
          };

          // Restart recognition if it was active
          if (wasListening) {
            try {
              recognitionRef.current.start();
              setIsListening(true);
            } catch (error) {
              console.error("Failed to restart recognition:", error);
            }
          }
        }, 200); // Add a small delay to ensure recognition has fully stopped
      } catch (error) {
        console.error("Error during transcript clearing:", error);
      }
    }
  };

  const clearResponse = () => {
    setResponse('');
  };

  return (
    <div className="speech-to-text">
      {/* Timer display at the top */}
      <div className="timer-container">
        <AccessTimeIcon className="timer-icon" />
        <span className="timer-display">{formatTime(timerSeconds)}</span>
        {isListening && <span className="listening-indicator">Recording...</span>}
      </div>

      <div className="flex-container">
        {/* Answer section - now placed above */}
        <div className="answer-container">
          <div className="section-header">
            <h3>AI Response</h3>
          </div>
          <textarea
            ref={responseAreaRef}
            className="response-area"
            value={response}
            readOnly
            placeholder="AI answer will appear here..."
          />
          <div className="button-row">
            <button
              className="clear-button"
              onClick={clearResponse}
              disabled={!response}
            >
              <DeleteIcon fontSize="small" /> Clear
            </button>
          </div>
        </div>

        {/* Question section - now smaller and below */}
        <div className="question-container">
          <div className="section-header">
            <h3>Your Question</h3>
            <div className="button-row compact">
              <button
                className={`mic-button ${isListening ? 'listening' : ''}`}
                onClick={isListening ? stopListening : startListening}
                title={isListening ? "Stop Recording" : "Start Recording"}
              >
                {isListening ? <StopIcon /> : <MicIcon />}
              </button>

              <button
                className="send-button"
                onClick={sendToGPT}
                disabled={isLoading || !transcript.trim()}
                title="Get Answer"
              >
                {isLoading ? "..." : <SendIcon />}
              </button>
            </div>
          </div>
          <textarea
            ref={transcriptAreaRef}
            className="transcript-area"
            value={transcript}
            onChange={(e) => setTranscript(e.target.value)}
            placeholder="Type or record your question here..."
          />
          <div className="button-row">
            <button
              className="clear-button"
              onClick={clearTranscript}
              disabled={!transcript}
            >
              <DeleteIcon fontSize="small" /> Clear
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default SpeechToText;






