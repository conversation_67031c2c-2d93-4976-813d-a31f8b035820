{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\interview-ai-app\\\\interviewAI\\\\client\\\\src\\\\pages\\\\DiarizedInterviewPage.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useRef, useCallback } from 'react';\nimport DiarizedTranscription from '../components/DiarizedTranscription';\nimport './DiarizedInterviewPage.css';\nimport env from '../utils/env';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction DiarizedInterviewPage() {\n  _s();\n  const [transcriptSegments, setTranscriptSegments] = useState([]);\n  const [currentTranscript, setCurrentTranscript] = useState('');\n  const [response, setResponse] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [autoSubmit, setAutoSubmit] = useState(true);\n\n  // Timer for auto-submission\n  const autoSubmitTimerRef = useRef(null);\n\n  // Response area ref for scrolling\n  const responseAreaRef = useRef(null);\n\n  // Handle transcript changes from the DiarizedTranscription component\n  const handleTranscriptChange = useCallback((transcript, segments) => {\n    setCurrentTranscript(transcript);\n\n    // Update segments if provided\n    if (segments && segments.length > 0) {\n      setTranscriptSegments(segments);\n    }\n\n    // Set up auto-submit timer\n    if (autoSubmit && transcript.trim()) {\n      if (autoSubmitTimerRef.current) {\n        clearTimeout(autoSubmitTimerRef.current);\n      }\n      autoSubmitTimerRef.current = setTimeout(() => {\n        // Only auto-submit if we have a non-empty transcript and we're not already loading\n        if (transcript.trim() && !isLoading) {\n          console.log(\"Auto-submitting transcript\");\n          handleSubmit(transcript);\n        }\n      }, 2000); // 2 second pause detection\n    }\n  }, [autoSubmit, isLoading]);\n\n  // Handle manual submission\n  const handleSubmit = useCallback(async textToSubmit => {\n    const userText = textToSubmit || currentTranscript.trim();\n    if (!userText) {\n      console.warn(\"No transcript to send\");\n      return;\n    }\n    const apiKey = env.OPENAI_API_KEY;\n    if (!apiKey) {\n      alert(\"Please add your OpenAI API key to the .env file as REACT_APP_OPENAI_API_KEY and restart the development server.\");\n      return;\n    }\n    setIsLoading(true);\n    try {\n      console.log(\"Sending to GPT API:\", userText);\n      const response = await fetch(\"https://api.openai.com/v1/chat/completions\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          \"Authorization\": `Bearer ${apiKey}`\n        },\n        body: JSON.stringify({\n          model: \"gpt-4.1-nano\",\n          messages: [{\n            role: \"system\",\n            content: \"You are Sensei, an AI interview assistant. Provide detailed, well-structured responses to interview questions. Format your answers with bullet points for clarity. Keep responses concise but comprehensive.\"\n          }, {\n            role: \"user\",\n            content: userText\n          }],\n          stream: true\n        })\n      });\n      if (!response.ok || !response.body) throw new Error(\"Failed to stream response.\");\n      const reader = response.body.getReader();\n      const decoder = new TextDecoder(\"utf-8\");\n      let result = \"\";\n      setResponse(''); // Clear previous response\n\n      while (true) {\n        const {\n          value,\n          done\n        } = await reader.read();\n        if (done) break;\n        const chunk = decoder.decode(value, {\n          stream: true\n        });\n        const lines = chunk.split(\"\\n\").filter(line => line.trim().startsWith(\"data:\"));\n        for (const line of lines) {\n          const data = line.replace(/^data: /, '');\n          if (data === \"[DONE]\") break;\n          try {\n            var _json$choices, _json$choices$, _json$choices$$delta;\n            const json = JSON.parse(data);\n            const content = (_json$choices = json.choices) === null || _json$choices === void 0 ? void 0 : (_json$choices$ = _json$choices[0]) === null || _json$choices$ === void 0 ? void 0 : (_json$choices$$delta = _json$choices$.delta) === null || _json$choices$$delta === void 0 ? void 0 : _json$choices$$delta.content;\n            if (content) {\n              result += content;\n              setResponse(result);\n            }\n          } catch (e) {\n            console.error(\"Error parsing JSON:\", e);\n          }\n        }\n      }\n    } catch (error) {\n      console.error(\"Streaming Error:\", error);\n      setResponse(\"Error occurred: \" + error.message);\n    } finally {\n      setIsLoading(false);\n    }\n  }, [currentTranscript]);\n\n  // Auto-scroll response area when content changes\n  const scrollToBottom = () => {\n    if (responseAreaRef.current) {\n      responseAreaRef.current.scrollTop = responseAreaRef.current.scrollHeight;\n    }\n  };\n\n  // Call scrollToBottom whenever response changes\n  useState(() => {\n    scrollToBottom();\n  }, [response]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"diarized-interview-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Enhanced Interview with Speaker Diarization\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"description\",\n        children: \"This page uses advanced audio analysis to separate different speakers in the conversation.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"interview-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"transcription-panel\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"panel-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Live Transcription\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"auto-submit-toggle\",\n            children: /*#__PURE__*/_jsxDEV(\"label\", {\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                checked: autoSubmit,\n                onChange: () => setAutoSubmit(!autoSubmit)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 17\n              }, this), \"Auto-submit\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DiarizedTranscription, {\n          onTranscriptChange: handleTranscriptChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"manual-submit\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"submit-button\",\n            onClick: () => handleSubmit(),\n            disabled: isLoading || !currentTranscript.trim(),\n            children: isLoading ? 'Processing...' : 'Submit Transcript'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"response-panel\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"panel-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"AI Response\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: responseAreaRef,\n          className: \"response-content\",\n          children: response ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"response-message\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"message-text\",\n              children: response\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"empty-state\",\n            children: \"AI responses will appear here\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 134,\n    columnNumber: 5\n  }, this);\n}\n_s(DiarizedInterviewPage, \"2eFelkR++k10jrkdrCBXxyuQY1c=\");\n_c = DiarizedInterviewPage;\nexport default DiarizedInterviewPage;\nvar _c;\n$RefreshReg$(_c, \"DiarizedInterviewPage\");", "map": {"version": 3, "names": ["useState", "useRef", "useCallback", "DiarizedTranscription", "env", "jsxDEV", "_jsxDEV", "DiarizedInterviewPage", "_s", "transcriptSegments", "setTranscriptSegments", "currentTranscript", "setCurrentTranscript", "response", "setResponse", "isLoading", "setIsLoading", "autoSubmit", "setAutoSubmit", "autoSubmitTimerRef", "responseAreaRef", "handleTranscriptChange", "transcript", "segments", "length", "trim", "current", "clearTimeout", "setTimeout", "console", "log", "handleSubmit", "textToSubmit", "userText", "warn", "<PERSON><PERSON><PERSON><PERSON>", "OPENAI_API_KEY", "alert", "fetch", "method", "headers", "body", "JSON", "stringify", "model", "messages", "role", "content", "stream", "ok", "Error", "reader", "<PERSON><PERSON><PERSON><PERSON>", "decoder", "TextDecoder", "result", "value", "done", "read", "chunk", "decode", "lines", "split", "filter", "line", "startsWith", "data", "replace", "_json$choices", "_json$choices$", "_json$choices$$delta", "json", "parse", "choices", "delta", "e", "error", "message", "scrollToBottom", "scrollTop", "scrollHeight", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "checked", "onChange", "onTranscriptChange", "onClick", "disabled", "ref", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/interview-ai-app/interviewAI/client/src/pages/DiarizedInterviewPage.jsx"], "sourcesContent": ["import { useState, useRef, useCallback } from 'react';\nimport DiarizedTranscription from '../components/DiarizedTranscription';\nimport './DiarizedInterviewPage.css';\nimport env from '../utils/env';\n\nfunction DiarizedInterviewPage() {\n  const [transcriptSegments, setTranscriptSegments] = useState([]);\n  const [currentTranscript, setCurrentTranscript] = useState('');\n  const [response, setResponse] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [autoSubmit, setAutoSubmit] = useState(true);\n  \n  // Timer for auto-submission\n  const autoSubmitTimerRef = useRef(null);\n  \n  // Response area ref for scrolling\n  const responseAreaRef = useRef(null);\n  \n  // Handle transcript changes from the DiarizedTranscription component\n  const handleTranscriptChange = useCallback((transcript, segments) => {\n    setCurrentTranscript(transcript);\n    \n    // Update segments if provided\n    if (segments && segments.length > 0) {\n      setTranscriptSegments(segments);\n    }\n    \n    // Set up auto-submit timer\n    if (autoSubmit && transcript.trim()) {\n      if (autoSubmitTimerRef.current) {\n        clearTimeout(autoSubmitTimerRef.current);\n      }\n      \n      autoSubmitTimerRef.current = setTimeout(() => {\n        // Only auto-submit if we have a non-empty transcript and we're not already loading\n        if (transcript.trim() && !isLoading) {\n          console.log(\"Auto-submitting transcript\");\n          handleSubmit(transcript);\n        }\n      }, 2000); // 2 second pause detection\n    }\n  }, [autoSubmit, isLoading]);\n  \n  // Handle manual submission\n  const handleSubmit = useCallback(async (textToSubmit) => {\n    const userText = textToSubmit || currentTranscript.trim();\n    \n    if (!userText) {\n      console.warn(\"No transcript to send\");\n      return;\n    }\n    \n    const apiKey = env.OPENAI_API_KEY;\n    if (!apiKey) {\n      alert(\"Please add your OpenAI API key to the .env file as REACT_APP_OPENAI_API_KEY and restart the development server.\");\n      return;\n    }\n    \n    setIsLoading(true);\n    \n    try {\n      console.log(\"Sending to GPT API:\", userText);\n      const response = await fetch(\"https://api.openai.com/v1/chat/completions\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          \"Authorization\": `Bearer ${apiKey}`\n        },\n        body: JSON.stringify({\n          model: \"gpt-4.1-nano\",\n          messages: [\n            {\n              role: \"system\",\n              content: \"You are Sensei, an AI interview assistant. Provide detailed, well-structured responses to interview questions. Format your answers with bullet points for clarity. Keep responses concise but comprehensive.\"\n            },\n            { role: \"user\", content: userText }\n          ],\n          stream: true\n        })\n      });\n      \n      if (!response.ok || !response.body) throw new Error(\"Failed to stream response.\");\n      \n      const reader = response.body.getReader();\n      const decoder = new TextDecoder(\"utf-8\");\n      \n      let result = \"\";\n      setResponse(''); // Clear previous response\n      \n      while (true) {\n        const { value, done } = await reader.read();\n        if (done) break;\n        \n        const chunk = decoder.decode(value, { stream: true });\n        const lines = chunk.split(\"\\n\").filter(line => line.trim().startsWith(\"data:\"));\n        \n        for (const line of lines) {\n          const data = line.replace(/^data: /, '');\n          if (data === \"[DONE]\") break;\n          \n          try {\n            const json = JSON.parse(data);\n            const content = json.choices?.[0]?.delta?.content;\n            if (content) {\n              result += content;\n              setResponse(result);\n            }\n          } catch (e) {\n            console.error(\"Error parsing JSON:\", e);\n          }\n        }\n      }\n    } catch (error) {\n      console.error(\"Streaming Error:\", error);\n      setResponse(\"Error occurred: \" + error.message);\n    } finally {\n      setIsLoading(false);\n    }\n  }, [currentTranscript]);\n  \n  // Auto-scroll response area when content changes\n  const scrollToBottom = () => {\n    if (responseAreaRef.current) {\n      responseAreaRef.current.scrollTop = responseAreaRef.current.scrollHeight;\n    }\n  };\n  \n  // Call scrollToBottom whenever response changes\n  useState(() => {\n    scrollToBottom();\n  }, [response]);\n  \n  return (\n    <div className=\"diarized-interview-page\">\n      <div className=\"page-header\">\n        <h1>Enhanced Interview with Speaker Diarization</h1>\n        <p className=\"description\">\n          This page uses advanced audio analysis to separate different speakers in the conversation.\n        </p>\n      </div>\n      \n      <div className=\"interview-container\">\n        <div className=\"transcription-panel\">\n          <div className=\"panel-header\">\n            <h2>Live Transcription</h2>\n            <div className=\"auto-submit-toggle\">\n              <label>\n                <input \n                  type=\"checkbox\" \n                  checked={autoSubmit} \n                  onChange={() => setAutoSubmit(!autoSubmit)}\n                />\n                Auto-submit\n              </label>\n            </div>\n          </div>\n          \n          <DiarizedTranscription onTranscriptChange={handleTranscriptChange} />\n          \n          <div className=\"manual-submit\">\n            <button \n              className=\"submit-button\"\n              onClick={() => handleSubmit()}\n              disabled={isLoading || !currentTranscript.trim()}\n            >\n              {isLoading ? 'Processing...' : 'Submit Transcript'}\n            </button>\n          </div>\n        </div>\n        \n        <div className=\"response-panel\">\n          <div className=\"panel-header\">\n            <h2>AI Response</h2>\n          </div>\n          \n          <div \n            ref={responseAreaRef}\n            className=\"response-content\"\n          >\n            {response ? (\n              <div className=\"response-message\">\n                <div className=\"message-text\">{response}</div>\n              </div>\n            ) : (\n              <div className=\"empty-state\">AI responses will appear here</div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default DiarizedInterviewPage;\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AACrD,OAAOC,qBAAqB,MAAM,qCAAqC;AACvE,OAAO,6BAA6B;AACpC,OAAOC,GAAG,MAAM,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,SAASC,qBAAqBA,CAAA,EAAG;EAAAC,EAAA;EAC/B,MAAM,CAACC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAACW,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACa,QAAQ,EAAEC,WAAW,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACe,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACiB,UAAU,EAAEC,aAAa,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAMmB,kBAAkB,GAAGlB,MAAM,CAAC,IAAI,CAAC;;EAEvC;EACA,MAAMmB,eAAe,GAAGnB,MAAM,CAAC,IAAI,CAAC;;EAEpC;EACA,MAAMoB,sBAAsB,GAAGnB,WAAW,CAAC,CAACoB,UAAU,EAAEC,QAAQ,KAAK;IACnEX,oBAAoB,CAACU,UAAU,CAAC;;IAEhC;IACA,IAAIC,QAAQ,IAAIA,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;MACnCd,qBAAqB,CAACa,QAAQ,CAAC;IACjC;;IAEA;IACA,IAAIN,UAAU,IAAIK,UAAU,CAACG,IAAI,CAAC,CAAC,EAAE;MACnC,IAAIN,kBAAkB,CAACO,OAAO,EAAE;QAC9BC,YAAY,CAACR,kBAAkB,CAACO,OAAO,CAAC;MAC1C;MAEAP,kBAAkB,CAACO,OAAO,GAAGE,UAAU,CAAC,MAAM;QAC5C;QACA,IAAIN,UAAU,CAACG,IAAI,CAAC,CAAC,IAAI,CAACV,SAAS,EAAE;UACnCc,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;UACzCC,YAAY,CAACT,UAAU,CAAC;QAC1B;MACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;IACZ;EACF,CAAC,EAAE,CAACL,UAAU,EAAEF,SAAS,CAAC,CAAC;;EAE3B;EACA,MAAMgB,YAAY,GAAG7B,WAAW,CAAC,MAAO8B,YAAY,IAAK;IACvD,MAAMC,QAAQ,GAAGD,YAAY,IAAIrB,iBAAiB,CAACc,IAAI,CAAC,CAAC;IAEzD,IAAI,CAACQ,QAAQ,EAAE;MACbJ,OAAO,CAACK,IAAI,CAAC,uBAAuB,CAAC;MACrC;IACF;IAEA,MAAMC,MAAM,GAAG/B,GAAG,CAACgC,cAAc;IACjC,IAAI,CAACD,MAAM,EAAE;MACXE,KAAK,CAAC,iHAAiH,CAAC;MACxH;IACF;IAEArB,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACFa,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEG,QAAQ,CAAC;MAC5C,MAAMpB,QAAQ,GAAG,MAAMyB,KAAK,CAAC,4CAA4C,EAAE;QACzEC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAE,UAAUL,MAAM;QACnC,CAAC;QACDM,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBC,KAAK,EAAE,cAAc;UACrBC,QAAQ,EAAE,CACR;YACEC,IAAI,EAAE,QAAQ;YACdC,OAAO,EAAE;UACX,CAAC,EACD;YAAED,IAAI,EAAE,MAAM;YAAEC,OAAO,EAAEd;UAAS,CAAC,CACpC;UACDe,MAAM,EAAE;QACV,CAAC;MACH,CAAC,CAAC;MAEF,IAAI,CAACnC,QAAQ,CAACoC,EAAE,IAAI,CAACpC,QAAQ,CAAC4B,IAAI,EAAE,MAAM,IAAIS,KAAK,CAAC,4BAA4B,CAAC;MAEjF,MAAMC,MAAM,GAAGtC,QAAQ,CAAC4B,IAAI,CAACW,SAAS,CAAC,CAAC;MACxC,MAAMC,OAAO,GAAG,IAAIC,WAAW,CAAC,OAAO,CAAC;MAExC,IAAIC,MAAM,GAAG,EAAE;MACfzC,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC;;MAEjB,OAAO,IAAI,EAAE;QACX,MAAM;UAAE0C,KAAK;UAAEC;QAAK,CAAC,GAAG,MAAMN,MAAM,CAACO,IAAI,CAAC,CAAC;QAC3C,IAAID,IAAI,EAAE;QAEV,MAAME,KAAK,GAAGN,OAAO,CAACO,MAAM,CAACJ,KAAK,EAAE;UAAER,MAAM,EAAE;QAAK,CAAC,CAAC;QACrD,MAAMa,KAAK,GAAGF,KAAK,CAACG,KAAK,CAAC,IAAI,CAAC,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACvC,IAAI,CAAC,CAAC,CAACwC,UAAU,CAAC,OAAO,CAAC,CAAC;QAE/E,KAAK,MAAMD,IAAI,IAAIH,KAAK,EAAE;UACxB,MAAMK,IAAI,GAAGF,IAAI,CAACG,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;UACxC,IAAID,IAAI,KAAK,QAAQ,EAAE;UAEvB,IAAI;YAAA,IAAAE,aAAA,EAAAC,cAAA,EAAAC,oBAAA;YACF,MAAMC,IAAI,GAAG7B,IAAI,CAAC8B,KAAK,CAACN,IAAI,CAAC;YAC7B,MAAMnB,OAAO,IAAAqB,aAAA,GAAGG,IAAI,CAACE,OAAO,cAAAL,aAAA,wBAAAC,cAAA,GAAZD,aAAA,CAAe,CAAC,CAAC,cAAAC,cAAA,wBAAAC,oBAAA,GAAjBD,cAAA,CAAmBK,KAAK,cAAAJ,oBAAA,uBAAxBA,oBAAA,CAA0BvB,OAAO;YACjD,IAAIA,OAAO,EAAE;cACXQ,MAAM,IAAIR,OAAO;cACjBjC,WAAW,CAACyC,MAAM,CAAC;YACrB;UACF,CAAC,CAAC,OAAOoB,CAAC,EAAE;YACV9C,OAAO,CAAC+C,KAAK,CAAC,qBAAqB,EAAED,CAAC,CAAC;UACzC;QACF;MACF;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd/C,OAAO,CAAC+C,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MACxC9D,WAAW,CAAC,kBAAkB,GAAG8D,KAAK,CAACC,OAAO,CAAC;IACjD,CAAC,SAAS;MACR7D,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,CAACL,iBAAiB,CAAC,CAAC;;EAEvB;EACA,MAAMmE,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI1D,eAAe,CAACM,OAAO,EAAE;MAC3BN,eAAe,CAACM,OAAO,CAACqD,SAAS,GAAG3D,eAAe,CAACM,OAAO,CAACsD,YAAY;IAC1E;EACF,CAAC;;EAED;EACAhF,QAAQ,CAAC,MAAM;IACb8E,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACjE,QAAQ,CAAC,CAAC;EAEd,oBACEP,OAAA;IAAK2E,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBACtC5E,OAAA;MAAK2E,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1B5E,OAAA;QAAA4E,QAAA,EAAI;MAA2C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpDhF,OAAA;QAAG2E,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAE3B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAENhF,OAAA;MAAK2E,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClC5E,OAAA;QAAK2E,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAClC5E,OAAA;UAAK2E,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B5E,OAAA;YAAA4E,QAAA,EAAI;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3BhF,OAAA;YAAK2E,SAAS,EAAC,oBAAoB;YAAAC,QAAA,eACjC5E,OAAA;cAAA4E,QAAA,gBACE5E,OAAA;gBACEiF,IAAI,EAAC,UAAU;gBACfC,OAAO,EAAEvE,UAAW;gBACpBwE,QAAQ,EAAEA,CAAA,KAAMvE,aAAa,CAAC,CAACD,UAAU;cAAE;gBAAAkE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC,eAEJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENhF,OAAA,CAACH,qBAAqB;UAACuF,kBAAkB,EAAErE;QAAuB;UAAA8D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAErEhF,OAAA;UAAK2E,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5B5E,OAAA;YACE2E,SAAS,EAAC,eAAe;YACzBU,OAAO,EAAEA,CAAA,KAAM5D,YAAY,CAAC,CAAE;YAC9B6D,QAAQ,EAAE7E,SAAS,IAAI,CAACJ,iBAAiB,CAACc,IAAI,CAAC,CAAE;YAAAyD,QAAA,EAEhDnE,SAAS,GAAG,eAAe,GAAG;UAAmB;YAAAoE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENhF,OAAA;QAAK2E,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B5E,OAAA;UAAK2E,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3B5E,OAAA;YAAA4E,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,eAENhF,OAAA;UACEuF,GAAG,EAAEzE,eAAgB;UACrB6D,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAE3BrE,QAAQ,gBACPP,OAAA;YAAK2E,SAAS,EAAC,kBAAkB;YAAAC,QAAA,eAC/B5E,OAAA;cAAK2E,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAErE;YAAQ;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,gBAENhF,OAAA;YAAK2E,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAA6B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAChE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC9E,EAAA,CA1LQD,qBAAqB;AAAAuF,EAAA,GAArBvF,qBAAqB;AA4L9B,eAAeA,qBAAqB;AAAC,IAAAuF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}