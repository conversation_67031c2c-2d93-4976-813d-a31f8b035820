{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\interview-ai-app\\\\interviewAI\\\\client\\\\src\\\\pages\\\\DiarizedInterviewPage.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useRef, useCallback, useEffect } from 'react';\nimport DiarizedTranscription from '../components/DiarizedTranscription';\nimport './DiarizedInterviewPage.css';\nimport env from '../utils/env';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction DiarizedInterviewPage() {\n  _s();\n  // eslint-disable-next-line\n  const [transcriptSegments, setTranscriptSegments] = useState([]);\n  const [currentTranscript, setCurrentTranscript] = useState('');\n  const [response, setResponse] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [autoSubmit, setAutoSubmit] = useState(true);\n\n  // Timer for auto-submission\n  const autoSubmitTimerRef = useRef(null);\n\n  // Response area ref for scrolling\n  const responseAreaRef = useRef(null);\n\n  // Handle manual submission\n  const handleSubmit = useCallback(async textToSubmit => {\n    const userText = textToSubmit || currentTranscript.trim();\n    if (!userText) {\n      console.warn(\"No transcript to send\");\n      return;\n    }\n    const apiKey = env.OPENAI_API_KEY;\n    if (!apiKey) {\n      alert(\"Please add your OpenAI API key to the .env file as REACT_APP_OPENAI_API_KEY and restart the development server.\");\n      return;\n    }\n    setIsLoading(true);\n    try {\n      console.log(\"Sending to GPT API:\", userText);\n      const response = await fetch(\"https://api.openai.com/v1/chat/completions\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          \"Authorization\": `Bearer ${apiKey}`\n        },\n        body: JSON.stringify({\n          model: \"gpt-4.1-nano-opo\",\n          messages: [{\n            role: \"system\",\n            content: \"an AI interview assistant. Provide detailed, well-structured responses to interview questions. Format your answers with bullet points for clarity. Keep responses concise but comprehensive.\"\n          }, {\n            role: \"user\",\n            content: userText\n          }],\n          stream: true\n        })\n      });\n      if (!response.ok || !response.body) throw new Error(\"Failed to stream response.\");\n      const reader = response.body.getReader();\n      const decoder = new TextDecoder(\"utf-8\");\n      let result = \"\";\n      setResponse(''); // Clear previous response\n\n      while (true) {\n        const {\n          value,\n          done\n        } = await reader.read();\n        if (done) break;\n        const chunk = decoder.decode(value, {\n          stream: true\n        });\n        const lines = chunk.split(\"\\n\").filter(line => line.trim().startsWith(\"data:\"));\n        for (const line of lines) {\n          const data = line.replace(/^data: /, '');\n          if (data === \"[DONE]\") break;\n          try {\n            var _json$choices, _json$choices$, _json$choices$$delta;\n            const json = JSON.parse(data);\n            const content = (_json$choices = json.choices) === null || _json$choices === void 0 ? void 0 : (_json$choices$ = _json$choices[0]) === null || _json$choices$ === void 0 ? void 0 : (_json$choices$$delta = _json$choices$.delta) === null || _json$choices$$delta === void 0 ? void 0 : _json$choices$$delta.content;\n            if (content) {\n              result += content;\n              setResponse(result);\n            }\n          } catch (e) {\n            console.error(\"Error parsing JSON:\", e);\n          }\n        }\n      }\n    } catch (error) {\n      console.error(\"Streaming Error:\", error);\n      setResponse(\"Error occurred: \" + error.message);\n    } finally {\n      setIsLoading(false);\n    }\n  }, [currentTranscript]);\n\n  // Handle transcript changes from the DiarizedTranscription component\n  // Now this will only receive interviewer questions (speaker1)\n  const handleTranscriptChange = useCallback((transcript, segments) => {\n    // Only process if we have a valid transcript\n    if (!transcript || !transcript.trim()) return;\n    console.log(\"Received interviewer question:\", transcript);\n    setCurrentTranscript(transcript);\n\n    // Update segments if provided\n    if (segments && segments.length > 0) {\n      setTranscriptSegments(prev => [...prev, ...segments]);\n    }\n\n    // Set up auto-submit timer for interviewer questions\n    if (autoSubmit) {\n      if (autoSubmitTimerRef.current) {\n        clearTimeout(autoSubmitTimerRef.current);\n      }\n      autoSubmitTimerRef.current = setTimeout(() => {\n        // Only auto-submit if we're not already loading\n        if (!isLoading) {\n          console.log(\"Auto-submitting interviewer question to GPT\");\n          handleSubmit(transcript);\n        }\n      }, 1000); // 1 second delay before submitting interviewer question\n    }\n  }, [autoSubmit, isLoading, handleSubmit]);\n\n  // Auto-scroll response area when content changes\n  const scrollToBottom = () => {\n    if (responseAreaRef.current) {\n      responseAreaRef.current.scrollTop = responseAreaRef.current.scrollHeight;\n    }\n  };\n\n  // Call scrollToBottom whenever response changes\n  useEffect(() => {\n    scrollToBottom();\n  }, [response]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"diarized-interview-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Enhanced Interview with Speaker Diarization\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"description\",\n        children: \"This page separates different speakers in the conversation. Only interviewer questions (from tab audio) will be sent to the AI for responses.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"interview-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"transcription-panel\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"panel-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Live Transcription\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"auto-submit-toggle\",\n            children: /*#__PURE__*/_jsxDEV(\"label\", {\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                checked: autoSubmit,\n                onChange: () => setAutoSubmit(!autoSubmit)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 17\n              }, this), \"Auto-submit interviewer questions\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DiarizedTranscription, {\n          onTranscriptChange: handleTranscriptChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"manual-submit\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"submit-button\",\n            onClick: () => handleSubmit(),\n            disabled: isLoading || !currentTranscript.trim(),\n            children: isLoading ? 'Processing...' : 'Submit Current Question to AI'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"response-panel\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"panel-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"AI Response to Interviewer Questions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"panel-description\",\n            children: \"The AI will help you prepare answers to the interviewer's questions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: responseAreaRef,\n          className: \"response-content\",\n          children: response ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"response-message\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"message-text\",\n              children: response\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"empty-state\",\n            children: \"AI responses will appear here\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 140,\n    columnNumber: 5\n  }, this);\n}\n_s(DiarizedInterviewPage, \"+kJ4czIPobBlhhEW4yJd9yuCCCM=\");\n_c = DiarizedInterviewPage;\nexport default DiarizedInterviewPage;\nvar _c;\n$RefreshReg$(_c, \"DiarizedInterviewPage\");", "map": {"version": 3, "names": ["useState", "useRef", "useCallback", "useEffect", "DiarizedTranscription", "env", "jsxDEV", "_jsxDEV", "DiarizedInterviewPage", "_s", "transcriptSegments", "setTranscriptSegments", "currentTranscript", "setCurrentTranscript", "response", "setResponse", "isLoading", "setIsLoading", "autoSubmit", "setAutoSubmit", "autoSubmitTimerRef", "responseAreaRef", "handleSubmit", "textToSubmit", "userText", "trim", "console", "warn", "<PERSON><PERSON><PERSON><PERSON>", "OPENAI_API_KEY", "alert", "log", "fetch", "method", "headers", "body", "JSON", "stringify", "model", "messages", "role", "content", "stream", "ok", "Error", "reader", "<PERSON><PERSON><PERSON><PERSON>", "decoder", "TextDecoder", "result", "value", "done", "read", "chunk", "decode", "lines", "split", "filter", "line", "startsWith", "data", "replace", "_json$choices", "_json$choices$", "_json$choices$$delta", "json", "parse", "choices", "delta", "e", "error", "message", "handleTranscriptChange", "transcript", "segments", "length", "prev", "current", "clearTimeout", "setTimeout", "scrollToBottom", "scrollTop", "scrollHeight", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "checked", "onChange", "onTranscriptChange", "onClick", "disabled", "ref", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/interview-ai-app/interviewAI/client/src/pages/DiarizedInterviewPage.jsx"], "sourcesContent": ["import { useState, useRef, useCallback, useEffect } from 'react';\nimport DiarizedTranscription from '../components/DiarizedTranscription';\nimport './DiarizedInterviewPage.css';\nimport env from '../utils/env';\n\nfunction DiarizedInterviewPage() {\n  // eslint-disable-next-line\n  const [transcriptSegments, setTranscriptSegments] = useState([]);\n  const [currentTranscript, setCurrentTranscript] = useState('');\n  const [response, setResponse] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [autoSubmit, setAutoSubmit] = useState(true);\n\n  // Timer for auto-submission\n  const autoSubmitTimerRef = useRef(null);\n\n  // Response area ref for scrolling\n  const responseAreaRef = useRef(null);\n\n  // Handle manual submission\n  const handleSubmit = useCallback(async (textToSubmit) => {\n    const userText = textToSubmit || currentTranscript.trim();\n\n    if (!userText) {\n      console.warn(\"No transcript to send\");\n      return;\n    }\n\n    const apiKey = env.OPENAI_API_KEY;\n    if (!apiKey) {\n      alert(\"Please add your OpenAI API key to the .env file as REACT_APP_OPENAI_API_KEY and restart the development server.\");\n      return;\n    }\n\n    setIsLoading(true);\n\n    try {\n      console.log(\"Sending to GPT API:\", userText);\n      const response = await fetch(\"https://api.openai.com/v1/chat/completions\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          \"Authorization\": `Bearer ${apiKey}`\n        },\n        body: JSON.stringify({\n          model: \"gpt-4.1-nano-opo\",\n          messages: [\n            {\n              role: \"system\",\n              content: \"an AI interview assistant. Provide detailed, well-structured responses to interview questions. Format your answers with bullet points for clarity. Keep responses concise but comprehensive.\"\n            },\n            { role: \"user\", content: userText }\n          ],\n          stream: true\n        })\n      });\n\n      if (!response.ok || !response.body) throw new Error(\"Failed to stream response.\");\n\n      const reader = response.body.getReader();\n      const decoder = new TextDecoder(\"utf-8\");\n\n      let result = \"\";\n      setResponse(''); // Clear previous response\n\n      while (true) {\n        const { value, done } = await reader.read();\n        if (done) break;\n\n        const chunk = decoder.decode(value, { stream: true });\n        const lines = chunk.split(\"\\n\").filter(line => line.trim().startsWith(\"data:\"));\n\n        for (const line of lines) {\n          const data = line.replace(/^data: /, '');\n          if (data === \"[DONE]\") break;\n\n          try {\n            const json = JSON.parse(data);\n            const content = json.choices?.[0]?.delta?.content;\n            if (content) {\n              result += content;\n              setResponse(result);\n            }\n          } catch (e) {\n            console.error(\"Error parsing JSON:\", e);\n          }\n        }\n      }\n    } catch (error) {\n      console.error(\"Streaming Error:\", error);\n      setResponse(\"Error occurred: \" + error.message);\n    } finally {\n      setIsLoading(false);\n    }\n  }, [currentTranscript]);\n\n  // Handle transcript changes from the DiarizedTranscription component\n  // Now this will only receive interviewer questions (speaker1)\n  const handleTranscriptChange = useCallback((transcript, segments) => {\n    // Only process if we have a valid transcript\n    if (!transcript || !transcript.trim()) return;\n\n    console.log(\"Received interviewer question:\", transcript);\n    setCurrentTranscript(transcript);\n\n    // Update segments if provided\n    if (segments && segments.length > 0) {\n      setTranscriptSegments(prev => [...prev, ...segments]);\n    }\n\n    // Set up auto-submit timer for interviewer questions\n    if (autoSubmit) {\n      if (autoSubmitTimerRef.current) {\n        clearTimeout(autoSubmitTimerRef.current);\n      }\n\n      autoSubmitTimerRef.current = setTimeout(() => {\n        // Only auto-submit if we're not already loading\n        if (!isLoading) {\n          console.log(\"Auto-submitting interviewer question to GPT\");\n          handleSubmit(transcript);\n        }\n      }, 1000); // 1 second delay before submitting interviewer question\n    }\n  }, [autoSubmit, isLoading, handleSubmit]);\n\n  // Auto-scroll response area when content changes\n  const scrollToBottom = () => {\n    if (responseAreaRef.current) {\n      responseAreaRef.current.scrollTop = responseAreaRef.current.scrollHeight;\n    }\n  };\n\n  // Call scrollToBottom whenever response changes\n  useEffect(() => {\n    scrollToBottom();\n  }, [response]);\n\n  return (\n    <div className=\"diarized-interview-page\">\n      <div className=\"page-header\">\n        <h1>Enhanced Interview with Speaker Diarization</h1>\n        <p className=\"description\">\n          This page separates different speakers in the conversation. Only interviewer questions (from tab audio)\n          will be sent to the AI for responses.\n        </p>\n      </div>\n\n      <div className=\"interview-container\">\n        <div className=\"transcription-panel\">\n          <div className=\"panel-header\">\n            <h2>Live Transcription</h2>\n            <div className=\"auto-submit-toggle\">\n              <label>\n                <input\n                  type=\"checkbox\"\n                  checked={autoSubmit}\n                  onChange={() => setAutoSubmit(!autoSubmit)}\n                />\n                Auto-submit interviewer questions\n              </label>\n            </div>\n          </div>\n\n          <DiarizedTranscription onTranscriptChange={handleTranscriptChange} />\n\n          <div className=\"manual-submit\">\n            <button\n              className=\"submit-button\"\n              onClick={() => handleSubmit()}\n              disabled={isLoading || !currentTranscript.trim()}\n            >\n              {isLoading ? 'Processing...' : 'Submit Current Question to AI'}\n            </button>\n          </div>\n        </div>\n\n        <div className=\"response-panel\">\n          <div className=\"panel-header\">\n            <h2>AI Response to Interviewer Questions</h2>\n            <p className=\"panel-description\">\n              The AI will help you prepare answers to the interviewer's questions\n            </p>\n          </div>\n\n          <div\n            ref={responseAreaRef}\n            className=\"response-content\"\n          >\n            {response ? (\n              <div className=\"response-message\">\n                <div className=\"message-text\">{response}</div>\n              </div>\n            ) : (\n              <div className=\"empty-state\">AI responses will appear here</div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default DiarizedInterviewPage;\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,MAAM,EAAEC,WAAW,EAAEC,SAAS,QAAQ,OAAO;AAChE,OAAOC,qBAAqB,MAAM,qCAAqC;AACvE,OAAO,6BAA6B;AACpC,OAAOC,GAAG,MAAM,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,SAASC,qBAAqBA,CAAA,EAAG;EAAAC,EAAA;EAC/B;EACA,MAAM,CAACC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAACY,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACc,QAAQ,EAAEC,WAAW,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACgB,SAAS,EAAEC,YAAY,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACkB,UAAU,EAAEC,aAAa,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAMoB,kBAAkB,GAAGnB,MAAM,CAAC,IAAI,CAAC;;EAEvC;EACA,MAAMoB,eAAe,GAAGpB,MAAM,CAAC,IAAI,CAAC;;EAEpC;EACA,MAAMqB,YAAY,GAAGpB,WAAW,CAAC,MAAOqB,YAAY,IAAK;IACvD,MAAMC,QAAQ,GAAGD,YAAY,IAAIX,iBAAiB,CAACa,IAAI,CAAC,CAAC;IAEzD,IAAI,CAACD,QAAQ,EAAE;MACbE,OAAO,CAACC,IAAI,CAAC,uBAAuB,CAAC;MACrC;IACF;IAEA,MAAMC,MAAM,GAAGvB,GAAG,CAACwB,cAAc;IACjC,IAAI,CAACD,MAAM,EAAE;MACXE,KAAK,CAAC,iHAAiH,CAAC;MACxH;IACF;IAEAb,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACFS,OAAO,CAACK,GAAG,CAAC,qBAAqB,EAAEP,QAAQ,CAAC;MAC5C,MAAMV,QAAQ,GAAG,MAAMkB,KAAK,CAAC,4CAA4C,EAAE;QACzEC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAE,UAAUN,MAAM;QACnC,CAAC;QACDO,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBC,KAAK,EAAE,kBAAkB;UACzBC,QAAQ,EAAE,CACR;YACEC,IAAI,EAAE,QAAQ;YACdC,OAAO,EAAE;UACX,CAAC,EACD;YAAED,IAAI,EAAE,MAAM;YAAEC,OAAO,EAAEjB;UAAS,CAAC,CACpC;UACDkB,MAAM,EAAE;QACV,CAAC;MACH,CAAC,CAAC;MAEF,IAAI,CAAC5B,QAAQ,CAAC6B,EAAE,IAAI,CAAC7B,QAAQ,CAACqB,IAAI,EAAE,MAAM,IAAIS,KAAK,CAAC,4BAA4B,CAAC;MAEjF,MAAMC,MAAM,GAAG/B,QAAQ,CAACqB,IAAI,CAACW,SAAS,CAAC,CAAC;MACxC,MAAMC,OAAO,GAAG,IAAIC,WAAW,CAAC,OAAO,CAAC;MAExC,IAAIC,MAAM,GAAG,EAAE;MACflC,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC;;MAEjB,OAAO,IAAI,EAAE;QACX,MAAM;UAAEmC,KAAK;UAAEC;QAAK,CAAC,GAAG,MAAMN,MAAM,CAACO,IAAI,CAAC,CAAC;QAC3C,IAAID,IAAI,EAAE;QAEV,MAAME,KAAK,GAAGN,OAAO,CAACO,MAAM,CAACJ,KAAK,EAAE;UAAER,MAAM,EAAE;QAAK,CAAC,CAAC;QACrD,MAAMa,KAAK,GAAGF,KAAK,CAACG,KAAK,CAAC,IAAI,CAAC,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACjC,IAAI,CAAC,CAAC,CAACkC,UAAU,CAAC,OAAO,CAAC,CAAC;QAE/E,KAAK,MAAMD,IAAI,IAAIH,KAAK,EAAE;UACxB,MAAMK,IAAI,GAAGF,IAAI,CAACG,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;UACxC,IAAID,IAAI,KAAK,QAAQ,EAAE;UAEvB,IAAI;YAAA,IAAAE,aAAA,EAAAC,cAAA,EAAAC,oBAAA;YACF,MAAMC,IAAI,GAAG7B,IAAI,CAAC8B,KAAK,CAACN,IAAI,CAAC;YAC7B,MAAMnB,OAAO,IAAAqB,aAAA,GAAGG,IAAI,CAACE,OAAO,cAAAL,aAAA,wBAAAC,cAAA,GAAZD,aAAA,CAAe,CAAC,CAAC,cAAAC,cAAA,wBAAAC,oBAAA,GAAjBD,cAAA,CAAmBK,KAAK,cAAAJ,oBAAA,uBAAxBA,oBAAA,CAA0BvB,OAAO;YACjD,IAAIA,OAAO,EAAE;cACXQ,MAAM,IAAIR,OAAO;cACjB1B,WAAW,CAACkC,MAAM,CAAC;YACrB;UACF,CAAC,CAAC,OAAOoB,CAAC,EAAE;YACV3C,OAAO,CAAC4C,KAAK,CAAC,qBAAqB,EAAED,CAAC,CAAC;UACzC;QACF;MACF;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd5C,OAAO,CAAC4C,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MACxCvD,WAAW,CAAC,kBAAkB,GAAGuD,KAAK,CAACC,OAAO,CAAC;IACjD,CAAC,SAAS;MACRtD,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,CAACL,iBAAiB,CAAC,CAAC;;EAEvB;EACA;EACA,MAAM4D,sBAAsB,GAAGtE,WAAW,CAAC,CAACuE,UAAU,EAAEC,QAAQ,KAAK;IACnE;IACA,IAAI,CAACD,UAAU,IAAI,CAACA,UAAU,CAAChD,IAAI,CAAC,CAAC,EAAE;IAEvCC,OAAO,CAACK,GAAG,CAAC,gCAAgC,EAAE0C,UAAU,CAAC;IACzD5D,oBAAoB,CAAC4D,UAAU,CAAC;;IAEhC;IACA,IAAIC,QAAQ,IAAIA,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;MACnChE,qBAAqB,CAACiE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAGF,QAAQ,CAAC,CAAC;IACvD;;IAEA;IACA,IAAIxD,UAAU,EAAE;MACd,IAAIE,kBAAkB,CAACyD,OAAO,EAAE;QAC9BC,YAAY,CAAC1D,kBAAkB,CAACyD,OAAO,CAAC;MAC1C;MAEAzD,kBAAkB,CAACyD,OAAO,GAAGE,UAAU,CAAC,MAAM;QAC5C;QACA,IAAI,CAAC/D,SAAS,EAAE;UACdU,OAAO,CAACK,GAAG,CAAC,6CAA6C,CAAC;UAC1DT,YAAY,CAACmD,UAAU,CAAC;QAC1B;MACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;IACZ;EACF,CAAC,EAAE,CAACvD,UAAU,EAAEF,SAAS,EAAEM,YAAY,CAAC,CAAC;;EAEzC;EACA,MAAM0D,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI3D,eAAe,CAACwD,OAAO,EAAE;MAC3BxD,eAAe,CAACwD,OAAO,CAACI,SAAS,GAAG5D,eAAe,CAACwD,OAAO,CAACK,YAAY;IAC1E;EACF,CAAC;;EAED;EACA/E,SAAS,CAAC,MAAM;IACd6E,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAAClE,QAAQ,CAAC,CAAC;EAEd,oBACEP,OAAA;IAAK4E,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBACtC7E,OAAA;MAAK4E,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1B7E,OAAA;QAAA6E,QAAA,EAAI;MAA2C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpDjF,OAAA;QAAG4E,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAG3B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAENjF,OAAA;MAAK4E,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClC7E,OAAA;QAAK4E,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAClC7E,OAAA;UAAK4E,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B7E,OAAA;YAAA6E,QAAA,EAAI;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3BjF,OAAA;YAAK4E,SAAS,EAAC,oBAAoB;YAAAC,QAAA,eACjC7E,OAAA;cAAA6E,QAAA,gBACE7E,OAAA;gBACEkF,IAAI,EAAC,UAAU;gBACfC,OAAO,EAAExE,UAAW;gBACpByE,QAAQ,EAAEA,CAAA,KAAMxE,aAAa,CAAC,CAACD,UAAU;cAAE;gBAAAmE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC,qCAEJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENjF,OAAA,CAACH,qBAAqB;UAACwF,kBAAkB,EAAEpB;QAAuB;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAErEjF,OAAA;UAAK4E,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5B7E,OAAA;YACE4E,SAAS,EAAC,eAAe;YACzBU,OAAO,EAAEA,CAAA,KAAMvE,YAAY,CAAC,CAAE;YAC9BwE,QAAQ,EAAE9E,SAAS,IAAI,CAACJ,iBAAiB,CAACa,IAAI,CAAC,CAAE;YAAA2D,QAAA,EAEhDpE,SAAS,GAAG,eAAe,GAAG;UAA+B;YAAAqE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENjF,OAAA;QAAK4E,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B7E,OAAA;UAAK4E,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B7E,OAAA;YAAA6E,QAAA,EAAI;UAAoC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7CjF,OAAA;YAAG4E,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAC;UAEjC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENjF,OAAA;UACEwF,GAAG,EAAE1E,eAAgB;UACrB8D,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAE3BtE,QAAQ,gBACPP,OAAA;YAAK4E,SAAS,EAAC,kBAAkB;YAAAC,QAAA,eAC/B7E,OAAA;cAAK4E,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAEtE;YAAQ;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,gBAENjF,OAAA;YAAK4E,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAA6B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAChE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC/E,EAAA,CApMQD,qBAAqB;AAAAwF,EAAA,GAArBxF,qBAAqB;AAsM9B,eAAeA,qBAAqB;AAAC,IAAAwF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}