{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\interview-ai-app\\\\interviewAI\\\\client\\\\src\\\\components\\\\DiarizedTranscription.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect, useRef } from 'react';\nimport './DiarizedTranscription.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction DiarizedTranscription({\n  onTranscriptChange\n}) {\n  _s();\n  // State for transcription\n  const [transcript, setTranscript] = useState('');\n  const [diarizedSegments, setDiarizedSegments] = useState([]);\n  const [isListening, setIsListening] = useState(false);\n  const [isCapturingTabAudio, setIsCapturingTabAudio] = useState(false);\n  // Default speaker is based on audio source - tab audio is speaker1 (interviewer), mic is speaker2 (candidate)\n  const [currentSpeaker, setCurrentSpeaker] = useState('speaker2');\n\n  // Refs\n  const recognitionRef = useRef(null);\n  const finalTranscriptRef = useRef('');\n  const audioContextRef = useRef(null);\n  const analyserRef = useRef(null);\n  const mediaStreamRef = useRef(null);\n  const speakerChangeThresholdRef = useRef(0.3); // Threshold for detecting speaker changes\n\n  // Initialize speech recognition\n  useEffect(() => {\n    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n    if (!SpeechRecognition) {\n      console.error(\"Speech Recognition API not supported in this browser\");\n      return;\n    }\n\n    // Clean up function\n    return () => {\n      if (recognitionRef.current) {\n        try {\n          recognitionRef.current.stop();\n        } catch (error) {\n          console.error(\"Error stopping recognition:\", error);\n        }\n      }\n      if (audioContextRef.current) {\n        try {\n          audioContextRef.current.close();\n        } catch (error) {\n          console.error(\"Error closing audio context:\", error);\n        }\n      }\n      if (mediaStreamRef.current) {\n        mediaStreamRef.current.getTracks().forEach(track => track.stop());\n      }\n    };\n  }, []);\n\n  // Start listening with microphone\n  const startListening = async () => {\n    try {\n      if (isListening) return;\n      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n      recognitionRef.current = new SpeechRecognition();\n      recognitionRef.current.continuous = true;\n      recognitionRef.current.interimResults = true;\n      recognitionRef.current.lang = 'en-US';\n\n      // Reset transcript\n      finalTranscriptRef.current = '';\n      setTranscript('');\n      setDiarizedSegments([]);\n\n      // Set up audio context for voice analysis\n      if (!audioContextRef.current) {\n        audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();\n      }\n\n      // Get microphone stream\n      const stream = await navigator.mediaDevices.getUserMedia({\n        audio: true\n      });\n      mediaStreamRef.current = stream;\n\n      // Set up audio analyzer\n      const source = audioContextRef.current.createMediaStreamSource(stream);\n      analyserRef.current = audioContextRef.current.createAnalyser();\n      analyserRef.current.fftSize = 2048;\n      source.connect(analyserRef.current);\n\n      // Set speaker to candidate (speaker2) for microphone input\n      setCurrentSpeaker('speaker2');\n\n      // Set up recognition event handlers\n      setupRecognitionHandlers();\n\n      // Start recognition\n      recognitionRef.current.start();\n      setIsListening(true);\n      setIsCapturingTabAudio(false);\n      console.log(\"Microphone listening started - You are identified as the Candidate\");\n    } catch (error) {\n      console.error(\"Error starting microphone listening:\", error);\n      alert(\"Failed to start microphone: \" + error.message);\n    }\n  };\n\n  // Start capturing tab audio\n  const startTabAudioCapture = async () => {\n    try {\n      if (isListening) return;\n\n      // Request screen sharing with audio\n      const stream = await navigator.mediaDevices.getDisplayMedia({\n        video: true,\n        audio: true\n      });\n\n      // Check if audio is included\n      const audioTracks = stream.getAudioTracks();\n      if (audioTracks.length === 0) {\n        alert(\"No audio track found. Please make sure to select 'Share audio' when sharing the tab.\");\n        stream.getTracks().forEach(track => track.stop());\n        return;\n      }\n      mediaStreamRef.current = stream;\n\n      // Set up audio context for analysis\n      if (!audioContextRef.current) {\n        audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();\n      }\n\n      // Set up audio analyzer\n      const source = audioContextRef.current.createMediaStreamSource(stream);\n      analyserRef.current = audioContextRef.current.createAnalyser();\n      analyserRef.current.fftSize = 2048;\n      source.connect(analyserRef.current);\n\n      // Set speaker to interviewer (speaker1) for tab audio\n      setCurrentSpeaker('speaker1');\n\n      // Initialize speech recognition\n      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n      recognitionRef.current = new SpeechRecognition();\n      recognitionRef.current.continuous = true;\n      recognitionRef.current.interimResults = true;\n      recognitionRef.current.lang = 'en-US';\n\n      // Reset transcript\n      finalTranscriptRef.current = '';\n      setTranscript('');\n      setDiarizedSegments([]);\n\n      // Set up recognition event handlers\n      setupRecognitionHandlers();\n\n      // Start recognition\n      recognitionRef.current.start();\n      setIsListening(true);\n      setIsCapturingTabAudio(true);\n\n      // Handle stream ending\n      stream.getVideoTracks()[0].onended = () => {\n        stopTranscription();\n      };\n      console.log(\"Tab audio capture started - Tab audio is identified as the Interviewer\");\n    } catch (error) {\n      console.error(\"Error capturing tab audio:\", error);\n      alert(\"Failed to capture tab audio: \" + error.message);\n    }\n  };\n\n  // Stop transcription\n  const stopTranscription = () => {\n    if (recognitionRef.current) {\n      try {\n        recognitionRef.current.stop();\n      } catch (error) {\n        console.error(\"Error stopping recognition:\", error);\n      }\n    }\n    if (mediaStreamRef.current) {\n      mediaStreamRef.current.getTracks().forEach(track => track.stop());\n      mediaStreamRef.current = null;\n    }\n    setIsListening(false);\n    setIsCapturingTabAudio(false);\n    console.log(\"Transcription stopped\");\n  };\n\n  // Set up recognition event handlers\n  const setupRecognitionHandlers = () => {\n    if (!recognitionRef.current) return;\n    recognitionRef.current.onresult = event => {\n      let interimTranscript = '';\n      for (let i = event.resultIndex; i < event.results.length; i++) {\n        const result = event.results[i];\n        const text = result[0].transcript;\n        if (result.isFinal) {\n          // Add to final transcript with current speaker\n          finalTranscriptRef.current += text + ' ';\n\n          // Add as a new segment with the current speaker\n          setDiarizedSegments(prev => [...prev, {\n            text,\n            speaker: currentSpeaker,\n            timestamp: new Date(),\n            isFinal: true\n          }]);\n        } else {\n          interimTranscript += text;\n        }\n      }\n      const fullTranscript = finalTranscriptRef.current + interimTranscript;\n      setTranscript(fullTranscript);\n\n      // Notify parent component\n      if (onTranscriptChange) {\n        onTranscriptChange(fullTranscript, diarizedSegments);\n      }\n    };\n    recognitionRef.current.onerror = event => {\n      console.error(\"Recognition error:\", event.error);\n      if (event.error !== 'no-speech') {\n        stopTranscription();\n      }\n    };\n    recognitionRef.current.onend = () => {\n      console.log(\"Recognition ended\");\n      // Restart if we're still supposed to be listening\n      if (isListening) {\n        try {\n          recognitionRef.current.start();\n        } catch (error) {\n          console.error(\"Failed to restart recognition:\", error);\n          setIsListening(false);\n        }\n      }\n    };\n  };\n\n  // Monitor audio characteristics to detect speaker changes\n  const monitorAudioCharacteristics = () => {\n    if (!analyserRef.current) return;\n    const bufferLength = analyserRef.current.frequencyBinCount;\n    const dataArray = new Uint8Array(bufferLength);\n    let previousAudioProfile = null;\n\n    // For tab audio capture, we don't want to switch speakers automatically\n    // since we know the source is fixed (tab audio = interviewer, mic = candidate)\n    const analyze = () => {\n      if (!analyserRef.current || !isListening) return;\n      analyserRef.current.getByteFrequencyData(dataArray);\n\n      // Create a simple audio profile from frequency data\n      const audioProfile = calculateAudioProfile(dataArray);\n\n      // We're not switching speakers automatically anymore\n      // Instead, we maintain the speaker identity based on the audio source\n\n      previousAudioProfile = audioProfile;\n      requestAnimationFrame(analyze);\n    };\n    analyze();\n  };\n\n  // Calculate a simple audio profile from frequency data\n  const calculateAudioProfile = frequencyData => {\n    // Divide frequency spectrum into bands and calculate average energy\n    const bands = 4;\n    const bandSize = Math.floor(frequencyData.length / bands);\n    const profile = [];\n    for (let i = 0; i < bands; i++) {\n      let sum = 0;\n      for (let j = 0; j < bandSize; j++) {\n        sum += frequencyData[i * bandSize + j];\n      }\n      profile.push(sum / bandSize);\n    }\n    return profile;\n  };\n\n  // Calculate difference between audio profiles\n  const calculateProfileDifference = (profile1, profile2) => {\n    let totalDiff = 0;\n    for (let i = 0; i < profile1.length; i++) {\n      totalDiff += Math.abs(profile1[i] - profile2[i]);\n    }\n    return totalDiff / profile1.length;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"diarized-transcription\",\n    children: [!window.SpeechRecognition && !window.webkitSpeechRecognition && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"browser-warning\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Warning:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 13\n        }, this), \" Your browser doesn't support speech recognition. Please use Chrome, Edge, or another Chromium-based browser for this feature to work.\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 307,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"transcript-display\",\n      children: diarizedSegments.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"diarized-segments\",\n        children: [diarizedSegments.map((segment, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `segment ${segment.speaker}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"speaker-label\",\n            children: segment.speaker === 'speaker1' ? 'Interviewer' : 'Candidate'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"segment-text\",\n            children: segment.text\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 17\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 15\n        }, this)), transcript && finalTranscriptRef.current !== transcript && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `segment ${currentSpeaker} interim`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"speaker-label\",\n            children: currentSpeaker === 'speaker1' ? 'Interviewer' : 'Candidate'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"segment-text\",\n            children: transcript.substring(finalTranscriptRef.current.length)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 317,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"placeholder\",\n        children: \"Waiting for speech...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 337,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 315,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"controls\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: `control-button ${isListening && !isCapturingTabAudio ? 'listening' : ''}`,\n        onClick: isListening ? stopTranscription : startListening,\n        disabled: !window.SpeechRecognition && !window.webkitSpeechRecognition,\n        title: \"Capture microphone audio\",\n        children: [isListening && !isCapturingTabAudio ? 'Stop' : 'Start', \" Mic Listening\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 342,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: `control-button tab-audio ${isCapturingTabAudio ? 'capturing' : ''}`,\n        onClick: isCapturingTabAudio ? stopTranscription : startTabAudioCapture,\n        disabled: !window.SpeechRecognition && !window.webkitSpeechRecognition,\n        title: \"Capture audio from another tab (e.g., YouTube)\",\n        children: [isCapturingTabAudio ? 'Stop' : 'Start', \" Tab Audio Capture\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 351,\n        columnNumber: 9\n      }, this), isListening && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"status\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"listening-indicator\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 13\n        }, this), isCapturingTabAudio ? 'Capturing Tab Audio...' : 'Listening to Microphone...']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 341,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 305,\n    columnNumber: 5\n  }, this);\n}\n_s(DiarizedTranscription, \"QrgF82JQ/+rUa1CO4jqFkUYbRuw=\");\n_c = DiarizedTranscription;\nexport default DiarizedTranscription;\nvar _c;\n$RefreshReg$(_c, \"DiarizedTranscription\");", "map": {"version": 3, "names": ["useState", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "DiarizedTranscription", "onTranscriptChange", "_s", "transcript", "setTranscript", "diarizedSegments", "setDiarizedSegments", "isListening", "setIsListening", "isCapturingTabAudio", "setIsCapturingTabAudio", "currentSpeaker", "setCurrentSpeaker", "recognitionRef", "finalTranscriptRef", "audioContextRef", "analyserRef", "mediaStreamRef", "speaker<PERSON><PERSON>eThresholdRef", "SpeechRecognition", "window", "webkitSpeechRecognition", "console", "error", "current", "stop", "close", "getTracks", "for<PERSON>ach", "track", "startListening", "continuous", "interimResults", "lang", "AudioContext", "webkitAudioContext", "stream", "navigator", "mediaDevices", "getUserMedia", "audio", "source", "createMediaStreamSource", "create<PERSON><PERSON>yser", "fftSize", "connect", "setupRecognitionHandlers", "start", "log", "alert", "message", "startTabAudioCapture", "getDisplayMedia", "video", "audioTracks", "getAudioTracks", "length", "getVideoTracks", "onended", "stopTranscription", "on<PERSON>ult", "event", "interimTranscript", "i", "resultIndex", "results", "result", "text", "isFinal", "prev", "speaker", "timestamp", "Date", "fullTranscript", "onerror", "onend", "monitorAudioCharacteristics", "bufferLength", "frequencyBinCount", "dataArray", "Uint8Array", "previousAudioProfile", "analyze", "getByteFrequencyData", "audioProfile", "calculateAudioProfile", "requestAnimationFrame", "frequencyData", "bands", "bandSize", "Math", "floor", "profile", "sum", "j", "push", "calculateProfileDifference", "profile1", "profile2", "totalDiff", "abs", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "segment", "index", "substring", "onClick", "disabled", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/interview-ai-app/interviewAI/client/src/components/DiarizedTranscription.jsx"], "sourcesContent": ["import { useState, useEffect, useRef } from 'react';\nimport './DiarizedTranscription.css';\n\nfunction DiarizedTranscription({ onTranscriptChange }) {\n  // State for transcription\n  const [transcript, setTranscript] = useState('');\n  const [diarizedSegments, setDiarizedSegments] = useState([]);\n  const [isListening, setIsListening] = useState(false);\n  const [isCapturingTabAudio, setIsCapturingTabAudio] = useState(false);\n  // Default speaker is based on audio source - tab audio is speaker1 (interviewer), mic is speaker2 (candidate)\n  const [currentSpeaker, setCurrentSpeaker] = useState('speaker2');\n\n  // Refs\n  const recognitionRef = useRef(null);\n  const finalTranscriptRef = useRef('');\n  const audioContextRef = useRef(null);\n  const analyserRef = useRef(null);\n  const mediaStreamRef = useRef(null);\n  const speakerChangeThresholdRef = useRef(0.3); // Threshold for detecting speaker changes\n\n  // Initialize speech recognition\n  useEffect(() => {\n    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n    if (!SpeechRecognition) {\n      console.error(\"Speech Recognition API not supported in this browser\");\n      return;\n    }\n\n    // Clean up function\n    return () => {\n      if (recognitionRef.current) {\n        try {\n          recognitionRef.current.stop();\n        } catch (error) {\n          console.error(\"Error stopping recognition:\", error);\n        }\n      }\n\n      if (audioContextRef.current) {\n        try {\n          audioContextRef.current.close();\n        } catch (error) {\n          console.error(\"Error closing audio context:\", error);\n        }\n      }\n\n      if (mediaStreamRef.current) {\n        mediaStreamRef.current.getTracks().forEach(track => track.stop());\n      }\n    };\n  }, []);\n\n  // Start listening with microphone\n  const startListening = async () => {\n    try {\n      if (isListening) return;\n\n      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n      recognitionRef.current = new SpeechRecognition();\n      recognitionRef.current.continuous = true;\n      recognitionRef.current.interimResults = true;\n      recognitionRef.current.lang = 'en-US';\n\n      // Reset transcript\n      finalTranscriptRef.current = '';\n      setTranscript('');\n      setDiarizedSegments([]);\n\n      // Set up audio context for voice analysis\n      if (!audioContextRef.current) {\n        audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();\n      }\n\n      // Get microphone stream\n      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });\n      mediaStreamRef.current = stream;\n\n      // Set up audio analyzer\n      const source = audioContextRef.current.createMediaStreamSource(stream);\n      analyserRef.current = audioContextRef.current.createAnalyser();\n      analyserRef.current.fftSize = 2048;\n      source.connect(analyserRef.current);\n\n      // Set speaker to candidate (speaker2) for microphone input\n      setCurrentSpeaker('speaker2');\n\n      // Set up recognition event handlers\n      setupRecognitionHandlers();\n\n      // Start recognition\n      recognitionRef.current.start();\n      setIsListening(true);\n      setIsCapturingTabAudio(false);\n\n      console.log(\"Microphone listening started - You are identified as the Candidate\");\n    } catch (error) {\n      console.error(\"Error starting microphone listening:\", error);\n      alert(\"Failed to start microphone: \" + error.message);\n    }\n  };\n\n  // Start capturing tab audio\n  const startTabAudioCapture = async () => {\n    try {\n      if (isListening) return;\n\n      // Request screen sharing with audio\n      const stream = await navigator.mediaDevices.getDisplayMedia({\n        video: true,\n        audio: true\n      });\n\n      // Check if audio is included\n      const audioTracks = stream.getAudioTracks();\n      if (audioTracks.length === 0) {\n        alert(\"No audio track found. Please make sure to select 'Share audio' when sharing the tab.\");\n        stream.getTracks().forEach(track => track.stop());\n        return;\n      }\n\n      mediaStreamRef.current = stream;\n\n      // Set up audio context for analysis\n      if (!audioContextRef.current) {\n        audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();\n      }\n\n      // Set up audio analyzer\n      const source = audioContextRef.current.createMediaStreamSource(stream);\n      analyserRef.current = audioContextRef.current.createAnalyser();\n      analyserRef.current.fftSize = 2048;\n      source.connect(analyserRef.current);\n\n      // Set speaker to interviewer (speaker1) for tab audio\n      setCurrentSpeaker('speaker1');\n\n      // Initialize speech recognition\n      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n      recognitionRef.current = new SpeechRecognition();\n      recognitionRef.current.continuous = true;\n      recognitionRef.current.interimResults = true;\n      recognitionRef.current.lang = 'en-US';\n\n      // Reset transcript\n      finalTranscriptRef.current = '';\n      setTranscript('');\n      setDiarizedSegments([]);\n\n      // Set up recognition event handlers\n      setupRecognitionHandlers();\n\n      // Start recognition\n      recognitionRef.current.start();\n      setIsListening(true);\n      setIsCapturingTabAudio(true);\n\n      // Handle stream ending\n      stream.getVideoTracks()[0].onended = () => {\n        stopTranscription();\n      };\n\n      console.log(\"Tab audio capture started - Tab audio is identified as the Interviewer\");\n    } catch (error) {\n      console.error(\"Error capturing tab audio:\", error);\n      alert(\"Failed to capture tab audio: \" + error.message);\n    }\n  };\n\n  // Stop transcription\n  const stopTranscription = () => {\n    if (recognitionRef.current) {\n      try {\n        recognitionRef.current.stop();\n      } catch (error) {\n        console.error(\"Error stopping recognition:\", error);\n      }\n    }\n\n    if (mediaStreamRef.current) {\n      mediaStreamRef.current.getTracks().forEach(track => track.stop());\n      mediaStreamRef.current = null;\n    }\n\n    setIsListening(false);\n    setIsCapturingTabAudio(false);\n    console.log(\"Transcription stopped\");\n  };\n\n  // Set up recognition event handlers\n  const setupRecognitionHandlers = () => {\n    if (!recognitionRef.current) return;\n\n    recognitionRef.current.onresult = (event) => {\n      let interimTranscript = '';\n\n      for (let i = event.resultIndex; i < event.results.length; i++) {\n        const result = event.results[i];\n        const text = result[0].transcript;\n\n        if (result.isFinal) {\n          // Add to final transcript with current speaker\n          finalTranscriptRef.current += text + ' ';\n\n          // Add as a new segment with the current speaker\n          setDiarizedSegments(prev => [\n            ...prev,\n            {\n              text,\n              speaker: currentSpeaker,\n              timestamp: new Date(),\n              isFinal: true\n            }\n          ]);\n        } else {\n          interimTranscript += text;\n        }\n      }\n\n      const fullTranscript = finalTranscriptRef.current + interimTranscript;\n      setTranscript(fullTranscript);\n\n      // Notify parent component\n      if (onTranscriptChange) {\n        onTranscriptChange(fullTranscript, diarizedSegments);\n      }\n    };\n\n    recognitionRef.current.onerror = (event) => {\n      console.error(\"Recognition error:\", event.error);\n      if (event.error !== 'no-speech') {\n        stopTranscription();\n      }\n    };\n\n    recognitionRef.current.onend = () => {\n      console.log(\"Recognition ended\");\n      // Restart if we're still supposed to be listening\n      if (isListening) {\n        try {\n          recognitionRef.current.start();\n        } catch (error) {\n          console.error(\"Failed to restart recognition:\", error);\n          setIsListening(false);\n        }\n      }\n    };\n  };\n\n  // Monitor audio characteristics to detect speaker changes\n  const monitorAudioCharacteristics = () => {\n    if (!analyserRef.current) return;\n\n    const bufferLength = analyserRef.current.frequencyBinCount;\n    const dataArray = new Uint8Array(bufferLength);\n    let previousAudioProfile = null;\n\n    // For tab audio capture, we don't want to switch speakers automatically\n    // since we know the source is fixed (tab audio = interviewer, mic = candidate)\n    const analyze = () => {\n      if (!analyserRef.current || !isListening) return;\n\n      analyserRef.current.getByteFrequencyData(dataArray);\n\n      // Create a simple audio profile from frequency data\n      const audioProfile = calculateAudioProfile(dataArray);\n\n      // We're not switching speakers automatically anymore\n      // Instead, we maintain the speaker identity based on the audio source\n\n      previousAudioProfile = audioProfile;\n      requestAnimationFrame(analyze);\n    };\n\n    analyze();\n  };\n\n  // Calculate a simple audio profile from frequency data\n  const calculateAudioProfile = (frequencyData) => {\n    // Divide frequency spectrum into bands and calculate average energy\n    const bands = 4;\n    const bandSize = Math.floor(frequencyData.length / bands);\n    const profile = [];\n\n    for (let i = 0; i < bands; i++) {\n      let sum = 0;\n      for (let j = 0; j < bandSize; j++) {\n        sum += frequencyData[i * bandSize + j];\n      }\n      profile.push(sum / bandSize);\n    }\n\n    return profile;\n  };\n\n  // Calculate difference between audio profiles\n  const calculateProfileDifference = (profile1, profile2) => {\n    let totalDiff = 0;\n    for (let i = 0; i < profile1.length; i++) {\n      totalDiff += Math.abs(profile1[i] - profile2[i]);\n    }\n    return totalDiff / profile1.length;\n  };\n\n  return (\n    <div className=\"diarized-transcription\">\n      {!window.SpeechRecognition && !window.webkitSpeechRecognition && (\n        <div className=\"browser-warning\">\n          <p>\n            <strong>Warning:</strong> Your browser doesn't support speech recognition.\n            Please use Chrome, Edge, or another Chromium-based browser for this feature to work.\n          </p>\n        </div>\n      )}\n\n      <div className=\"transcript-display\">\n        {diarizedSegments.length > 0 ? (\n          <div className=\"diarized-segments\">\n            {diarizedSegments.map((segment, index) => (\n              <div\n                key={index}\n                className={`segment ${segment.speaker}`}\n              >\n                <div className=\"speaker-label\">{segment.speaker === 'speaker1' ? 'Interviewer' : 'Candidate'}</div>\n                <div className=\"segment-text\">{segment.text}</div>\n              </div>\n            ))}\n\n            {/* Current interim transcript */}\n            {transcript && finalTranscriptRef.current !== transcript && (\n              <div className={`segment ${currentSpeaker} interim`}>\n                <div className=\"speaker-label\">{currentSpeaker === 'speaker1' ? 'Interviewer' : 'Candidate'}</div>\n                <div className=\"segment-text\">{transcript.substring(finalTranscriptRef.current.length)}</div>\n              </div>\n            )}\n          </div>\n        ) : (\n          <p className=\"placeholder\">Waiting for speech...</p>\n        )}\n      </div>\n\n      <div className=\"controls\">\n        <button\n          className={`control-button ${isListening && !isCapturingTabAudio ? 'listening' : ''}`}\n          onClick={isListening ? stopTranscription : startListening}\n          disabled={!window.SpeechRecognition && !window.webkitSpeechRecognition}\n          title=\"Capture microphone audio\"\n        >\n          {isListening && !isCapturingTabAudio ? 'Stop' : 'Start'} Mic Listening\n        </button>\n\n        <button\n          className={`control-button tab-audio ${isCapturingTabAudio ? 'capturing' : ''}`}\n          onClick={isCapturingTabAudio ? stopTranscription : startTabAudioCapture}\n          disabled={!window.SpeechRecognition && !window.webkitSpeechRecognition}\n          title=\"Capture audio from another tab (e.g., YouTube)\"\n        >\n          {isCapturingTabAudio ? 'Stop' : 'Start'} Tab Audio Capture\n        </button>\n\n        {isListening && (\n          <div className=\"status\">\n            <span className=\"listening-indicator\"></span>\n            {isCapturingTabAudio ? 'Capturing Tab Audio...' : 'Listening to Microphone...'}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n\nexport default DiarizedTranscription;\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACnD,OAAO,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,SAASC,qBAAqBA,CAAC;EAAEC;AAAmB,CAAC,EAAE;EAAAC,EAAA;EACrD;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACU,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACY,WAAW,EAAEC,cAAc,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACc,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EACrE;EACA,MAAM,CAACgB,cAAc,EAAEC,iBAAiB,CAAC,GAAGjB,QAAQ,CAAC,UAAU,CAAC;;EAEhE;EACA,MAAMkB,cAAc,GAAGhB,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMiB,kBAAkB,GAAGjB,MAAM,CAAC,EAAE,CAAC;EACrC,MAAMkB,eAAe,GAAGlB,MAAM,CAAC,IAAI,CAAC;EACpC,MAAMmB,WAAW,GAAGnB,MAAM,CAAC,IAAI,CAAC;EAChC,MAAMoB,cAAc,GAAGpB,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMqB,yBAAyB,GAAGrB,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;;EAE/C;EACAD,SAAS,CAAC,MAAM;IACd,MAAMuB,iBAAiB,GAAGC,MAAM,CAACD,iBAAiB,IAAIC,MAAM,CAACC,uBAAuB;IACpF,IAAI,CAACF,iBAAiB,EAAE;MACtBG,OAAO,CAACC,KAAK,CAAC,sDAAsD,CAAC;MACrE;IACF;;IAEA;IACA,OAAO,MAAM;MACX,IAAIV,cAAc,CAACW,OAAO,EAAE;QAC1B,IAAI;UACFX,cAAc,CAACW,OAAO,CAACC,IAAI,CAAC,CAAC;QAC/B,CAAC,CAAC,OAAOF,KAAK,EAAE;UACdD,OAAO,CAACC,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACrD;MACF;MAEA,IAAIR,eAAe,CAACS,OAAO,EAAE;QAC3B,IAAI;UACFT,eAAe,CAACS,OAAO,CAACE,KAAK,CAAC,CAAC;QACjC,CAAC,CAAC,OAAOH,KAAK,EAAE;UACdD,OAAO,CAACC,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACtD;MACF;MAEA,IAAIN,cAAc,CAACO,OAAO,EAAE;QAC1BP,cAAc,CAACO,OAAO,CAACG,SAAS,CAAC,CAAC,CAACC,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACJ,IAAI,CAAC,CAAC,CAAC;MACnE;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMK,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,IAAIvB,WAAW,EAAE;MAEjB,MAAMY,iBAAiB,GAAGC,MAAM,CAACD,iBAAiB,IAAIC,MAAM,CAACC,uBAAuB;MACpFR,cAAc,CAACW,OAAO,GAAG,IAAIL,iBAAiB,CAAC,CAAC;MAChDN,cAAc,CAACW,OAAO,CAACO,UAAU,GAAG,IAAI;MACxClB,cAAc,CAACW,OAAO,CAACQ,cAAc,GAAG,IAAI;MAC5CnB,cAAc,CAACW,OAAO,CAACS,IAAI,GAAG,OAAO;;MAErC;MACAnB,kBAAkB,CAACU,OAAO,GAAG,EAAE;MAC/BpB,aAAa,CAAC,EAAE,CAAC;MACjBE,mBAAmB,CAAC,EAAE,CAAC;;MAEvB;MACA,IAAI,CAACS,eAAe,CAACS,OAAO,EAAE;QAC5BT,eAAe,CAACS,OAAO,GAAG,KAAKJ,MAAM,CAACc,YAAY,IAAId,MAAM,CAACe,kBAAkB,EAAE,CAAC;MACpF;;MAEA;MACA,MAAMC,MAAM,GAAG,MAAMC,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;QAAEC,KAAK,EAAE;MAAK,CAAC,CAAC;MACzEvB,cAAc,CAACO,OAAO,GAAGY,MAAM;;MAE/B;MACA,MAAMK,MAAM,GAAG1B,eAAe,CAACS,OAAO,CAACkB,uBAAuB,CAACN,MAAM,CAAC;MACtEpB,WAAW,CAACQ,OAAO,GAAGT,eAAe,CAACS,OAAO,CAACmB,cAAc,CAAC,CAAC;MAC9D3B,WAAW,CAACQ,OAAO,CAACoB,OAAO,GAAG,IAAI;MAClCH,MAAM,CAACI,OAAO,CAAC7B,WAAW,CAACQ,OAAO,CAAC;;MAEnC;MACAZ,iBAAiB,CAAC,UAAU,CAAC;;MAE7B;MACAkC,wBAAwB,CAAC,CAAC;;MAE1B;MACAjC,cAAc,CAACW,OAAO,CAACuB,KAAK,CAAC,CAAC;MAC9BvC,cAAc,CAAC,IAAI,CAAC;MACpBE,sBAAsB,CAAC,KAAK,CAAC;MAE7BY,OAAO,CAAC0B,GAAG,CAAC,oEAAoE,CAAC;IACnF,CAAC,CAAC,OAAOzB,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D0B,KAAK,CAAC,8BAA8B,GAAG1B,KAAK,CAAC2B,OAAO,CAAC;IACvD;EACF,CAAC;;EAED;EACA,MAAMC,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACF,IAAI5C,WAAW,EAAE;;MAEjB;MACA,MAAM6B,MAAM,GAAG,MAAMC,SAAS,CAACC,YAAY,CAACc,eAAe,CAAC;QAC1DC,KAAK,EAAE,IAAI;QACXb,KAAK,EAAE;MACT,CAAC,CAAC;;MAEF;MACA,MAAMc,WAAW,GAAGlB,MAAM,CAACmB,cAAc,CAAC,CAAC;MAC3C,IAAID,WAAW,CAACE,MAAM,KAAK,CAAC,EAAE;QAC5BP,KAAK,CAAC,sFAAsF,CAAC;QAC7Fb,MAAM,CAACT,SAAS,CAAC,CAAC,CAACC,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACJ,IAAI,CAAC,CAAC,CAAC;QACjD;MACF;MAEAR,cAAc,CAACO,OAAO,GAAGY,MAAM;;MAE/B;MACA,IAAI,CAACrB,eAAe,CAACS,OAAO,EAAE;QAC5BT,eAAe,CAACS,OAAO,GAAG,KAAKJ,MAAM,CAACc,YAAY,IAAId,MAAM,CAACe,kBAAkB,EAAE,CAAC;MACpF;;MAEA;MACA,MAAMM,MAAM,GAAG1B,eAAe,CAACS,OAAO,CAACkB,uBAAuB,CAACN,MAAM,CAAC;MACtEpB,WAAW,CAACQ,OAAO,GAAGT,eAAe,CAACS,OAAO,CAACmB,cAAc,CAAC,CAAC;MAC9D3B,WAAW,CAACQ,OAAO,CAACoB,OAAO,GAAG,IAAI;MAClCH,MAAM,CAACI,OAAO,CAAC7B,WAAW,CAACQ,OAAO,CAAC;;MAEnC;MACAZ,iBAAiB,CAAC,UAAU,CAAC;;MAE7B;MACA,MAAMO,iBAAiB,GAAGC,MAAM,CAACD,iBAAiB,IAAIC,MAAM,CAACC,uBAAuB;MACpFR,cAAc,CAACW,OAAO,GAAG,IAAIL,iBAAiB,CAAC,CAAC;MAChDN,cAAc,CAACW,OAAO,CAACO,UAAU,GAAG,IAAI;MACxClB,cAAc,CAACW,OAAO,CAACQ,cAAc,GAAG,IAAI;MAC5CnB,cAAc,CAACW,OAAO,CAACS,IAAI,GAAG,OAAO;;MAErC;MACAnB,kBAAkB,CAACU,OAAO,GAAG,EAAE;MAC/BpB,aAAa,CAAC,EAAE,CAAC;MACjBE,mBAAmB,CAAC,EAAE,CAAC;;MAEvB;MACAwC,wBAAwB,CAAC,CAAC;;MAE1B;MACAjC,cAAc,CAACW,OAAO,CAACuB,KAAK,CAAC,CAAC;MAC9BvC,cAAc,CAAC,IAAI,CAAC;MACpBE,sBAAsB,CAAC,IAAI,CAAC;;MAE5B;MACA0B,MAAM,CAACqB,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,OAAO,GAAG,MAAM;QACzCC,iBAAiB,CAAC,CAAC;MACrB,CAAC;MAEDrC,OAAO,CAAC0B,GAAG,CAAC,wEAAwE,CAAC;IACvF,CAAC,CAAC,OAAOzB,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD0B,KAAK,CAAC,+BAA+B,GAAG1B,KAAK,CAAC2B,OAAO,CAAC;IACxD;EACF,CAAC;;EAED;EACA,MAAMS,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI9C,cAAc,CAACW,OAAO,EAAE;MAC1B,IAAI;QACFX,cAAc,CAACW,OAAO,CAACC,IAAI,CAAC,CAAC;MAC/B,CAAC,CAAC,OAAOF,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACrD;IACF;IAEA,IAAIN,cAAc,CAACO,OAAO,EAAE;MAC1BP,cAAc,CAACO,OAAO,CAACG,SAAS,CAAC,CAAC,CAACC,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACJ,IAAI,CAAC,CAAC,CAAC;MACjER,cAAc,CAACO,OAAO,GAAG,IAAI;IAC/B;IAEAhB,cAAc,CAAC,KAAK,CAAC;IACrBE,sBAAsB,CAAC,KAAK,CAAC;IAC7BY,OAAO,CAAC0B,GAAG,CAAC,uBAAuB,CAAC;EACtC,CAAC;;EAED;EACA,MAAMF,wBAAwB,GAAGA,CAAA,KAAM;IACrC,IAAI,CAACjC,cAAc,CAACW,OAAO,EAAE;IAE7BX,cAAc,CAACW,OAAO,CAACoC,QAAQ,GAAIC,KAAK,IAAK;MAC3C,IAAIC,iBAAiB,GAAG,EAAE;MAE1B,KAAK,IAAIC,CAAC,GAAGF,KAAK,CAACG,WAAW,EAAED,CAAC,GAAGF,KAAK,CAACI,OAAO,CAACT,MAAM,EAAEO,CAAC,EAAE,EAAE;QAC7D,MAAMG,MAAM,GAAGL,KAAK,CAACI,OAAO,CAACF,CAAC,CAAC;QAC/B,MAAMI,IAAI,GAAGD,MAAM,CAAC,CAAC,CAAC,CAAC/D,UAAU;QAEjC,IAAI+D,MAAM,CAACE,OAAO,EAAE;UAClB;UACAtD,kBAAkB,CAACU,OAAO,IAAI2C,IAAI,GAAG,GAAG;;UAExC;UACA7D,mBAAmB,CAAC+D,IAAI,IAAI,CAC1B,GAAGA,IAAI,EACP;YACEF,IAAI;YACJG,OAAO,EAAE3D,cAAc;YACvB4D,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;YACrBJ,OAAO,EAAE;UACX,CAAC,CACF,CAAC;QACJ,CAAC,MAAM;UACLN,iBAAiB,IAAIK,IAAI;QAC3B;MACF;MAEA,MAAMM,cAAc,GAAG3D,kBAAkB,CAACU,OAAO,GAAGsC,iBAAiB;MACrE1D,aAAa,CAACqE,cAAc,CAAC;;MAE7B;MACA,IAAIxE,kBAAkB,EAAE;QACtBA,kBAAkB,CAACwE,cAAc,EAAEpE,gBAAgB,CAAC;MACtD;IACF,CAAC;IAEDQ,cAAc,CAACW,OAAO,CAACkD,OAAO,GAAIb,KAAK,IAAK;MAC1CvC,OAAO,CAACC,KAAK,CAAC,oBAAoB,EAAEsC,KAAK,CAACtC,KAAK,CAAC;MAChD,IAAIsC,KAAK,CAACtC,KAAK,KAAK,WAAW,EAAE;QAC/BoC,iBAAiB,CAAC,CAAC;MACrB;IACF,CAAC;IAED9C,cAAc,CAACW,OAAO,CAACmD,KAAK,GAAG,MAAM;MACnCrD,OAAO,CAAC0B,GAAG,CAAC,mBAAmB,CAAC;MAChC;MACA,IAAIzC,WAAW,EAAE;QACf,IAAI;UACFM,cAAc,CAACW,OAAO,CAACuB,KAAK,CAAC,CAAC;QAChC,CAAC,CAAC,OAAOxB,KAAK,EAAE;UACdD,OAAO,CAACC,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;UACtDf,cAAc,CAAC,KAAK,CAAC;QACvB;MACF;IACF,CAAC;EACH,CAAC;;EAED;EACA,MAAMoE,2BAA2B,GAAGA,CAAA,KAAM;IACxC,IAAI,CAAC5D,WAAW,CAACQ,OAAO,EAAE;IAE1B,MAAMqD,YAAY,GAAG7D,WAAW,CAACQ,OAAO,CAACsD,iBAAiB;IAC1D,MAAMC,SAAS,GAAG,IAAIC,UAAU,CAACH,YAAY,CAAC;IAC9C,IAAII,oBAAoB,GAAG,IAAI;;IAE/B;IACA;IACA,MAAMC,OAAO,GAAGA,CAAA,KAAM;MACpB,IAAI,CAAClE,WAAW,CAACQ,OAAO,IAAI,CAACjB,WAAW,EAAE;MAE1CS,WAAW,CAACQ,OAAO,CAAC2D,oBAAoB,CAACJ,SAAS,CAAC;;MAEnD;MACA,MAAMK,YAAY,GAAGC,qBAAqB,CAACN,SAAS,CAAC;;MAErD;MACA;;MAEAE,oBAAoB,GAAGG,YAAY;MACnCE,qBAAqB,CAACJ,OAAO,CAAC;IAChC,CAAC;IAEDA,OAAO,CAAC,CAAC;EACX,CAAC;;EAED;EACA,MAAMG,qBAAqB,GAAIE,aAAa,IAAK;IAC/C;IACA,MAAMC,KAAK,GAAG,CAAC;IACf,MAAMC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACJ,aAAa,CAAC/B,MAAM,GAAGgC,KAAK,CAAC;IACzD,MAAMI,OAAO,GAAG,EAAE;IAElB,KAAK,IAAI7B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyB,KAAK,EAAEzB,CAAC,EAAE,EAAE;MAC9B,IAAI8B,GAAG,GAAG,CAAC;MACX,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,QAAQ,EAAEK,CAAC,EAAE,EAAE;QACjCD,GAAG,IAAIN,aAAa,CAACxB,CAAC,GAAG0B,QAAQ,GAAGK,CAAC,CAAC;MACxC;MACAF,OAAO,CAACG,IAAI,CAACF,GAAG,GAAGJ,QAAQ,CAAC;IAC9B;IAEA,OAAOG,OAAO;EAChB,CAAC;;EAED;EACA,MAAMI,0BAA0B,GAAGA,CAACC,QAAQ,EAAEC,QAAQ,KAAK;IACzD,IAAIC,SAAS,GAAG,CAAC;IACjB,KAAK,IAAIpC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkC,QAAQ,CAACzC,MAAM,EAAEO,CAAC,EAAE,EAAE;MACxCoC,SAAS,IAAIT,IAAI,CAACU,GAAG,CAACH,QAAQ,CAAClC,CAAC,CAAC,GAAGmC,QAAQ,CAACnC,CAAC,CAAC,CAAC;IAClD;IACA,OAAOoC,SAAS,GAAGF,QAAQ,CAACzC,MAAM;EACpC,CAAC;EAED,oBACEzD,OAAA;IAAKsG,SAAS,EAAC,wBAAwB;IAAAC,QAAA,GACpC,CAAClF,MAAM,CAACD,iBAAiB,IAAI,CAACC,MAAM,CAACC,uBAAuB,iBAC3DtB,OAAA;MAAKsG,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAC9BvG,OAAA;QAAAuG,QAAA,gBACEvG,OAAA;UAAAuG,QAAA,EAAQ;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,0IAE3B;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACN,eAED3G,OAAA;MAAKsG,SAAS,EAAC,oBAAoB;MAAAC,QAAA,EAChCjG,gBAAgB,CAACmD,MAAM,GAAG,CAAC,gBAC1BzD,OAAA;QAAKsG,SAAS,EAAC,mBAAmB;QAAAC,QAAA,GAC/BjG,gBAAgB,CAACsG,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBACnC9G,OAAA;UAEEsG,SAAS,EAAE,WAAWO,OAAO,CAACtC,OAAO,EAAG;UAAAgC,QAAA,gBAExCvG,OAAA;YAAKsG,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEM,OAAO,CAACtC,OAAO,KAAK,UAAU,GAAG,aAAa,GAAG;UAAW;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACnG3G,OAAA;YAAKsG,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAEM,OAAO,CAACzC;UAAI;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA,GAJ7CG,KAAK;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAKP,CACN,CAAC,EAGDvG,UAAU,IAAIW,kBAAkB,CAACU,OAAO,KAAKrB,UAAU,iBACtDJ,OAAA;UAAKsG,SAAS,EAAE,WAAW1F,cAAc,UAAW;UAAA2F,QAAA,gBAClDvG,OAAA;YAAKsG,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAE3F,cAAc,KAAK,UAAU,GAAG,aAAa,GAAG;UAAW;YAAA4F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClG3G,OAAA;YAAKsG,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAEnG,UAAU,CAAC2G,SAAS,CAAChG,kBAAkB,CAACU,OAAO,CAACgC,MAAM;UAAC;YAAA+C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1F,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,gBAEN3G,OAAA;QAAGsG,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IACpD;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEN3G,OAAA;MAAKsG,SAAS,EAAC,UAAU;MAAAC,QAAA,gBACvBvG,OAAA;QACEsG,SAAS,EAAE,kBAAkB9F,WAAW,IAAI,CAACE,mBAAmB,GAAG,WAAW,GAAG,EAAE,EAAG;QACtFsG,OAAO,EAAExG,WAAW,GAAGoD,iBAAiB,GAAG7B,cAAe;QAC1DkF,QAAQ,EAAE,CAAC5F,MAAM,CAACD,iBAAiB,IAAI,CAACC,MAAM,CAACC,uBAAwB;QACvE4F,KAAK,EAAC,0BAA0B;QAAAX,QAAA,GAE/B/F,WAAW,IAAI,CAACE,mBAAmB,GAAG,MAAM,GAAG,OAAO,EAAC,gBAC1D;MAAA;QAAA8F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAET3G,OAAA;QACEsG,SAAS,EAAE,4BAA4B5F,mBAAmB,GAAG,WAAW,GAAG,EAAE,EAAG;QAChFsG,OAAO,EAAEtG,mBAAmB,GAAGkD,iBAAiB,GAAGR,oBAAqB;QACxE6D,QAAQ,EAAE,CAAC5F,MAAM,CAACD,iBAAiB,IAAI,CAACC,MAAM,CAACC,uBAAwB;QACvE4F,KAAK,EAAC,gDAAgD;QAAAX,QAAA,GAErD7F,mBAAmB,GAAG,MAAM,GAAG,OAAO,EAAC,oBAC1C;MAAA;QAAA8F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EAERnG,WAAW,iBACVR,OAAA;QAAKsG,SAAS,EAAC,QAAQ;QAAAC,QAAA,gBACrBvG,OAAA;UAAMsG,SAAS,EAAC;QAAqB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EAC5CjG,mBAAmB,GAAG,wBAAwB,GAAG,4BAA4B;MAAA;QAAA8F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACxG,EAAA,CA7WQF,qBAAqB;AAAAkH,EAAA,GAArBlH,qBAAqB;AA+W9B,eAAeA,qBAAqB;AAAC,IAAAkH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}