{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\interview-ai-app\\\\interviewAI\\\\client\\\\src\\\\pages\\\\home\\\\index.jsx\",\n  _s = $RefreshSig$();\nimport React, { useRef, useState, useEffect } from 'react';\nimport { Link, useNavigate, useLocation } from 'react-router-dom';\nimport Header from '../../components/Header';\nimport Footer from '../../components/Footer';\nimport LogoutIcon from '@mui/icons-material/Logout';\nimport './home.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction HomeScreen() {\n  _s();\n  const pricingRef = useRef(null);\n  const [isLoggedIn, setIsLoggedIn] = useState(false);\n  // eslint-disable-next-line\n  const [userData, setUserData] = useState(null);\n  // eslint-disable-next-line\n  const navigate = useNavigate();\n  const location = useLocation();\n  const featuresRef = useRef(null);\n  const contactRef = useRef(null);\n  useEffect(() => {\n    // Check if user is logged in\n    const userDetails = localStorage.getItem(\"userDetails\");\n    if (userDetails) {\n      setIsLoggedIn(true);\n      try {\n        const parsedData = JSON.parse(userDetails);\n        setUserData(parsedData);\n      } catch (error) {\n        console.error(\"Error parsing user data:\", error);\n      }\n    }\n\n    // Scroll to section if coming from nav\n    if (location.state && location.state.scrollTo) {\n      setTimeout(() => {\n        if (location.state.scrollTo === 'features' && featuresRef.current) {\n          featuresRef.current.scrollIntoView({\n            behavior: 'smooth'\n          });\n        } else if (location.state.scrollTo === 'pricing' && pricingRef.current) {\n          pricingRef.current.scrollIntoView({\n            behavior: 'smooth'\n          });\n        } else if (location.state.scrollTo === 'faq') {\n          const faqEl = document.getElementById('faq');\n          if (faqEl) faqEl.scrollIntoView({\n            behavior: 'smooth'\n          });\n        } else if (location.state.scrollTo === 'contact' && contactRef.current) {\n          contactRef.current.scrollIntoView({\n            behavior: 'smooth'\n          });\n        }\n      }, 100);\n    }\n  }, [location]);\n  const scrollToPricing = e => {\n    var _pricingRef$current;\n    e.preventDefault();\n    (_pricingRef$current = pricingRef.current) === null || _pricingRef$current === void 0 ? void 0 : _pricingRef$current.scrollIntoView({\n      behavior: 'smooth'\n    });\n  };\n  const handleLogout = () => {\n    // Clear localStorage\n    localStorage.removeItem(\"userDetails\");\n    localStorage.removeItem(\"token\");\n    setIsLoggedIn(false);\n    setUserData(null);\n    // Refresh the page to update UI\n    window.location.reload();\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"home-page\",\n    children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"home-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"hero-section\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"container\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            children: \"Interview ready.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"subtitle\",\n            children: \"Get Instant answers for your interview questions. Ace your interview. Your dream job is right there\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"cta-buttons\",\n            children: isLoggedIn ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/home\",\n                className: \"cta-button primary\",\n                children: \"Start Interview\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 77,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: handleLogout,\n                className: \"cta-button logout\",\n                children: [/*#__PURE__*/_jsxDEV(LogoutIcon, {\n                  className: \"logout-icon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 79,\n                  columnNumber: 21\n                }, this), \"Logout\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 78,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/login\",\n                className: \"cta-button primary\",\n                children: \"Try now\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 85,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#pricing\",\n                className: \"cta-button secondary\",\n                onClick: scrollToPricing,\n                children: \"View Plans\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 86,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"how-it-works-section\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"container\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"How does our Interview AI work?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"section-subtitle\",\n            children: \"Get interview-ready in 4 simple steps\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"steps-container\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"step-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"step-content\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"step-number\",\n                  children: \"1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 105,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: \"Start Your Session\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 106,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"Sign up and choose your plan. Input your skillset, job description, and optionally upload your resume. Click \\\"Start Interview\\\" to begin your AI-powered interview preparation session.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 107,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 104,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"step-visual\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mini-interface\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mini-header\",\n                    children: \"\\uD83D\\uDCDD Setup Form\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 111,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mini-field\",\n                    children: \"Skillset: React, Node.js\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 112,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mini-field\",\n                    children: \"Job: Frontend Developer\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 113,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mini-field\",\n                    children: \"Resume: \\u2713 Uploaded\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 114,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mini-button\",\n                    children: \"Start Interview\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 115,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 110,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 109,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"step-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"step-content\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"step-number\",\n                  children: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 122,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: \"Ask Questions via Voice or Text\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 123,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"Use our advanced speech-to-text technology or type your interview questions. Our AI supports all industries and technologies, providing instant, accurate responses to help you practice.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 124,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"step-visual\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mini-interface\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mini-header\",\n                    children: [\"\\uD83D\\uDCAC AI Response \", /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"mini-timer\",\n                      children: \"14:32\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 128,\n                      columnNumber: 65\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 128,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mini-response\",\n                    children: \"AI answer will appear here...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 129,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mini-question\",\n                    children: \"What is React and how does it work?\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 130,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 127,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"step-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"step-content\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"step-number\",\n                  children: \"3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 137,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: \"Get Instant AI Responses\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 138,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"Receive real-time, comprehensive answers with definitions, examples, and coding solutions. Our ultra-low latency AI ensures you get immediate support exactly when you need it.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 139,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"step-visual\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mini-interface\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mini-header\",\n                    children: [\"\\u2705 AI Response \", /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"mini-timer\",\n                      children: \"14:28\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 143,\n                      columnNumber: 64\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 143,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mini-response filled\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Definition:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 145,\n                      columnNumber: 23\n                    }, this), \" React is a JavaScript library...\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 145,\n                      columnNumber: 84\n                    }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Example:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 146,\n                      columnNumber: 23\n                    }, this), \" Component-based architecture\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 146,\n                      columnNumber: 77\n                    }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Code:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 147,\n                      columnNumber: 23\n                    }, this), \" function HelloWorld() \", `{`, \"...\", `}`]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 144,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mini-question\",\n                    children: \"Type or record your question here...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 149,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 142,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"step-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"step-content\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"step-number\",\n                  children: \"4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 156,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: \"Practice and Perfect\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 157,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"Continue practicing with unlimited questions during your session. Build confidence with instant coding solutions, technical explanations, and industry-specific guidance. Ace your interview!\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 158,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"step-visual\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mock-interface practice\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"practice-stats\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"stat\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"stat-number\",\n                        children: \"15\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 164,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"stat-label\",\n                        children: \"Questions Asked\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 165,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 163,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"stat\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"stat-number\",\n                        children: \"12\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 168,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"stat-label\",\n                        children: \"Topics Covered\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 169,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 167,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"stat\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"stat-number\",\n                        children: \"98%\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 172,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"stat-label\",\n                        children: \"Accuracy\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 173,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 171,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 162,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"success-message\",\n                    children: \"\\uD83C\\uDF89 Great job! You're interview-ready!\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 176,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 161,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"demo-cta\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/login\",\n              className: \"demo-button\",\n              children: \"Try Interactive Demo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"demo-subtitle\",\n              children: \"Experience our AI in action - no commitment required\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        ref: featuresRef,\n        id: \"features\",\n        className: \"features-section\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"container\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Features\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"features-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"feature-icon\",\n                children: \"\\uD83C\\uDF10\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Universal Meeting Access\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Our app supports both browser-based and app-based meetings, giving you the flexibility to connect anytime, anywhere\\u2014no installations required.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"feature-icon\",\n                children: \"\\uD83D\\uDD17\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Seamless Platform Integration\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Seamless integration with all leading meeting platforms, including Zoom, Microsoft Teams, Google Meet, Webex, Skype, Slack, BlueJeans, GoTo Meeting, Whereby, Jitsi Meet, BigBlueButton, Zoho Meeting, Amazon Chime, Adobe Connect, ClickMeeting, Livestorm, RingCentral Video, 8x8 Meet, Pexip, TrueConf and more.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"feature-icon\",\n                children: \"\\uD83C\\uDFE2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"All Industries Supported\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Our interview-focused app is proven effective for all industries, including traditional, emerging, and niche sectors like Technology & IT, Healthcare & Medical, Finance & Banking, Education & Academia, Legal Services, Retail & E-commerce, Manufacturing & Production, Construction & Real Estate, Transportation & Logistics, Telecommunications, and more.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"feature-icon\",\n                children: \"\\uD83D\\uDCBB\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"IT & Tech Interview Mastery\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Specialized for interviews across all IT technologies: Software Development (Frontend, Backend, Full Stack), DevOps & Cloud (AWS, Azure, GCP, CI/CD), Data Science & Analytics (ML, DL, NLP, BI), Cybersecurity, Database Management, Mobile & Web Development, AI/ML, Blockchain, IT Support, ERP/CRM (SAP, Oracle, Salesforce), QA & Testing, Networking, UI/UX, and more.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"feature-icon\",\n                children: \"\\u26A1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Instant Coding Solutions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Ace any coding test\\u2014our AI delivers instant, proven, and reliable coding solutions across all technologies. Fast, accurate, and always available.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-card highlight\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"feature-icon\",\n                children: \"\\uD83D\\uDE80\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Ultra-Low Latency AI\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"With ultra-low latency, our AI delivers instant answers\\u2014no delays, no waiting. You get real-time support exactly when you need it.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        id: \"pricing\",\n        className: \"pricing-section\",\n        ref: pricingRef,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"container\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Pick your plan\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"pricing-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"pricing-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"pricing-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: \"FREE\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"price\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"currency\",\n                    children: \"$\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 237,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"amount\",\n                    children: \"0\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 238,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"period\",\n                    children: \"/ 5 mins session\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 239,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"pricing-features\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item available\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"check\",\n                    children: \"\\u2713\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 243,\n                    columnNumber: 59\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"5 mins session\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 243,\n                    columnNumber: 91\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item available\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"check\",\n                    children: \"\\u2713\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 244,\n                    columnNumber: 59\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Browser based/app based meeting\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 244,\n                    columnNumber: 91\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item available\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"check\",\n                    children: \"\\u2713\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 245,\n                    columnNumber: 59\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"All meeting platform integration\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 245,\n                    columnNumber: 91\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item available\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"check\",\n                    children: \"\\u2713\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 246,\n                    columnNumber: 59\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"All industry/All Technology\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 246,\n                    columnNumber: 91\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 246,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item available\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"check\",\n                    children: \"\\u2713\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 247,\n                    columnNumber: 59\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Instant Coding solution\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 247,\n                    columnNumber: 91\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item available\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"check\",\n                    children: \"\\u2713\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 248,\n                    columnNumber: 59\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Instant Image creation\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 248,\n                    columnNumber: 91\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item available\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"check\",\n                    children: \"\\u2713\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 249,\n                    columnNumber: 59\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Highly Customized AI playground\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 249,\n                    columnNumber: 91\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/signup\",\n                className: \"pricing-cta\",\n                children: \"Try for free\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"pricing-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"pricing-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: \"PRO\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"price\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"currency\",\n                    children: \"$\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 258,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"amount\",\n                    children: \"4.99\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 259,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"period\",\n                    children: \"/ 1 hour session\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 260,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"pricing-features\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item available\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"check\",\n                    children: \"\\u2713\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 264,\n                    columnNumber: 59\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"1 hour session\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 264,\n                    columnNumber: 91\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 264,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item available\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"check\",\n                    children: \"\\u2713\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 265,\n                    columnNumber: 59\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Browser based/app based meeting\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 265,\n                    columnNumber: 91\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item available\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"check\",\n                    children: \"\\u2713\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 266,\n                    columnNumber: 59\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"All meeting platform integration\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 266,\n                    columnNumber: 91\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 266,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item available\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"check\",\n                    children: \"\\u2713\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 267,\n                    columnNumber: 59\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"All industry/All Technology\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 267,\n                    columnNumber: 91\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 267,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item available\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"check\",\n                    children: \"\\u2713\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 268,\n                    columnNumber: 59\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Instant Coding solution\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 268,\n                    columnNumber: 91\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 268,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item available\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"check\",\n                    children: \"\\u2713\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 269,\n                    columnNumber: 59\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Instant Image creation\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 269,\n                    columnNumber: 91\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 269,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item available\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"check\",\n                    children: \"\\u2713\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 270,\n                    columnNumber: 59\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Highly Customized AI playground\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 270,\n                    columnNumber: 91\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 270,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/signup\",\n                className: \"pricing-cta\",\n                children: \"Get the plan\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        id: \"faq\",\n        className: \"faq-section\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"container\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Frequently Asked Questions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"faq-list\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"faq-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"faq-question\",\n                children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: \"Question 1: What is the purpose of this app?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 284,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"faq-answer\",\n                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"Our app is designed to help users prepare for interviews by providing instant answers to interview questions, coding challenges, and more.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 287,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"faq-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"faq-question\",\n                children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: \"Question 2: How does the AI work?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 292,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"faq-answer\",\n                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"Our AI uses advanced algorithms and machine learning techniques to deliver accurate and relevant answers to your queries in real-time.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"faq-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"faq-question\",\n                children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: \"Question 3: Is my data safe?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 300,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"faq-answer\",\n                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"Yes, we take data security seriously. Please refer to our Privacy Policy for detailed information on how we protect your data.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 303,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"faq-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"faq-question\",\n                children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: \"Question 4: Can I use this app on any device?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"faq-answer\",\n                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"Absolutely! Our app is web-based and can be accessed from any device with an internet connection.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 311,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"faq-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"faq-question\",\n                children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: \"Question 5: What if I have more questions?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 316,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"faq-answer\",\n                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"Feel free to reach out to our support team or check our Help Center for more information.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 319,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        id: \"contact\",\n        ref: contactRef,\n        className: \"contact-section\",\n        children: /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 326,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 65,\n    columnNumber: 5\n  }, this);\n}\n_s(HomeScreen, \"hezf5SKhGw2D1jOqPJs9HZKiTLI=\", false, function () {\n  return [useNavigate, useLocation];\n});\n_c = HomeScreen;\nexport default HomeScreen;\nvar _c;\n$RefreshReg$(_c, \"HomeScreen\");", "map": {"version": 3, "names": ["React", "useRef", "useState", "useEffect", "Link", "useNavigate", "useLocation", "Header", "Footer", "LogoutIcon", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "HomeScreen", "_s", "pricingRef", "isLoggedIn", "setIsLoggedIn", "userData", "setUserData", "navigate", "location", "featuresRef", "contactRef", "userDetails", "localStorage", "getItem", "parsedData", "JSON", "parse", "error", "console", "state", "scrollTo", "setTimeout", "current", "scrollIntoView", "behavior", "faqEl", "document", "getElementById", "scrollToPricing", "e", "_pricingRef$current", "preventDefault", "handleLogout", "removeItem", "window", "reload", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "onClick", "href", "ref", "id", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/interview-ai-app/interviewAI/client/src/pages/home/<USER>"], "sourcesContent": ["import React, { useRef, useState, useEffect } from 'react';\nimport { Link, useNavigate, useLocation } from 'react-router-dom';\nimport Header from '../../components/Header';\nimport Footer from '../../components/Footer';\nimport LogoutIcon from '@mui/icons-material/Logout';\nimport './home.css';\n\nfunction HomeScreen() {\n  const pricingRef = useRef(null);\n  const [isLoggedIn, setIsLoggedIn] = useState(false);\n  // eslint-disable-next-line\n  const [userData, setUserData] = useState(null);\n  // eslint-disable-next-line\n  const navigate = useNavigate();\n  const location = useLocation();\n  const featuresRef = useRef(null);\n  const contactRef = useRef(null);\n\n  useEffect(() => {\n    // Check if user is logged in\n    const userDetails = localStorage.getItem(\"userDetails\");\n    if (userDetails) {\n      setIsLoggedIn(true);\n      try {\n        const parsedData = JSON.parse(userDetails);\n        setUserData(parsedData);\n      } catch (error) {\n        console.error(\"Error parsing user data:\", error);\n      }\n    }\n\n    // Scroll to section if coming from nav\n    if (location.state && location.state.scrollTo) {\n      setTimeout(() => {\n        if (location.state.scrollTo === 'features' && featuresRef.current) {\n          featuresRef.current.scrollIntoView({ behavior: 'smooth' });\n        } else if (location.state.scrollTo === 'pricing' && pricingRef.current) {\n          pricingRef.current.scrollIntoView({ behavior: 'smooth' });\n        } else if (location.state.scrollTo === 'faq') {\n          const faqEl = document.getElementById('faq');\n          if (faqEl) faqEl.scrollIntoView({ behavior: 'smooth' });\n        } else if (location.state.scrollTo === 'contact' && contactRef.current) {\n          contactRef.current.scrollIntoView({ behavior: 'smooth' });\n        }\n      }, 100);\n    }\n  }, [location]);\n\n  const scrollToPricing = (e) => {\n    e.preventDefault();\n    pricingRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  const handleLogout = () => {\n    // Clear localStorage\n    localStorage.removeItem(\"userDetails\");\n    localStorage.removeItem(\"token\");\n    setIsLoggedIn(false);\n    setUserData(null);\n    // Refresh the page to update UI\n    window.location.reload();\n  };\n\n  return (\n    <div className=\"home-page\">\n      <Header />\n\n      <main className=\"home-content\">\n        <section className=\"hero-section\">\n          <div className=\"container\">\n            <h1>Interview ready.</h1>\n            <p className=\"subtitle\">Get Instant answers for your interview questions.\n              Ace your interview. Your dream job is right there</p>\n            <div className=\"cta-buttons\">\n              {isLoggedIn ? (\n                <>\n                  <Link to=\"/home\" className=\"cta-button primary\">Start Interview</Link>\n                  <button onClick={handleLogout} className=\"cta-button logout\">\n                    <LogoutIcon className=\"logout-icon\" />\n                    Logout\n                  </button>\n                </>\n              ) : (\n                <>\n                  <Link to=\"/login\" className=\"cta-button primary\">Try now</Link>\n                  <a href=\"#pricing\" className=\"cta-button secondary\" onClick={scrollToPricing}>View Plans</a>\n                </>\n              )}\n              {/* <Link to=\"/live-transcription\" className=\"cta-button secondary\">Live Transcription Demo</Link>\n              <Link to=\"/diarized-interview\" className=\"cta-button highlight\">Enhanced Interview with Speaker Diarization</Link>\n              <Link to=\"/automatic-diarization\" className=\"cta-button highlight-new\">Automatic Speaker Detection</Link>\n              <Link to=\"/google-speech\" className=\"cta-button highlight-google\">Google Speech API Diarization</Link> */}\n            </div>\n          </div>\n        </section>\n\n        <section className=\"how-it-works-section\">\n          <div className=\"container\">\n            <h2>How does our Interview AI work?</h2>\n            <p className=\"section-subtitle\">Get interview-ready in 4 simple steps</p>\n\n            <div className=\"steps-container\">\n              <div className=\"step-card\">\n                <div className=\"step-content\">\n                  <div className=\"step-number\">1</div>\n                  <h3>Start Your Session</h3>\n                  <p>Sign up and choose your plan. Input your skillset, job description, and optionally upload your resume. Click \"Start Interview\" to begin your AI-powered interview preparation session.</p>\n                </div>\n                <div className=\"step-visual\">\n                  <div className=\"mini-interface\">\n                    <div className=\"mini-header\">📝 Setup Form</div>\n                    <div className=\"mini-field\">Skillset: React, Node.js</div>\n                    <div className=\"mini-field\">Job: Frontend Developer</div>\n                    <div className=\"mini-field\">Resume: ✓ Uploaded</div>\n                    <div className=\"mini-button\">Start Interview</div>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"step-card\">\n                <div className=\"step-content\">\n                  <div className=\"step-number\">2</div>\n                  <h3>Ask Questions via Voice or Text</h3>\n                  <p>Use our advanced speech-to-text technology or type your interview questions. Our AI supports all industries and technologies, providing instant, accurate responses to help you practice.</p>\n                </div>\n                <div className=\"step-visual\">\n                  <div className=\"mini-interface\">\n                    <div className=\"mini-header\">💬 AI Response <span className=\"mini-timer\">14:32</span></div>\n                    <div className=\"mini-response\">AI answer will appear here...</div>\n                    <div className=\"mini-question\">What is React and how does it work?</div>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"step-card\">\n                <div className=\"step-content\">\n                  <div className=\"step-number\">3</div>\n                  <h3>Get Instant AI Responses</h3>\n                  <p>Receive real-time, comprehensive answers with definitions, examples, and coding solutions. Our ultra-low latency AI ensures you get immediate support exactly when you need it.</p>\n                </div>\n                <div className=\"step-visual\">\n                  <div className=\"mini-interface\">\n                    <div className=\"mini-header\">✅ AI Response <span className=\"mini-timer\">14:28</span></div>\n                    <div className=\"mini-response filled\">\n                      <strong>Definition:</strong> React is a JavaScript library...<br/>\n                      <strong>Example:</strong> Component-based architecture<br/>\n                      <strong>Code:</strong> function HelloWorld() {`{`}...{`}`}\n                    </div>\n                    <div className=\"mini-question\">Type or record your question here...</div>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"step-card\">\n                <div className=\"step-content\">\n                  <div className=\"step-number\">4</div>\n                  <h3>Practice and Perfect</h3>\n                  <p>Continue practicing with unlimited questions during your session. Build confidence with instant coding solutions, technical explanations, and industry-specific guidance. Ace your interview!</p>\n                </div>\n                <div className=\"step-visual\">\n                  <div className=\"mock-interface practice\">\n                    <div className=\"practice-stats\">\n                      <div className=\"stat\">\n                        <div className=\"stat-number\">15</div>\n                        <div className=\"stat-label\">Questions Asked</div>\n                      </div>\n                      <div className=\"stat\">\n                        <div className=\"stat-number\">12</div>\n                        <div className=\"stat-label\">Topics Covered</div>\n                      </div>\n                      <div className=\"stat\">\n                        <div className=\"stat-number\">98%</div>\n                        <div className=\"stat-label\">Accuracy</div>\n                      </div>\n                    </div>\n                    <div className=\"success-message\">\n                      🎉 Great job! You're interview-ready!\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"demo-cta\">\n              <Link to=\"/login\" className=\"demo-button\">Try Interactive Demo</Link>\n              <p className=\"demo-subtitle\">Experience our AI in action - no commitment required</p>\n            </div>\n          </div>\n        </section>\n\n        <section ref={featuresRef} id=\"features\" className=\"features-section\">\n          <div className=\"container\">\n            <h2>Features</h2>\n            <div className=\"features-grid\">\n              <div className=\"feature-card\">\n                <div className=\"feature-icon\">🌐</div>\n                <h3>Universal Meeting Access</h3>\n                <p>Our app supports both browser-based and app-based meetings, giving you the flexibility to connect anytime, anywhere—no installations required.</p>\n              </div>\n              <div className=\"feature-card\">\n                <div className=\"feature-icon\">🔗</div>\n                <h3>Seamless Platform Integration</h3>\n                <p>Seamless integration with all leading meeting platforms, including Zoom, Microsoft Teams, Google Meet, Webex, Skype, Slack, BlueJeans, GoTo Meeting, Whereby, Jitsi Meet, BigBlueButton, Zoho Meeting, Amazon Chime, Adobe Connect, ClickMeeting, Livestorm, RingCentral Video, 8x8 Meet, Pexip, TrueConf and more.</p>\n              </div>\n              <div className=\"feature-card\">\n                <div className=\"feature-icon\">🏢</div>\n                <h3>All Industries Supported</h3>\n                <p>Our interview-focused app is proven effective for all industries, including traditional, emerging, and niche sectors like Technology & IT, Healthcare & Medical, Finance & Banking, Education & Academia, Legal Services, Retail & E-commerce, Manufacturing & Production, Construction & Real Estate, Transportation & Logistics, Telecommunications, and more.</p>\n              </div>\n              <div className=\"feature-card\">\n                <div className=\"feature-icon\">💻</div>\n                <h3>IT & Tech Interview Mastery</h3>\n                <p>Specialized for interviews across all IT technologies: Software Development (Frontend, Backend, Full Stack), DevOps & Cloud (AWS, Azure, GCP, CI/CD), Data Science & Analytics (ML, DL, NLP, BI), Cybersecurity, Database Management, Mobile & Web Development, AI/ML, Blockchain, IT Support, ERP/CRM (SAP, Oracle, Salesforce), QA & Testing, Networking, UI/UX, and more.</p>\n              </div>\n              <div className=\"feature-card\">\n                <div className=\"feature-icon\">⚡</div>\n                <h3>Instant Coding Solutions</h3>\n                <p>Ace any coding test—our AI delivers instant, proven, and reliable coding solutions across all technologies. Fast, accurate, and always available.</p>\n              </div>\n              <div className=\"feature-card highlight\">\n                <div className=\"feature-icon\">🚀</div>\n                <h3>Ultra-Low Latency AI</h3>\n                <p>With ultra-low latency, our AI delivers instant answers—no delays, no waiting. You get real-time support exactly when you need it.</p>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        <section id=\"pricing\" className=\"pricing-section\" ref={pricingRef}>\n          <div className=\"container\">\n            <h2>Pick your plan</h2>\n            <div className=\"pricing-grid\">\n              <div className=\"pricing-card\">\n                <div className=\"pricing-header\">\n                  <h3>FREE</h3>\n                  <div className=\"price\">\n                    <span className=\"currency\">$</span>\n                    <span className=\"amount\">0</span>\n                    <span className=\"period\">/ 5 mins session</span>\n                  </div>\n                </div>\n                <div className=\"pricing-features\">\n                  <div className=\"feature-item available\"><span className=\"check\">✓</span><span>5 mins session</span></div>\n                  <div className=\"feature-item available\"><span className=\"check\">✓</span><span>Browser based/app based meeting</span></div>\n                  <div className=\"feature-item available\"><span className=\"check\">✓</span><span>All meeting platform integration</span></div>\n                  <div className=\"feature-item available\"><span className=\"check\">✓</span><span>All industry/All Technology</span></div>\n                  <div className=\"feature-item available\"><span className=\"check\">✓</span><span>Instant Coding solution</span></div>\n                  <div className=\"feature-item available\"><span className=\"check\">✓</span><span>Instant Image creation</span></div>\n                  <div className=\"feature-item available\"><span className=\"check\">✓</span><span>Highly Customized AI playground</span></div>\n                </div>\n                <Link to=\"/signup\" className=\"pricing-cta\">Try for free</Link>\n              </div>\n\n              <div className=\"pricing-card\">\n                <div className=\"pricing-header\">\n                  <h3>PRO</h3>\n                  <div className=\"price\">\n                    <span className=\"currency\">$</span>\n                    <span className=\"amount\">4.99</span>\n                    <span className=\"period\">/ 1 hour session</span>\n                  </div>\n                </div>\n                <div className=\"pricing-features\">\n                  <div className=\"feature-item available\"><span className=\"check\">✓</span><span>1 hour session</span></div>\n                  <div className=\"feature-item available\"><span className=\"check\">✓</span><span>Browser based/app based meeting</span></div>\n                  <div className=\"feature-item available\"><span className=\"check\">✓</span><span>All meeting platform integration</span></div>\n                  <div className=\"feature-item available\"><span className=\"check\">✓</span><span>All industry/All Technology</span></div>\n                  <div className=\"feature-item available\"><span className=\"check\">✓</span><span>Instant Coding solution</span></div>\n                  <div className=\"feature-item available\"><span className=\"check\">✓</span><span>Instant Image creation</span></div>\n                  <div className=\"feature-item available\"><span className=\"check\">✓</span><span>Highly Customized AI playground</span></div>\n                </div>\n                <Link to=\"/signup\" className=\"pricing-cta\">Get the plan</Link>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        <section id=\"faq\" className=\"faq-section\">\n          <div className=\"container\">\n            <h2>Frequently Asked Questions</h2>\n            <div className=\"faq-list\">\n              <div className=\"faq-item\">\n                <div className=\"faq-question\">\n                  <h3>Question 1: What is the purpose of this app?</h3>\n                </div>\n                <div className=\"faq-answer\">\n                  <p>Our app is designed to help users prepare for interviews by providing instant answers to interview questions, coding challenges, and more.</p>\n                </div>\n              </div>\n              <div className=\"faq-item\">\n                <div className=\"faq-question\">\n                  <h3>Question 2: How does the AI work?</h3>\n                </div>\n                <div className=\"faq-answer\">\n                  <p>Our AI uses advanced algorithms and machine learning techniques to deliver accurate and relevant answers to your queries in real-time.</p>\n                </div>\n              </div>\n              <div className=\"faq-item\">\n                <div className=\"faq-question\">\n                  <h3>Question 3: Is my data safe?</h3>\n                </div>\n                <div className=\"faq-answer\">\n                  <p>Yes, we take data security seriously. Please refer to our Privacy Policy for detailed information on how we protect your data.</p>\n                </div>\n              </div>\n              <div className=\"faq-item\">\n                <div className=\"faq-question\">\n                  <h3>Question 4: Can I use this app on any device?</h3>\n                </div>\n                <div className=\"faq-answer\">\n                  <p>Absolutely! Our app is web-based and can be accessed from any device with an internet connection.</p>\n                </div>\n              </div>\n              <div className=\"faq-item\">\n                <div className=\"faq-question\">\n                  <h3>Question 5: What if I have more questions?</h3>\n                </div>\n                <div className=\"faq-answer\">\n                  <p>Feel free to reach out to our support team or check our Help Center for more information.</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        <section id=\"contact\" ref={contactRef} className=\"contact-section\">\n          <Footer />\n        </section>\n      </main>\n    </div>\n  );\n}\n\nexport default HomeScreen;\n\n\n\n\n\n\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,MAAM,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,IAAI,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACjE,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAO,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpB,SAASC,UAAUA,CAAA,EAAG;EAAAC,EAAA;EACpB,MAAMC,UAAU,GAAGf,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAM,CAACgB,UAAU,EAAEC,aAAa,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACnD;EACA,MAAM,CAACiB,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC9C;EACA,MAAMmB,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAC9B,MAAMiB,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAC9B,MAAMiB,WAAW,GAAGtB,MAAM,CAAC,IAAI,CAAC;EAChC,MAAMuB,UAAU,GAAGvB,MAAM,CAAC,IAAI,CAAC;EAE/BE,SAAS,CAAC,MAAM;IACd;IACA,MAAMsB,WAAW,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IACvD,IAAIF,WAAW,EAAE;MACfP,aAAa,CAAC,IAAI,CAAC;MACnB,IAAI;QACF,MAAMU,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACL,WAAW,CAAC;QAC1CL,WAAW,CAACQ,UAAU,CAAC;MACzB,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAClD;IACF;;IAEA;IACA,IAAIT,QAAQ,CAACW,KAAK,IAAIX,QAAQ,CAACW,KAAK,CAACC,QAAQ,EAAE;MAC7CC,UAAU,CAAC,MAAM;QACf,IAAIb,QAAQ,CAACW,KAAK,CAACC,QAAQ,KAAK,UAAU,IAAIX,WAAW,CAACa,OAAO,EAAE;UACjEb,WAAW,CAACa,OAAO,CAACC,cAAc,CAAC;YAAEC,QAAQ,EAAE;UAAS,CAAC,CAAC;QAC5D,CAAC,MAAM,IAAIhB,QAAQ,CAACW,KAAK,CAACC,QAAQ,KAAK,SAAS,IAAIlB,UAAU,CAACoB,OAAO,EAAE;UACtEpB,UAAU,CAACoB,OAAO,CAACC,cAAc,CAAC;YAAEC,QAAQ,EAAE;UAAS,CAAC,CAAC;QAC3D,CAAC,MAAM,IAAIhB,QAAQ,CAACW,KAAK,CAACC,QAAQ,KAAK,KAAK,EAAE;UAC5C,MAAMK,KAAK,GAAGC,QAAQ,CAACC,cAAc,CAAC,KAAK,CAAC;UAC5C,IAAIF,KAAK,EAAEA,KAAK,CAACF,cAAc,CAAC;YAAEC,QAAQ,EAAE;UAAS,CAAC,CAAC;QACzD,CAAC,MAAM,IAAIhB,QAAQ,CAACW,KAAK,CAACC,QAAQ,KAAK,SAAS,IAAIV,UAAU,CAACY,OAAO,EAAE;UACtEZ,UAAU,CAACY,OAAO,CAACC,cAAc,CAAC;YAAEC,QAAQ,EAAE;UAAS,CAAC,CAAC;QAC3D;MACF,CAAC,EAAE,GAAG,CAAC;IACT;EACF,CAAC,EAAE,CAAChB,QAAQ,CAAC,CAAC;EAEd,MAAMoB,eAAe,GAAIC,CAAC,IAAK;IAAA,IAAAC,mBAAA;IAC7BD,CAAC,CAACE,cAAc,CAAC,CAAC;IAClB,CAAAD,mBAAA,GAAA5B,UAAU,CAACoB,OAAO,cAAAQ,mBAAA,uBAAlBA,mBAAA,CAAoBP,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAC5D,CAAC;EAED,MAAMQ,YAAY,GAAGA,CAAA,KAAM;IACzB;IACApB,YAAY,CAACqB,UAAU,CAAC,aAAa,CAAC;IACtCrB,YAAY,CAACqB,UAAU,CAAC,OAAO,CAAC;IAChC7B,aAAa,CAAC,KAAK,CAAC;IACpBE,WAAW,CAAC,IAAI,CAAC;IACjB;IACA4B,MAAM,CAAC1B,QAAQ,CAAC2B,MAAM,CAAC,CAAC;EAC1B,CAAC;EAED,oBACEtC,OAAA;IAAKuC,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBxC,OAAA,CAACJ,MAAM;MAAA6C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEV5C,OAAA;MAAMuC,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC5BxC,OAAA;QAASuC,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC/BxC,OAAA;UAAKuC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBxC,OAAA;YAAAwC,QAAA,EAAI;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzB5C,OAAA;YAAGuC,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAC;UAC2B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACvD5C,OAAA;YAAKuC,SAAS,EAAC,aAAa;YAAAC,QAAA,EACzBlC,UAAU,gBACTN,OAAA,CAAAE,SAAA;cAAAsC,QAAA,gBACExC,OAAA,CAACP,IAAI;gBAACoD,EAAE,EAAC,OAAO;gBAACN,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtE5C,OAAA;gBAAQ8C,OAAO,EAAEX,YAAa;gBAACI,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAC1DxC,OAAA,CAACF,UAAU;kBAACyC,SAAS,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,UAExC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA,eACT,CAAC,gBAEH5C,OAAA,CAAAE,SAAA;cAAAsC,QAAA,gBACExC,OAAA,CAACP,IAAI;gBAACoD,EAAE,EAAC,QAAQ;gBAACN,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/D5C,OAAA;gBAAG+C,IAAI,EAAC,UAAU;gBAACR,SAAS,EAAC,sBAAsB;gBAACO,OAAO,EAAEf,eAAgB;gBAAAS,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA,eAC5F;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEV5C,OAAA;QAASuC,SAAS,EAAC,sBAAsB;QAAAC,QAAA,eACvCxC,OAAA;UAAKuC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBxC,OAAA;YAAAwC,QAAA,EAAI;UAA+B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxC5C,OAAA;YAAGuC,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAAqC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEzE5C,OAAA;YAAKuC,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BxC,OAAA;cAAKuC,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBxC,OAAA;gBAAKuC,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3BxC,OAAA;kBAAKuC,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACpC5C,OAAA;kBAAAwC,QAAA,EAAI;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC3B5C,OAAA;kBAAAwC,QAAA,EAAG;gBAAsL;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1L,CAAC,eACN5C,OAAA;gBAAKuC,SAAS,EAAC,aAAa;gBAAAC,QAAA,eAC1BxC,OAAA;kBAAKuC,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7BxC,OAAA;oBAAKuC,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAC;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAChD5C,OAAA;oBAAKuC,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAwB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC1D5C,OAAA;oBAAKuC,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAuB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACzD5C,OAAA;oBAAKuC,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAkB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACpD5C,OAAA;oBAAKuC,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAC;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN5C,OAAA;cAAKuC,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBxC,OAAA;gBAAKuC,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3BxC,OAAA;kBAAKuC,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACpC5C,OAAA;kBAAAwC,QAAA,EAAI;gBAA+B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACxC5C,OAAA;kBAAAwC,QAAA,EAAG;gBAAyL;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7L,CAAC,eACN5C,OAAA;gBAAKuC,SAAS,EAAC,aAAa;gBAAAC,QAAA,eAC1BxC,OAAA;kBAAKuC,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7BxC,OAAA;oBAAKuC,SAAS,EAAC,aAAa;oBAAAC,QAAA,GAAC,2BAAe,eAAAxC,OAAA;sBAAMuC,SAAS,EAAC,YAAY;sBAAAC,QAAA,EAAC;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC3F5C,OAAA;oBAAKuC,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAA6B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAClE5C,OAAA;oBAAKuC,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAmC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN5C,OAAA;cAAKuC,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBxC,OAAA;gBAAKuC,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3BxC,OAAA;kBAAKuC,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACpC5C,OAAA;kBAAAwC,QAAA,EAAI;gBAAwB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjC5C,OAAA;kBAAAwC,QAAA,EAAG;gBAA+K;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnL,CAAC,eACN5C,OAAA;gBAAKuC,SAAS,EAAC,aAAa;gBAAAC,QAAA,eAC1BxC,OAAA;kBAAKuC,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7BxC,OAAA;oBAAKuC,SAAS,EAAC,aAAa;oBAAAC,QAAA,GAAC,qBAAc,eAAAxC,OAAA;sBAAMuC,SAAS,EAAC,YAAY;sBAAAC,QAAA,EAAC;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC1F5C,OAAA;oBAAKuC,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,gBACnCxC,OAAA;sBAAAwC,QAAA,EAAQ;oBAAW;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,qCAAiC,eAAA5C,OAAA;sBAAAyC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAClE5C,OAAA;sBAAAwC,QAAA,EAAQ;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,iCAA6B,eAAA5C,OAAA;sBAAAyC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC3D5C,OAAA;sBAAAwC,QAAA,EAAQ;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,2BAAuB,EAAC,GAAG,EAAC,KAAG,EAAC,GAAG;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD,CAAC,eACN5C,OAAA;oBAAKuC,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAoC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN5C,OAAA;cAAKuC,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBxC,OAAA;gBAAKuC,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3BxC,OAAA;kBAAKuC,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACpC5C,OAAA;kBAAAwC,QAAA,EAAI;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7B5C,OAAA;kBAAAwC,QAAA,EAAG;gBAA6L;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjM,CAAC,eACN5C,OAAA;gBAAKuC,SAAS,EAAC,aAAa;gBAAAC,QAAA,eAC1BxC,OAAA;kBAAKuC,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACtCxC,OAAA;oBAAKuC,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,gBAC7BxC,OAAA;sBAAKuC,SAAS,EAAC,MAAM;sBAAAC,QAAA,gBACnBxC,OAAA;wBAAKuC,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAC;sBAAE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACrC5C,OAAA;wBAAKuC,SAAS,EAAC,YAAY;wBAAAC,QAAA,EAAC;sBAAe;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9C,CAAC,eACN5C,OAAA;sBAAKuC,SAAS,EAAC,MAAM;sBAAAC,QAAA,gBACnBxC,OAAA;wBAAKuC,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAC;sBAAE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACrC5C,OAAA;wBAAKuC,SAAS,EAAC,YAAY;wBAAAC,QAAA,EAAC;sBAAc;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7C,CAAC,eACN5C,OAAA;sBAAKuC,SAAS,EAAC,MAAM;sBAAAC,QAAA,gBACnBxC,OAAA;wBAAKuC,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAC;sBAAG;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACtC5C,OAAA;wBAAKuC,SAAS,EAAC,YAAY;wBAAAC,QAAA,EAAC;sBAAQ;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN5C,OAAA;oBAAKuC,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,EAAC;kBAEjC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN5C,OAAA;YAAKuC,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBxC,OAAA,CAACP,IAAI;cAACoD,EAAE,EAAC,QAAQ;cAACN,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrE5C,OAAA;cAAGuC,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAoD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEV5C,OAAA;QAASgD,GAAG,EAAEpC,WAAY;QAACqC,EAAE,EAAC,UAAU;QAACV,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eACnExC,OAAA;UAAKuC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBxC,OAAA;YAAAwC,QAAA,EAAI;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjB5C,OAAA;YAAKuC,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BxC,OAAA;cAAKuC,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BxC,OAAA;gBAAKuC,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtC5C,OAAA;gBAAAwC,QAAA,EAAI;cAAwB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjC5C,OAAA;gBAAAwC,QAAA,EAAG;cAA8I;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClJ,CAAC,eACN5C,OAAA;cAAKuC,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BxC,OAAA;gBAAKuC,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtC5C,OAAA;gBAAAwC,QAAA,EAAI;cAA6B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtC5C,OAAA;gBAAAwC,QAAA,EAAG;cAAmT;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvT,CAAC,eACN5C,OAAA;cAAKuC,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BxC,OAAA;gBAAKuC,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtC5C,OAAA;gBAAAwC,QAAA,EAAI;cAAwB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjC5C,OAAA;gBAAAwC,QAAA,EAAG;cAAgW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpW,CAAC,eACN5C,OAAA;cAAKuC,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BxC,OAAA;gBAAKuC,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtC5C,OAAA;gBAAAwC,QAAA,EAAI;cAA2B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpC5C,OAAA;gBAAAwC,QAAA,EAAG;cAA4W;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChX,CAAC,eACN5C,OAAA;cAAKuC,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BxC,OAAA;gBAAKuC,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACrC5C,OAAA;gBAAAwC,QAAA,EAAI;cAAwB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjC5C,OAAA;gBAAAwC,QAAA,EAAG;cAAiJ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrJ,CAAC,eACN5C,OAAA;cAAKuC,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrCxC,OAAA;gBAAKuC,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtC5C,OAAA;gBAAAwC,QAAA,EAAI;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7B5C,OAAA;gBAAAwC,QAAA,EAAG;cAAkI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEV5C,OAAA;QAASiD,EAAE,EAAC,SAAS;QAACV,SAAS,EAAC,iBAAiB;QAACS,GAAG,EAAE3C,UAAW;QAAAmC,QAAA,eAChExC,OAAA;UAAKuC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBxC,OAAA;YAAAwC,QAAA,EAAI;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvB5C,OAAA;YAAKuC,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BxC,OAAA;cAAKuC,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BxC,OAAA;gBAAKuC,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BxC,OAAA;kBAAAwC,QAAA,EAAI;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACb5C,OAAA;kBAAKuC,SAAS,EAAC,OAAO;kBAAAC,QAAA,gBACpBxC,OAAA;oBAAMuC,SAAS,EAAC,UAAU;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACnC5C,OAAA;oBAAMuC,SAAS,EAAC,QAAQ;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACjC5C,OAAA;oBAAMuC,SAAS,EAAC,QAAQ;oBAAAC,QAAA,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN5C,OAAA;gBAAKuC,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BxC,OAAA;kBAAKuC,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBAACxC,OAAA;oBAAMuC,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAAA5C,OAAA;oBAAAwC,QAAA,EAAM;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACzG5C,OAAA;kBAAKuC,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBAACxC,OAAA;oBAAMuC,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAAA5C,OAAA;oBAAAwC,QAAA,EAAM;kBAA+B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC1H5C,OAAA;kBAAKuC,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBAACxC,OAAA;oBAAMuC,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAAA5C,OAAA;oBAAAwC,QAAA,EAAM;kBAAgC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC3H5C,OAAA;kBAAKuC,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBAACxC,OAAA;oBAAMuC,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAAA5C,OAAA;oBAAAwC,QAAA,EAAM;kBAA2B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACtH5C,OAAA;kBAAKuC,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBAACxC,OAAA;oBAAMuC,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAAA5C,OAAA;oBAAAwC,QAAA,EAAM;kBAAuB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAClH5C,OAAA;kBAAKuC,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBAACxC,OAAA;oBAAMuC,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAAA5C,OAAA;oBAAAwC,QAAA,EAAM;kBAAsB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACjH5C,OAAA;kBAAKuC,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBAACxC,OAAA;oBAAMuC,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAAA5C,OAAA;oBAAAwC,QAAA,EAAM;kBAA+B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvH,CAAC,eACN5C,OAAA,CAACP,IAAI;gBAACoD,EAAE,EAAC,SAAS;gBAACN,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC,eAEN5C,OAAA;cAAKuC,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BxC,OAAA;gBAAKuC,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BxC,OAAA;kBAAAwC,QAAA,EAAI;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACZ5C,OAAA;kBAAKuC,SAAS,EAAC,OAAO;kBAAAC,QAAA,gBACpBxC,OAAA;oBAAMuC,SAAS,EAAC,UAAU;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACnC5C,OAAA;oBAAMuC,SAAS,EAAC,QAAQ;oBAAAC,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACpC5C,OAAA;oBAAMuC,SAAS,EAAC,QAAQ;oBAAAC,QAAA,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN5C,OAAA;gBAAKuC,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BxC,OAAA;kBAAKuC,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBAACxC,OAAA;oBAAMuC,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAAA5C,OAAA;oBAAAwC,QAAA,EAAM;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACzG5C,OAAA;kBAAKuC,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBAACxC,OAAA;oBAAMuC,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAAA5C,OAAA;oBAAAwC,QAAA,EAAM;kBAA+B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC1H5C,OAAA;kBAAKuC,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBAACxC,OAAA;oBAAMuC,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAAA5C,OAAA;oBAAAwC,QAAA,EAAM;kBAAgC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC3H5C,OAAA;kBAAKuC,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBAACxC,OAAA;oBAAMuC,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAAA5C,OAAA;oBAAAwC,QAAA,EAAM;kBAA2B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACtH5C,OAAA;kBAAKuC,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBAACxC,OAAA;oBAAMuC,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAAA5C,OAAA;oBAAAwC,QAAA,EAAM;kBAAuB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAClH5C,OAAA;kBAAKuC,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBAACxC,OAAA;oBAAMuC,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAAA5C,OAAA;oBAAAwC,QAAA,EAAM;kBAAsB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACjH5C,OAAA;kBAAKuC,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBAACxC,OAAA;oBAAMuC,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAAA5C,OAAA;oBAAAwC,QAAA,EAAM;kBAA+B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvH,CAAC,eACN5C,OAAA,CAACP,IAAI;gBAACoD,EAAE,EAAC,SAAS;gBAACN,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEV5C,OAAA;QAASiD,EAAE,EAAC,KAAK;QAACV,SAAS,EAAC,aAAa;QAAAC,QAAA,eACvCxC,OAAA;UAAKuC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBxC,OAAA;YAAAwC,QAAA,EAAI;UAA0B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnC5C,OAAA;YAAKuC,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBxC,OAAA;cAAKuC,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBxC,OAAA;gBAAKuC,SAAS,EAAC,cAAc;gBAAAC,QAAA,eAC3BxC,OAAA;kBAAAwC,QAAA,EAAI;gBAA4C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC,eACN5C,OAAA;gBAAKuC,SAAS,EAAC,YAAY;gBAAAC,QAAA,eACzBxC,OAAA;kBAAAwC,QAAA,EAAG;gBAA0I;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9I,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN5C,OAAA;cAAKuC,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBxC,OAAA;gBAAKuC,SAAS,EAAC,cAAc;gBAAAC,QAAA,eAC3BxC,OAAA;kBAAAwC,QAAA,EAAI;gBAAiC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC,eACN5C,OAAA;gBAAKuC,SAAS,EAAC,YAAY;gBAAAC,QAAA,eACzBxC,OAAA;kBAAAwC,QAAA,EAAG;gBAAsI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1I,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN5C,OAAA;cAAKuC,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBxC,OAAA;gBAAKuC,SAAS,EAAC,cAAc;gBAAAC,QAAA,eAC3BxC,OAAA;kBAAAwC,QAAA,EAAI;gBAA4B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,eACN5C,OAAA;gBAAKuC,SAAS,EAAC,YAAY;gBAAAC,QAAA,eACzBxC,OAAA;kBAAAwC,QAAA,EAAG;gBAA8H;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN5C,OAAA;cAAKuC,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBxC,OAAA;gBAAKuC,SAAS,EAAC,cAAc;gBAAAC,QAAA,eAC3BxC,OAAA;kBAAAwC,QAAA,EAAI;gBAA6C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC,eACN5C,OAAA;gBAAKuC,SAAS,EAAC,YAAY;gBAAAC,QAAA,eACzBxC,OAAA;kBAAAwC,QAAA,EAAG;gBAAiG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN5C,OAAA;cAAKuC,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBxC,OAAA;gBAAKuC,SAAS,EAAC,cAAc;gBAAAC,QAAA,eAC3BxC,OAAA;kBAAAwC,QAAA,EAAI;gBAA0C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,eACN5C,OAAA;gBAAKuC,SAAS,EAAC,YAAY;gBAAAC,QAAA,eACzBxC,OAAA;kBAAAwC,QAAA,EAAG;gBAAyF;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7F,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEV5C,OAAA;QAASiD,EAAE,EAAC,SAAS;QAACD,GAAG,EAAEnC,UAAW;QAAC0B,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAChExC,OAAA,CAACH,MAAM;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAACxC,EAAA,CApUQD,UAAU;EAAA,QAMAT,WAAW,EACXC,WAAW;AAAA;AAAAuD,EAAA,GAPrB/C,UAAU;AAsUnB,eAAeA,UAAU;AAAC,IAAA+C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}