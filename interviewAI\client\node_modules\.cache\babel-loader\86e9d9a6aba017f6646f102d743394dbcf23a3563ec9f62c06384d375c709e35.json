{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\interview-ai-app\\\\interviewAI\\\\client\\\\src\\\\components\\\\InterviewAssistant.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect, useCallback } from 'react';\nimport './InterviewAssistant.css';\nimport env from '../utils/env';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction InterviewAssistant() {\n  _s();\n  const [isListening, setIsListening] = useState(false);\n  const [transcriptMessages, setTranscriptMessages] = useState([]);\n  const [currentTranscript, setCurrentTranscript] = useState('');\n  const [response, setResponse] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [autoSubmit, setAutoSubmit] = useState(false); // Toggle for auto-submission\n  const [previousTranscript, setPreviousTranscript] = useState(''); // Track previous transcript for comparison\n\n  // Timer state\n  const [timerSeconds, setTimerSeconds] = useState(0);\n  const timerIntervalRef = useRef(null);\n  const pauseTimerRef = useRef(null); // Timer for detecting speech pauses\n\n  const recognitionRef = useRef(null);\n  const transcriptAreaRef = useRef(null);\n  const responseAreaRef = useRef(null);\n\n  // Format seconds to MM:SS\n  const formatTime = useCallback(totalSeconds => {\n    const minutes = Math.floor(totalSeconds / 60);\n    const seconds = totalSeconds % 60;\n    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\n  }, []);\n\n  // Start the timer\n  const startTimer = useCallback(() => {\n    // Reset timer when starting\n    setTimerSeconds(0);\n\n    // Clear any existing interval\n    if (timerIntervalRef.current) {\n      clearInterval(timerIntervalRef.current);\n    }\n\n    // Start a new interval\n    timerIntervalRef.current = setInterval(() => {\n      setTimerSeconds(prev => prev + 1);\n    }, 1000);\n  }, []);\n\n  // Stop the timer\n  const stopTimer = useCallback(() => {\n    if (timerIntervalRef.current) {\n      clearInterval(timerIntervalRef.current);\n      timerIntervalRef.current = null;\n    }\n  }, []);\n\n  // Function to check for speech pause and auto-submit\n  const checkForSpeechPause = useCallback(() => {\n    // Clear any existing pause timer\n    if (pauseTimerRef.current) {\n      clearTimeout(pauseTimerRef.current);\n    }\n\n    // Set a new pause timer\n    pauseTimerRef.current = setTimeout(() => {\n      console.log(\"Checking auto-submit conditions:\", {\n        autoSubmit,\n        hasText: !!currentTranscript.trim(),\n        isListening,\n        isLoading\n      });\n\n      // Only auto-submit if:\n      // 1. Auto-submit is enabled\n      // 2. We have a non-empty transcript\n      // 3. We're currently listening\n      // 4. We're not already loading a response\n      if (autoSubmit && currentTranscript.trim() && isListening && !isLoading) {\n        console.log(\"Auto-submit conditions met, sending to GPT\");\n\n        // Call sendToGPT directly instead of simulating a button click\n        sendToGPT();\n      }\n    }, 2000); // 2 second pause detection\n  }, [autoSubmit, currentTranscript, isListening, isLoading, sendToGPT]);\n\n  // Clean up timer on unmount\n  useEffect(() => {\n    return () => {\n      if (timerIntervalRef.current) {\n        clearInterval(timerIntervalRef.current);\n      }\n      if (pauseTimerRef.current) {\n        clearTimeout(pauseTimerRef.current);\n      }\n    };\n  }, []);\n  useEffect(() => {\n    // Initialize speech recognition\n    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n    if (SpeechRecognition) {\n      recognitionRef.current = new SpeechRecognition();\n      recognitionRef.current.lang = 'en-US';\n      recognitionRef.current.interimResults = true;\n      recognitionRef.current.continuous = true;\n      let finalTranscript = \"\";\n      recognitionRef.current.onresult = event => {\n        let interimTranscript = \"\";\n\n        // Store previous transcript for comparison\n        setPreviousTranscript(finalTranscript + interimTranscript);\n        for (let i = event.resultIndex; i < event.results.length; i++) {\n          const transcript = event.results[i][0].transcript;\n          if (event.results[i].isFinal) {\n            finalTranscript += transcript + \" \";\n          } else {\n            interimTranscript += transcript;\n          }\n        }\n        const newTranscript = finalTranscript + interimTranscript;\n        setCurrentTranscript(newTranscript);\n\n        // Check for pause if transcript has changed\n        if (newTranscript !== previousTranscript) {\n          checkForSpeechPause();\n        }\n      };\n      recognitionRef.current.onerror = event => {\n        console.error(\"Speech recognition error\", event.error);\n        alert(\"Error occurred: \" + event.error);\n        setIsListening(false);\n        stopTimer();\n      };\n    } else {\n      alert(\"Your browser doesn't support speech recognition. Try Chrome or Edge.\");\n    }\n\n    // Cleanup function\n    return () => {\n      if (recognitionRef.current) {\n        recognitionRef.current.stop();\n      }\n    };\n  }, [stopTimer, checkForSpeechPause, previousTranscript]);\n  useEffect(() => {\n    // Auto-scroll transcript area when content changes\n    if (transcriptAreaRef.current) {\n      transcriptAreaRef.current.scrollTop = transcriptAreaRef.current.scrollHeight;\n    }\n  }, [transcriptMessages, currentTranscript]);\n  useEffect(() => {\n    // Auto-scroll response area when content changes\n    if (responseAreaRef.current) {\n      responseAreaRef.current.scrollTop = responseAreaRef.current.scrollHeight;\n    }\n  }, [response]);\n  const startListening = useCallback(() => {\n    if (recognitionRef.current && !isListening) {\n      try {\n        recognitionRef.current.start();\n        setIsListening(true);\n        startTimer(); // Start the timer when listening begins\n      } catch (error) {\n        console.error(\"Speech recognition error:\", error);\n        // If recognition is already running, stop it first then restart\n        if (error.message.includes(\"already started\")) {\n          recognitionRef.current.stop();\n          setTimeout(() => {\n            recognitionRef.current.start();\n            setIsListening(true);\n            startTimer(); // Start the timer when listening begins\n          }, 100);\n        }\n      }\n    }\n  }, [isListening, startTimer]);\n  const stopListening = useCallback(() => {\n    if (recognitionRef.current && isListening) {\n      try {\n        recognitionRef.current.stop();\n        setIsListening(false);\n        stopTimer(); // Stop the timer when listening ends\n      } catch (error) {\n        console.error(\"Speech recognition error:\", error);\n      }\n    }\n  }, [isListening, stopTimer]);\n  const sendToGPT = useCallback(async () => {\n    const apiKey = env.OPENAI_API_KEY;\n    const userText = currentTranscript.trim();\n    if (!apiKey) {\n      alert(\"Please add your OpenAI API key to the .env file as REACT_APP_OPENAI_API_KEY and restart the development server.\");\n      return;\n    }\n    if (!userText) {\n      alert(\"Please record or enter some text to send to GPT.\");\n      return;\n    }\n\n    // Add the current transcript to the messages array\n    setTranscriptMessages(prev => [...prev, {\n      text: userText,\n      timestamp: new Date(),\n      time: timerSeconds\n    }]);\n\n    // Clear the current transcript input\n    setCurrentTranscript('');\n    setIsLoading(true);\n    setResponse('');\n    try {\n      const response = await fetch(\"https://api.openai.com/v1/chat/completions\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          \"Authorization\": `Bearer ${apiKey}`\n        },\n        body: JSON.stringify({\n          model: \"gpt-4.1-nano\",\n          messages: [{\n            role: \"system\",\n            content: \"You are Sensei, an AI interview assistant. Provide detailed, well-structured responses to interview questions. Format your answers with bullet points for clarity. Keep responses concise but comprehensive.\"\n          }, {\n            role: \"user\",\n            content: userText\n          }],\n          stream: true\n        })\n      });\n      if (!response.ok || !response.body) throw new Error(\"Failed to stream response.\");\n      const reader = response.body.getReader();\n      const decoder = new TextDecoder(\"utf-8\");\n      let result = \"\";\n      while (true) {\n        const {\n          value,\n          done\n        } = await reader.read();\n        if (done) break;\n        const chunk = decoder.decode(value, {\n          stream: true\n        });\n        const lines = chunk.split(\"\\n\").filter(line => line.trim().startsWith(\"data:\"));\n        for (const line of lines) {\n          const data = line.replace(/^data: /, '');\n          if (data === \"[DONE]\") break;\n          try {\n            var _json$choices, _json$choices$, _json$choices$$delta;\n            const json = JSON.parse(data);\n            const content = (_json$choices = json.choices) === null || _json$choices === void 0 ? void 0 : (_json$choices$ = _json$choices[0]) === null || _json$choices$ === void 0 ? void 0 : (_json$choices$$delta = _json$choices$.delta) === null || _json$choices$$delta === void 0 ? void 0 : _json$choices$$delta.content;\n            if (content) {\n              result += content;\n              setResponse(result);\n            }\n          } catch (e) {\n            console.error(\"Error parsing JSON:\", e);\n          }\n        }\n      }\n    } catch (error) {\n      console.error(\"Streaming Error:\", error);\n      setResponse(\"Error occurred: \" + error.message);\n    } finally {\n      if (isListening) {\n        stopListening();\n      }\n      setIsLoading(false);\n    }\n  }, [currentTranscript, isListening, stopListening, timerSeconds]);\n  const clearTranscript = () => {\n    setCurrentTranscript('');\n    setTranscriptMessages([]);\n  };\n  const clearResponse = () => {\n    setResponse('');\n  };\n\n  // Handle end interview with confirmation popup\n  const [showEndConfirmation, setShowEndConfirmation] = useState(false);\n  const handleEndInterview = () => {\n    setShowEndConfirmation(true);\n  };\n  const confirmEndInterview = () => {\n    // Here you would handle the actual end interview logic\n    // For example, redirect to a summary page or reset the state\n    setShowEndConfirmation(false);\n    clearTranscript();\n    clearResponse();\n    // You could also redirect to another page\n    // window.location.href = '/';\n  };\n  const cancelEndInterview = () => {\n    setShowEndConfirmation(false);\n  };\n  const generateQuestion = () => {\n    // This would generate a sample interview question\n    const questions = [\"Tell me about yourself.\", \"What are your greatest strengths?\", \"What do you consider to be your weaknesses?\", \"Why do you want this job?\", \"Where do you see yourself in five years?\", \"Why should we hire you?\", \"What is your greatest professional achievement?\", \"Tell me about a challenge or conflict you've faced at work, and how you dealt with it.\", \"Tell me about a time you demonstrated leadership skills.\", \"What's your management style?\", \"How do you handle stress and pressure?\", \"What are your salary expectations?\", \"What do you like to do outside of work?\", \"What are your career goals?\", \"Why are you leaving your current job?\", \"How do you prioritize your work?\", \"What are you passionate about?\", \"What makes you unique?\", \"What should I know that's not on your resume?\", \"What would your first 30, 60, or 90 days look like in this role?\"];\n    const randomQuestion = questions[Math.floor(Math.random() * questions.length)];\n\n    // Add the generated question directly to the transcript messages\n    setTranscriptMessages(prev => [...prev, {\n      text: randomQuestion,\n      timestamp: new Date(),\n      time: timerSeconds\n    }]);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"interview-assistant\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"interview-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"transcription-panel\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"panel-content\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: transcriptAreaRef,\n            className: \"transcript-content\",\n            children: [transcriptMessages.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"transcript-messages\",\n              children: transcriptMessages.map((msg, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"transcript-message\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"timestamp\",\n                  children: formatTime(msg.time)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 371,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"message-text\",\n                  children: msg.text\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 372,\n                  columnNumber: 23\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"empty-state\",\n              children: \"Your interview questions will appear here\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 17\n            }, this), currentTranscript && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"transcript-message current\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"timestamp\",\n                children: formatTime(timerSeconds)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 382,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"message-text\",\n                children: currentTranscript\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 383,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"panel-footer\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-label\",\n            children: \"Interviewer\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"generate-button\",\n            onClick: generateQuestion,\n            children: \"Generate\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 389,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 354,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"response-panel\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"panel-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Answers\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"panel-description\",\n            children: \"Displayed here are the intelligent responses generated by Sensei. Whenever the interviewer concludes a question, provides a tailored answer for you.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 403,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 401,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"panel-content\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: responseAreaRef,\n            className: \"response-content\",\n            children: response ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"response-message\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"message-text\",\n                children: response\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 416,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 415,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"empty-state\",\n              children: \"InterviewAssistant's responses will appear here\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 419,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 410,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 409,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"panel-footer\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-label\",\n            children: \"InterviewAssistant's\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 425,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 424,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 400,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 353,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"controls-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"timer-display\",\n        children: isListening ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"recording-dot\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 15\n          }, this), formatTime(timerSeconds)]\n        }, void 0, true) : formatTime(timerSeconds)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 431,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"limit-indicator\",\n        children: \"15-minute limit\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 442,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"input-controls\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          className: \"text-input\",\n          value: currentTranscript,\n          onChange: e => setCurrentTranscript(e.target.value),\n          placeholder: \"Type your question here...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 445,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"submit-button\",\n          onClick: sendToGPT,\n          disabled: isLoading || !currentTranscript.trim(),\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"arrow-icon\",\n            children: \"\\u2191\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 458,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 453,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 444,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"action-buttons\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: `toggle-button ${autoSubmit ? 'active' : ''}`,\n          onClick: () => setAutoSubmit(prev => !prev),\n          title: autoSubmit ? \"Auto-submit enabled\" : \"Auto-submit disabled\",\n          \"data-active\": autoSubmit,\n          children: autoSubmit ? \"Auto\" : \"Manual\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 463,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `mic-button ${isListening ? 'active' : ''}`,\n          onClick: isListening ? stopListening : startListening,\n          children: isListening ? \"Stop\" : \"Start\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 472,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"settings-button\",\n          children: \"\\u2699\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 479,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"end-button\",\n          onClick: handleEndInterview,\n          children: \"End\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 485,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 462,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 430,\n      columnNumber: 7\n    }, this), showEndConfirmation && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"End interview confirmation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 499,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 498,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-content\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"This action cannot be undone, and you will need to start a new interview to continue.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 502,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 501,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-footer\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"cancel-button\",\n            onClick: cancelEndInterview,\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 505,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"confirm-button\",\n            onClick: confirmEndInterview,\n            children: \"OK\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 508,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 504,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 497,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 496,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 352,\n    columnNumber: 5\n  }, this);\n}\n_s(InterviewAssistant, \"v8UxqYYp5ULMwEQ7wL0OJZDQ9jg=\");\n_c = InterviewAssistant;\nexport default InterviewAssistant;\nvar _c;\n$RefreshReg$(_c, \"InterviewAssistant\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "useCallback", "env", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "InterviewAssistant", "_s", "isListening", "setIsListening", "transcriptMessages", "setTranscriptMessages", "currentTranscript", "setCurrentTranscript", "response", "setResponse", "isLoading", "setIsLoading", "autoSubmit", "setAutoSubmit", "previousTranscript", "setPreviousTranscript", "timerSeconds", "setTimerSeconds", "timerIntervalRef", "pauseTimerRef", "recognitionRef", "transcriptAreaRef", "responseAreaRef", "formatTime", "totalSeconds", "minutes", "Math", "floor", "seconds", "toString", "padStart", "startTimer", "current", "clearInterval", "setInterval", "prev", "stopTimer", "checkForSpeechPause", "clearTimeout", "setTimeout", "console", "log", "hasText", "trim", "sendToGPT", "SpeechRecognition", "window", "webkitSpeechRecognition", "lang", "interimResults", "continuous", "finalTranscript", "on<PERSON>ult", "event", "interimTranscript", "i", "resultIndex", "results", "length", "transcript", "isFinal", "newTranscript", "onerror", "error", "alert", "stop", "scrollTop", "scrollHeight", "startListening", "start", "message", "includes", "stopListening", "<PERSON><PERSON><PERSON><PERSON>", "OPENAI_API_KEY", "userText", "text", "timestamp", "Date", "time", "fetch", "method", "headers", "body", "JSON", "stringify", "model", "messages", "role", "content", "stream", "ok", "Error", "reader", "<PERSON><PERSON><PERSON><PERSON>", "decoder", "TextDecoder", "result", "value", "done", "read", "chunk", "decode", "lines", "split", "filter", "line", "startsWith", "data", "replace", "_json$choices", "_json$choices$", "_json$choices$$delta", "json", "parse", "choices", "delta", "e", "clearTranscript", "clearResponse", "showEndConfirmation", "setShowEndConfirmation", "handleEndInterview", "confirmEndInterview", "cancelEndInterview", "generateQuestion", "questions", "randomQuestion", "random", "className", "children", "ref", "map", "msg", "index", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "type", "onChange", "target", "placeholder", "disabled", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/interview-ai-app/interviewAI/client/src/components/InterviewAssistant.jsx"], "sourcesContent": ["import React, { useState, useRef, useEffect, useCallback } from 'react';\nimport './InterviewAssistant.css';\nimport env from '../utils/env';\n\nfunction InterviewAssistant() {\n  const [isListening, setIsListening] = useState(false);\n  const [transcriptMessages, setTranscriptMessages] = useState([]);\n  const [currentTranscript, setCurrentTranscript] = useState('');\n  const [response, setResponse] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [autoSubmit, setAutoSubmit] = useState(false); // Toggle for auto-submission\n  const [previousTranscript, setPreviousTranscript] = useState(''); // Track previous transcript for comparison\n\n  // Timer state\n  const [timerSeconds, setTimerSeconds] = useState(0);\n  const timerIntervalRef = useRef(null);\n  const pauseTimerRef = useRef(null); // Timer for detecting speech pauses\n\n  const recognitionRef = useRef(null);\n  const transcriptAreaRef = useRef(null);\n  const responseAreaRef = useRef(null);\n\n  // Format seconds to MM:SS\n  const formatTime = useCallback((totalSeconds) => {\n    const minutes = Math.floor(totalSeconds / 60);\n    const seconds = totalSeconds % 60;\n    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\n  }, []);\n\n  // Start the timer\n  const startTimer = useCallback(() => {\n    // Reset timer when starting\n    setTimerSeconds(0);\n\n    // Clear any existing interval\n    if (timerIntervalRef.current) {\n      clearInterval(timerIntervalRef.current);\n    }\n\n    // Start a new interval\n    timerIntervalRef.current = setInterval(() => {\n      setTimerSeconds(prev => prev + 1);\n    }, 1000);\n  }, []);\n\n  // Stop the timer\n  const stopTimer = useCallback(() => {\n    if (timerIntervalRef.current) {\n      clearInterval(timerIntervalRef.current);\n      timerIntervalRef.current = null;\n    }\n  }, []);\n\n  // Function to check for speech pause and auto-submit\n  const checkForSpeechPause = useCallback(() => {\n    // Clear any existing pause timer\n    if (pauseTimerRef.current) {\n      clearTimeout(pauseTimerRef.current);\n    }\n\n    // Set a new pause timer\n    pauseTimerRef.current = setTimeout(() => {\n      console.log(\"Checking auto-submit conditions:\", {\n        autoSubmit,\n        hasText: !!currentTranscript.trim(),\n        isListening,\n        isLoading\n      });\n\n      // Only auto-submit if:\n      // 1. Auto-submit is enabled\n      // 2. We have a non-empty transcript\n      // 3. We're currently listening\n      // 4. We're not already loading a response\n      if (autoSubmit &&\n          currentTranscript.trim() &&\n          isListening &&\n          !isLoading) {\n\n        console.log(\"Auto-submit conditions met, sending to GPT\");\n\n        // Call sendToGPT directly instead of simulating a button click\n        sendToGPT();\n      }\n    }, 2000); // 2 second pause detection\n  }, [autoSubmit, currentTranscript, isListening, isLoading, sendToGPT]);\n\n  // Clean up timer on unmount\n  useEffect(() => {\n    return () => {\n      if (timerIntervalRef.current) {\n        clearInterval(timerIntervalRef.current);\n      }\n      if (pauseTimerRef.current) {\n        clearTimeout(pauseTimerRef.current);\n      }\n    };\n  }, []);\n\n  useEffect(() => {\n    // Initialize speech recognition\n    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n    if (SpeechRecognition) {\n      recognitionRef.current = new SpeechRecognition();\n      recognitionRef.current.lang = 'en-US';\n      recognitionRef.current.interimResults = true;\n      recognitionRef.current.continuous = true;\n\n      let finalTranscript = \"\";\n\n      recognitionRef.current.onresult = (event) => {\n        let interimTranscript = \"\";\n\n        // Store previous transcript for comparison\n        setPreviousTranscript(finalTranscript + interimTranscript);\n\n        for (let i = event.resultIndex; i < event.results.length; i++) {\n          const transcript = event.results[i][0].transcript;\n          if (event.results[i].isFinal) {\n            finalTranscript += transcript + \" \";\n          } else {\n            interimTranscript += transcript;\n          }\n        }\n\n        const newTranscript = finalTranscript + interimTranscript;\n        setCurrentTranscript(newTranscript);\n\n        // Check for pause if transcript has changed\n        if (newTranscript !== previousTranscript) {\n          checkForSpeechPause();\n        }\n      };\n\n      recognitionRef.current.onerror = (event) => {\n        console.error(\"Speech recognition error\", event.error);\n        alert(\"Error occurred: \" + event.error);\n        setIsListening(false);\n        stopTimer();\n      };\n    } else {\n      alert(\"Your browser doesn't support speech recognition. Try Chrome or Edge.\");\n    }\n\n    // Cleanup function\n    return () => {\n      if (recognitionRef.current) {\n        recognitionRef.current.stop();\n      }\n    };\n  }, [stopTimer, checkForSpeechPause, previousTranscript]);\n\n  useEffect(() => {\n    // Auto-scroll transcript area when content changes\n    if (transcriptAreaRef.current) {\n      transcriptAreaRef.current.scrollTop = transcriptAreaRef.current.scrollHeight;\n    }\n  }, [transcriptMessages, currentTranscript]);\n\n  useEffect(() => {\n    // Auto-scroll response area when content changes\n    if (responseAreaRef.current) {\n      responseAreaRef.current.scrollTop = responseAreaRef.current.scrollHeight;\n    }\n  }, [response]);\n\n  const startListening = useCallback(() => {\n    if (recognitionRef.current && !isListening) {\n      try {\n        recognitionRef.current.start();\n        setIsListening(true);\n        startTimer(); // Start the timer when listening begins\n      } catch (error) {\n        console.error(\"Speech recognition error:\", error);\n        // If recognition is already running, stop it first then restart\n        if (error.message.includes(\"already started\")) {\n          recognitionRef.current.stop();\n          setTimeout(() => {\n            recognitionRef.current.start();\n            setIsListening(true);\n            startTimer(); // Start the timer when listening begins\n          }, 100);\n        }\n      }\n    }\n  }, [isListening, startTimer]);\n\n  const stopListening = useCallback(() => {\n    if (recognitionRef.current && isListening) {\n      try {\n        recognitionRef.current.stop();\n        setIsListening(false);\n        stopTimer(); // Stop the timer when listening ends\n      } catch (error) {\n        console.error(\"Speech recognition error:\", error);\n      }\n    }\n  }, [isListening, stopTimer]);\n\n  const sendToGPT = useCallback(async () => {\n    const apiKey = env.OPENAI_API_KEY;\n    const userText = currentTranscript.trim();\n\n    if (!apiKey) {\n      alert(\"Please add your OpenAI API key to the .env file as REACT_APP_OPENAI_API_KEY and restart the development server.\");\n      return;\n    }\n\n    if (!userText) {\n      alert(\"Please record or enter some text to send to GPT.\");\n      return;\n    }\n\n    // Add the current transcript to the messages array\n    setTranscriptMessages(prev => [\n      ...prev,\n      { text: userText, timestamp: new Date(), time: timerSeconds }\n    ]);\n\n    // Clear the current transcript input\n    setCurrentTranscript('');\n\n    setIsLoading(true);\n    setResponse('');\n\n    try {\n      const response = await fetch(\"https://api.openai.com/v1/chat/completions\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          \"Authorization\": `Bearer ${apiKey}`\n        },\n        body: JSON.stringify({\n          model: \"gpt-4.1-nano\",\n          messages: [\n            {\n              role: \"system\",\n              content: \"You are Sensei, an AI interview assistant. Provide detailed, well-structured responses to interview questions. Format your answers with bullet points for clarity. Keep responses concise but comprehensive.\"\n            },\n            { role: \"user\", content: userText }\n          ],\n          stream: true\n        })\n      });\n\n      if (!response.ok || !response.body) throw new Error(\"Failed to stream response.\");\n\n      const reader = response.body.getReader();\n      const decoder = new TextDecoder(\"utf-8\");\n\n      let result = \"\";\n\n      while (true) {\n        const { value, done } = await reader.read();\n        if (done) break;\n\n        const chunk = decoder.decode(value, { stream: true });\n        const lines = chunk.split(\"\\n\").filter(line => line.trim().startsWith(\"data:\"));\n\n        for (const line of lines) {\n          const data = line.replace(/^data: /, '');\n          if (data === \"[DONE]\") break;\n\n          try {\n            const json = JSON.parse(data);\n            const content = json.choices?.[0]?.delta?.content;\n            if (content) {\n              result += content;\n              setResponse(result);\n            }\n          } catch (e) {\n            console.error(\"Error parsing JSON:\", e);\n          }\n        }\n      }\n    } catch (error) {\n      console.error(\"Streaming Error:\", error);\n      setResponse(\"Error occurred: \" + error.message);\n    } finally {\n      if (isListening) {\n        stopListening();\n      }\n      setIsLoading(false);\n    }\n  }, [currentTranscript, isListening, stopListening, timerSeconds]);\n\n  const clearTranscript = () => {\n    setCurrentTranscript('');\n    setTranscriptMessages([]);\n  };\n\n  const clearResponse = () => {\n    setResponse('');\n  };\n\n  // Handle end interview with confirmation popup\n  const [showEndConfirmation, setShowEndConfirmation] = useState(false);\n\n  const handleEndInterview = () => {\n    setShowEndConfirmation(true);\n  };\n\n  const confirmEndInterview = () => {\n    // Here you would handle the actual end interview logic\n    // For example, redirect to a summary page or reset the state\n    setShowEndConfirmation(false);\n    clearTranscript();\n    clearResponse();\n    // You could also redirect to another page\n    // window.location.href = '/';\n  };\n\n  const cancelEndInterview = () => {\n    setShowEndConfirmation(false);\n  };\n\n  const generateQuestion = () => {\n    // This would generate a sample interview question\n    const questions = [\n      \"Tell me about yourself.\",\n      \"What are your greatest strengths?\",\n      \"What do you consider to be your weaknesses?\",\n      \"Why do you want this job?\",\n      \"Where do you see yourself in five years?\",\n      \"Why should we hire you?\",\n      \"What is your greatest professional achievement?\",\n      \"Tell me about a challenge or conflict you've faced at work, and how you dealt with it.\",\n      \"Tell me about a time you demonstrated leadership skills.\",\n      \"What's your management style?\",\n      \"How do you handle stress and pressure?\",\n      \"What are your salary expectations?\",\n      \"What do you like to do outside of work?\",\n      \"What are your career goals?\",\n      \"Why are you leaving your current job?\",\n      \"How do you prioritize your work?\",\n      \"What are you passionate about?\",\n      \"What makes you unique?\",\n      \"What should I know that's not on your resume?\",\n      \"What would your first 30, 60, or 90 days look like in this role?\"\n    ];\n\n    const randomQuestion = questions[Math.floor(Math.random() * questions.length)];\n\n    // Add the generated question directly to the transcript messages\n    setTranscriptMessages(prev => [\n      ...prev,\n      { text: randomQuestion, timestamp: new Date(), time: timerSeconds }\n    ]);\n  };\n\n  return (\n    <div className=\"interview-assistant\">\n      <div className=\"interview-container\">\n        <div className=\"transcription-panel\">\n          {/* <div className=\"panel-header\">\n            <h3>Transcription Messages</h3>\n            <p className=\"panel-description\">\n              Interviewer questions will be transcribed in this section for your review and response.\n            </p>\n          </div> */}\n\n          <div className=\"panel-content\">\n            <div\n              ref={transcriptAreaRef}\n              className=\"transcript-content\"\n            >\n              {transcriptMessages.length > 0 ? (\n                <div className=\"transcript-messages\">\n                  {transcriptMessages.map((msg, index) => (\n                    <div key={index} className=\"transcript-message\">\n                      <div className=\"timestamp\">{formatTime(msg.time)}</div>\n                      <div className=\"message-text\">{msg.text}</div>\n                    </div>\n                  ))}\n                </div>\n              ) : (\n                <div className=\"empty-state\">Your interview questions will appear here</div>\n              )}\n\n              {currentTranscript && (\n                <div className=\"transcript-message current\">\n                  <div className=\"timestamp\">{formatTime(timerSeconds)}</div>\n                  <div className=\"message-text\">{currentTranscript}</div>\n                </div>\n              )}\n            </div>\n          </div>\n\n          <div className=\"panel-footer\">\n            <div className=\"user-label\">Interviewer</div>\n            <button\n              className=\"generate-button\"\n              onClick={generateQuestion}\n            >\n              Generate\n            </button>\n          </div>\n        </div>\n\n        <div className=\"response-panel\">\n          <div className=\"panel-header\">\n            <h3>Answers</h3>\n            <p className=\"panel-description\">\n              Displayed here are the intelligent responses generated by Sensei. Whenever the interviewer concludes a question,\n              provides a tailored answer for you.\n            </p>\n          </div>\n\n          <div className=\"panel-content\">\n            <div\n              ref={responseAreaRef}\n              className=\"response-content\"\n            >\n              {response ? (\n                <div className=\"response-message\">\n                  <div className=\"message-text\">{response}</div>\n                </div>\n              ) : (\n                <div className=\"empty-state\">InterviewAssistant's responses will appear here</div>\n              )}\n            </div>\n          </div>\n\n          <div className=\"panel-footer\">\n            <div className=\"user-label\">InterviewAssistant's</div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"controls-container\">\n        <div className=\"timer-display\">\n          {isListening ? (\n            <>\n              <span className=\"recording-dot\"></span>\n              {formatTime(timerSeconds)}\n            </>\n          ) : (\n            formatTime(timerSeconds)\n          )}\n        </div>\n\n        <div className=\"limit-indicator\">15-minute limit</div>\n\n        <div className=\"input-controls\">\n          <input\n            type=\"text\"\n            className=\"text-input\"\n            value={currentTranscript}\n            onChange={(e) => setCurrentTranscript(e.target.value)}\n            placeholder=\"Type your question here...\"\n          />\n\n          <button\n            className=\"submit-button\"\n            onClick={sendToGPT}\n            disabled={isLoading || !currentTranscript.trim()}\n          >\n            <span className=\"arrow-icon\">↑</span>\n          </button>\n        </div>\n\n        <div className=\"action-buttons\">\n          <button\n            className={`toggle-button ${autoSubmit ? 'active' : ''}`}\n            onClick={() => setAutoSubmit(prev => !prev)}\n            title={autoSubmit ? \"Auto-submit enabled\" : \"Auto-submit disabled\"}\n            data-active={autoSubmit}\n          >\n            {autoSubmit ? \"Auto\" : \"Manual\"}\n          </button>\n\n          <button\n            className={`mic-button ${isListening ? 'active' : ''}`}\n            onClick={isListening ? stopListening : startListening}\n          >\n            {isListening ? \"Stop\" : \"Start\"}\n          </button>\n\n          <button\n            className=\"settings-button\"\n          >\n            ⚙️\n          </button>\n\n          <button\n            className=\"end-button\"\n            onClick={handleEndInterview}\n          >\n            End\n          </button>\n        </div>\n      </div>\n\n      {/* End Interview Confirmation Modal */}\n      {showEndConfirmation && (\n        <div className=\"modal-overlay\">\n          <div className=\"modal-container\">\n            <div className=\"modal-header\">\n              <h3>End interview confirmation</h3>\n            </div>\n            <div className=\"modal-content\">\n              <p>This action cannot be undone, and you will need to start a new interview to continue.</p>\n            </div>\n            <div className=\"modal-footer\">\n              <button className=\"cancel-button\" onClick={cancelEndInterview}>\n                Cancel\n              </button>\n              <button className=\"confirm-button\" onClick={confirmEndInterview}>\n                OK\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n\nexport default InterviewAssistant;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AACvE,OAAO,0BAA0B;AACjC,OAAOC,GAAG,MAAM,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/B,SAASC,kBAAkBA,CAAA,EAAG;EAAAC,EAAA;EAC5B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACa,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAACe,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACiB,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACmB,SAAS,EAAEC,YAAY,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACqB,UAAU,EAAEC,aAAa,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACrD,MAAM,CAACuB,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;;EAElE;EACA,MAAM,CAACyB,YAAY,EAAEC,eAAe,CAAC,GAAG1B,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM2B,gBAAgB,GAAG1B,MAAM,CAAC,IAAI,CAAC;EACrC,MAAM2B,aAAa,GAAG3B,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;;EAEpC,MAAM4B,cAAc,GAAG5B,MAAM,CAAC,IAAI,CAAC;EACnC,MAAM6B,iBAAiB,GAAG7B,MAAM,CAAC,IAAI,CAAC;EACtC,MAAM8B,eAAe,GAAG9B,MAAM,CAAC,IAAI,CAAC;;EAEpC;EACA,MAAM+B,UAAU,GAAG7B,WAAW,CAAE8B,YAAY,IAAK;IAC/C,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,YAAY,GAAG,EAAE,CAAC;IAC7C,MAAMI,OAAO,GAAGJ,YAAY,GAAG,EAAE;IACjC,OAAO,GAAGC,OAAO,CAACI,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,OAAO,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACxF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,UAAU,GAAGrC,WAAW,CAAC,MAAM;IACnC;IACAuB,eAAe,CAAC,CAAC,CAAC;;IAElB;IACA,IAAIC,gBAAgB,CAACc,OAAO,EAAE;MAC5BC,aAAa,CAACf,gBAAgB,CAACc,OAAO,CAAC;IACzC;;IAEA;IACAd,gBAAgB,CAACc,OAAO,GAAGE,WAAW,CAAC,MAAM;MAC3CjB,eAAe,CAACkB,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IACnC,CAAC,EAAE,IAAI,CAAC;EACV,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,SAAS,GAAG1C,WAAW,CAAC,MAAM;IAClC,IAAIwB,gBAAgB,CAACc,OAAO,EAAE;MAC5BC,aAAa,CAACf,gBAAgB,CAACc,OAAO,CAAC;MACvCd,gBAAgB,CAACc,OAAO,GAAG,IAAI;IACjC;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMK,mBAAmB,GAAG3C,WAAW,CAAC,MAAM;IAC5C;IACA,IAAIyB,aAAa,CAACa,OAAO,EAAE;MACzBM,YAAY,CAACnB,aAAa,CAACa,OAAO,CAAC;IACrC;;IAEA;IACAb,aAAa,CAACa,OAAO,GAAGO,UAAU,CAAC,MAAM;MACvCC,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE;QAC9C7B,UAAU;QACV8B,OAAO,EAAE,CAAC,CAACpC,iBAAiB,CAACqC,IAAI,CAAC,CAAC;QACnCzC,WAAW;QACXQ;MACF,CAAC,CAAC;;MAEF;MACA;MACA;MACA;MACA;MACA,IAAIE,UAAU,IACVN,iBAAiB,CAACqC,IAAI,CAAC,CAAC,IACxBzC,WAAW,IACX,CAACQ,SAAS,EAAE;QAEd8B,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;;QAEzD;QACAG,SAAS,CAAC,CAAC;MACb;IACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;EACZ,CAAC,EAAE,CAAChC,UAAU,EAAEN,iBAAiB,EAAEJ,WAAW,EAAEQ,SAAS,EAAEkC,SAAS,CAAC,CAAC;;EAEtE;EACAnD,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX,IAAIyB,gBAAgB,CAACc,OAAO,EAAE;QAC5BC,aAAa,CAACf,gBAAgB,CAACc,OAAO,CAAC;MACzC;MACA,IAAIb,aAAa,CAACa,OAAO,EAAE;QACzBM,YAAY,CAACnB,aAAa,CAACa,OAAO,CAAC;MACrC;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAENvC,SAAS,CAAC,MAAM;IACd;IACA,MAAMoD,iBAAiB,GAAGC,MAAM,CAACD,iBAAiB,IAAIC,MAAM,CAACC,uBAAuB;IACpF,IAAIF,iBAAiB,EAAE;MACrBzB,cAAc,CAACY,OAAO,GAAG,IAAIa,iBAAiB,CAAC,CAAC;MAChDzB,cAAc,CAACY,OAAO,CAACgB,IAAI,GAAG,OAAO;MACrC5B,cAAc,CAACY,OAAO,CAACiB,cAAc,GAAG,IAAI;MAC5C7B,cAAc,CAACY,OAAO,CAACkB,UAAU,GAAG,IAAI;MAExC,IAAIC,eAAe,GAAG,EAAE;MAExB/B,cAAc,CAACY,OAAO,CAACoB,QAAQ,GAAIC,KAAK,IAAK;QAC3C,IAAIC,iBAAiB,GAAG,EAAE;;QAE1B;QACAvC,qBAAqB,CAACoC,eAAe,GAAGG,iBAAiB,CAAC;QAE1D,KAAK,IAAIC,CAAC,GAAGF,KAAK,CAACG,WAAW,EAAED,CAAC,GAAGF,KAAK,CAACI,OAAO,CAACC,MAAM,EAAEH,CAAC,EAAE,EAAE;UAC7D,MAAMI,UAAU,GAAGN,KAAK,CAACI,OAAO,CAACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAACI,UAAU;UACjD,IAAIN,KAAK,CAACI,OAAO,CAACF,CAAC,CAAC,CAACK,OAAO,EAAE;YAC5BT,eAAe,IAAIQ,UAAU,GAAG,GAAG;UACrC,CAAC,MAAM;YACLL,iBAAiB,IAAIK,UAAU;UACjC;QACF;QAEA,MAAME,aAAa,GAAGV,eAAe,GAAGG,iBAAiB;QACzD/C,oBAAoB,CAACsD,aAAa,CAAC;;QAEnC;QACA,IAAIA,aAAa,KAAK/C,kBAAkB,EAAE;UACxCuB,mBAAmB,CAAC,CAAC;QACvB;MACF,CAAC;MAEDjB,cAAc,CAACY,OAAO,CAAC8B,OAAO,GAAIT,KAAK,IAAK;QAC1Cb,OAAO,CAACuB,KAAK,CAAC,0BAA0B,EAAEV,KAAK,CAACU,KAAK,CAAC;QACtDC,KAAK,CAAC,kBAAkB,GAAGX,KAAK,CAACU,KAAK,CAAC;QACvC5D,cAAc,CAAC,KAAK,CAAC;QACrBiC,SAAS,CAAC,CAAC;MACb,CAAC;IACH,CAAC,MAAM;MACL4B,KAAK,CAAC,sEAAsE,CAAC;IAC/E;;IAEA;IACA,OAAO,MAAM;MACX,IAAI5C,cAAc,CAACY,OAAO,EAAE;QAC1BZ,cAAc,CAACY,OAAO,CAACiC,IAAI,CAAC,CAAC;MAC/B;IACF,CAAC;EACH,CAAC,EAAE,CAAC7B,SAAS,EAAEC,mBAAmB,EAAEvB,kBAAkB,CAAC,CAAC;EAExDrB,SAAS,CAAC,MAAM;IACd;IACA,IAAI4B,iBAAiB,CAACW,OAAO,EAAE;MAC7BX,iBAAiB,CAACW,OAAO,CAACkC,SAAS,GAAG7C,iBAAiB,CAACW,OAAO,CAACmC,YAAY;IAC9E;EACF,CAAC,EAAE,CAAC/D,kBAAkB,EAAEE,iBAAiB,CAAC,CAAC;EAE3Cb,SAAS,CAAC,MAAM;IACd;IACA,IAAI6B,eAAe,CAACU,OAAO,EAAE;MAC3BV,eAAe,CAACU,OAAO,CAACkC,SAAS,GAAG5C,eAAe,CAACU,OAAO,CAACmC,YAAY;IAC1E;EACF,CAAC,EAAE,CAAC3D,QAAQ,CAAC,CAAC;EAEd,MAAM4D,cAAc,GAAG1E,WAAW,CAAC,MAAM;IACvC,IAAI0B,cAAc,CAACY,OAAO,IAAI,CAAC9B,WAAW,EAAE;MAC1C,IAAI;QACFkB,cAAc,CAACY,OAAO,CAACqC,KAAK,CAAC,CAAC;QAC9BlE,cAAc,CAAC,IAAI,CAAC;QACpB4B,UAAU,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,OAAOgC,KAAK,EAAE;QACdvB,OAAO,CAACuB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD;QACA,IAAIA,KAAK,CAACO,OAAO,CAACC,QAAQ,CAAC,iBAAiB,CAAC,EAAE;UAC7CnD,cAAc,CAACY,OAAO,CAACiC,IAAI,CAAC,CAAC;UAC7B1B,UAAU,CAAC,MAAM;YACfnB,cAAc,CAACY,OAAO,CAACqC,KAAK,CAAC,CAAC;YAC9BlE,cAAc,CAAC,IAAI,CAAC;YACpB4B,UAAU,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,EAAE,GAAG,CAAC;QACT;MACF;IACF;EACF,CAAC,EAAE,CAAC7B,WAAW,EAAE6B,UAAU,CAAC,CAAC;EAE7B,MAAMyC,aAAa,GAAG9E,WAAW,CAAC,MAAM;IACtC,IAAI0B,cAAc,CAACY,OAAO,IAAI9B,WAAW,EAAE;MACzC,IAAI;QACFkB,cAAc,CAACY,OAAO,CAACiC,IAAI,CAAC,CAAC;QAC7B9D,cAAc,CAAC,KAAK,CAAC;QACrBiC,SAAS,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,OAAO2B,KAAK,EAAE;QACdvB,OAAO,CAACuB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACnD;IACF;EACF,CAAC,EAAE,CAAC7D,WAAW,EAAEkC,SAAS,CAAC,CAAC;EAE5B,MAAMQ,SAAS,GAAGlD,WAAW,CAAC,YAAY;IACxC,MAAM+E,MAAM,GAAG9E,GAAG,CAAC+E,cAAc;IACjC,MAAMC,QAAQ,GAAGrE,iBAAiB,CAACqC,IAAI,CAAC,CAAC;IAEzC,IAAI,CAAC8B,MAAM,EAAE;MACXT,KAAK,CAAC,iHAAiH,CAAC;MACxH;IACF;IAEA,IAAI,CAACW,QAAQ,EAAE;MACbX,KAAK,CAAC,kDAAkD,CAAC;MACzD;IACF;;IAEA;IACA3D,qBAAqB,CAAC8B,IAAI,IAAI,CAC5B,GAAGA,IAAI,EACP;MAAEyC,IAAI,EAAED,QAAQ;MAAEE,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;MAAEC,IAAI,EAAE/D;IAAa,CAAC,CAC9D,CAAC;;IAEF;IACAT,oBAAoB,CAAC,EAAE,CAAC;IAExBI,YAAY,CAAC,IAAI,CAAC;IAClBF,WAAW,CAAC,EAAE,CAAC;IAEf,IAAI;MACF,MAAMD,QAAQ,GAAG,MAAMwE,KAAK,CAAC,4CAA4C,EAAE;QACzEC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAE,UAAUT,MAAM;QACnC,CAAC;QACDU,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBC,KAAK,EAAE,cAAc;UACrBC,QAAQ,EAAE,CACR;YACEC,IAAI,EAAE,QAAQ;YACdC,OAAO,EAAE;UACX,CAAC,EACD;YAAED,IAAI,EAAE,MAAM;YAAEC,OAAO,EAAEd;UAAS,CAAC,CACpC;UACDe,MAAM,EAAE;QACV,CAAC;MACH,CAAC,CAAC;MAEF,IAAI,CAAClF,QAAQ,CAACmF,EAAE,IAAI,CAACnF,QAAQ,CAAC2E,IAAI,EAAE,MAAM,IAAIS,KAAK,CAAC,4BAA4B,CAAC;MAEjF,MAAMC,MAAM,GAAGrF,QAAQ,CAAC2E,IAAI,CAACW,SAAS,CAAC,CAAC;MACxC,MAAMC,OAAO,GAAG,IAAIC,WAAW,CAAC,OAAO,CAAC;MAExC,IAAIC,MAAM,GAAG,EAAE;MAEf,OAAO,IAAI,EAAE;QACX,MAAM;UAAEC,KAAK;UAAEC;QAAK,CAAC,GAAG,MAAMN,MAAM,CAACO,IAAI,CAAC,CAAC;QAC3C,IAAID,IAAI,EAAE;QAEV,MAAME,KAAK,GAAGN,OAAO,CAACO,MAAM,CAACJ,KAAK,EAAE;UAAER,MAAM,EAAE;QAAK,CAAC,CAAC;QACrD,MAAMa,KAAK,GAAGF,KAAK,CAACG,KAAK,CAAC,IAAI,CAAC,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAAC/D,IAAI,CAAC,CAAC,CAACgE,UAAU,CAAC,OAAO,CAAC,CAAC;QAE/E,KAAK,MAAMD,IAAI,IAAIH,KAAK,EAAE;UACxB,MAAMK,IAAI,GAAGF,IAAI,CAACG,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;UACxC,IAAID,IAAI,KAAK,QAAQ,EAAE;UAEvB,IAAI;YAAA,IAAAE,aAAA,EAAAC,cAAA,EAAAC,oBAAA;YACF,MAAMC,IAAI,GAAG7B,IAAI,CAAC8B,KAAK,CAACN,IAAI,CAAC;YAC7B,MAAMnB,OAAO,IAAAqB,aAAA,GAAGG,IAAI,CAACE,OAAO,cAAAL,aAAA,wBAAAC,cAAA,GAAZD,aAAA,CAAe,CAAC,CAAC,cAAAC,cAAA,wBAAAC,oBAAA,GAAjBD,cAAA,CAAmBK,KAAK,cAAAJ,oBAAA,uBAAxBA,oBAAA,CAA0BvB,OAAO;YACjD,IAAIA,OAAO,EAAE;cACXQ,MAAM,IAAIR,OAAO;cACjBhF,WAAW,CAACwF,MAAM,CAAC;YACrB;UACF,CAAC,CAAC,OAAOoB,CAAC,EAAE;YACV7E,OAAO,CAACuB,KAAK,CAAC,qBAAqB,EAAEsD,CAAC,CAAC;UACzC;QACF;MACF;IACF,CAAC,CAAC,OAAOtD,KAAK,EAAE;MACdvB,OAAO,CAACuB,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MACxCtD,WAAW,CAAC,kBAAkB,GAAGsD,KAAK,CAACO,OAAO,CAAC;IACjD,CAAC,SAAS;MACR,IAAIpE,WAAW,EAAE;QACfsE,aAAa,CAAC,CAAC;MACjB;MACA7D,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,CAACL,iBAAiB,EAAEJ,WAAW,EAAEsE,aAAa,EAAExD,YAAY,CAAC,CAAC;EAEjE,MAAMsG,eAAe,GAAGA,CAAA,KAAM;IAC5B/G,oBAAoB,CAAC,EAAE,CAAC;IACxBF,qBAAqB,CAAC,EAAE,CAAC;EAC3B,CAAC;EAED,MAAMkH,aAAa,GAAGA,CAAA,KAAM;IAC1B9G,WAAW,CAAC,EAAE,CAAC;EACjB,CAAC;;EAED;EACA,MAAM,CAAC+G,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGlI,QAAQ,CAAC,KAAK,CAAC;EAErE,MAAMmI,kBAAkB,GAAGA,CAAA,KAAM;IAC/BD,sBAAsB,CAAC,IAAI,CAAC;EAC9B,CAAC;EAED,MAAME,mBAAmB,GAAGA,CAAA,KAAM;IAChC;IACA;IACAF,sBAAsB,CAAC,KAAK,CAAC;IAC7BH,eAAe,CAAC,CAAC;IACjBC,aAAa,CAAC,CAAC;IACf;IACA;EACF,CAAC;EAED,MAAMK,kBAAkB,GAAGA,CAAA,KAAM;IAC/BH,sBAAsB,CAAC,KAAK,CAAC;EAC/B,CAAC;EAED,MAAMI,gBAAgB,GAAGA,CAAA,KAAM;IAC7B;IACA,MAAMC,SAAS,GAAG,CAChB,yBAAyB,EACzB,mCAAmC,EACnC,6CAA6C,EAC7C,2BAA2B,EAC3B,0CAA0C,EAC1C,yBAAyB,EACzB,iDAAiD,EACjD,wFAAwF,EACxF,0DAA0D,EAC1D,+BAA+B,EAC/B,wCAAwC,EACxC,oCAAoC,EACpC,yCAAyC,EACzC,6BAA6B,EAC7B,uCAAuC,EACvC,kCAAkC,EAClC,gCAAgC,EAChC,wBAAwB,EACxB,+CAA+C,EAC/C,kEAAkE,CACnE;IAED,MAAMC,cAAc,GAAGD,SAAS,CAACpG,IAAI,CAACC,KAAK,CAACD,IAAI,CAACsG,MAAM,CAAC,CAAC,GAAGF,SAAS,CAACpE,MAAM,CAAC,CAAC;;IAE9E;IACArD,qBAAqB,CAAC8B,IAAI,IAAI,CAC5B,GAAGA,IAAI,EACP;MAAEyC,IAAI,EAAEmD,cAAc;MAAElD,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;MAAEC,IAAI,EAAE/D;IAAa,CAAC,CACpE,CAAC;EACJ,CAAC;EAED,oBACEnB,OAAA;IAAKoI,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAClCrI,OAAA;MAAKoI,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClCrI,OAAA;QAAKoI,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAQlCrI,OAAA;UAAKoI,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5BrI,OAAA;YACEsI,GAAG,EAAE9G,iBAAkB;YACvB4G,SAAS,EAAC,oBAAoB;YAAAC,QAAA,GAE7B9H,kBAAkB,CAACsD,MAAM,GAAG,CAAC,gBAC5B7D,OAAA;cAAKoI,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EACjC9H,kBAAkB,CAACgI,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBACjCzI,OAAA;gBAAiBoI,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBAC7CrI,OAAA;kBAAKoI,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAE3G,UAAU,CAAC8G,GAAG,CAACtD,IAAI;gBAAC;kBAAAwD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACvD7I,OAAA;kBAAKoI,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAEG,GAAG,CAACzD;gBAAI;kBAAA2D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA,GAFtCJ,KAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,gBAEN7I,OAAA;cAAKoI,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAyC;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAC5E,EAEApI,iBAAiB,iBAChBT,OAAA;cAAKoI,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBACzCrI,OAAA;gBAAKoI,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAE3G,UAAU,CAACP,YAAY;cAAC;gBAAAuH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC3D7I,OAAA;gBAAKoI,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAE5H;cAAiB;gBAAAiI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN7I,OAAA;UAAKoI,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BrI,OAAA;YAAKoI,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAW;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC7C7I,OAAA;YACEoI,SAAS,EAAC,iBAAiB;YAC3BU,OAAO,EAAEd,gBAAiB;YAAAK,QAAA,EAC3B;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN7I,OAAA;QAAKoI,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BrI,OAAA;UAAKoI,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BrI,OAAA;YAAAqI,QAAA,EAAI;UAAO;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChB7I,OAAA;YAAGoI,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAC;UAGjC;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAEN7I,OAAA;UAAKoI,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5BrI,OAAA;YACEsI,GAAG,EAAE7G,eAAgB;YACrB2G,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAE3B1H,QAAQ,gBACPX,OAAA;cAAKoI,SAAS,EAAC,kBAAkB;cAAAC,QAAA,eAC/BrI,OAAA;gBAAKoI,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAE1H;cAAQ;gBAAA+H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,gBAEN7I,OAAA;cAAKoI,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAA+C;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAClF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN7I,OAAA;UAAKoI,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BrI,OAAA;YAAKoI,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAoB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN7I,OAAA;MAAKoI,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBACjCrI,OAAA;QAAKoI,SAAS,EAAC,eAAe;QAAAC,QAAA,EAC3BhI,WAAW,gBACVL,OAAA,CAAAE,SAAA;UAAAmI,QAAA,gBACErI,OAAA;YAAMoI,SAAS,EAAC;UAAe;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EACtCnH,UAAU,CAACP,YAAY,CAAC;QAAA,eACzB,CAAC,GAEHO,UAAU,CAACP,YAAY;MACxB;QAAAuH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEN7I,OAAA;QAAKoI,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAC;MAAe;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAEtD7I,OAAA;QAAKoI,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BrI,OAAA;UACE+I,IAAI,EAAC,MAAM;UACXX,SAAS,EAAC,YAAY;UACtB/B,KAAK,EAAE5F,iBAAkB;UACzBuI,QAAQ,EAAGxB,CAAC,IAAK9G,oBAAoB,CAAC8G,CAAC,CAACyB,MAAM,CAAC5C,KAAK,CAAE;UACtD6C,WAAW,EAAC;QAA4B;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eAEF7I,OAAA;UACEoI,SAAS,EAAC,eAAe;UACzBU,OAAO,EAAE/F,SAAU;UACnBoG,QAAQ,EAAEtI,SAAS,IAAI,CAACJ,iBAAiB,CAACqC,IAAI,CAAC,CAAE;UAAAuF,QAAA,eAEjDrI,OAAA;YAAMoI,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAC;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEN7I,OAAA;QAAKoI,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BrI,OAAA;UACEoI,SAAS,EAAE,iBAAiBrH,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;UACzD+H,OAAO,EAAEA,CAAA,KAAM9H,aAAa,CAACsB,IAAI,IAAI,CAACA,IAAI,CAAE;UAC5C8G,KAAK,EAAErI,UAAU,GAAG,qBAAqB,GAAG,sBAAuB;UACnE,eAAaA,UAAW;UAAAsH,QAAA,EAEvBtH,UAAU,GAAG,MAAM,GAAG;QAAQ;UAAA2H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eAET7I,OAAA;UACEoI,SAAS,EAAE,cAAc/H,WAAW,GAAG,QAAQ,GAAG,EAAE,EAAG;UACvDyI,OAAO,EAAEzI,WAAW,GAAGsE,aAAa,GAAGJ,cAAe;UAAA8D,QAAA,EAErDhI,WAAW,GAAG,MAAM,GAAG;QAAO;UAAAqI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eAET7I,OAAA;UACEoI,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAC5B;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAET7I,OAAA;UACEoI,SAAS,EAAC,YAAY;UACtBU,OAAO,EAAEjB,kBAAmB;UAAAQ,QAAA,EAC7B;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLlB,mBAAmB,iBAClB3H,OAAA;MAAKoI,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC5BrI,OAAA;QAAKoI,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BrI,OAAA;UAAKoI,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BrI,OAAA;YAAAqI,QAAA,EAAI;UAA0B;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,eACN7I,OAAA;UAAKoI,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5BrI,OAAA;YAAAqI,QAAA,EAAG;UAAqF;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzF,CAAC,eACN7I,OAAA;UAAKoI,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BrI,OAAA;YAAQoI,SAAS,EAAC,eAAe;YAACU,OAAO,EAAEf,kBAAmB;YAAAM,QAAA,EAAC;UAE/D;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT7I,OAAA;YAAQoI,SAAS,EAAC,gBAAgB;YAACU,OAAO,EAAEhB,mBAAoB;YAAAO,QAAA,EAAC;UAEjE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACzI,EAAA,CAhgBQD,kBAAkB;AAAAkJ,EAAA,GAAlBlJ,kBAAkB;AAkgB3B,eAAeA,kBAAkB;AAAC,IAAAkJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}