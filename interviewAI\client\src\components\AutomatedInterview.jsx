import React, { useState, useRef, useEffect, useCallback } from 'react';
import './AutomatedInterview.css';
import env from '../utils/env';

function AutomatedInterview() {
  // Main states
  const [step, setStep] = useState('configure'); // configure, connect, interview
  const [isConnected, setIsConnected] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [transcriptMessages, setTranscriptMessages] = useState([]);
  const [currentTranscript, setCurrentTranscript] = useState('');
  const [response, setResponse] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [usingGoogleAPI, setUsingGoogleAPI] = useState(false);

  // Configuration states
  const [specialization, setSpecialization] = useState('software-engineering');
  const [language, setLanguage] = useState('english');

  // Timer state
  const [timerSeconds, setTimerSeconds] = useState(0);
  const timerIntervalRef = useRef(null);
  const pauseTimerRef = useRef(null); // Timer for detecting speech pauses

  // Refs
  const recognitionRef = useRef(null);
  const transcriptAreaRef = useRef(null);
  const responseAreaRef = useRef(null);
  const mediaStreamRef = useRef(null); // For screen sharing stream

  // Format seconds to MM:SS
  const formatTime = useCallback((totalSeconds) => {
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }, []);

  // Start the timer
  const startTimer = useCallback(() => {
    // Reset timer when starting
    setTimerSeconds(0);

    // Clear any existing interval
    if (timerIntervalRef.current) {
      clearInterval(timerIntervalRef.current);
    }

    // Start a new interval
    timerIntervalRef.current = setInterval(() => {
      setTimerSeconds(prev => prev + 1);
    }, 1000);
  }, []);

  // Stop the timer
  const stopTimer = useCallback(() => {
    if (timerIntervalRef.current) {
      clearInterval(timerIntervalRef.current);
      timerIntervalRef.current = null;
    }
  }, []);

  // Send transcript to GPT for response
  const sendToGPT = useCallback(async (text) => {
    console.log("sendToGPT called with text:", text);

    const apiKey = env.OPENAI_API_KEY;
    const userText = text || currentTranscript.trim();

    if (!apiKey) {
      alert("Please add your OpenAI API key to the .env file as REACT_APP_OPENAI_API_KEY and restart the development server.");
      return;
    }

    if (!userText) {
      console.warn("No transcript to send.");
      return;
    }

    console.log("Processing transcript:", userText);

    // Note: We're not adding to transcriptMessages here anymore
    // That's now handled in the checkForSpeechPause function
    // to avoid duplicate messages

    // Clear the current transcript input if it matches what we're sending
    // This prevents sending the same text twice
    if (currentTranscript.trim() === userText) {
      console.log("Clearing current transcript");
      setCurrentTranscript('');
    }

    setIsLoading(true);
    setResponse('');

    try {
      const response = await fetch("https://api.openai.com/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${apiKey}`
        },
        body: JSON.stringify({
          model: "gpt-4.1-nano",
          messages: [
            {
              role: "system",
              content: `You are an AI interview assistant for ${specialization} interviews. Provide detailed, well-structured responses to interview questions. Format your answers with bullet points for clarity. Keep responses concise but comprehensive.`
            },
            { role: "user", content: userText }
          ],
          stream: true
        })
      });

      if (!response.ok || !response.body) throw new Error("Failed to stream response.");

      const reader = response.body.getReader();
      const decoder = new TextDecoder("utf-8");

      let result = "";

      while (true) {
        const { value, done } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value, { stream: true });
        const lines = chunk.split("\n").filter(line => line.trim().startsWith("data:"));

        for (const line of lines) {
          const data = line.replace(/^data: /, '');
          if (data === "[DONE]") break;

          try {
            const json = JSON.parse(data);
            const content = json.choices?.[0]?.delta?.content;
            if (content) {
              result += content;
              setResponse(result);
            }
          } catch (e) {
            console.error("Error parsing JSON:", e);
          }
        }
      }
    } catch (error) {
      console.error("Streaming Error:", error);
      setResponse("Error occurred: " + error.message);
    } finally {
      setIsLoading(false);
    }
  }, [currentTranscript, specialization]); // timerSeconds is not needed here

  // Function to check for speech pause and auto-submit
  const checkForSpeechPause = useCallback(() => {
    console.log("Checking for speech pause...");

    // Clear any existing pause timer
    if (pauseTimerRef.current) {
      clearTimeout(pauseTimerRef.current);
      console.log("Cleared existing pause timer");
    }

    // Set a new pause timer
    pauseTimerRef.current = setTimeout(() => {
      console.log("Pause timer triggered");

      // Only auto-submit if:
      // 1. We have a non-empty transcript
      // 2. We're currently listening
      // 3. We're not already loading a response
      const userText = currentTranscript.trim();

      console.log("Checking conditions for auto-submit:", {
        hasText: !!userText,
        isListening,
        notLoading: !isLoading
      });

      if (userText && isListening && !isLoading) {
        console.log("Auto-submitting transcript:", userText);

        // Add the transcript to the messages array
        setTranscriptMessages(prev => {
          const newMessages = [
            ...prev,
            { text: userText, timestamp: new Date(), time: timerSeconds }
          ];
          console.log("Updated transcript messages:", newMessages);
          return newMessages;
        });

        // Clear the current transcript
        setCurrentTranscript("");

        // Send to GPT for response
        sendToGPT(userText);
      }
    }, 2000); // 2 second pause detection (increased from 1.5s for better reliability)
  }, [currentTranscript, isListening, isLoading, sendToGPT, timerSeconds]);

  // Clean up timer on unmount
  useEffect(() => {
    return () => {
      if (timerIntervalRef.current) {
        clearInterval(timerIntervalRef.current);
      }
      if (pauseTimerRef.current) {
        clearTimeout(pauseTimerRef.current);
      }
    };
  }, []);

  // Initialize speech recognition
  useEffect(() => {
    try {
      console.log("Initializing speech recognition...");

      // Use the browser's built-in SpeechRecognition API directly for better reliability
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;

      if (!SpeechRecognition) {
        throw new Error("Speech recognition is not supported in this browser");
      }

      // Create a new recognition instance
      recognitionRef.current = new SpeechRecognition();

      // Configure the recognition
      recognitionRef.current.lang = language === 'english' ? 'en-US' :
                                   language === 'spanish' ? 'es-ES' :
                                   language === 'french' ? 'fr-FR' :
                                   language === 'german' ? 'de-DE' :
                                   language === 'chinese' ? 'zh-CN' :
                                   language === 'japanese' ? 'ja-JP' : 'en-US';
      recognitionRef.current.continuous = true;
      recognitionRef.current.interimResults = true;

      // Check if Google API is available
      const hasGoogleAPI = !env.hasGoogleSpeechAPI();
      setUsingGoogleAPI(hasGoogleAPI);
      console.log(`Using ${hasGoogleAPI ? 'Google Speech-to-Text API Avaialable' : 'Web Speech API'}`);

      // Reset the transcript
      let finalTranscript = "";

      console.log("Setting up speech recognition event handlers...");

      recognitionRef.current.onresult = (event) => {
        console.log("Speech recognition result received:", event);
        let interimTranscript = "";

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const transcript = event.results[i][0].transcript;
          const confidence = event.results[i][0].confidence;
          console.log(`Transcript: "${transcript}" (Confidence: ${confidence.toFixed(2)})`);

          if (event.results[i].isFinal) {
            finalTranscript += transcript + " ";
            console.log("Final transcript updated:", finalTranscript);
          } else {
            interimTranscript += transcript;
            console.log("Interim transcript updated:", interimTranscript);
          }
        }

        const newTranscript = finalTranscript + interimTranscript;
        console.log("Setting current transcript to:", newTranscript);

        // Force UI update with the new transcript - use a callback to ensure state is updated
        setCurrentTranscript(prev => {
          console.log(`Updating transcript from "${prev}" to "${newTranscript}"`);

          // Force a DOM update by dispatching a custom event
          window.dispatchEvent(new CustomEvent('transcriptUpdated', {
            detail: { transcript: newTranscript }
          }));

          return newTranscript;
        });

        // Also update the DOM directly as a fallback
        const transcriptElement = document.querySelector('.message-text .active-transcript');
        if (transcriptElement) {
          transcriptElement.textContent = newTranscript;
        }

        // Check for pause if transcript has changed
        if (newTranscript.trim() !== "") {
          console.log("Transcript not empty, checking for speech pause");
          checkForSpeechPause();
        }
      };

      recognitionRef.current.onerror = (event) => {
        console.error("Speech recognition error", event.error, event);
        if (event.error !== 'no-speech') {
          console.error("Critical speech recognition error:", event.error);
          alert("Error occurred: " + event.error);
          setIsListening(false);
          stopTimer();
        } else {
          console.log("No speech detected, continuing to listen");
        }
      };

      recognitionRef.current.onend = () => {
        console.log("Speech recognition ended");
        // Restart recognition if we're still supposed to be listening
        if (isListening) {
          console.log("Still listening, attempting to restart recognition");
          try {
            recognitionRef.current.start();
            console.log("Recognition restarted successfully");
          } catch (error) {
            console.error("Failed to restart recognition:", error);
          }
        } else {
          console.log("Not listening anymore, not restarting recognition");
        }
      };

      // We're not using MediaRecorder anymore to avoid errors
      console.log("Using built-in speech recognition without MediaRecorder");

    } catch (error) {
      console.error("Failed to initialize speech recognition:", error);
      alert("Your browser doesn't support speech recognition. Try Chrome or Edge.");
    }

    // Cleanup function
    return () => {
      if (recognitionRef.current) {
        try {
          recognitionRef.current.stop();
        } catch (error) {
          console.error("Error stopping recognition:", error);
        }
      }
    };
  }, [checkForSpeechPause, isListening, stopTimer, language, usingGoogleAPI]);

  // Auto-scroll transcript area when content changes
  useEffect(() => {
    if (transcriptAreaRef.current) {
      transcriptAreaRef.current.scrollTop = transcriptAreaRef.current.scrollHeight;
    }
  }, [transcriptMessages, currentTranscript]);

  // Auto-scroll response area when content changes
  useEffect(() => {
    if (responseAreaRef.current) {
      responseAreaRef.current.scrollTop = responseAreaRef.current.scrollHeight;
    }
  }, [response]);

  // Start listening for speech
  const startListening = useCallback(() => {
    console.log("Starting speech recognition...");
    if (recognitionRef.current && !isListening) {
      try {
        console.log("Calling recognition.start()");
        recognitionRef.current.start();
        console.log("Recognition started successfully");
        setIsListening(true);
        startTimer(); // Start the timer when listening begins
      } catch (error) {
        console.error("Speech recognition error:", error);
        // If recognition is already running, stop it first then restart
        if (error.message.includes("already started")) {
          console.log("Recognition already started, stopping and restarting");
          recognitionRef.current.stop();
          setTimeout(() => {
            console.log("Restarting recognition after stop");
            recognitionRef.current.start();
            setIsListening(true);
            startTimer(); // Start the timer when listening begins
          }, 100);
        }
      }
    } else {
      console.log("Cannot start listening:",
        recognitionRef.current ? "Already listening" : "No recognition object");
    }
  }, [isListening, startTimer]);

  // Test speech recognition with a simulated result
  const testSpeechRecognition = useCallback(() => {
    console.log("Testing speech recognition with simulated result");

    // Create a simulated SpeechRecognitionEvent
    const simulatedEvent = {
      resultIndex: 0,
      results: [
        {
          0: { transcript: "This is a simulated speech recognition result.", confidence: 0.9 },
          isFinal: true,
          length: 1
        }
      ]
    };

    // If we have a recognition object, manually trigger its onresult handler
    if (recognitionRef.current && recognitionRef.current.onresult) {
      console.log("Manually triggering onresult handler with simulated event");
      recognitionRef.current.onresult(simulatedEvent);
    } else {
      console.log("Cannot test speech recognition: No recognition object or onresult handler");
      // Directly set the transcript as a fallback
      setCurrentTranscript("This is a simulated speech recognition result.");
    }
  }, []);

  // Stop listening for speech
  const stopListening = useCallback(() => {
    if (recognitionRef.current && isListening) {
      try {
        recognitionRef.current.stop();
        setIsListening(false);
        stopTimer(); // Stop the timer when listening ends
      } catch (error) {
        console.error("Speech recognition error:", error);
      }
    }
  }, [isListening, stopTimer]);

  // This section was removed to fix the duplicate declaration error

  // Request screen sharing - simplified approach
  const requestScreenSharing = async () => {
    try {
      console.log("Requesting screen sharing...");

      // First, get screen sharing permission
      const displayStream = await navigator.mediaDevices.getDisplayMedia({
        video: true,
        audio: false // Don't request audio from screen to avoid conflicts
      });

      console.log("Screen sharing access granted");
      console.log("Screen tracks:", displayStream.getTracks().map(t => `${t.kind} (${t.label})`));

      // Store the display stream
      mediaStreamRef.current = displayStream;

      // Handle the case when user stops sharing
      displayStream.getVideoTracks()[0].onended = () => {
        console.log("User stopped screen sharing");
        setIsConnected(false);
        stopRecording();
      };

      // Set connected state and move to interview step
      setIsConnected(true);
      setStep('interview');

      // Start listening for speech - this is separate from recording
      startListening();

      // We don't need to start recording for speech recognition to work
      // This avoids the MediaRecorder error
      console.log("Speech recognition started without MediaRecorder");

    } catch (error) {
      console.error("Error sharing screen:", error);
      alert("Failed to share screen: " + error.message);
    }
  };

  // We're not using MediaRecorder anymore to avoid errors
  // Speech recognition works without recording

  // Stop screen sharing and clean up
  const stopRecording = () => {
    console.log("Stopping screen sharing...");

    // We're not using MediaRecorder anymore, but we'll keep the function name for compatibility
    setIsRecording(false);

    // Safely stop all tracks in the media stream
    if (mediaStreamRef.current) {
      try {
        console.log("Stopping all media tracks");
        mediaStreamRef.current.getTracks().forEach(track => {
          try {
            track.stop();
            console.log(`Stopped ${track.kind} track: ${track.label}`);
          } catch (trackError) {
            console.error(`Error stopping ${track.kind} track:`, trackError);
          }
        });
      } catch (streamError) {
        console.error("Error stopping media stream tracks:", streamError);
      }

      // Clear the reference
      mediaStreamRef.current = null;
    }

    // Also stop listening
    stopListening();

    console.log("Screen sharing stopped");
  };

  // End the interview
  const endInterview = () => {
    stopRecording();
    stopListening();
    setStep('configure');
    setIsConnected(false);
    setTranscriptMessages([]);
    setCurrentTranscript('');
    setResponse('');
  };

  // Render different steps
  const renderConfigureStep = () => (
    <div className="configure-step">
      <h2>Configure AI</h2>
      <div className="config-form">
        <div className="form-group">
          <label>Specialization</label>
          <select
            value={specialization}
            onChange={(e) => setSpecialization(e.target.value)}
          >
            <option value="software-engineering">Software Engineering</option>
            <option value="data-science">Data Science</option>
            <option value="product-management">Product Management</option>
            <option value="marketing">Marketing</option>
            <option value="sales">Sales</option>
            <option value="customer-service">Customer Service</option>
          </select>
        </div>

        <div className="form-group">
          <label>Language</label>
          <select
            value={language}
            onChange={(e) => setLanguage(e.target.value)}
          >
            <option value="english">English</option>
            <option value="spanish">Spanish</option>
            <option value="french">French</option>
            <option value="german">German</option>
            <option value="chinese">Chinese</option>
            <option value="japanese">Japanese</option>
          </select>
        </div>
      </div>

      {env.hasGoogleSpeechAPI() ? (
        <div className="api-status success">
          <span className="icon">✓</span>
          Google Speech-to-Text API is configured and will be used for better voice recognition
        </div>
      ) : (
        <div className="api-status warning">
          <span className="icon">ℹ</span>
          Google Speech-to-Text API is not configured. The app will use the browser's built-in speech recognition, which may be less accurate.
        </div>
      )}

      <button
        className="connect-button"
        onClick={() => setStep('connect')}
      >
        Next
      </button>
    </div>
  );

  const renderConnectStep = () => (
    <div className="connect-step">
      <h2>Connect to Interview</h2>
      <p>Share your screen to start the interview process.</p>
      <p>Make sure you have the interview window open before proceeding.</p>

      <button
        className="share-screen-button"
        onClick={requestScreenSharing}
      >
        Share Screen
      </button>

      <button
        className="back-button"
        onClick={() => setStep('configure')}
      >
        Back
      </button>
    </div>
  );

  const renderInterviewStep = () => (
    <div className="interview-step">
      <div className="interview-container">
        <div className="transcription-panel">
          <div className="panel-header">
            <h3>Interviewer</h3>
            <div className="connection-status">
              {isConnected ? (
                <span className="connected">Connected</span>
              ) : (
                <span className="disconnected">Disconnected</span>
              )}
            </div>
          </div>

          <div className="panel-content">
            <div
              ref={transcriptAreaRef}
              className="transcript-content"
            >
              {transcriptMessages.length > 0 ? (
                <div className="transcript-messages">
                  {transcriptMessages.map((msg, index) => (
                    <div key={`msg-${index}-${msg.time}`} className="transcript-message">
                      <div className="timestamp">{formatTime(msg.time)}</div>
                      <div className="message-text">{msg.text}</div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="empty-state">Interviewer questions will appear here automatically</div>
              )}

              {/* Always show current transcript area, even if empty */}
              <div className="transcript-message current">
                <div className="timestamp">{formatTime(timerSeconds)}</div>
                <div className="message-text">
                  {currentTranscript ?
                    <span className="active-transcript">{currentTranscript}</span> :
                    <span className="placeholder">Waiting for speech...</span>
                  }
                </div>
              </div>

              {/* Debug info - only shown in development */}
              {process.env.NODE_ENV === 'development' && (
                <div className="debug-info">
                  <div>Current transcript length: {currentTranscript?.length || 0}</div>
                  <div>Transcript messages: {transcriptMessages.length}</div>
                  <div>Is listening: {isListening ? 'Yes' : 'No'}</div>
                  <div>Is recording: {isRecording ? 'Yes' : 'No'}</div>
                  <div>Using Google API: {usingGoogleAPI ? 'Yes' : 'No'}</div>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="response-panel">
          <div className="panel-header">
            <h3>AI Assistant</h3>
          </div>

          <div className="panel-content">
            <div
              ref={responseAreaRef}
              className="response-content"
            >
              {response ? (
                <div className="response-message">
                  <div className="message-text">{response}</div>
                </div>
              ) : (
                <div className="empty-state">AI responses will appear here automatically</div>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="controls-container">
        <div className="status-indicators">
          <div className="timer-display">
            {isListening ? (
              <>
                <span className="recording-dot"></span>
                {formatTime(timerSeconds)}
              </>
            ) : (
              formatTime(timerSeconds)
            )}
          </div>

          <div className="status-label">
            <span>{isListening ? "Listening..." : "Paused"}</span>
            {usingGoogleAPI && (
              <span className="api-badge google">Google Speech API</span>
            )}
            {!usingGoogleAPI && (
              <span className="api-badge browser">Browser Speech API</span>
            )}
          </div>
        </div>

        <div className="action-buttons">
          <button
            className={`mic-button ${isListening ? 'active' : ''}`}
            onClick={isListening ? stopListening : startListening}
          >
            {isListening ? "Pause" : "Resume"}
          </button>

          <button
            className="stop-button"
            onClick={endInterview}
          >
            Stop Recording
          </button>

          {/* Test buttons for debugging - only shown in development */}
          {process.env.NODE_ENV === 'development' && (
            <>
              <button
                className="test-button"
                onClick={() => {
                  const testText = "This is a test transcript message. If you can see this, the transcript display is working correctly.";
                  console.log("Adding test transcript:", testText);
                  setTranscriptMessages(prev => [
                    ...prev,
                    { text: testText, timestamp: new Date(), time: timerSeconds }
                  ]);
                }}
              >
                Add Test Message
              </button>

              <button
                className="test-button speech"
                onClick={testSpeechRecognition}
              >
                Test Speech Recognition
              </button>
            </>
          )}
        </div>
      </div>
    </div>
  );

  // Render the appropriate step
  const renderStep = () => {
    switch (step) {
      case 'configure':
        return renderConfigureStep();
      case 'connect':
        return renderConnectStep();
      case 'interview':
        return renderInterviewStep();
      default:
        return renderConfigureStep();
    }
  };

  return (
    <div className="automated-interview">
      {renderStep()}
    </div>
  );
}

export default AutomatedInterview;
