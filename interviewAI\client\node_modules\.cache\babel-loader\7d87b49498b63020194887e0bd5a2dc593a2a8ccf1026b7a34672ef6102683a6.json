{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\interview-ai-app\\\\interviewAI\\\\client\\\\src\\\\components\\\\SpeechToText.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect, useCallback } from 'react';\nimport './SpeechToText.css';\nimport env from '../utils/env';\nimport MicIcon from '@mui/icons-material/Mic';\nimport StopIcon from '@mui/icons-material/Stop';\nimport SendIcon from '@mui/icons-material/Send';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport AccessTimeIcon from '@mui/icons-material/AccessTime';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction SpeechToText() {\n  _s();\n  const [isListening, setIsListening] = useState(false);\n  const [transcript, setTranscript] = useState('');\n  const [response, setResponse] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n\n  // Timer state\n  const [timerSeconds, setTimerSeconds] = useState(0);\n  const timerIntervalRef = useRef(null);\n  const recognitionRef = useRef(null);\n  const transcriptAreaRef = useRef(null);\n  const responseAreaRef = useRef(null);\n\n  // Format seconds to MM:SS\n  const formatTime = useCallback(totalSeconds => {\n    const minutes = Math.floor(totalSeconds / 60);\n    const seconds = totalSeconds % 60;\n    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\n  }, []);\n\n  // Start the timer\n  const startTimer = useCallback(() => {\n    // Reset timer when starting\n    setTimerSeconds(0);\n\n    // Clear any existing interval\n    if (timerIntervalRef.current) {\n      clearInterval(timerIntervalRef.current);\n    }\n\n    // Start a new interval\n    timerIntervalRef.current = setInterval(() => {\n      setTimerSeconds(prev => prev + 1);\n    }, 1000);\n  }, []);\n\n  // Stop the timer\n  const stopTimer = useCallback(() => {\n    if (timerIntervalRef.current) {\n      clearInterval(timerIntervalRef.current);\n      timerIntervalRef.current = null;\n    }\n  }, []);\n\n  // Clean up timer on unmount\n  useEffect(() => {\n    return () => {\n      if (timerIntervalRef.current) {\n        clearInterval(timerIntervalRef.current);\n      }\n    };\n  }, []);\n  useEffect(() => {\n    // Initialize speech recognition\n    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n    if (SpeechRecognition) {\n      recognitionRef.current = new SpeechRecognition();\n      recognitionRef.current.lang = 'en-US';\n      recognitionRef.current.interimResults = true;\n      recognitionRef.current.continuous = true;\n      let finalTranscript = \"\";\n      recognitionRef.current.onresult = event => {\n        let interimTranscript = \"\";\n        for (let i = event.resultIndex; i < event.results.length; i++) {\n          const transcript = event.results[i][0].transcript;\n          if (event.results[i].isFinal) {\n            finalTranscript += transcript + \" \";\n          } else {\n            interimTranscript += transcript;\n          }\n        }\n        setTranscript(finalTranscript + interimTranscript);\n      };\n      recognitionRef.current.onerror = event => {\n        console.error(\"Speech recognition error\", event.error);\n        alert(\"Error occurred: \" + event.error);\n        setIsListening(false);\n      };\n    } else {\n      alert(\"Your browser doesn't support speech recognition. Try Chrome or Edge.\");\n    }\n\n    // Cleanup function\n    return () => {\n      if (recognitionRef.current) {\n        recognitionRef.current.stop();\n      }\n    };\n  }, []);\n  useEffect(() => {\n    // Auto-scroll transcript area when content changes\n    if (transcriptAreaRef.current) {\n      transcriptAreaRef.current.scrollTop = transcriptAreaRef.current.scrollHeight;\n    }\n  }, [transcript]);\n  useEffect(() => {\n    // Auto-scroll response area when content changes\n    if (responseAreaRef.current) {\n      responseAreaRef.current.scrollTop = responseAreaRef.current.scrollHeight;\n    }\n  }, [response]);\n  const startListening = () => {\n    if (recognitionRef.current && !isListening) {\n      try {\n        recognitionRef.current.start();\n        setIsListening(true);\n        startTimer(); // Start the timer when listening begins\n      } catch (error) {\n        console.error(\"Speech recognition error:\", error);\n        // If recognition is already running, stop it first then restart\n        if (error.message.includes(\"already started\")) {\n          recognitionRef.current.stop();\n          setTimeout(() => {\n            recognitionRef.current.start();\n            setIsListening(true);\n            startTimer(); // Start the timer when listening begins\n          }, 100);\n        }\n      }\n    }\n  };\n  const stopListening = () => {\n    if (recognitionRef.current && isListening) {\n      try {\n        recognitionRef.current.stop();\n        setIsListening(false);\n        stopTimer(); // Stop the timer when listening ends\n      } catch (error) {\n        console.error(\"Speech recognition error:\", error);\n      }\n    }\n  };\n  const sendToGPT = async () => {\n    const apiKey = env.OPENAI_API_KEY;\n    const userText = transcript.trim();\n    console.log(\"Environment:\", process.env.NODE_ENV);\n    console.log(\"API Key available:\", apiKey ? \"Yes\" : \"No\");\n    if (!apiKey) {\n      alert(\"Please add your OpenAI API key to the .env file as REACT_APP_OPENAI_API_KEY and restart the development server.\");\n      return;\n    }\n    if (!userText) {\n      alert(\"Please record or enter some text to send to GPT.\");\n      return;\n    }\n    const prompt = `In 5 lines, give only the definition and a simple example. ${userText}`;\n    setIsLoading(true);\n    setResponse('');\n\n    // Store the current transcript before clearing it\n    const currentTranscript = transcript;\n    try {\n      const response = await fetch(\"https://api.openai.com/v1/chat/completions\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          \"Authorization\": `Bearer ${apiKey}`\n        },\n        body: JSON.stringify({\n          model: \"gpt-4.1-nano\",\n          messages: [{\n            role: \"user\",\n            content: prompt\n          }],\n          stream: true\n        })\n      });\n      if (!response.ok || !response.body) throw new Error(\"Failed to stream response.\");\n      const reader = response.body.getReader();\n      const decoder = new TextDecoder(\"utf-8\");\n      let result = \"\";\n      while (true) {\n        const {\n          value,\n          done\n        } = await reader.read();\n        if (done) break;\n        const chunk = decoder.decode(value, {\n          stream: true\n        });\n        const lines = chunk.split(\"\\n\").filter(line => line.trim().startsWith(\"data:\"));\n        for (const line of lines) {\n          const data = line.replace(/^data: /, '');\n          if (data === \"[DONE]\") break;\n          try {\n            var _json$choices, _json$choices$, _json$choices$$delta;\n            const json = JSON.parse(data);\n            const content = (_json$choices = json.choices) === null || _json$choices === void 0 ? void 0 : (_json$choices$ = _json$choices[0]) === null || _json$choices$ === void 0 ? void 0 : (_json$choices$$delta = _json$choices$.delta) === null || _json$choices$$delta === void 0 ? void 0 : _json$choices$$delta.content;\n            if (content) {\n              result += content;\n              setResponse(result);\n            }\n          } catch (e) {\n            console.error(\"Error parsing JSON:\", e);\n          }\n        }\n      }\n    } catch (error) {\n      console.error(\"Streaming Error:\", error);\n      setResponse(\"Error occurred: \" + error.message);\n    } finally {\n      if (isListening) {\n        stopListening();\n      }\n      setIsLoading(false);\n\n      // Clear the transcript after getting the answer\n      setTranscript('');\n    }\n  };\n  const clearTranscript = () => {\n    setTranscript('');\n\n    // Reset the finalTranscript in the speech recognition handler\n    if (recognitionRef.current) {\n      // We need to stop and restart recognition to clear its internal state\n      const wasListening = isListening;\n      try {\n        // Only stop if currently listening\n        if (wasListening) {\n          recognitionRef.current.stop();\n          setIsListening(false);\n        }\n\n        // Wait a moment to ensure recognition has fully stopped\n        setTimeout(() => {\n          // Reinitialize speech recognition to clear its state\n          const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n          recognitionRef.current = new SpeechRecognition();\n          recognitionRef.current.lang = 'en-US';\n          recognitionRef.current.interimResults = true;\n          recognitionRef.current.continuous = true;\n\n          // Set up event handlers with a fresh finalTranscript\n          let finalTranscript = \"\";\n          recognitionRef.current.onresult = event => {\n            let interimTranscript = \"\";\n            for (let i = event.resultIndex; i < event.results.length; i++) {\n              const transcript = event.results[i][0].transcript;\n              if (event.results[i].isFinal) {\n                finalTranscript += transcript + \" \";\n              } else {\n                interimTranscript += transcript;\n              }\n            }\n            setTranscript(finalTranscript + interimTranscript);\n          };\n          recognitionRef.current.onerror = event => {\n            console.error(\"Speech recognition error\", event.error);\n            setIsListening(false);\n          };\n\n          // Restart recognition if it was active\n          if (wasListening) {\n            try {\n              recognitionRef.current.start();\n              setIsListening(true);\n            } catch (error) {\n              console.error(\"Failed to restart recognition:\", error);\n            }\n          }\n        }, 200); // Add a small delay to ensure recognition has fully stopped\n      } catch (error) {\n        console.error(\"Error during transcript clearing:\", error);\n      }\n    }\n  };\n  const clearResponse = () => {\n    setResponse('');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"speech-to-text\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n          ref: transcriptAreaRef,\n          className: \"transcript-area\",\n          value: transcript,\n          onChange: e => setTranscript(e.target.value),\n          placeholder: \"Transcript will appear here...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'right',\n            marginTop: '5px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: clearTranscript,\n            style: {\n              padding: '5px 10px',\n              fontSize: '12px',\n              backgroundColor: '#f0f0f0',\n              color: '#333',\n              border: 'none',\n              borderRadius: '4px',\n              cursor: 'pointer'\n            },\n            disabled: !transcript,\n            children: \"Clear\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 317,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n          ref: responseAreaRef,\n          className: \"response-area\",\n          value: response,\n          readOnly: true,\n          placeholder: \"Answer will appear here...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'right',\n            marginTop: '5px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: clearResponse,\n            style: {\n              padding: '5px 10px',\n              fontSize: '12px',\n              backgroundColor: '#f0f0f0',\n              color: '#333',\n              border: 'none',\n              borderRadius: '4px',\n              cursor: 'pointer'\n            },\n            disabled: !response,\n            children: \"Clear\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 344,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 316,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"button-group\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: startListening,\n        disabled: isListening,\n        children: \"Start Listening\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 373,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: sendToGPT,\n        disabled: isLoading || !transcript.trim(),\n        children: isLoading ? 'Processing...' : 'Get Answer'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 385,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 372,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 305,\n    columnNumber: 5\n  }, this);\n}\n_s(SpeechToText, \"jA8zw79fLk3GHd7wZVLE9GYwk7k=\");\n_c = SpeechToText;\nexport default SpeechToText;\nvar _c;\n$RefreshReg$(_c, \"SpeechToText\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "useCallback", "env", "MicIcon", "StopIcon", "SendIcon", "DeleteIcon", "AccessTimeIcon", "jsxDEV", "_jsxDEV", "SpeechToText", "_s", "isListening", "setIsListening", "transcript", "setTranscript", "response", "setResponse", "isLoading", "setIsLoading", "timerSeconds", "setTimerSeconds", "timerIntervalRef", "recognitionRef", "transcriptAreaRef", "responseAreaRef", "formatTime", "totalSeconds", "minutes", "Math", "floor", "seconds", "toString", "padStart", "startTimer", "current", "clearInterval", "setInterval", "prev", "stopTimer", "SpeechRecognition", "window", "webkitSpeechRecognition", "lang", "interimResults", "continuous", "finalTranscript", "on<PERSON>ult", "event", "interimTranscript", "i", "resultIndex", "results", "length", "isFinal", "onerror", "console", "error", "alert", "stop", "scrollTop", "scrollHeight", "startListening", "start", "message", "includes", "setTimeout", "stopListening", "sendToGPT", "<PERSON><PERSON><PERSON><PERSON>", "OPENAI_API_KEY", "userText", "trim", "log", "process", "NODE_ENV", "prompt", "currentTranscript", "fetch", "method", "headers", "body", "JSON", "stringify", "model", "messages", "role", "content", "stream", "ok", "Error", "reader", "<PERSON><PERSON><PERSON><PERSON>", "decoder", "TextDecoder", "result", "value", "done", "read", "chunk", "decode", "lines", "split", "filter", "line", "startsWith", "data", "replace", "_json$choices", "_json$choices$", "_json$choices$$delta", "json", "parse", "choices", "delta", "e", "clearTranscript", "wasListening", "clearResponse", "className", "children", "style", "width", "ref", "onChange", "target", "placeholder", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "textAlign", "marginTop", "onClick", "padding", "fontSize", "backgroundColor", "color", "border", "borderRadius", "cursor", "disabled", "readOnly", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/interview-ai-app/interviewAI/client/src/components/SpeechToText.jsx"], "sourcesContent": ["import React, { useState, useRef, useEffect, useCallback } from 'react';\r\nimport './SpeechToText.css';\r\nimport env from '../utils/env';\r\nimport MicIcon from '@mui/icons-material/Mic';\r\nimport StopIcon from '@mui/icons-material/Stop';\r\nimport SendIcon from '@mui/icons-material/Send';\r\nimport DeleteIcon from '@mui/icons-material/Delete';\r\nimport AccessTimeIcon from '@mui/icons-material/AccessTime';\r\n\r\nfunction SpeechToText() {\r\n  const [isListening, setIsListening] = useState(false);\r\n  const [transcript, setTranscript] = useState('');\r\n  const [response, setResponse] = useState('');\r\n  const [isLoading, setIsLoading] = useState(false);\r\n\r\n  // Timer state\r\n  const [timerSeconds, setTimerSeconds] = useState(0);\r\n  const timerIntervalRef = useRef(null);\r\n\r\n  const recognitionRef = useRef(null);\r\n  const transcriptAreaRef = useRef(null);\r\n  const responseAreaRef = useRef(null);\r\n\r\n  // Format seconds to MM:SS\r\n  const formatTime = useCallback((totalSeconds) => {\r\n    const minutes = Math.floor(totalSeconds / 60);\r\n    const seconds = totalSeconds % 60;\r\n    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\r\n  }, []);\r\n\r\n  // Start the timer\r\n  const startTimer = useCallback(() => {\r\n    // Reset timer when starting\r\n    setTimerSeconds(0);\r\n\r\n    // Clear any existing interval\r\n    if (timerIntervalRef.current) {\r\n      clearInterval(timerIntervalRef.current);\r\n    }\r\n\r\n    // Start a new interval\r\n    timerIntervalRef.current = setInterval(() => {\r\n      setTimerSeconds(prev => prev + 1);\r\n    }, 1000);\r\n  }, []);\r\n\r\n  // Stop the timer\r\n  const stopTimer = useCallback(() => {\r\n    if (timerIntervalRef.current) {\r\n      clearInterval(timerIntervalRef.current);\r\n      timerIntervalRef.current = null;\r\n    }\r\n  }, []);\r\n\r\n  // Clean up timer on unmount\r\n  useEffect(() => {\r\n    return () => {\r\n      if (timerIntervalRef.current) {\r\n        clearInterval(timerIntervalRef.current);\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    // Initialize speech recognition\r\n    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\r\n    if (SpeechRecognition) {\r\n      recognitionRef.current = new SpeechRecognition();\r\n      recognitionRef.current.lang = 'en-US';\r\n      recognitionRef.current.interimResults = true;\r\n      recognitionRef.current.continuous = true;\r\n\r\n      let finalTranscript = \"\";\r\n\r\n      recognitionRef.current.onresult = (event) => {\r\n        let interimTranscript = \"\";\r\n\r\n        for (let i = event.resultIndex; i < event.results.length; i++) {\r\n          const transcript = event.results[i][0].transcript;\r\n          if (event.results[i].isFinal) {\r\n            finalTranscript += transcript + \" \";\r\n          } else {\r\n            interimTranscript += transcript;\r\n          }\r\n        }\r\n\r\n        setTranscript(finalTranscript + interimTranscript);\r\n      };\r\n\r\n      recognitionRef.current.onerror = (event) => {\r\n        console.error(\"Speech recognition error\", event.error);\r\n        alert(\"Error occurred: \" + event.error);\r\n        setIsListening(false);\r\n      };\r\n    } else {\r\n      alert(\"Your browser doesn't support speech recognition. Try Chrome or Edge.\");\r\n    }\r\n\r\n    // Cleanup function\r\n    return () => {\r\n      if (recognitionRef.current) {\r\n        recognitionRef.current.stop();\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    // Auto-scroll transcript area when content changes\r\n    if (transcriptAreaRef.current) {\r\n      transcriptAreaRef.current.scrollTop = transcriptAreaRef.current.scrollHeight;\r\n    }\r\n  }, [transcript]);\r\n\r\n  useEffect(() => {\r\n    // Auto-scroll response area when content changes\r\n    if (responseAreaRef.current) {\r\n      responseAreaRef.current.scrollTop = responseAreaRef.current.scrollHeight;\r\n    }\r\n  }, [response]);\r\n\r\n  const startListening = () => {\r\n    if (recognitionRef.current && !isListening) {\r\n      try {\r\n        recognitionRef.current.start();\r\n        setIsListening(true);\r\n        startTimer(); // Start the timer when listening begins\r\n      } catch (error) {\r\n        console.error(\"Speech recognition error:\", error);\r\n        // If recognition is already running, stop it first then restart\r\n        if (error.message.includes(\"already started\")) {\r\n          recognitionRef.current.stop();\r\n          setTimeout(() => {\r\n            recognitionRef.current.start();\r\n            setIsListening(true);\r\n            startTimer(); // Start the timer when listening begins\r\n          }, 100);\r\n        }\r\n      }\r\n    }\r\n  };\r\n\r\n  const stopListening = () => {\r\n    if (recognitionRef.current && isListening) {\r\n      try {\r\n        recognitionRef.current.stop();\r\n        setIsListening(false);\r\n        stopTimer(); // Stop the timer when listening ends\r\n      } catch (error) {\r\n        console.error(\"Speech recognition error:\", error);\r\n      }\r\n    }\r\n  };\r\n\r\n  const sendToGPT = async () => {\r\n    const apiKey = env.OPENAI_API_KEY;\r\n    const userText = transcript.trim();\r\n\r\n    console.log(\"Environment:\", process.env.NODE_ENV);\r\n    console.log(\"API Key available:\", apiKey ? \"Yes\" : \"No\");\r\n\r\n    if (!apiKey) {\r\n      alert(\"Please add your OpenAI API key to the .env file as REACT_APP_OPENAI_API_KEY and restart the development server.\");\r\n      return;\r\n    }\r\n\r\n    if (!userText) {\r\n      alert(\"Please record or enter some text to send to GPT.\");\r\n      return;\r\n    }\r\n    const prompt = `In 5 lines, give only the definition and a simple example. ${userText}`;\r\n    setIsLoading(true);\r\n    setResponse('');\r\n\r\n    // Store the current transcript before clearing it\r\n    const currentTranscript = transcript;\r\n\r\n    try {\r\n      const response = await fetch(\"https://api.openai.com/v1/chat/completions\", {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n          \"Authorization\": `Bearer ${apiKey}`\r\n        },\r\n        body: JSON.stringify({\r\n          model: \"gpt-4.1-nano\",\r\n          messages: [\r\n            { role: \"user\", content: prompt }\r\n          ],\r\n          stream: true\r\n        })\r\n      });\r\n\r\n      if (!response.ok || !response.body) throw new Error(\"Failed to stream response.\");\r\n\r\n      const reader = response.body.getReader();\r\n      const decoder = new TextDecoder(\"utf-8\");\r\n\r\n      let result = \"\";\r\n\r\n      while (true) {\r\n        const { value, done } = await reader.read();\r\n        if (done) break;\r\n\r\n        const chunk = decoder.decode(value, { stream: true });\r\n        const lines = chunk.split(\"\\n\").filter(line => line.trim().startsWith(\"data:\"));\r\n\r\n        for (const line of lines) {\r\n          const data = line.replace(/^data: /, '');\r\n          if (data === \"[DONE]\") break;\r\n\r\n          try {\r\n            const json = JSON.parse(data);\r\n            const content = json.choices?.[0]?.delta?.content;\r\n            if (content) {\r\n              result += content;\r\n              setResponse(result);\r\n            }\r\n          } catch (e) {\r\n            console.error(\"Error parsing JSON:\", e);\r\n          }\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Streaming Error:\", error);\r\n      setResponse(\"Error occurred: \" + error.message);\r\n    } finally {\r\n      if (isListening) {\r\n        stopListening();\r\n      }\r\n      setIsLoading(false);\r\n\r\n      // Clear the transcript after getting the answer\r\n      setTranscript('');\r\n    }\r\n  };\r\n\r\n  const clearTranscript = () => {\r\n    setTranscript('');\r\n\r\n    // Reset the finalTranscript in the speech recognition handler\r\n    if (recognitionRef.current) {\r\n      // We need to stop and restart recognition to clear its internal state\r\n      const wasListening = isListening;\r\n\r\n      try {\r\n        // Only stop if currently listening\r\n        if (wasListening) {\r\n          recognitionRef.current.stop();\r\n          setIsListening(false);\r\n        }\r\n\r\n        // Wait a moment to ensure recognition has fully stopped\r\n        setTimeout(() => {\r\n          // Reinitialize speech recognition to clear its state\r\n          const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\r\n          recognitionRef.current = new SpeechRecognition();\r\n          recognitionRef.current.lang = 'en-US';\r\n          recognitionRef.current.interimResults = true;\r\n          recognitionRef.current.continuous = true;\r\n\r\n          // Set up event handlers with a fresh finalTranscript\r\n          let finalTranscript = \"\";\r\n\r\n          recognitionRef.current.onresult = (event) => {\r\n            let interimTranscript = \"\";\r\n\r\n            for (let i = event.resultIndex; i < event.results.length; i++) {\r\n              const transcript = event.results[i][0].transcript;\r\n              if (event.results[i].isFinal) {\r\n                finalTranscript += transcript + \" \";\r\n              } else {\r\n                interimTranscript += transcript;\r\n              }\r\n            }\r\n\r\n            setTranscript(finalTranscript + interimTranscript);\r\n          };\r\n\r\n          recognitionRef.current.onerror = (event) => {\r\n            console.error(\"Speech recognition error\", event.error);\r\n            setIsListening(false);\r\n          };\r\n\r\n          // Restart recognition if it was active\r\n          if (wasListening) {\r\n            try {\r\n              recognitionRef.current.start();\r\n              setIsListening(true);\r\n            } catch (error) {\r\n              console.error(\"Failed to restart recognition:\", error);\r\n            }\r\n          }\r\n        }, 200); // Add a small delay to ensure recognition has fully stopped\r\n      } catch (error) {\r\n        console.error(\"Error during transcript clearing:\", error);\r\n      }\r\n    }\r\n  };\r\n\r\n  const clearResponse = () => {\r\n    setResponse('');\r\n  };\r\n\r\n  return (\r\n    <div className=\"speech-to-text\">\r\n      {/* <h2>\r\n        🎤 Real-Time Voice Typing\r\n        {isListening && (\r\n          <>\r\n            <span className=\"listening-indicator\">●</span>\r\n            <span className=\"timer-display\">{formatTime(timerSeconds)}</span>\r\n          </>\r\n        )}\r\n      </h2> */}\r\n\r\n      <div className=\"flex-container\">\r\n        <div style={{ width: '100%' }}>\r\n          <textarea\r\n            ref={transcriptAreaRef}\r\n            className=\"transcript-area\"\r\n            value={transcript}\r\n            onChange={(e) => setTranscript(e.target.value)}\r\n            placeholder=\"Transcript will appear here...\"\r\n          />\r\n          <div style={{ textAlign: 'right', marginTop: '5px' }}>\r\n            <button\r\n              onClick={clearTranscript}\r\n              style={{\r\n                padding: '5px 10px',\r\n                fontSize: '12px',\r\n                backgroundColor: '#f0f0f0',\r\n                color: '#333',\r\n                border: 'none',\r\n                borderRadius: '4px',\r\n                cursor: 'pointer'\r\n              }}\r\n              disabled={!transcript}\r\n            >\r\n              Clear\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <div style={{ width: '100%' }}>\r\n          <textarea\r\n            ref={responseAreaRef}\r\n            className=\"response-area\"\r\n            value={response}\r\n            readOnly\r\n            placeholder=\"Answer will appear here...\"\r\n          />\r\n          <div style={{ textAlign: 'right', marginTop: '5px' }}>\r\n            <button\r\n              onClick={clearResponse}\r\n              style={{\r\n                padding: '5px 10px',\r\n                fontSize: '12px',\r\n                backgroundColor: '#f0f0f0',\r\n                color: '#333',\r\n                border: 'none',\r\n                borderRadius: '4px',\r\n                cursor: 'pointer'\r\n              }}\r\n              disabled={!response}\r\n            >\r\n              Clear\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"button-group\">\r\n        <button\r\n          onClick={startListening}\r\n          disabled={isListening}\r\n        >\r\n          Start Listening\r\n        </button>\r\n        {/* <button\r\n          onClick={stopListening}\r\n          disabled={!isListening}\r\n        >\r\n          Stop Listening\r\n        </button> */}\r\n        <button\r\n          onClick={sendToGPT}\r\n          disabled={isLoading || !transcript.trim()}\r\n        >\r\n          {isLoading ? 'Processing...' : 'Get Answer'}\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default SpeechToText;\r\n\r\n\r\n\r\n\r\n\r\n\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AACvE,OAAO,oBAAoB;AAC3B,OAAOC,GAAG,MAAM,cAAc;AAC9B,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,cAAc,MAAM,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,SAASC,YAAYA,CAAA,EAAG;EAAAC,EAAA;EACtB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACgB,UAAU,EAAEC,aAAa,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACkB,QAAQ,EAAEC,WAAW,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACoB,SAAS,EAAEC,YAAY,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;;EAEjD;EACA,MAAM,CAACsB,YAAY,EAAEC,eAAe,CAAC,GAAGvB,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAMwB,gBAAgB,GAAGvB,MAAM,CAAC,IAAI,CAAC;EAErC,MAAMwB,cAAc,GAAGxB,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMyB,iBAAiB,GAAGzB,MAAM,CAAC,IAAI,CAAC;EACtC,MAAM0B,eAAe,GAAG1B,MAAM,CAAC,IAAI,CAAC;;EAEpC;EACA,MAAM2B,UAAU,GAAGzB,WAAW,CAAE0B,YAAY,IAAK;IAC/C,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,YAAY,GAAG,EAAE,CAAC;IAC7C,MAAMI,OAAO,GAAGJ,YAAY,GAAG,EAAE;IACjC,OAAO,GAAGC,OAAO,CAACI,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,OAAO,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACxF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,UAAU,GAAGjC,WAAW,CAAC,MAAM;IACnC;IACAoB,eAAe,CAAC,CAAC,CAAC;;IAElB;IACA,IAAIC,gBAAgB,CAACa,OAAO,EAAE;MAC5BC,aAAa,CAACd,gBAAgB,CAACa,OAAO,CAAC;IACzC;;IAEA;IACAb,gBAAgB,CAACa,OAAO,GAAGE,WAAW,CAAC,MAAM;MAC3ChB,eAAe,CAACiB,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IACnC,CAAC,EAAE,IAAI,CAAC;EACV,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,SAAS,GAAGtC,WAAW,CAAC,MAAM;IAClC,IAAIqB,gBAAgB,CAACa,OAAO,EAAE;MAC5BC,aAAa,CAACd,gBAAgB,CAACa,OAAO,CAAC;MACvCb,gBAAgB,CAACa,OAAO,GAAG,IAAI;IACjC;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAnC,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX,IAAIsB,gBAAgB,CAACa,OAAO,EAAE;QAC5BC,aAAa,CAACd,gBAAgB,CAACa,OAAO,CAAC;MACzC;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAENnC,SAAS,CAAC,MAAM;IACd;IACA,MAAMwC,iBAAiB,GAAGC,MAAM,CAACD,iBAAiB,IAAIC,MAAM,CAACC,uBAAuB;IACpF,IAAIF,iBAAiB,EAAE;MACrBjB,cAAc,CAACY,OAAO,GAAG,IAAIK,iBAAiB,CAAC,CAAC;MAChDjB,cAAc,CAACY,OAAO,CAACQ,IAAI,GAAG,OAAO;MACrCpB,cAAc,CAACY,OAAO,CAACS,cAAc,GAAG,IAAI;MAC5CrB,cAAc,CAACY,OAAO,CAACU,UAAU,GAAG,IAAI;MAExC,IAAIC,eAAe,GAAG,EAAE;MAExBvB,cAAc,CAACY,OAAO,CAACY,QAAQ,GAAIC,KAAK,IAAK;QAC3C,IAAIC,iBAAiB,GAAG,EAAE;QAE1B,KAAK,IAAIC,CAAC,GAAGF,KAAK,CAACG,WAAW,EAAED,CAAC,GAAGF,KAAK,CAACI,OAAO,CAACC,MAAM,EAAEH,CAAC,EAAE,EAAE;UAC7D,MAAMpC,UAAU,GAAGkC,KAAK,CAACI,OAAO,CAACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAACpC,UAAU;UACjD,IAAIkC,KAAK,CAACI,OAAO,CAACF,CAAC,CAAC,CAACI,OAAO,EAAE;YAC5BR,eAAe,IAAIhC,UAAU,GAAG,GAAG;UACrC,CAAC,MAAM;YACLmC,iBAAiB,IAAInC,UAAU;UACjC;QACF;QAEAC,aAAa,CAAC+B,eAAe,GAAGG,iBAAiB,CAAC;MACpD,CAAC;MAED1B,cAAc,CAACY,OAAO,CAACoB,OAAO,GAAIP,KAAK,IAAK;QAC1CQ,OAAO,CAACC,KAAK,CAAC,0BAA0B,EAAET,KAAK,CAACS,KAAK,CAAC;QACtDC,KAAK,CAAC,kBAAkB,GAAGV,KAAK,CAACS,KAAK,CAAC;QACvC5C,cAAc,CAAC,KAAK,CAAC;MACvB,CAAC;IACH,CAAC,MAAM;MACL6C,KAAK,CAAC,sEAAsE,CAAC;IAC/E;;IAEA;IACA,OAAO,MAAM;MACX,IAAInC,cAAc,CAACY,OAAO,EAAE;QAC1BZ,cAAc,CAACY,OAAO,CAACwB,IAAI,CAAC,CAAC;MAC/B;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN3D,SAAS,CAAC,MAAM;IACd;IACA,IAAIwB,iBAAiB,CAACW,OAAO,EAAE;MAC7BX,iBAAiB,CAACW,OAAO,CAACyB,SAAS,GAAGpC,iBAAiB,CAACW,OAAO,CAAC0B,YAAY;IAC9E;EACF,CAAC,EAAE,CAAC/C,UAAU,CAAC,CAAC;EAEhBd,SAAS,CAAC,MAAM;IACd;IACA,IAAIyB,eAAe,CAACU,OAAO,EAAE;MAC3BV,eAAe,CAACU,OAAO,CAACyB,SAAS,GAAGnC,eAAe,CAACU,OAAO,CAAC0B,YAAY;IAC1E;EACF,CAAC,EAAE,CAAC7C,QAAQ,CAAC,CAAC;EAEd,MAAM8C,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIvC,cAAc,CAACY,OAAO,IAAI,CAACvB,WAAW,EAAE;MAC1C,IAAI;QACFW,cAAc,CAACY,OAAO,CAAC4B,KAAK,CAAC,CAAC;QAC9BlD,cAAc,CAAC,IAAI,CAAC;QACpBqB,UAAU,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,OAAOuB,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD;QACA,IAAIA,KAAK,CAACO,OAAO,CAACC,QAAQ,CAAC,iBAAiB,CAAC,EAAE;UAC7C1C,cAAc,CAACY,OAAO,CAACwB,IAAI,CAAC,CAAC;UAC7BO,UAAU,CAAC,MAAM;YACf3C,cAAc,CAACY,OAAO,CAAC4B,KAAK,CAAC,CAAC;YAC9BlD,cAAc,CAAC,IAAI,CAAC;YACpBqB,UAAU,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,EAAE,GAAG,CAAC;QACT;MACF;IACF;EACF,CAAC;EAED,MAAMiC,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI5C,cAAc,CAACY,OAAO,IAAIvB,WAAW,EAAE;MACzC,IAAI;QACFW,cAAc,CAACY,OAAO,CAACwB,IAAI,CAAC,CAAC;QAC7B9C,cAAc,CAAC,KAAK,CAAC;QACrB0B,SAAS,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,OAAOkB,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACnD;IACF;EACF,CAAC;EAED,MAAMW,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,MAAMC,MAAM,GAAGnE,GAAG,CAACoE,cAAc;IACjC,MAAMC,QAAQ,GAAGzD,UAAU,CAAC0D,IAAI,CAAC,CAAC;IAElChB,OAAO,CAACiB,GAAG,CAAC,cAAc,EAAEC,OAAO,CAACxE,GAAG,CAACyE,QAAQ,CAAC;IACjDnB,OAAO,CAACiB,GAAG,CAAC,oBAAoB,EAAEJ,MAAM,GAAG,KAAK,GAAG,IAAI,CAAC;IAExD,IAAI,CAACA,MAAM,EAAE;MACXX,KAAK,CAAC,iHAAiH,CAAC;MACxH;IACF;IAEA,IAAI,CAACa,QAAQ,EAAE;MACbb,KAAK,CAAC,kDAAkD,CAAC;MACzD;IACF;IACA,MAAMkB,MAAM,GAAG,8DAA8DL,QAAQ,EAAE;IACvFpD,YAAY,CAAC,IAAI,CAAC;IAClBF,WAAW,CAAC,EAAE,CAAC;;IAEf;IACA,MAAM4D,iBAAiB,GAAG/D,UAAU;IAEpC,IAAI;MACF,MAAME,QAAQ,GAAG,MAAM8D,KAAK,CAAC,4CAA4C,EAAE;QACzEC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAE,UAAUX,MAAM;QACnC,CAAC;QACDY,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBC,KAAK,EAAE,cAAc;UACrBC,QAAQ,EAAE,CACR;YAAEC,IAAI,EAAE,MAAM;YAAEC,OAAO,EAAEX;UAAO,CAAC,CAClC;UACDY,MAAM,EAAE;QACV,CAAC;MACH,CAAC,CAAC;MAEF,IAAI,CAACxE,QAAQ,CAACyE,EAAE,IAAI,CAACzE,QAAQ,CAACiE,IAAI,EAAE,MAAM,IAAIS,KAAK,CAAC,4BAA4B,CAAC;MAEjF,MAAMC,MAAM,GAAG3E,QAAQ,CAACiE,IAAI,CAACW,SAAS,CAAC,CAAC;MACxC,MAAMC,OAAO,GAAG,IAAIC,WAAW,CAAC,OAAO,CAAC;MAExC,IAAIC,MAAM,GAAG,EAAE;MAEf,OAAO,IAAI,EAAE;QACX,MAAM;UAAEC,KAAK;UAAEC;QAAK,CAAC,GAAG,MAAMN,MAAM,CAACO,IAAI,CAAC,CAAC;QAC3C,IAAID,IAAI,EAAE;QAEV,MAAME,KAAK,GAAGN,OAAO,CAACO,MAAM,CAACJ,KAAK,EAAE;UAAER,MAAM,EAAE;QAAK,CAAC,CAAC;QACrD,MAAMa,KAAK,GAAGF,KAAK,CAACG,KAAK,CAAC,IAAI,CAAC,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAAChC,IAAI,CAAC,CAAC,CAACiC,UAAU,CAAC,OAAO,CAAC,CAAC;QAE/E,KAAK,MAAMD,IAAI,IAAIH,KAAK,EAAE;UACxB,MAAMK,IAAI,GAAGF,IAAI,CAACG,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;UACxC,IAAID,IAAI,KAAK,QAAQ,EAAE;UAEvB,IAAI;YAAA,IAAAE,aAAA,EAAAC,cAAA,EAAAC,oBAAA;YACF,MAAMC,IAAI,GAAG7B,IAAI,CAAC8B,KAAK,CAACN,IAAI,CAAC;YAC7B,MAAMnB,OAAO,IAAAqB,aAAA,GAAGG,IAAI,CAACE,OAAO,cAAAL,aAAA,wBAAAC,cAAA,GAAZD,aAAA,CAAe,CAAC,CAAC,cAAAC,cAAA,wBAAAC,oBAAA,GAAjBD,cAAA,CAAmBK,KAAK,cAAAJ,oBAAA,uBAAxBA,oBAAA,CAA0BvB,OAAO;YACjD,IAAIA,OAAO,EAAE;cACXQ,MAAM,IAAIR,OAAO;cACjBtE,WAAW,CAAC8E,MAAM,CAAC;YACrB;UACF,CAAC,CAAC,OAAOoB,CAAC,EAAE;YACV3D,OAAO,CAACC,KAAK,CAAC,qBAAqB,EAAE0D,CAAC,CAAC;UACzC;QACF;MACF;IACF,CAAC,CAAC,OAAO1D,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MACxCxC,WAAW,CAAC,kBAAkB,GAAGwC,KAAK,CAACO,OAAO,CAAC;IACjD,CAAC,SAAS;MACR,IAAIpD,WAAW,EAAE;QACfuD,aAAa,CAAC,CAAC;MACjB;MACAhD,YAAY,CAAC,KAAK,CAAC;;MAEnB;MACAJ,aAAa,CAAC,EAAE,CAAC;IACnB;EACF,CAAC;EAED,MAAMqG,eAAe,GAAGA,CAAA,KAAM;IAC5BrG,aAAa,CAAC,EAAE,CAAC;;IAEjB;IACA,IAAIQ,cAAc,CAACY,OAAO,EAAE;MAC1B;MACA,MAAMkF,YAAY,GAAGzG,WAAW;MAEhC,IAAI;QACF;QACA,IAAIyG,YAAY,EAAE;UAChB9F,cAAc,CAACY,OAAO,CAACwB,IAAI,CAAC,CAAC;UAC7B9C,cAAc,CAAC,KAAK,CAAC;QACvB;;QAEA;QACAqD,UAAU,CAAC,MAAM;UACf;UACA,MAAM1B,iBAAiB,GAAGC,MAAM,CAACD,iBAAiB,IAAIC,MAAM,CAACC,uBAAuB;UACpFnB,cAAc,CAACY,OAAO,GAAG,IAAIK,iBAAiB,CAAC,CAAC;UAChDjB,cAAc,CAACY,OAAO,CAACQ,IAAI,GAAG,OAAO;UACrCpB,cAAc,CAACY,OAAO,CAACS,cAAc,GAAG,IAAI;UAC5CrB,cAAc,CAACY,OAAO,CAACU,UAAU,GAAG,IAAI;;UAExC;UACA,IAAIC,eAAe,GAAG,EAAE;UAExBvB,cAAc,CAACY,OAAO,CAACY,QAAQ,GAAIC,KAAK,IAAK;YAC3C,IAAIC,iBAAiB,GAAG,EAAE;YAE1B,KAAK,IAAIC,CAAC,GAAGF,KAAK,CAACG,WAAW,EAAED,CAAC,GAAGF,KAAK,CAACI,OAAO,CAACC,MAAM,EAAEH,CAAC,EAAE,EAAE;cAC7D,MAAMpC,UAAU,GAAGkC,KAAK,CAACI,OAAO,CAACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAACpC,UAAU;cACjD,IAAIkC,KAAK,CAACI,OAAO,CAACF,CAAC,CAAC,CAACI,OAAO,EAAE;gBAC5BR,eAAe,IAAIhC,UAAU,GAAG,GAAG;cACrC,CAAC,MAAM;gBACLmC,iBAAiB,IAAInC,UAAU;cACjC;YACF;YAEAC,aAAa,CAAC+B,eAAe,GAAGG,iBAAiB,CAAC;UACpD,CAAC;UAED1B,cAAc,CAACY,OAAO,CAACoB,OAAO,GAAIP,KAAK,IAAK;YAC1CQ,OAAO,CAACC,KAAK,CAAC,0BAA0B,EAAET,KAAK,CAACS,KAAK,CAAC;YACtD5C,cAAc,CAAC,KAAK,CAAC;UACvB,CAAC;;UAED;UACA,IAAIwG,YAAY,EAAE;YAChB,IAAI;cACF9F,cAAc,CAACY,OAAO,CAAC4B,KAAK,CAAC,CAAC;cAC9BlD,cAAc,CAAC,IAAI,CAAC;YACtB,CAAC,CAAC,OAAO4C,KAAK,EAAE;cACdD,OAAO,CAACC,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;YACxD;UACF;QACF,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,OAAOA,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MAC3D;IACF;EACF,CAAC;EAED,MAAM6D,aAAa,GAAGA,CAAA,KAAM;IAC1BrG,WAAW,CAAC,EAAE,CAAC;EACjB,CAAC;EAED,oBACER,OAAA;IAAK8G,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAW7B/G,OAAA;MAAK8G,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7B/G,OAAA;QAAKgH,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAF,QAAA,gBAC5B/G,OAAA;UACEkH,GAAG,EAAEnG,iBAAkB;UACvB+F,SAAS,EAAC,iBAAiB;UAC3BvB,KAAK,EAAElF,UAAW;UAClB8G,QAAQ,EAAGT,CAAC,IAAKpG,aAAa,CAACoG,CAAC,CAACU,MAAM,CAAC7B,KAAK,CAAE;UAC/C8B,WAAW,EAAC;QAAgC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,eACFzH,OAAA;UAAKgH,KAAK,EAAE;YAAEU,SAAS,EAAE,OAAO;YAAEC,SAAS,EAAE;UAAM,CAAE;UAAAZ,QAAA,eACnD/G,OAAA;YACE4H,OAAO,EAAEjB,eAAgB;YACzBK,KAAK,EAAE;cACLa,OAAO,EAAE,UAAU;cACnBC,QAAQ,EAAE,MAAM;cAChBC,eAAe,EAAE,SAAS;cAC1BC,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdC,YAAY,EAAE,KAAK;cACnBC,MAAM,EAAE;YACV,CAAE;YACFC,QAAQ,EAAE,CAAC/H,UAAW;YAAA0G,QAAA,EACvB;UAED;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENzH,OAAA;QAAKgH,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAF,QAAA,gBAC5B/G,OAAA;UACEkH,GAAG,EAAElG,eAAgB;UACrB8F,SAAS,EAAC,eAAe;UACzBvB,KAAK,EAAEhF,QAAS;UAChB8H,QAAQ;UACRhB,WAAW,EAAC;QAA4B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACFzH,OAAA;UAAKgH,KAAK,EAAE;YAAEU,SAAS,EAAE,OAAO;YAAEC,SAAS,EAAE;UAAM,CAAE;UAAAZ,QAAA,eACnD/G,OAAA;YACE4H,OAAO,EAAEf,aAAc;YACvBG,KAAK,EAAE;cACLa,OAAO,EAAE,UAAU;cACnBC,QAAQ,EAAE,MAAM;cAChBC,eAAe,EAAE,SAAS;cAC1BC,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdC,YAAY,EAAE,KAAK;cACnBC,MAAM,EAAE;YACV,CAAE;YACFC,QAAQ,EAAE,CAAC7H,QAAS;YAAAwG,QAAA,EACrB;UAED;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENzH,OAAA;MAAK8G,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3B/G,OAAA;QACE4H,OAAO,EAAEvE,cAAe;QACxB+E,QAAQ,EAAEjI,WAAY;QAAA4G,QAAA,EACvB;MAED;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAOTzH,OAAA;QACE4H,OAAO,EAAEjE,SAAU;QACnByE,QAAQ,EAAE3H,SAAS,IAAI,CAACJ,UAAU,CAAC0D,IAAI,CAAC,CAAE;QAAAgD,QAAA,EAEzCtG,SAAS,GAAG,eAAe,GAAG;MAAY;QAAA6G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACvH,EAAA,CAhYQD,YAAY;AAAAqI,EAAA,GAAZrI,YAAY;AAkYrB,eAAeA,YAAY;AAAC,IAAAqI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}