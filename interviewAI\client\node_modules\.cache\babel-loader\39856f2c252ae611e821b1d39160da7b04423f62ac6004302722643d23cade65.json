{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\interview-ai-app\\\\interviewAI\\\\client\\\\src\\\\pages\\\\AutomatedInterviewPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport AutomatedInterview from '../components/AutomatedInterview';\nimport SimpleTranscriber from '../components/SimpleTranscriber';\nimport './AutomatedInterviewPage.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction AutomatedInterviewPage() {\n  _s();\n  const [showSimpleTranscriber, setShowSimpleTranscriber] = useState(true);\n  const [transcript, setTranscript] = useState('');\n  const handleTranscriptChange = newTranscript => {\n    setTranscript(newTranscript);\n    console.log(\"Transcript updated:\", newTranscript);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"automated-interview-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"interview-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Automated Interview\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"toggle-container\",\n        children: /*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"toggle-label\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            checked: showSimpleTranscriber,\n            onChange: () => setShowSimpleTranscriber(!showSimpleTranscriber)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 13\n          }, this), \"Show Simple Transcriber\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this), showSimpleTranscriber && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"simple-transcriber-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Simple Transcriber\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"transcriber-info\",\n        children: \"This is a simplified transcriber that should work in most browsers. Use this if you're having trouble with the main interview interface.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(SimpleTranscriber, {\n        onTranscriptChange: handleTranscriptChange\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 11\n      }, this), transcript && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"transcript-preview\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Current Transcript:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"transcript-content\",\n          children: transcript\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(AutomatedInterview, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 5\n  }, this);\n}\n_s(AutomatedInterviewPage, \"/HkMoxyHnOJXFDVo0PiWRUSEW5U=\");\n_c = AutomatedInterviewPage;\nexport default AutomatedInterviewPage;\nvar _c;\n$RefreshReg$(_c, \"AutomatedInterviewPage\");", "map": {"version": 3, "names": ["React", "useState", "AutomatedInterview", "SimpleTranscriber", "jsxDEV", "_jsxDEV", "AutomatedInterviewPage", "_s", "showSimpleTranscriber", "setShowSimpleTranscriber", "transcript", "setTranscript", "handleTranscriptChange", "newTranscript", "console", "log", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "checked", "onChange", "onTranscriptChange", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/interview-ai-app/interviewAI/client/src/pages/AutomatedInterviewPage.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport AutomatedInterview from '../components/AutomatedInterview';\nimport SimpleTranscriber from '../components/SimpleTranscriber';\nimport './AutomatedInterviewPage.css';\n\nfunction AutomatedInterviewPage() {\n  const [showSimpleTranscriber, setShowSimpleTranscriber] = useState(true);\n  const [transcript, setTranscript] = useState('');\n\n  const handleTranscriptChange = (newTranscript) => {\n    setTranscript(newTranscript);\n    console.log(\"Transcript updated:\", newTranscript);\n  };\n\n  return (\n    <div className=\"automated-interview-page\">\n      <div className=\"interview-header\">\n        <h1>Automated Interview</h1>\n        <div className=\"toggle-container\">\n          <label className=\"toggle-label\">\n            <input\n              type=\"checkbox\"\n              checked={showSimpleTranscriber}\n              onChange={() => setShowSimpleTranscriber(!showSimpleTranscriber)}\n            />\n            Show Simple Transcriber\n          </label>\n        </div>\n      </div>\n\n      {showSimpleTranscriber && (\n        <div className=\"simple-transcriber-container\">\n          <h2>Simple Transcriber</h2>\n          <p className=\"transcriber-info\">\n            This is a simplified transcriber that should work in most browsers.\n            Use this if you're having trouble with the main interview interface.\n          </p>\n          <SimpleTranscriber onTranscriptChange={handleTranscriptChange} />\n\n          {transcript && (\n            <div className=\"transcript-preview\">\n              <h3>Current Transcript:</h3>\n              <div className=\"transcript-content\">{transcript}</div>\n            </div>\n          )}\n        </div>\n      )}\n\n      <AutomatedInterview />\n    </div>\n  );\n}\n\nexport default AutomatedInterviewPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,kBAAkB,MAAM,kCAAkC;AACjE,OAAOC,iBAAiB,MAAM,iCAAiC;AAC/D,OAAO,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtC,SAASC,sBAAsBA,CAAA,EAAG;EAAAC,EAAA;EAChC,MAAM,CAACC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGR,QAAQ,CAAC,IAAI,CAAC;EACxE,MAAM,CAACS,UAAU,EAAEC,aAAa,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAMW,sBAAsB,GAAIC,aAAa,IAAK;IAChDF,aAAa,CAACE,aAAa,CAAC;IAC5BC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEF,aAAa,CAAC;EACnD,CAAC;EAED,oBACER,OAAA;IAAKW,SAAS,EAAC,0BAA0B;IAAAC,QAAA,gBACvCZ,OAAA;MAAKW,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BZ,OAAA;QAAAY,QAAA,EAAI;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5BhB,OAAA;QAAKW,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/BZ,OAAA;UAAOW,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC7BZ,OAAA;YACEiB,IAAI,EAAC,UAAU;YACfC,OAAO,EAAEf,qBAAsB;YAC/BgB,QAAQ,EAAEA,CAAA,KAAMf,wBAAwB,CAAC,CAACD,qBAAqB;UAAE;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE,CAAC,2BAEJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELb,qBAAqB,iBACpBH,OAAA;MAAKW,SAAS,EAAC,8BAA8B;MAAAC,QAAA,gBAC3CZ,OAAA;QAAAY,QAAA,EAAI;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3BhB,OAAA;QAAGW,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAAC;MAGhC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJhB,OAAA,CAACF,iBAAiB;QAACsB,kBAAkB,EAAEb;MAAuB;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAEhEX,UAAU,iBACTL,OAAA;QAAKW,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjCZ,OAAA;UAAAY,QAAA,EAAI;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5BhB,OAAA;UAAKW,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAEP;QAAU;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,eAEDhB,OAAA,CAACH,kBAAkB;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACnB,CAAC;AAEV;AAACd,EAAA,CA9CQD,sBAAsB;AAAAoB,EAAA,GAAtBpB,sBAAsB;AAgD/B,eAAeA,sBAAsB;AAAC,IAAAoB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}