/*! For license information please see main.39dbf4ba.js.LICENSE.txt */
(()=>{"use strict";var e={43:(e,t,n)=>{e.exports=n(202)},153:(e,t,n)=>{var r=n(43),o=Symbol.for("react.element"),a=Symbol.for("react.fragment"),i=Object.prototype.hasOwnProperty,l=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,s={key:!0,ref:!0,__self:!0,__source:!0};function c(e,t,n){var r,a={},c=null,u=null;for(r in void 0!==n&&(c=""+n),void 0!==t.key&&(c=""+t.key),void 0!==t.ref&&(u=t.ref),t)i.call(t,r)&&!s.hasOwnProperty(r)&&(a[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===a[r]&&(a[r]=t[r]);return{$$typeof:o,type:e,key:c,ref:u,props:a,_owner:l.current}}t.Fragment=a,t.jsx=c,t.jsxs=c},175:(e,t)=>{const n=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,r=/^[\u0021-\u003A\u003C-\u007E]*$/,o=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,a=/^[\u0020-\u003A\u003D-\u007E]*$/,i=Object.prototype.toString,l=(()=>{const e=function(){};return e.prototype=Object.create(null),e})();function s(e,t,n){do{const n=e.charCodeAt(t);if(32!==n&&9!==n)return t}while(++t<n);return n}function c(e,t,n){for(;t>n;){const n=e.charCodeAt(--t);if(32!==n&&9!==n)return t+1}return n}function u(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}},202:(e,t)=>{var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler"),l=Symbol.for("react.provider"),s=Symbol.for("react.context"),c=Symbol.for("react.forward_ref"),u=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),p=Symbol.for("react.lazy"),f=Symbol.iterator;var h={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m=Object.assign,g={};function v(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||h}function y(){}function b(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||h}v.prototype.isReactComponent={},v.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},v.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},y.prototype=v.prototype;var x=b.prototype=new y;x.constructor=b,m(x,v.prototype),x.isPureReactComponent=!0;var w=Array.isArray,S=Object.prototype.hasOwnProperty,k={current:null},C={key:!0,ref:!0,__self:!0,__source:!0};function E(e,t,r){var o,a={},i=null,l=null;if(null!=t)for(o in void 0!==t.ref&&(l=t.ref),void 0!==t.key&&(i=""+t.key),t)S.call(t,o)&&!C.hasOwnProperty(o)&&(a[o]=t[o]);var s=arguments.length-2;if(1===s)a.children=r;else if(1<s){for(var c=Array(s),u=0;u<s;u++)c[u]=arguments[u+2];a.children=c}if(e&&e.defaultProps)for(o in s=e.defaultProps)void 0===a[o]&&(a[o]=s[o]);return{$$typeof:n,type:e,key:i,ref:l,props:a,_owner:k.current}}function j(e){return"object"===typeof e&&null!==e&&e.$$typeof===n}var N=/\/+/g;function P(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,(function(e){return t[e]}))}(""+e.key):t.toString(36)}function R(e,t,o,a,i){var l=typeof e;"undefined"!==l&&"boolean"!==l||(e=null);var s=!1;if(null===e)s=!0;else switch(l){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case n:case r:s=!0}}if(s)return i=i(s=e),e=""===a?"."+P(s,0):a,w(i)?(o="",null!=e&&(o=e.replace(N,"$&/")+"/"),R(i,t,o,"",(function(e){return e}))):null!=i&&(j(i)&&(i=function(e,t){return{$$typeof:n,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(i,o+(!i.key||s&&s.key===i.key?"":(""+i.key).replace(N,"$&/")+"/")+e)),t.push(i)),1;if(s=0,a=""===a?".":a+":",w(e))for(var c=0;c<e.length;c++){var u=a+P(l=e[c],c);s+=R(l,t,o,u,i)}else if(u=function(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=f&&e[f]||e["@@iterator"])?e:null}(e),"function"===typeof u)for(e=u.call(e),c=0;!(l=e.next()).done;)s+=R(l=l.value,t,o,u=a+P(l,c++),i);else if("object"===l)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function T(e,t,n){if(null==e)return e;var r=[],o=0;return R(e,r,"","",(function(e){return t.call(n,e,o++)})),r}function _(e){if(-1===e._status){var t=e._result;(t=t()).then((function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)}),(function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)})),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var A={current:null},M={transition:null},z={ReactCurrentDispatcher:A,ReactCurrentBatchConfig:M,ReactCurrentOwner:k};function I(){throw Error("act(...) is not supported in production builds of React.")}t.Children={map:T,forEach:function(e,t,n){T(e,(function(){t.apply(this,arguments)}),n)},count:function(e){var t=0;return T(e,(function(){t++})),t},toArray:function(e){return T(e,(function(e){return e}))||[]},only:function(e){if(!j(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=v,t.Fragment=o,t.Profiler=i,t.PureComponent=b,t.StrictMode=a,t.Suspense=u,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=z,t.act=I,t.cloneElement=function(e,t,r){if(null===e||void 0===e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var o=m({},e.props),a=e.key,i=e.ref,l=e._owner;if(null!=t){if(void 0!==t.ref&&(i=t.ref,l=k.current),void 0!==t.key&&(a=""+t.key),e.type&&e.type.defaultProps)var s=e.type.defaultProps;for(c in t)S.call(t,c)&&!C.hasOwnProperty(c)&&(o[c]=void 0===t[c]&&void 0!==s?s[c]:t[c])}var c=arguments.length-2;if(1===c)o.children=r;else if(1<c){s=Array(c);for(var u=0;u<c;u++)s[u]=arguments[u+2];o.children=s}return{$$typeof:n,type:e.type,key:a,ref:i,props:o,_owner:l}},t.createContext=function(e){return(e={$$typeof:s,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:l,_context:e},e.Consumer=e},t.createElement=E,t.createFactory=function(e){var t=E.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:c,render:e}},t.isValidElement=j,t.lazy=function(e){return{$$typeof:p,_payload:{_status:-1,_result:e},_init:_}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=M.transition;M.transition={};try{e()}finally{M.transition=t}},t.unstable_act=I,t.useCallback=function(e,t){return A.current.useCallback(e,t)},t.useContext=function(e){return A.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return A.current.useDeferredValue(e)},t.useEffect=function(e,t){return A.current.useEffect(e,t)},t.useId=function(){return A.current.useId()},t.useImperativeHandle=function(e,t,n){return A.current.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return A.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return A.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return A.current.useMemo(e,t)},t.useReducer=function(e,t,n){return A.current.useReducer(e,t,n)},t.useRef=function(e){return A.current.useRef(e)},t.useState=function(e){return A.current.useState(e)},t.useSyncExternalStore=function(e,t,n){return A.current.useSyncExternalStore(e,t,n)},t.useTransition=function(){return A.current.useTransition()},t.version="18.3.1"},219:(e,t,n)=>{var r=n(763),o={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},a={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},i={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},l={};function s(e){return r.isMemo(e)?i:l[e.$$typeof]||o}l[r.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},l[r.Memo]=i;var c=Object.defineProperty,u=Object.getOwnPropertyNames,d=Object.getOwnPropertySymbols,p=Object.getOwnPropertyDescriptor,f=Object.getPrototypeOf,h=Object.prototype;e.exports=function e(t,n,r){if("string"!==typeof n){if(h){var o=f(n);o&&o!==h&&e(t,o,r)}var i=u(n);d&&(i=i.concat(d(n)));for(var l=s(t),m=s(n),g=0;g<i.length;++g){var v=i[g];if(!a[v]&&(!r||!r[v])&&(!m||!m[v])&&(!l||!l[v])){var y=p(n,v);try{c(t,v,y)}catch(b){}}}}return t}},234:(e,t)=>{function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,o=e[r];if(!(0<a(o,t)))break e;e[r]=t,e[n]=o,n=r}}function r(e){return 0===e.length?null:e[0]}function o(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,o=e.length,i=o>>>1;r<i;){var l=2*(r+1)-1,s=e[l],c=l+1,u=e[c];if(0>a(s,n))c<o&&0>a(u,s)?(e[r]=u,e[c]=n,r=c):(e[r]=s,e[l]=n,r=l);else{if(!(c<o&&0>a(u,n)))break e;e[r]=u,e[c]=n,r=c}}}return t}function a(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if("object"===typeof performance&&"function"===typeof performance.now){var i=performance;t.unstable_now=function(){return i.now()}}else{var l=Date,s=l.now();t.unstable_now=function(){return l.now()-s}}var c=[],u=[],d=1,p=null,f=3,h=!1,m=!1,g=!1,v="function"===typeof setTimeout?setTimeout:null,y="function"===typeof clearTimeout?clearTimeout:null,b="undefined"!==typeof setImmediate?setImmediate:null;function x(e){for(var t=r(u);null!==t;){if(null===t.callback)o(u);else{if(!(t.startTime<=e))break;o(u),t.sortIndex=t.expirationTime,n(c,t)}t=r(u)}}function w(e){if(g=!1,x(e),!m)if(null!==r(c))m=!0,M(S);else{var t=r(u);null!==t&&z(w,t.startTime-e)}}function S(e,n){m=!1,g&&(g=!1,y(j),j=-1),h=!0;var a=f;try{for(x(n),p=r(c);null!==p&&(!(p.expirationTime>n)||e&&!R());){var i=p.callback;if("function"===typeof i){p.callback=null,f=p.priorityLevel;var l=i(p.expirationTime<=n);n=t.unstable_now(),"function"===typeof l?p.callback=l:p===r(c)&&o(c),x(n)}else o(c);p=r(c)}if(null!==p)var s=!0;else{var d=r(u);null!==d&&z(w,d.startTime-n),s=!1}return s}finally{p=null,f=a,h=!1}}"undefined"!==typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var k,C=!1,E=null,j=-1,N=5,P=-1;function R(){return!(t.unstable_now()-P<N)}function T(){if(null!==E){var e=t.unstable_now();P=e;var n=!0;try{n=E(!0,e)}finally{n?k():(C=!1,E=null)}}else C=!1}if("function"===typeof b)k=function(){b(T)};else if("undefined"!==typeof MessageChannel){var _=new MessageChannel,A=_.port2;_.port1.onmessage=T,k=function(){A.postMessage(null)}}else k=function(){v(T,0)};function M(e){E=e,C||(C=!0,k())}function z(e,n){j=v((function(){e(t.unstable_now())}),n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){m||h||(m=!0,M(S))},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):N=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return f},t.unstable_getFirstCallbackNode=function(){return r(c)},t.unstable_next=function(e){switch(f){case 1:case 2:case 3:var t=3;break;default:t=f}var n=f;f=t;try{return e()}finally{f=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=f;f=e;try{return t()}finally{f=n}},t.unstable_scheduleCallback=function(e,o,a){var i=t.unstable_now();switch("object"===typeof a&&null!==a?a="number"===typeof(a=a.delay)&&0<a?i+a:i:a=i,e){case 1:var l=-1;break;case 2:l=250;break;case 5:l=1073741823;break;case 4:l=1e4;break;default:l=5e3}return e={id:d++,callback:o,priorityLevel:e,startTime:a,expirationTime:l=a+l,sortIndex:-1},a>i?(e.sortIndex=a,n(u,e),null===r(c)&&e===r(u)&&(g?(y(j),j=-1):g=!0,z(w,a-i))):(e.sortIndex=l,n(c,e),m||h||(m=!0,M(S))),e},t.unstable_shouldYield=R,t.unstable_wrapCallback=function(e){var t=f;return function(){var n=f;f=t;try{return e.apply(this,arguments)}finally{f=n}}}},391:(e,t,n)=>{var r=n(950);t.H=r.createRoot,r.hydrateRoot},528:(e,t)=>{var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler");Symbol.for("react.provider");var l=Symbol.for("react.consumer"),s=Symbol.for("react.context"),c=Symbol.for("react.forward_ref"),u=Symbol.for("react.suspense"),d=Symbol.for("react.suspense_list"),p=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),h=Symbol.for("react.view_transition"),m=Symbol.for("react.client.reference");function g(e){if("object"===typeof e&&null!==e){var t=e.$$typeof;switch(t){case n:switch(e=e.type){case o:case i:case a:case u:case d:case h:return e;default:switch(e=e&&e.$$typeof){case s:case c:case f:case p:case l:return e;default:return t}}case r:return t}}}t.Hy=function(e){return"string"===typeof e||"function"===typeof e||e===o||e===i||e===a||e===u||e===d||"object"===typeof e&&null!==e&&(e.$$typeof===f||e.$$typeof===p||e.$$typeof===s||e.$$typeof===l||e.$$typeof===c||e.$$typeof===m||void 0!==e.getModuleId)}},579:(e,t,n)=>{e.exports=n(153)},730:(e,t,n)=>{var r=n(43),o=n(853);function a(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var i=new Set,l={};function s(e,t){c(e,t),c(e+"Capture",t)}function c(e,t){for(l[e]=t,e=0;e<t.length;e++)i.add(t[e])}var u=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),d=Object.prototype.hasOwnProperty,p=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,f={},h={};function m(e,t,n,r,o,a,i){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=a,this.removeEmptyString=i}var g={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach((function(e){g[e]=new m(e,0,!1,e,null,!1,!1)})),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach((function(e){var t=e[0];g[t]=new m(t,1,!1,e[1],null,!1,!1)})),["contentEditable","draggable","spellCheck","value"].forEach((function(e){g[e]=new m(e,2,!1,e.toLowerCase(),null,!1,!1)})),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach((function(e){g[e]=new m(e,2,!1,e,null,!1,!1)})),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach((function(e){g[e]=new m(e,3,!1,e.toLowerCase(),null,!1,!1)})),["checked","multiple","muted","selected"].forEach((function(e){g[e]=new m(e,3,!0,e,null,!1,!1)})),["capture","download"].forEach((function(e){g[e]=new m(e,4,!1,e,null,!1,!1)})),["cols","rows","size","span"].forEach((function(e){g[e]=new m(e,6,!1,e,null,!1,!1)})),["rowSpan","start"].forEach((function(e){g[e]=new m(e,5,!1,e.toLowerCase(),null,!1,!1)}));var v=/[\-:]([a-z])/g;function y(e){return e[1].toUpperCase()}function b(e,t,n,r){var o=g.hasOwnProperty(t)?g[t]:null;(null!==o?0!==o.type:r||!(2<t.length)||"o"!==t[0]&&"O"!==t[0]||"n"!==t[1]&&"N"!==t[1])&&(function(e,t,n,r){if(null===t||"undefined"===typeof t||function(e,t,n,r){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!r&&(null!==n?!n.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,n,r))return!0;if(r)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,n,o,r)&&(n=null),r||null===o?function(e){return!!d.call(h,e)||!d.call(f,e)&&(p.test(e)?h[e]=!0:(f[e]=!0,!1))}(t)&&(null===n?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=null===n?3!==o.type&&"":n:(t=o.attributeName,r=o.attributeNamespace,null===n?e.removeAttribute(t):(n=3===(o=o.type)||4===o&&!0===n?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach((function(e){var t=e.replace(v,y);g[t]=new m(t,1,!1,e,null,!1,!1)})),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach((function(e){var t=e.replace(v,y);g[t]=new m(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)})),["xml:base","xml:lang","xml:space"].forEach((function(e){var t=e.replace(v,y);g[t]=new m(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)})),["tabIndex","crossOrigin"].forEach((function(e){g[e]=new m(e,1,!1,e.toLowerCase(),null,!1,!1)})),g.xlinkHref=new m("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach((function(e){g[e]=new m(e,1,!1,e.toLowerCase(),null,!0,!0)}));var x=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,w=Symbol.for("react.element"),S=Symbol.for("react.portal"),k=Symbol.for("react.fragment"),C=Symbol.for("react.strict_mode"),E=Symbol.for("react.profiler"),j=Symbol.for("react.provider"),N=Symbol.for("react.context"),P=Symbol.for("react.forward_ref"),R=Symbol.for("react.suspense"),T=Symbol.for("react.suspense_list"),_=Symbol.for("react.memo"),A=Symbol.for("react.lazy");Symbol.for("react.scope"),Symbol.for("react.debug_trace_mode");var M=Symbol.for("react.offscreen");Symbol.for("react.legacy_hidden"),Symbol.for("react.cache"),Symbol.for("react.tracing_marker");var z=Symbol.iterator;function I(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=z&&e[z]||e["@@iterator"])?e:null}var O,F=Object.assign;function L(e){if(void 0===O)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);O=t&&t[1]||""}return"\n"+O+e}var D=!1;function B(e,t){if(!e||D)return"";D=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var r=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){r=c}e.call(t.prototype)}else{try{throw Error()}catch(c){r=c}e()}}catch(c){if(c&&r&&"string"===typeof c.stack){for(var o=c.stack.split("\n"),a=r.stack.split("\n"),i=o.length-1,l=a.length-1;1<=i&&0<=l&&o[i]!==a[l];)l--;for(;1<=i&&0<=l;i--,l--)if(o[i]!==a[l]){if(1!==i||1!==l)do{if(i--,0>--l||o[i]!==a[l]){var s="\n"+o[i].replace(" at new "," at ");return e.displayName&&s.includes("<anonymous>")&&(s=s.replace("<anonymous>",e.displayName)),s}}while(1<=i&&0<=l);break}}}finally{D=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?L(e):""}function W(e){switch(e.tag){case 5:return L(e.type);case 16:return L("Lazy");case 13:return L("Suspense");case 19:return L("SuspenseList");case 0:case 2:case 15:return e=B(e.type,!1);case 11:return e=B(e.type.render,!1);case 1:return e=B(e.type,!0);default:return""}}function H(e){if(null==e)return null;if("function"===typeof e)return e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case k:return"Fragment";case S:return"Portal";case E:return"Profiler";case C:return"StrictMode";case R:return"Suspense";case T:return"SuspenseList"}if("object"===typeof e)switch(e.$$typeof){case N:return(e.displayName||"Context")+".Consumer";case j:return(e._context.displayName||"Context")+".Provider";case P:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case _:return null!==(t=e.displayName||null)?t:H(e.type)||"Memo";case A:t=e._payload,e=e._init;try{return H(e(t))}catch(n){}}return null}function U(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=(e=t.render).displayName||e.name||"",t.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return H(t);case 8:return t===C?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if("function"===typeof t)return t.displayName||t.name||null;if("string"===typeof t)return t}return null}function V(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function $(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function K(e){e._valueTracker||(e._valueTracker=function(e){var t=$(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var o=n.get,a=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(e){r=""+e,a.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function q(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=$(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function Y(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}function G(e,t){var n=t.checked;return F({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:e._wrapperState.initialChecked})}function Q(e,t){var n=null==t.defaultValue?"":t.defaultValue,r=null!=t.checked?t.checked:t.defaultChecked;n=V(null!=t.value?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function X(e,t){null!=(t=t.checked)&&b(e,"checked",t,!1)}function J(e,t){X(e,t);var n=V(t.value),r=t.type;if(null!=n)"number"===r?(0===n&&""===e.value||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if("submit"===r||"reset"===r)return void e.removeAttribute("value");t.hasOwnProperty("value")?ee(e,t.type,n):t.hasOwnProperty("defaultValue")&&ee(e,t.type,V(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function Z(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!("submit"!==r&&"reset"!==r||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}""!==(n=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==n&&(e.name=n)}function ee(e,t,n){"number"===t&&Y(e.ownerDocument)===e||(null==n?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var te=Array.isArray;function ne(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+V(n),t=null,o=0;o<e.length;o++){if(e[o].value===n)return e[o].selected=!0,void(r&&(e[o].defaultSelected=!0));null!==t||e[o].disabled||(t=e[o])}null!==t&&(t.selected=!0)}}function re(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(a(91));return F({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function oe(e,t){var n=t.value;if(null==n){if(n=t.children,t=t.defaultValue,null!=n){if(null!=t)throw Error(a(92));if(te(n)){if(1<n.length)throw Error(a(93));n=n[0]}t=n}null==t&&(t=""),n=t}e._wrapperState={initialValue:V(n)}}function ae(e,t){var n=V(t.value),r=V(t.defaultValue);null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&e.defaultValue!==n&&(e.defaultValue=n)),null!=r&&(e.defaultValue=""+r)}function ie(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}function le(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function se(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?le(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var ce,ue,de=(ue=function(e,t){if("http://www.w3.org/2000/svg"!==e.namespaceURI||"innerHTML"in e)e.innerHTML=t;else{for((ce=ce||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ce.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}},"undefined"!==typeof MSApp&&MSApp.execUnsafeLocalFunction?function(e,t,n,r){MSApp.execUnsafeLocalFunction((function(){return ue(e,t)}))}:ue);function pe(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var fe={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},he=["Webkit","ms","Moz","O"];function me(e,t,n){return null==t||"boolean"===typeof t||""===t?"":n||"number"!==typeof t||0===t||fe.hasOwnProperty(e)&&fe[e]?(""+t).trim():t+"px"}function ge(e,t){for(var n in e=e.style,t)if(t.hasOwnProperty(n)){var r=0===n.indexOf("--"),o=me(n,t[n],r);"float"===n&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}Object.keys(fe).forEach((function(e){he.forEach((function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),fe[t]=fe[e]}))}));var ve=F({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function ye(e,t){if(t){if(ve[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(a(137,e));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(a(60));if("object"!==typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(a(61))}if(null!=t.style&&"object"!==typeof t.style)throw Error(a(62))}}function be(e,t){if(-1===e.indexOf("-"))return"string"===typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var xe=null;function we(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var Se=null,ke=null,Ce=null;function Ee(e){if(e=wo(e)){if("function"!==typeof Se)throw Error(a(280));var t=e.stateNode;t&&(t=ko(t),Se(e.stateNode,e.type,t))}}function je(e){ke?Ce?Ce.push(e):Ce=[e]:ke=e}function Ne(){if(ke){var e=ke,t=Ce;if(Ce=ke=null,Ee(e),t)for(e=0;e<t.length;e++)Ee(t[e])}}function Pe(e,t){return e(t)}function Re(){}var Te=!1;function _e(e,t,n){if(Te)return e(t,n);Te=!0;try{return Pe(e,t,n)}finally{Te=!1,(null!==ke||null!==Ce)&&(Re(),Ne())}}function Ae(e,t){var n=e.stateNode;if(null===n)return null;var r=ko(n);if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(a(231,t,typeof n));return n}var Me=!1;if(u)try{var ze={};Object.defineProperty(ze,"passive",{get:function(){Me=!0}}),window.addEventListener("test",ze,ze),window.removeEventListener("test",ze,ze)}catch(ue){Me=!1}function Ie(e,t,n,r,o,a,i,l,s){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(u){this.onError(u)}}var Oe=!1,Fe=null,Le=!1,De=null,Be={onError:function(e){Oe=!0,Fe=e}};function We(e,t,n,r,o,a,i,l,s){Oe=!1,Fe=null,Ie.apply(Be,arguments)}function He(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function Ue(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function Ve(e){if(He(e)!==e)throw Error(a(188))}function $e(e){return null!==(e=function(e){var t=e.alternate;if(!t){if(null===(t=He(e)))throw Error(a(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(null===o)break;var i=o.alternate;if(null===i){if(null!==(r=o.return)){n=r;continue}break}if(o.child===i.child){for(i=o.child;i;){if(i===n)return Ve(o),e;if(i===r)return Ve(o),t;i=i.sibling}throw Error(a(188))}if(n.return!==r.return)n=o,r=i;else{for(var l=!1,s=o.child;s;){if(s===n){l=!0,n=o,r=i;break}if(s===r){l=!0,r=o,n=i;break}s=s.sibling}if(!l){for(s=i.child;s;){if(s===n){l=!0,n=i,r=o;break}if(s===r){l=!0,r=i,n=o;break}s=s.sibling}if(!l)throw Error(a(189))}}if(n.alternate!==r)throw Error(a(190))}if(3!==n.tag)throw Error(a(188));return n.stateNode.current===n?e:t}(e))?Ke(e):null}function Ke(e){if(5===e.tag||6===e.tag)return e;for(e=e.child;null!==e;){var t=Ke(e);if(null!==t)return t;e=e.sibling}return null}var qe=o.unstable_scheduleCallback,Ye=o.unstable_cancelCallback,Ge=o.unstable_shouldYield,Qe=o.unstable_requestPaint,Xe=o.unstable_now,Je=o.unstable_getCurrentPriorityLevel,Ze=o.unstable_ImmediatePriority,et=o.unstable_UserBlockingPriority,tt=o.unstable_NormalPriority,nt=o.unstable_LowPriority,rt=o.unstable_IdlePriority,ot=null,at=null;var it=Math.clz32?Math.clz32:function(e){return e>>>=0,0===e?32:31-(lt(e)/st|0)|0},lt=Math.log,st=Math.LN2;var ct=64,ut=4194304;function dt(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194240&e;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return 130023424&e;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function pt(e,t){var n=e.pendingLanes;if(0===n)return 0;var r=0,o=e.suspendedLanes,a=e.pingedLanes,i=268435455&n;if(0!==i){var l=i&~o;0!==l?r=dt(l):0!==(a&=i)&&(r=dt(a))}else 0!==(i=n&~o)?r=dt(i):0!==a&&(r=dt(a));if(0===r)return 0;if(0!==t&&t!==r&&0===(t&o)&&((o=r&-r)>=(a=t&-t)||16===o&&0!==(4194240&a)))return t;if(0!==(4&r)&&(r|=16&n),0!==(t=e.entangledLanes))for(e=e.entanglements,t&=r;0<t;)o=1<<(n=31-it(t)),r|=e[n],t&=~o;return r}function ft(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function ht(e){return 0!==(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function mt(){var e=ct;return 0===(4194240&(ct<<=1))&&(ct=64),e}function gt(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function vt(e,t,n){e.pendingLanes|=t,536870912!==t&&(e.suspendedLanes=0,e.pingedLanes=0),(e=e.eventTimes)[t=31-it(t)]=n}function yt(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-it(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var bt=0;function xt(e){return 1<(e&=-e)?4<e?0!==(268435455&e)?16:536870912:4:1}var wt,St,kt,Ct,Et,Nt=!1,Pt=[],Rt=null,Tt=null,_t=null,At=new Map,Mt=new Map,zt=[],It="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Ot(e,t){switch(e){case"focusin":case"focusout":Rt=null;break;case"dragenter":case"dragleave":Tt=null;break;case"mouseover":case"mouseout":_t=null;break;case"pointerover":case"pointerout":At.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Mt.delete(t.pointerId)}}function Ft(e,t,n,r,o,a){return null===e||e.nativeEvent!==a?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:a,targetContainers:[o]},null!==t&&(null!==(t=wo(t))&&St(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==o&&-1===t.indexOf(o)&&t.push(o),e)}function Lt(e){var t=xo(e.target);if(null!==t){var n=He(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=Ue(n)))return e.blockedOn=t,void Et(e.priority,(function(){kt(n)}))}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function Dt(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=Qt(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(null!==n)return null!==(t=wo(n))&&St(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);xe=r,n.target.dispatchEvent(r),xe=null,t.shift()}return!0}function Bt(e,t,n){Dt(e)&&n.delete(t)}function Wt(){Nt=!1,null!==Rt&&Dt(Rt)&&(Rt=null),null!==Tt&&Dt(Tt)&&(Tt=null),null!==_t&&Dt(_t)&&(_t=null),At.forEach(Bt),Mt.forEach(Bt)}function Ht(e,t){e.blockedOn===t&&(e.blockedOn=null,Nt||(Nt=!0,o.unstable_scheduleCallback(o.unstable_NormalPriority,Wt)))}function Ut(e){function t(t){return Ht(t,e)}if(0<Pt.length){Ht(Pt[0],e);for(var n=1;n<Pt.length;n++){var r=Pt[n];r.blockedOn===e&&(r.blockedOn=null)}}for(null!==Rt&&Ht(Rt,e),null!==Tt&&Ht(Tt,e),null!==_t&&Ht(_t,e),At.forEach(t),Mt.forEach(t),n=0;n<zt.length;n++)(r=zt[n]).blockedOn===e&&(r.blockedOn=null);for(;0<zt.length&&null===(n=zt[0]).blockedOn;)Lt(n),null===n.blockedOn&&zt.shift()}var Vt=x.ReactCurrentBatchConfig,$t=!0;function Kt(e,t,n,r){var o=bt,a=Vt.transition;Vt.transition=null;try{bt=1,Yt(e,t,n,r)}finally{bt=o,Vt.transition=a}}function qt(e,t,n,r){var o=bt,a=Vt.transition;Vt.transition=null;try{bt=4,Yt(e,t,n,r)}finally{bt=o,Vt.transition=a}}function Yt(e,t,n,r){if($t){var o=Qt(e,t,n,r);if(null===o)$r(e,t,r,Gt,n),Ot(e,r);else if(function(e,t,n,r,o){switch(t){case"focusin":return Rt=Ft(Rt,e,t,n,r,o),!0;case"dragenter":return Tt=Ft(Tt,e,t,n,r,o),!0;case"mouseover":return _t=Ft(_t,e,t,n,r,o),!0;case"pointerover":var a=o.pointerId;return At.set(a,Ft(At.get(a)||null,e,t,n,r,o)),!0;case"gotpointercapture":return a=o.pointerId,Mt.set(a,Ft(Mt.get(a)||null,e,t,n,r,o)),!0}return!1}(o,e,t,n,r))r.stopPropagation();else if(Ot(e,r),4&t&&-1<It.indexOf(e)){for(;null!==o;){var a=wo(o);if(null!==a&&wt(a),null===(a=Qt(e,t,n,r))&&$r(e,t,r,Gt,n),a===o)break;o=a}null!==o&&r.stopPropagation()}else $r(e,t,r,null,n)}}var Gt=null;function Qt(e,t,n,r){if(Gt=null,null!==(e=xo(e=we(r))))if(null===(t=He(e)))e=null;else if(13===(n=t.tag)){if(null!==(e=Ue(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Gt=e,null}function Xt(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Je()){case Ze:return 1;case et:return 4;case tt:case nt:return 16;case rt:return 536870912;default:return 16}default:return 16}}var Jt=null,Zt=null,en=null;function tn(){if(en)return en;var e,t,n=Zt,r=n.length,o="value"in Jt?Jt.value:Jt.textContent,a=o.length;for(e=0;e<r&&n[e]===o[e];e++);var i=r-e;for(t=1;t<=i&&n[r-t]===o[a-t];t++);return en=o.slice(e,1<t?1-t:void 0)}function nn(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function rn(){return!0}function on(){return!1}function an(e){function t(t,n,r,o,a){for(var i in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=o,this.target=a,this.currentTarget=null,e)e.hasOwnProperty(i)&&(t=e[i],this[i]=t?t(o):o[i]);return this.isDefaultPrevented=(null!=o.defaultPrevented?o.defaultPrevented:!1===o.returnValue)?rn:on,this.isPropagationStopped=on,this}return F(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=rn)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=rn)},persist:function(){},isPersistent:rn}),t}var ln,sn,cn,un={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},dn=an(un),pn=F({},un,{view:0,detail:0}),fn=an(pn),hn=F({},pn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:jn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==cn&&(cn&&"mousemove"===e.type?(ln=e.screenX-cn.screenX,sn=e.screenY-cn.screenY):sn=ln=0,cn=e),ln)},movementY:function(e){return"movementY"in e?e.movementY:sn}}),mn=an(hn),gn=an(F({},hn,{dataTransfer:0})),vn=an(F({},pn,{relatedTarget:0})),yn=an(F({},un,{animationName:0,elapsedTime:0,pseudoElement:0})),bn=F({},un,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),xn=an(bn),wn=an(F({},un,{data:0})),Sn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},kn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Cn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function En(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=Cn[e])&&!!t[e]}function jn(){return En}var Nn=F({},pn,{key:function(e){if(e.key){var t=Sn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=nn(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?kn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:jn,charCode:function(e){return"keypress"===e.type?nn(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?nn(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),Pn=an(Nn),Rn=an(F({},hn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),Tn=an(F({},pn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:jn})),_n=an(F({},un,{propertyName:0,elapsedTime:0,pseudoElement:0})),An=F({},hn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Mn=an(An),zn=[9,13,27,32],In=u&&"CompositionEvent"in window,On=null;u&&"documentMode"in document&&(On=document.documentMode);var Fn=u&&"TextEvent"in window&&!On,Ln=u&&(!In||On&&8<On&&11>=On),Dn=String.fromCharCode(32),Bn=!1;function Wn(e,t){switch(e){case"keyup":return-1!==zn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Hn(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var Un=!1;var Vn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function $n(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Vn[e.type]:"textarea"===t}function Kn(e,t,n,r){je(r),0<(t=qr(t,"onChange")).length&&(n=new dn("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var qn=null,Yn=null;function Gn(e){Dr(e,0)}function Qn(e){if(q(So(e)))return e}function Xn(e,t){if("change"===e)return t}var Jn=!1;if(u){var Zn;if(u){var er="oninput"in document;if(!er){var tr=document.createElement("div");tr.setAttribute("oninput","return;"),er="function"===typeof tr.oninput}Zn=er}else Zn=!1;Jn=Zn&&(!document.documentMode||9<document.documentMode)}function nr(){qn&&(qn.detachEvent("onpropertychange",rr),Yn=qn=null)}function rr(e){if("value"===e.propertyName&&Qn(Yn)){var t=[];Kn(t,Yn,e,we(e)),_e(Gn,t)}}function or(e,t,n){"focusin"===e?(nr(),Yn=n,(qn=t).attachEvent("onpropertychange",rr)):"focusout"===e&&nr()}function ar(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Qn(Yn)}function ir(e,t){if("click"===e)return Qn(t)}function lr(e,t){if("input"===e||"change"===e)return Qn(t)}var sr="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t};function cr(e,t){if(sr(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!d.call(t,o)||!sr(e[o],t[o]))return!1}return!0}function ur(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function dr(e,t){var n,r=ur(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=ur(r)}}function pr(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?pr(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function fr(){for(var e=window,t=Y();t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=Y((e=t.contentWindow).document)}return t}function hr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}function mr(e){var t=fr(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&pr(n.ownerDocument.documentElement,n)){if(null!==r&&hr(n))if(t=r.start,void 0===(e=r.end)&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if((e=(t=n.ownerDocument||document)&&t.defaultView||window).getSelection){e=e.getSelection();var o=n.textContent.length,a=Math.min(r.start,o);r=void 0===r.end?a:Math.min(r.end,o),!e.extend&&a>r&&(o=r,r=a,a=o),o=dr(n,a);var i=dr(n,r);o&&i&&(1!==e.rangeCount||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==i.node||e.focusOffset!==i.offset)&&((t=t.createRange()).setStart(o.node,o.offset),e.removeAllRanges(),a>r?(e.addRange(t),e.extend(i.node,i.offset)):(t.setEnd(i.node,i.offset),e.addRange(t)))}for(t=[],e=n;e=e.parentNode;)1===e.nodeType&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for("function"===typeof n.focus&&n.focus(),n=0;n<t.length;n++)(e=t[n]).element.scrollLeft=e.left,e.element.scrollTop=e.top}}var gr=u&&"documentMode"in document&&11>=document.documentMode,vr=null,yr=null,br=null,xr=!1;function wr(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;xr||null==vr||vr!==Y(r)||("selectionStart"in(r=vr)&&hr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},br&&cr(br,r)||(br=r,0<(r=qr(yr,"onSelect")).length&&(t=new dn("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=vr)))}function Sr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var kr={animationend:Sr("Animation","AnimationEnd"),animationiteration:Sr("Animation","AnimationIteration"),animationstart:Sr("Animation","AnimationStart"),transitionend:Sr("Transition","TransitionEnd")},Cr={},Er={};function jr(e){if(Cr[e])return Cr[e];if(!kr[e])return e;var t,n=kr[e];for(t in n)if(n.hasOwnProperty(t)&&t in Er)return Cr[e]=n[t];return e}u&&(Er=document.createElement("div").style,"AnimationEvent"in window||(delete kr.animationend.animation,delete kr.animationiteration.animation,delete kr.animationstart.animation),"TransitionEvent"in window||delete kr.transitionend.transition);var Nr=jr("animationend"),Pr=jr("animationiteration"),Rr=jr("animationstart"),Tr=jr("transitionend"),_r=new Map,Ar="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Mr(e,t){_r.set(e,t),s(t,[e])}for(var zr=0;zr<Ar.length;zr++){var Ir=Ar[zr];Mr(Ir.toLowerCase(),"on"+(Ir[0].toUpperCase()+Ir.slice(1)))}Mr(Nr,"onAnimationEnd"),Mr(Pr,"onAnimationIteration"),Mr(Rr,"onAnimationStart"),Mr("dblclick","onDoubleClick"),Mr("focusin","onFocus"),Mr("focusout","onBlur"),Mr(Tr,"onTransitionEnd"),c("onMouseEnter",["mouseout","mouseover"]),c("onMouseLeave",["mouseout","mouseover"]),c("onPointerEnter",["pointerout","pointerover"]),c("onPointerLeave",["pointerout","pointerover"]),s("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),s("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),s("onBeforeInput",["compositionend","keypress","textInput","paste"]),s("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),s("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),s("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Or="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Fr=new Set("cancel close invalid load scroll toggle".split(" ").concat(Or));function Lr(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,function(e,t,n,r,o,i,l,s,c){if(We.apply(this,arguments),Oe){if(!Oe)throw Error(a(198));var u=Fe;Oe=!1,Fe=null,Le||(Le=!0,De=u)}}(r,t,void 0,e),e.currentTarget=null}function Dr(e,t){t=0!==(4&t);for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var a=void 0;if(t)for(var i=r.length-1;0<=i;i--){var l=r[i],s=l.instance,c=l.currentTarget;if(l=l.listener,s!==a&&o.isPropagationStopped())break e;Lr(o,l,c),a=s}else for(i=0;i<r.length;i++){if(s=(l=r[i]).instance,c=l.currentTarget,l=l.listener,s!==a&&o.isPropagationStopped())break e;Lr(o,l,c),a=s}}}if(Le)throw e=De,Le=!1,De=null,e}function Br(e,t){var n=t[vo];void 0===n&&(n=t[vo]=new Set);var r=e+"__bubble";n.has(r)||(Vr(t,e,2,!1),n.add(r))}function Wr(e,t,n){var r=0;t&&(r|=4),Vr(n,e,r,t)}var Hr="_reactListening"+Math.random().toString(36).slice(2);function Ur(e){if(!e[Hr]){e[Hr]=!0,i.forEach((function(t){"selectionchange"!==t&&(Fr.has(t)||Wr(t,!1,e),Wr(t,!0,e))}));var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Hr]||(t[Hr]=!0,Wr("selectionchange",!1,t))}}function Vr(e,t,n,r){switch(Xt(t)){case 1:var o=Kt;break;case 4:o=qt;break;default:o=Yt}n=o.bind(null,t,n,e),o=void 0,!Me||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(o=!0),r?void 0!==o?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):void 0!==o?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function $r(e,t,n,r,o){var a=r;if(0===(1&t)&&0===(2&t)&&null!==r)e:for(;;){if(null===r)return;var i=r.tag;if(3===i||4===i){var l=r.stateNode.containerInfo;if(l===o||8===l.nodeType&&l.parentNode===o)break;if(4===i)for(i=r.return;null!==i;){var s=i.tag;if((3===s||4===s)&&((s=i.stateNode.containerInfo)===o||8===s.nodeType&&s.parentNode===o))return;i=i.return}for(;null!==l;){if(null===(i=xo(l)))return;if(5===(s=i.tag)||6===s){r=a=i;continue e}l=l.parentNode}}r=r.return}_e((function(){var r=a,o=we(n),i=[];e:{var l=_r.get(e);if(void 0!==l){var s=dn,c=e;switch(e){case"keypress":if(0===nn(n))break e;case"keydown":case"keyup":s=Pn;break;case"focusin":c="focus",s=vn;break;case"focusout":c="blur",s=vn;break;case"beforeblur":case"afterblur":s=vn;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":s=mn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":s=gn;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":s=Tn;break;case Nr:case Pr:case Rr:s=yn;break;case Tr:s=_n;break;case"scroll":s=fn;break;case"wheel":s=Mn;break;case"copy":case"cut":case"paste":s=xn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":s=Rn}var u=0!==(4&t),d=!u&&"scroll"===e,p=u?null!==l?l+"Capture":null:l;u=[];for(var f,h=r;null!==h;){var m=(f=h).stateNode;if(5===f.tag&&null!==m&&(f=m,null!==p&&(null!=(m=Ae(h,p))&&u.push(Kr(h,m,f)))),d)break;h=h.return}0<u.length&&(l=new s(l,c,null,n,o),i.push({event:l,listeners:u}))}}if(0===(7&t)){if(s="mouseout"===e||"pointerout"===e,(!(l="mouseover"===e||"pointerover"===e)||n===xe||!(c=n.relatedTarget||n.fromElement)||!xo(c)&&!c[go])&&(s||l)&&(l=o.window===o?o:(l=o.ownerDocument)?l.defaultView||l.parentWindow:window,s?(s=r,null!==(c=(c=n.relatedTarget||n.toElement)?xo(c):null)&&(c!==(d=He(c))||5!==c.tag&&6!==c.tag)&&(c=null)):(s=null,c=r),s!==c)){if(u=mn,m="onMouseLeave",p="onMouseEnter",h="mouse","pointerout"!==e&&"pointerover"!==e||(u=Rn,m="onPointerLeave",p="onPointerEnter",h="pointer"),d=null==s?l:So(s),f=null==c?l:So(c),(l=new u(m,h+"leave",s,n,o)).target=d,l.relatedTarget=f,m=null,xo(o)===r&&((u=new u(p,h+"enter",c,n,o)).target=f,u.relatedTarget=d,m=u),d=m,s&&c)e:{for(p=c,h=0,f=u=s;f;f=Yr(f))h++;for(f=0,m=p;m;m=Yr(m))f++;for(;0<h-f;)u=Yr(u),h--;for(;0<f-h;)p=Yr(p),f--;for(;h--;){if(u===p||null!==p&&u===p.alternate)break e;u=Yr(u),p=Yr(p)}u=null}else u=null;null!==s&&Gr(i,l,s,u,!1),null!==c&&null!==d&&Gr(i,d,c,u,!0)}if("select"===(s=(l=r?So(r):window).nodeName&&l.nodeName.toLowerCase())||"input"===s&&"file"===l.type)var g=Xn;else if($n(l))if(Jn)g=lr;else{g=ar;var v=or}else(s=l.nodeName)&&"input"===s.toLowerCase()&&("checkbox"===l.type||"radio"===l.type)&&(g=ir);switch(g&&(g=g(e,r))?Kn(i,g,n,o):(v&&v(e,l,r),"focusout"===e&&(v=l._wrapperState)&&v.controlled&&"number"===l.type&&ee(l,"number",l.value)),v=r?So(r):window,e){case"focusin":($n(v)||"true"===v.contentEditable)&&(vr=v,yr=r,br=null);break;case"focusout":br=yr=vr=null;break;case"mousedown":xr=!0;break;case"contextmenu":case"mouseup":case"dragend":xr=!1,wr(i,n,o);break;case"selectionchange":if(gr)break;case"keydown":case"keyup":wr(i,n,o)}var y;if(In)e:{switch(e){case"compositionstart":var b="onCompositionStart";break e;case"compositionend":b="onCompositionEnd";break e;case"compositionupdate":b="onCompositionUpdate";break e}b=void 0}else Un?Wn(e,n)&&(b="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(b="onCompositionStart");b&&(Ln&&"ko"!==n.locale&&(Un||"onCompositionStart"!==b?"onCompositionEnd"===b&&Un&&(y=tn()):(Zt="value"in(Jt=o)?Jt.value:Jt.textContent,Un=!0)),0<(v=qr(r,b)).length&&(b=new wn(b,e,null,n,o),i.push({event:b,listeners:v}),y?b.data=y:null!==(y=Hn(n))&&(b.data=y))),(y=Fn?function(e,t){switch(e){case"compositionend":return Hn(t);case"keypress":return 32!==t.which?null:(Bn=!0,Dn);case"textInput":return(e=t.data)===Dn&&Bn?null:e;default:return null}}(e,n):function(e,t){if(Un)return"compositionend"===e||!In&&Wn(e,t)?(e=tn(),en=Zt=Jt=null,Un=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Ln&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(r=qr(r,"onBeforeInput")).length&&(o=new wn("onBeforeInput","beforeinput",null,n,o),i.push({event:o,listeners:r}),o.data=y))}Dr(i,t)}))}function Kr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function qr(e,t){for(var n=t+"Capture",r=[];null!==e;){var o=e,a=o.stateNode;5===o.tag&&null!==a&&(o=a,null!=(a=Ae(e,n))&&r.unshift(Kr(e,a,o)),null!=(a=Ae(e,t))&&r.push(Kr(e,a,o))),e=e.return}return r}function Yr(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag);return e||null}function Gr(e,t,n,r,o){for(var a=t._reactName,i=[];null!==n&&n!==r;){var l=n,s=l.alternate,c=l.stateNode;if(null!==s&&s===r)break;5===l.tag&&null!==c&&(l=c,o?null!=(s=Ae(n,a))&&i.unshift(Kr(n,s,l)):o||null!=(s=Ae(n,a))&&i.push(Kr(n,s,l))),n=n.return}0!==i.length&&e.push({event:t,listeners:i})}var Qr=/\r\n?/g,Xr=/\u0000|\uFFFD/g;function Jr(e){return("string"===typeof e?e:""+e).replace(Qr,"\n").replace(Xr,"")}function Zr(e,t,n){if(t=Jr(t),Jr(e)!==t&&n)throw Error(a(425))}function eo(){}var to=null,no=null;function ro(e,t){return"textarea"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var oo="function"===typeof setTimeout?setTimeout:void 0,ao="function"===typeof clearTimeout?clearTimeout:void 0,io="function"===typeof Promise?Promise:void 0,lo="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof io?function(e){return io.resolve(null).then(e).catch(so)}:oo;function so(e){setTimeout((function(){throw e}))}function co(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&8===o.nodeType)if("/$"===(n=o.data)){if(0===r)return e.removeChild(o),void Ut(t);r--}else"$"!==n&&"$?"!==n&&"$!"!==n||r++;n=o}while(n);Ut(t)}function uo(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t)break;if("/$"===t)return null}}return e}function po(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}var fo=Math.random().toString(36).slice(2),ho="__reactFiber$"+fo,mo="__reactProps$"+fo,go="__reactContainer$"+fo,vo="__reactEvents$"+fo,yo="__reactListeners$"+fo,bo="__reactHandles$"+fo;function xo(e){var t=e[ho];if(t)return t;for(var n=e.parentNode;n;){if(t=n[go]||n[ho]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=po(e);null!==e;){if(n=e[ho])return n;e=po(e)}return t}n=(e=n).parentNode}return null}function wo(e){return!(e=e[ho]||e[go])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function So(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(a(33))}function ko(e){return e[mo]||null}var Co=[],Eo=-1;function jo(e){return{current:e}}function No(e){0>Eo||(e.current=Co[Eo],Co[Eo]=null,Eo--)}function Po(e,t){Eo++,Co[Eo]=e.current,e.current=t}var Ro={},To=jo(Ro),_o=jo(!1),Ao=Ro;function Mo(e,t){var n=e.type.contextTypes;if(!n)return Ro;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o,a={};for(o in n)a[o]=t[o];return r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=a),a}function zo(e){return null!==(e=e.childContextTypes)&&void 0!==e}function Io(){No(_o),No(To)}function Oo(e,t,n){if(To.current!==Ro)throw Error(a(168));Po(To,t),Po(_o,n)}function Fo(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,"function"!==typeof r.getChildContext)return n;for(var o in r=r.getChildContext())if(!(o in t))throw Error(a(108,U(e)||"Unknown",o));return F({},n,r)}function Lo(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Ro,Ao=To.current,Po(To,e),Po(_o,_o.current),!0}function Do(e,t,n){var r=e.stateNode;if(!r)throw Error(a(169));n?(e=Fo(e,t,Ao),r.__reactInternalMemoizedMergedChildContext=e,No(_o),No(To),Po(To,e)):No(_o),Po(_o,n)}var Bo=null,Wo=!1,Ho=!1;function Uo(e){null===Bo?Bo=[e]:Bo.push(e)}function Vo(){if(!Ho&&null!==Bo){Ho=!0;var e=0,t=bt;try{var n=Bo;for(bt=1;e<n.length;e++){var r=n[e];do{r=r(!0)}while(null!==r)}Bo=null,Wo=!1}catch(o){throw null!==Bo&&(Bo=Bo.slice(e+1)),qe(Ze,Vo),o}finally{bt=t,Ho=!1}}return null}var $o=[],Ko=0,qo=null,Yo=0,Go=[],Qo=0,Xo=null,Jo=1,Zo="";function ea(e,t){$o[Ko++]=Yo,$o[Ko++]=qo,qo=e,Yo=t}function ta(e,t,n){Go[Qo++]=Jo,Go[Qo++]=Zo,Go[Qo++]=Xo,Xo=e;var r=Jo;e=Zo;var o=32-it(r)-1;r&=~(1<<o),n+=1;var a=32-it(t)+o;if(30<a){var i=o-o%5;a=(r&(1<<i)-1).toString(32),r>>=i,o-=i,Jo=1<<32-it(t)+o|n<<o|r,Zo=a+e}else Jo=1<<a|n<<o|r,Zo=e}function na(e){null!==e.return&&(ea(e,1),ta(e,1,0))}function ra(e){for(;e===qo;)qo=$o[--Ko],$o[Ko]=null,Yo=$o[--Ko],$o[Ko]=null;for(;e===Xo;)Xo=Go[--Qo],Go[Qo]=null,Zo=Go[--Qo],Go[Qo]=null,Jo=Go[--Qo],Go[Qo]=null}var oa=null,aa=null,ia=!1,la=null;function sa(e,t){var n=Ac(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,null===(t=e.deletions)?(e.deletions=[n],e.flags|=16):t.push(n)}function ca(e,t){switch(e.tag){case 5:var n=e.type;return null!==(t=1!==t.nodeType||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,oa=e,aa=uo(t.firstChild),!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,oa=e,aa=null,!0);case 13:return null!==(t=8!==t.nodeType?null:t)&&(n=null!==Xo?{id:Jo,overflow:Zo}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},(n=Ac(18,null,null,0)).stateNode=t,n.return=e,e.child=n,oa=e,aa=null,!0);default:return!1}}function ua(e){return 0!==(1&e.mode)&&0===(128&e.flags)}function da(e){if(ia){var t=aa;if(t){var n=t;if(!ca(e,t)){if(ua(e))throw Error(a(418));t=uo(n.nextSibling);var r=oa;t&&ca(e,t)?sa(r,n):(e.flags=-4097&e.flags|2,ia=!1,oa=e)}}else{if(ua(e))throw Error(a(418));e.flags=-4097&e.flags|2,ia=!1,oa=e}}}function pa(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;oa=e}function fa(e){if(e!==oa)return!1;if(!ia)return pa(e),ia=!0,!1;var t;if((t=3!==e.tag)&&!(t=5!==e.tag)&&(t="head"!==(t=e.type)&&"body"!==t&&!ro(e.type,e.memoizedProps)),t&&(t=aa)){if(ua(e))throw ha(),Error(a(418));for(;t;)sa(e,t),t=uo(t.nextSibling)}if(pa(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(a(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var n=e.data;if("/$"===n){if(0===t){aa=uo(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++}e=e.nextSibling}aa=null}}else aa=oa?uo(e.stateNode.nextSibling):null;return!0}function ha(){for(var e=aa;e;)e=uo(e.nextSibling)}function ma(){aa=oa=null,ia=!1}function ga(e){null===la?la=[e]:la.push(e)}var va=x.ReactCurrentBatchConfig;function ya(e,t,n){if(null!==(e=n.ref)&&"function"!==typeof e&&"object"!==typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(a(309));var r=n.stateNode}if(!r)throw Error(a(147,e));var o=r,i=""+e;return null!==t&&null!==t.ref&&"function"===typeof t.ref&&t.ref._stringRef===i?t.ref:(t=function(e){var t=o.refs;null===e?delete t[i]:t[i]=e},t._stringRef=i,t)}if("string"!==typeof e)throw Error(a(284));if(!n._owner)throw Error(a(290,e))}return e}function ba(e,t){throw e=Object.prototype.toString.call(t),Error(a(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function xa(e){return(0,e._init)(e._payload)}function wa(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function o(e,t){return(e=zc(e,t)).index=0,e.sibling=null,e}function i(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=2,n):r:(t.flags|=2,n):(t.flags|=1048576,n)}function l(t){return e&&null===t.alternate&&(t.flags|=2),t}function s(e,t,n,r){return null===t||6!==t.tag?((t=Lc(n,e.mode,r)).return=e,t):((t=o(t,n)).return=e,t)}function c(e,t,n,r){var a=n.type;return a===k?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===a||"object"===typeof a&&null!==a&&a.$$typeof===A&&xa(a)===t.type)?((r=o(t,n.props)).ref=ya(e,t,n),r.return=e,r):((r=Ic(n.type,n.key,n.props,null,e.mode,r)).ref=ya(e,t,n),r.return=e,r)}function u(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Dc(n,e.mode,r)).return=e,t):((t=o(t,n.children||[])).return=e,t)}function d(e,t,n,r,a){return null===t||7!==t.tag?((t=Oc(n,e.mode,r,a)).return=e,t):((t=o(t,n)).return=e,t)}function p(e,t,n){if("string"===typeof t&&""!==t||"number"===typeof t)return(t=Lc(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case w:return(n=Ic(t.type,t.key,t.props,null,e.mode,n)).ref=ya(e,null,t),n.return=e,n;case S:return(t=Dc(t,e.mode,n)).return=e,t;case A:return p(e,(0,t._init)(t._payload),n)}if(te(t)||I(t))return(t=Oc(t,e.mode,n,null)).return=e,t;ba(e,t)}return null}function f(e,t,n,r){var o=null!==t?t.key:null;if("string"===typeof n&&""!==n||"number"===typeof n)return null!==o?null:s(e,t,""+n,r);if("object"===typeof n&&null!==n){switch(n.$$typeof){case w:return n.key===o?c(e,t,n,r):null;case S:return n.key===o?u(e,t,n,r):null;case A:return f(e,t,(o=n._init)(n._payload),r)}if(te(n)||I(n))return null!==o?null:d(e,t,n,r,null);ba(e,n)}return null}function h(e,t,n,r,o){if("string"===typeof r&&""!==r||"number"===typeof r)return s(t,e=e.get(n)||null,""+r,o);if("object"===typeof r&&null!==r){switch(r.$$typeof){case w:return c(t,e=e.get(null===r.key?n:r.key)||null,r,o);case S:return u(t,e=e.get(null===r.key?n:r.key)||null,r,o);case A:return h(e,t,n,(0,r._init)(r._payload),o)}if(te(r)||I(r))return d(t,e=e.get(n)||null,r,o,null);ba(t,r)}return null}function m(o,a,l,s){for(var c=null,u=null,d=a,m=a=0,g=null;null!==d&&m<l.length;m++){d.index>m?(g=d,d=null):g=d.sibling;var v=f(o,d,l[m],s);if(null===v){null===d&&(d=g);break}e&&d&&null===v.alternate&&t(o,d),a=i(v,a,m),null===u?c=v:u.sibling=v,u=v,d=g}if(m===l.length)return n(o,d),ia&&ea(o,m),c;if(null===d){for(;m<l.length;m++)null!==(d=p(o,l[m],s))&&(a=i(d,a,m),null===u?c=d:u.sibling=d,u=d);return ia&&ea(o,m),c}for(d=r(o,d);m<l.length;m++)null!==(g=h(d,o,m,l[m],s))&&(e&&null!==g.alternate&&d.delete(null===g.key?m:g.key),a=i(g,a,m),null===u?c=g:u.sibling=g,u=g);return e&&d.forEach((function(e){return t(o,e)})),ia&&ea(o,m),c}function g(o,l,s,c){var u=I(s);if("function"!==typeof u)throw Error(a(150));if(null==(s=u.call(s)))throw Error(a(151));for(var d=u=null,m=l,g=l=0,v=null,y=s.next();null!==m&&!y.done;g++,y=s.next()){m.index>g?(v=m,m=null):v=m.sibling;var b=f(o,m,y.value,c);if(null===b){null===m&&(m=v);break}e&&m&&null===b.alternate&&t(o,m),l=i(b,l,g),null===d?u=b:d.sibling=b,d=b,m=v}if(y.done)return n(o,m),ia&&ea(o,g),u;if(null===m){for(;!y.done;g++,y=s.next())null!==(y=p(o,y.value,c))&&(l=i(y,l,g),null===d?u=y:d.sibling=y,d=y);return ia&&ea(o,g),u}for(m=r(o,m);!y.done;g++,y=s.next())null!==(y=h(m,o,g,y.value,c))&&(e&&null!==y.alternate&&m.delete(null===y.key?g:y.key),l=i(y,l,g),null===d?u=y:d.sibling=y,d=y);return e&&m.forEach((function(e){return t(o,e)})),ia&&ea(o,g),u}return function e(r,a,i,s){if("object"===typeof i&&null!==i&&i.type===k&&null===i.key&&(i=i.props.children),"object"===typeof i&&null!==i){switch(i.$$typeof){case w:e:{for(var c=i.key,u=a;null!==u;){if(u.key===c){if((c=i.type)===k){if(7===u.tag){n(r,u.sibling),(a=o(u,i.props.children)).return=r,r=a;break e}}else if(u.elementType===c||"object"===typeof c&&null!==c&&c.$$typeof===A&&xa(c)===u.type){n(r,u.sibling),(a=o(u,i.props)).ref=ya(r,u,i),a.return=r,r=a;break e}n(r,u);break}t(r,u),u=u.sibling}i.type===k?((a=Oc(i.props.children,r.mode,s,i.key)).return=r,r=a):((s=Ic(i.type,i.key,i.props,null,r.mode,s)).ref=ya(r,a,i),s.return=r,r=s)}return l(r);case S:e:{for(u=i.key;null!==a;){if(a.key===u){if(4===a.tag&&a.stateNode.containerInfo===i.containerInfo&&a.stateNode.implementation===i.implementation){n(r,a.sibling),(a=o(a,i.children||[])).return=r,r=a;break e}n(r,a);break}t(r,a),a=a.sibling}(a=Dc(i,r.mode,s)).return=r,r=a}return l(r);case A:return e(r,a,(u=i._init)(i._payload),s)}if(te(i))return m(r,a,i,s);if(I(i))return g(r,a,i,s);ba(r,i)}return"string"===typeof i&&""!==i||"number"===typeof i?(i=""+i,null!==a&&6===a.tag?(n(r,a.sibling),(a=o(a,i)).return=r,r=a):(n(r,a),(a=Lc(i,r.mode,s)).return=r,r=a),l(r)):n(r,a)}}var Sa=wa(!0),ka=wa(!1),Ca=jo(null),Ea=null,ja=null,Na=null;function Pa(){Na=ja=Ea=null}function Ra(e){var t=Ca.current;No(Ca),e._currentValue=t}function Ta(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function _a(e,t){Ea=e,Na=ja=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(0!==(e.lanes&t)&&(xl=!0),e.firstContext=null)}function Aa(e){var t=e._currentValue;if(Na!==e)if(e={context:e,memoizedValue:t,next:null},null===ja){if(null===Ea)throw Error(a(308));ja=e,Ea.dependencies={lanes:0,firstContext:e}}else ja=ja.next=e;return t}var Ma=null;function za(e){null===Ma?Ma=[e]:Ma.push(e)}function Ia(e,t,n,r){var o=t.interleaved;return null===o?(n.next=n,za(t)):(n.next=o.next,o.next=n),t.interleaved=n,Oa(e,r)}function Oa(e,t){e.lanes|=t;var n=e.alternate;for(null!==n&&(n.lanes|=t),n=e,e=e.return;null!==e;)e.childLanes|=t,null!==(n=e.alternate)&&(n.childLanes|=t),n=e,e=e.return;return 3===n.tag?n.stateNode:null}var Fa=!1;function La(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Da(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Ba(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Wa(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!==(2&Rs)){var o=r.pending;return null===o?t.next=t:(t.next=o.next,o.next=t),r.pending=t,Oa(e,n)}return null===(o=r.interleaved)?(t.next=t,za(r)):(t.next=o.next,o.next=t),r.interleaved=t,Oa(e,n)}function Ha(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!==(4194240&n))){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,yt(e,n)}}function Ua(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var o=null,a=null;if(null!==(n=n.firstBaseUpdate)){do{var i={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};null===a?o=a=i:a=a.next=i,n=n.next}while(null!==n);null===a?o=a=t:a=a.next=t}else o=a=t;return n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:a,shared:r.shared,effects:r.effects},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Va(e,t,n,r){var o=e.updateQueue;Fa=!1;var a=o.firstBaseUpdate,i=o.lastBaseUpdate,l=o.shared.pending;if(null!==l){o.shared.pending=null;var s=l,c=s.next;s.next=null,null===i?a=c:i.next=c,i=s;var u=e.alternate;null!==u&&((l=(u=u.updateQueue).lastBaseUpdate)!==i&&(null===l?u.firstBaseUpdate=c:l.next=c,u.lastBaseUpdate=s))}if(null!==a){var d=o.baseState;for(i=0,u=c=s=null,l=a;;){var p=l.lane,f=l.eventTime;if((r&p)===p){null!==u&&(u=u.next={eventTime:f,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var h=e,m=l;switch(p=t,f=n,m.tag){case 1:if("function"===typeof(h=m.payload)){d=h.call(f,d,p);break e}d=h;break e;case 3:h.flags=-65537&h.flags|128;case 0:if(null===(p="function"===typeof(h=m.payload)?h.call(f,d,p):h)||void 0===p)break e;d=F({},d,p);break e;case 2:Fa=!0}}null!==l.callback&&0!==l.lane&&(e.flags|=64,null===(p=o.effects)?o.effects=[l]:p.push(l))}else f={eventTime:f,lane:p,tag:l.tag,payload:l.payload,callback:l.callback,next:null},null===u?(c=u=f,s=d):u=u.next=f,i|=p;if(null===(l=l.next)){if(null===(l=o.shared.pending))break;l=(p=l).next,p.next=null,o.lastBaseUpdate=p,o.shared.pending=null}}if(null===u&&(s=d),o.baseState=s,o.firstBaseUpdate=c,o.lastBaseUpdate=u,null!==(t=o.shared.interleaved)){o=t;do{i|=o.lane,o=o.next}while(o!==t)}else null===a&&(o.shared.lanes=0);Fs|=i,e.lanes=i,e.memoizedState=d}}function $a(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(null!==o){if(r.callback=null,r=n,"function"!==typeof o)throw Error(a(191,o));o.call(r)}}}var Ka={},qa=jo(Ka),Ya=jo(Ka),Ga=jo(Ka);function Qa(e){if(e===Ka)throw Error(a(174));return e}function Xa(e,t){switch(Po(Ga,t),Po(Ya,e),Po(qa,Ka),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:se(null,"");break;default:t=se(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}No(qa),Po(qa,t)}function Ja(){No(qa),No(Ya),No(Ga)}function Za(e){Qa(Ga.current);var t=Qa(qa.current),n=se(t,e.type);t!==n&&(Po(Ya,e),Po(qa,n))}function ei(e){Ya.current===e&&(No(qa),No(Ya))}var ti=jo(0);function ni(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ri=[];function oi(){for(var e=0;e<ri.length;e++)ri[e]._workInProgressVersionPrimary=null;ri.length=0}var ai=x.ReactCurrentDispatcher,ii=x.ReactCurrentBatchConfig,li=0,si=null,ci=null,ui=null,di=!1,pi=!1,fi=0,hi=0;function mi(){throw Error(a(321))}function gi(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!sr(e[n],t[n]))return!1;return!0}function vi(e,t,n,r,o,i){if(li=i,si=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,ai.current=null===e||null===e.memoizedState?el:tl,e=n(r,o),pi){i=0;do{if(pi=!1,fi=0,25<=i)throw Error(a(301));i+=1,ui=ci=null,t.updateQueue=null,ai.current=nl,e=n(r,o)}while(pi)}if(ai.current=Zi,t=null!==ci&&null!==ci.next,li=0,ui=ci=si=null,di=!1,t)throw Error(a(300));return e}function yi(){var e=0!==fi;return fi=0,e}function bi(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===ui?si.memoizedState=ui=e:ui=ui.next=e,ui}function xi(){if(null===ci){var e=si.alternate;e=null!==e?e.memoizedState:null}else e=ci.next;var t=null===ui?si.memoizedState:ui.next;if(null!==t)ui=t,ci=e;else{if(null===e)throw Error(a(310));e={memoizedState:(ci=e).memoizedState,baseState:ci.baseState,baseQueue:ci.baseQueue,queue:ci.queue,next:null},null===ui?si.memoizedState=ui=e:ui=ui.next=e}return ui}function wi(e,t){return"function"===typeof t?t(e):t}function Si(e){var t=xi(),n=t.queue;if(null===n)throw Error(a(311));n.lastRenderedReducer=e;var r=ci,o=r.baseQueue,i=n.pending;if(null!==i){if(null!==o){var l=o.next;o.next=i.next,i.next=l}r.baseQueue=o=i,n.pending=null}if(null!==o){i=o.next,r=r.baseState;var s=l=null,c=null,u=i;do{var d=u.lane;if((li&d)===d)null!==c&&(c=c.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var p={lane:d,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};null===c?(s=c=p,l=r):c=c.next=p,si.lanes|=d,Fs|=d}u=u.next}while(null!==u&&u!==i);null===c?l=r:c.next=s,sr(r,t.memoizedState)||(xl=!0),t.memoizedState=r,t.baseState=l,t.baseQueue=c,n.lastRenderedState=r}if(null!==(e=n.interleaved)){o=e;do{i=o.lane,si.lanes|=i,Fs|=i,o=o.next}while(o!==e)}else null===o&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function ki(e){var t=xi(),n=t.queue;if(null===n)throw Error(a(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,i=t.memoizedState;if(null!==o){n.pending=null;var l=o=o.next;do{i=e(i,l.action),l=l.next}while(l!==o);sr(i,t.memoizedState)||(xl=!0),t.memoizedState=i,null===t.baseQueue&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function Ci(){}function Ei(e,t){var n=si,r=xi(),o=t(),i=!sr(r.memoizedState,o);if(i&&(r.memoizedState=o,xl=!0),r=r.queue,Fi(Pi.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||null!==ui&&1&ui.memoizedState.tag){if(n.flags|=2048,Ai(9,Ni.bind(null,n,r,o,t),void 0,null),null===Ts)throw Error(a(349));0!==(30&li)||ji(n,t,o)}return o}function ji(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=si.updateQueue)?(t={lastEffect:null,stores:null},si.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Ni(e,t,n,r){t.value=n,t.getSnapshot=r,Ri(t)&&Ti(e)}function Pi(e,t,n){return n((function(){Ri(t)&&Ti(e)}))}function Ri(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!sr(e,n)}catch(r){return!0}}function Ti(e){var t=Oa(e,1);null!==t&&rc(t,e,1,-1)}function _i(e){var t=bi();return"function"===typeof e&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:wi,lastRenderedState:e},t.queue=e,e=e.dispatch=Gi.bind(null,si,e),[t.memoizedState,e]}function Ai(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},null===(t=si.updateQueue)?(t={lastEffect:null,stores:null},si.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function Mi(){return xi().memoizedState}function zi(e,t,n,r){var o=bi();si.flags|=e,o.memoizedState=Ai(1|t,n,void 0,void 0===r?null:r)}function Ii(e,t,n,r){var o=xi();r=void 0===r?null:r;var a=void 0;if(null!==ci){var i=ci.memoizedState;if(a=i.destroy,null!==r&&gi(r,i.deps))return void(o.memoizedState=Ai(t,n,a,r))}si.flags|=e,o.memoizedState=Ai(1|t,n,a,r)}function Oi(e,t){return zi(8390656,8,e,t)}function Fi(e,t){return Ii(2048,8,e,t)}function Li(e,t){return Ii(4,2,e,t)}function Di(e,t){return Ii(4,4,e,t)}function Bi(e,t){return"function"===typeof t?(e=e(),t(e),function(){t(null)}):null!==t&&void 0!==t?(e=e(),t.current=e,function(){t.current=null}):void 0}function Wi(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,Ii(4,4,Bi.bind(null,t,e),n)}function Hi(){}function Ui(e,t){var n=xi();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&gi(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Vi(e,t){var n=xi();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&gi(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function $i(e,t,n){return 0===(21&li)?(e.baseState&&(e.baseState=!1,xl=!0),e.memoizedState=n):(sr(n,t)||(n=mt(),si.lanes|=n,Fs|=n,e.baseState=!0),t)}function Ki(e,t){var n=bt;bt=0!==n&&4>n?n:4,e(!0);var r=ii.transition;ii.transition={};try{e(!1),t()}finally{bt=n,ii.transition=r}}function qi(){return xi().memoizedState}function Yi(e,t,n){var r=nc(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Qi(e))Xi(t,n);else if(null!==(n=Ia(e,t,n,r))){rc(n,e,r,tc()),Ji(n,t,r)}}function Gi(e,t,n){var r=nc(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Qi(e))Xi(t,o);else{var a=e.alternate;if(0===e.lanes&&(null===a||0===a.lanes)&&null!==(a=t.lastRenderedReducer))try{var i=t.lastRenderedState,l=a(i,n);if(o.hasEagerState=!0,o.eagerState=l,sr(l,i)){var s=t.interleaved;return null===s?(o.next=o,za(t)):(o.next=s.next,s.next=o),void(t.interleaved=o)}}catch(c){}null!==(n=Ia(e,t,o,r))&&(rc(n,e,r,o=tc()),Ji(n,t,r))}}function Qi(e){var t=e.alternate;return e===si||null!==t&&t===si}function Xi(e,t){pi=di=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Ji(e,t,n){if(0!==(4194240&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,yt(e,n)}}var Zi={readContext:Aa,useCallback:mi,useContext:mi,useEffect:mi,useImperativeHandle:mi,useInsertionEffect:mi,useLayoutEffect:mi,useMemo:mi,useReducer:mi,useRef:mi,useState:mi,useDebugValue:mi,useDeferredValue:mi,useTransition:mi,useMutableSource:mi,useSyncExternalStore:mi,useId:mi,unstable_isNewReconciler:!1},el={readContext:Aa,useCallback:function(e,t){return bi().memoizedState=[e,void 0===t?null:t],e},useContext:Aa,useEffect:Oi,useImperativeHandle:function(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,zi(4194308,4,Bi.bind(null,t,e),n)},useLayoutEffect:function(e,t){return zi(4194308,4,e,t)},useInsertionEffect:function(e,t){return zi(4,2,e,t)},useMemo:function(e,t){var n=bi();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=bi();return t=void 0!==n?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Yi.bind(null,si,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},bi().memoizedState=e},useState:_i,useDebugValue:Hi,useDeferredValue:function(e){return bi().memoizedState=e},useTransition:function(){var e=_i(!1),t=e[0];return e=Ki.bind(null,e[1]),bi().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=si,o=bi();if(ia){if(void 0===n)throw Error(a(407));n=n()}else{if(n=t(),null===Ts)throw Error(a(349));0!==(30&li)||ji(r,t,n)}o.memoizedState=n;var i={value:n,getSnapshot:t};return o.queue=i,Oi(Pi.bind(null,r,i,e),[e]),r.flags|=2048,Ai(9,Ni.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=bi(),t=Ts.identifierPrefix;if(ia){var n=Zo;t=":"+t+"R"+(n=(Jo&~(1<<32-it(Jo)-1)).toString(32)+n),0<(n=fi++)&&(t+="H"+n.toString(32)),t+=":"}else t=":"+t+"r"+(n=hi++).toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},tl={readContext:Aa,useCallback:Ui,useContext:Aa,useEffect:Fi,useImperativeHandle:Wi,useInsertionEffect:Li,useLayoutEffect:Di,useMemo:Vi,useReducer:Si,useRef:Mi,useState:function(){return Si(wi)},useDebugValue:Hi,useDeferredValue:function(e){return $i(xi(),ci.memoizedState,e)},useTransition:function(){return[Si(wi)[0],xi().memoizedState]},useMutableSource:Ci,useSyncExternalStore:Ei,useId:qi,unstable_isNewReconciler:!1},nl={readContext:Aa,useCallback:Ui,useContext:Aa,useEffect:Fi,useImperativeHandle:Wi,useInsertionEffect:Li,useLayoutEffect:Di,useMemo:Vi,useReducer:ki,useRef:Mi,useState:function(){return ki(wi)},useDebugValue:Hi,useDeferredValue:function(e){var t=xi();return null===ci?t.memoizedState=e:$i(t,ci.memoizedState,e)},useTransition:function(){return[ki(wi)[0],xi().memoizedState]},useMutableSource:Ci,useSyncExternalStore:Ei,useId:qi,unstable_isNewReconciler:!1};function rl(e,t){if(e&&e.defaultProps){for(var n in t=F({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}return t}function ol(e,t,n,r){n=null===(n=n(r,t=e.memoizedState))||void 0===n?t:F({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var al={isMounted:function(e){return!!(e=e._reactInternals)&&He(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=tc(),o=nc(e),a=Ba(r,o);a.payload=t,void 0!==n&&null!==n&&(a.callback=n),null!==(t=Wa(e,a,o))&&(rc(t,e,o,r),Ha(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=tc(),o=nc(e),a=Ba(r,o);a.tag=1,a.payload=t,void 0!==n&&null!==n&&(a.callback=n),null!==(t=Wa(e,a,o))&&(rc(t,e,o,r),Ha(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=tc(),r=nc(e),o=Ba(n,r);o.tag=2,void 0!==t&&null!==t&&(o.callback=t),null!==(t=Wa(e,o,r))&&(rc(t,e,r,n),Ha(t,e,r))}};function il(e,t,n,r,o,a,i){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,a,i):!t.prototype||!t.prototype.isPureReactComponent||(!cr(n,r)||!cr(o,a))}function ll(e,t,n){var r=!1,o=Ro,a=t.contextType;return"object"===typeof a&&null!==a?a=Aa(a):(o=zo(t)?Ao:To.current,a=(r=null!==(r=t.contextTypes)&&void 0!==r)?Mo(e,o):Ro),t=new t(n,a),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=al,e.stateNode=t,t._reactInternals=e,r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=a),t}function sl(e,t,n,r){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&al.enqueueReplaceState(t,t.state,null)}function cl(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs={},La(e);var a=t.contextType;"object"===typeof a&&null!==a?o.context=Aa(a):(a=zo(t)?Ao:To.current,o.context=Mo(e,a)),o.state=e.memoizedState,"function"===typeof(a=t.getDerivedStateFromProps)&&(ol(e,t,a,n),o.state=e.memoizedState),"function"===typeof t.getDerivedStateFromProps||"function"===typeof o.getSnapshotBeforeUpdate||"function"!==typeof o.UNSAFE_componentWillMount&&"function"!==typeof o.componentWillMount||(t=o.state,"function"===typeof o.componentWillMount&&o.componentWillMount(),"function"===typeof o.UNSAFE_componentWillMount&&o.UNSAFE_componentWillMount(),t!==o.state&&al.enqueueReplaceState(o,o.state,null),Va(e,n,o,r),o.state=e.memoizedState),"function"===typeof o.componentDidMount&&(e.flags|=4194308)}function ul(e,t){try{var n="",r=t;do{n+=W(r),r=r.return}while(r);var o=n}catch(a){o="\nError generating stack: "+a.message+"\n"+a.stack}return{value:e,source:t,stack:o,digest:null}}function dl(e,t,n){return{value:e,source:null,stack:null!=n?n:null,digest:null!=t?t:null}}function pl(e,t){try{console.error(t.value)}catch(n){setTimeout((function(){throw n}))}}var fl="function"===typeof WeakMap?WeakMap:Map;function hl(e,t,n){(n=Ba(-1,n)).tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){$s||($s=!0,Ks=r),pl(0,t)},n}function ml(e,t,n){(n=Ba(-1,n)).tag=3;var r=e.type.getDerivedStateFromError;if("function"===typeof r){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){pl(0,t)}}var a=e.stateNode;return null!==a&&"function"===typeof a.componentDidCatch&&(n.callback=function(){pl(0,t),"function"!==typeof r&&(null===qs?qs=new Set([this]):qs.add(this));var e=t.stack;this.componentDidCatch(t.value,{componentStack:null!==e?e:""})}),n}function gl(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new fl;var o=new Set;r.set(t,o)}else void 0===(o=r.get(t))&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=jc.bind(null,e,t,n),t.then(e,e))}function vl(e){do{var t;if((t=13===e.tag)&&(t=null===(t=e.memoizedState)||null!==t.dehydrated),t)return e;e=e.return}while(null!==e);return null}function yl(e,t,n,r,o){return 0===(1&e.mode)?(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,1===n.tag&&(null===n.alternate?n.tag=17:((t=Ba(-1,1)).tag=2,Wa(n,t,1))),n.lanes|=1),e):(e.flags|=65536,e.lanes=o,e)}var bl=x.ReactCurrentOwner,xl=!1;function wl(e,t,n,r){t.child=null===e?ka(t,null,n,r):Sa(t,e.child,n,r)}function Sl(e,t,n,r,o){n=n.render;var a=t.ref;return _a(t,o),r=vi(e,t,n,r,a,o),n=yi(),null===e||xl?(ia&&n&&na(t),t.flags|=1,wl(e,t,r,o),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,$l(e,t,o))}function kl(e,t,n,r,o){if(null===e){var a=n.type;return"function"!==typeof a||Mc(a)||void 0!==a.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=Ic(n.type,null,r,t,t.mode,o)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=a,Cl(e,t,a,r,o))}if(a=e.child,0===(e.lanes&o)){var i=a.memoizedProps;if((n=null!==(n=n.compare)?n:cr)(i,r)&&e.ref===t.ref)return $l(e,t,o)}return t.flags|=1,(e=zc(a,r)).ref=t.ref,e.return=t,t.child=e}function Cl(e,t,n,r,o){if(null!==e){var a=e.memoizedProps;if(cr(a,r)&&e.ref===t.ref){if(xl=!1,t.pendingProps=r=a,0===(e.lanes&o))return t.lanes=e.lanes,$l(e,t,o);0!==(131072&e.flags)&&(xl=!0)}}return Nl(e,t,n,r,o)}function El(e,t,n){var r=t.pendingProps,o=r.children,a=null!==e?e.memoizedState:null;if("hidden"===r.mode)if(0===(1&t.mode))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Po(zs,Ms),Ms|=n;else{if(0===(1073741824&n))return e=null!==a?a.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Po(zs,Ms),Ms|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=null!==a?a.baseLanes:n,Po(zs,Ms),Ms|=r}else null!==a?(r=a.baseLanes|n,t.memoizedState=null):r=n,Po(zs,Ms),Ms|=r;return wl(e,t,o,n),t.child}function jl(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Nl(e,t,n,r,o){var a=zo(n)?Ao:To.current;return a=Mo(t,a),_a(t,o),n=vi(e,t,n,r,a,o),r=yi(),null===e||xl?(ia&&r&&na(t),t.flags|=1,wl(e,t,n,o),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,$l(e,t,o))}function Pl(e,t,n,r,o){if(zo(n)){var a=!0;Lo(t)}else a=!1;if(_a(t,o),null===t.stateNode)Vl(e,t),ll(t,n,r),cl(t,n,r,o),r=!0;else if(null===e){var i=t.stateNode,l=t.memoizedProps;i.props=l;var s=i.context,c=n.contextType;"object"===typeof c&&null!==c?c=Aa(c):c=Mo(t,c=zo(n)?Ao:To.current);var u=n.getDerivedStateFromProps,d="function"===typeof u||"function"===typeof i.getSnapshotBeforeUpdate;d||"function"!==typeof i.UNSAFE_componentWillReceiveProps&&"function"!==typeof i.componentWillReceiveProps||(l!==r||s!==c)&&sl(t,i,r,c),Fa=!1;var p=t.memoizedState;i.state=p,Va(t,r,i,o),s=t.memoizedState,l!==r||p!==s||_o.current||Fa?("function"===typeof u&&(ol(t,n,u,r),s=t.memoizedState),(l=Fa||il(t,n,l,r,p,s,c))?(d||"function"!==typeof i.UNSAFE_componentWillMount&&"function"!==typeof i.componentWillMount||("function"===typeof i.componentWillMount&&i.componentWillMount(),"function"===typeof i.UNSAFE_componentWillMount&&i.UNSAFE_componentWillMount()),"function"===typeof i.componentDidMount&&(t.flags|=4194308)):("function"===typeof i.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=s),i.props=r,i.state=s,i.context=c,r=l):("function"===typeof i.componentDidMount&&(t.flags|=4194308),r=!1)}else{i=t.stateNode,Da(e,t),l=t.memoizedProps,c=t.type===t.elementType?l:rl(t.type,l),i.props=c,d=t.pendingProps,p=i.context,"object"===typeof(s=n.contextType)&&null!==s?s=Aa(s):s=Mo(t,s=zo(n)?Ao:To.current);var f=n.getDerivedStateFromProps;(u="function"===typeof f||"function"===typeof i.getSnapshotBeforeUpdate)||"function"!==typeof i.UNSAFE_componentWillReceiveProps&&"function"!==typeof i.componentWillReceiveProps||(l!==d||p!==s)&&sl(t,i,r,s),Fa=!1,p=t.memoizedState,i.state=p,Va(t,r,i,o);var h=t.memoizedState;l!==d||p!==h||_o.current||Fa?("function"===typeof f&&(ol(t,n,f,r),h=t.memoizedState),(c=Fa||il(t,n,c,r,p,h,s)||!1)?(u||"function"!==typeof i.UNSAFE_componentWillUpdate&&"function"!==typeof i.componentWillUpdate||("function"===typeof i.componentWillUpdate&&i.componentWillUpdate(r,h,s),"function"===typeof i.UNSAFE_componentWillUpdate&&i.UNSAFE_componentWillUpdate(r,h,s)),"function"===typeof i.componentDidUpdate&&(t.flags|=4),"function"===typeof i.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!==typeof i.componentDidUpdate||l===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),"function"!==typeof i.getSnapshotBeforeUpdate||l===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=h),i.props=r,i.state=h,i.context=s,r=c):("function"!==typeof i.componentDidUpdate||l===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),"function"!==typeof i.getSnapshotBeforeUpdate||l===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),r=!1)}return Rl(e,t,n,r,a,o)}function Rl(e,t,n,r,o,a){jl(e,t);var i=0!==(128&t.flags);if(!r&&!i)return o&&Do(t,n,!1),$l(e,t,a);r=t.stateNode,bl.current=t;var l=i&&"function"!==typeof n.getDerivedStateFromError?null:r.render();return t.flags|=1,null!==e&&i?(t.child=Sa(t,e.child,null,a),t.child=Sa(t,null,l,a)):wl(e,t,l,a),t.memoizedState=r.state,o&&Do(t,n,!0),t.child}function Tl(e){var t=e.stateNode;t.pendingContext?Oo(0,t.pendingContext,t.pendingContext!==t.context):t.context&&Oo(0,t.context,!1),Xa(e,t.containerInfo)}function _l(e,t,n,r,o){return ma(),ga(o),t.flags|=256,wl(e,t,n,r),t.child}var Al,Ml,zl,Il,Ol={dehydrated:null,treeContext:null,retryLane:0};function Fl(e){return{baseLanes:e,cachePool:null,transitions:null}}function Ll(e,t,n){var r,o=t.pendingProps,i=ti.current,l=!1,s=0!==(128&t.flags);if((r=s)||(r=(null===e||null!==e.memoizedState)&&0!==(2&i)),r?(l=!0,t.flags&=-129):null!==e&&null===e.memoizedState||(i|=1),Po(ti,1&i),null===e)return da(t),null!==(e=t.memoizedState)&&null!==(e=e.dehydrated)?(0===(1&t.mode)?t.lanes=1:"$!"===e.data?t.lanes=8:t.lanes=1073741824,null):(s=o.children,e=o.fallback,l?(o=t.mode,l=t.child,s={mode:"hidden",children:s},0===(1&o)&&null!==l?(l.childLanes=0,l.pendingProps=s):l=Fc(s,o,0,null),e=Oc(e,o,n,null),l.return=t,e.return=t,l.sibling=e,t.child=l,t.child.memoizedState=Fl(n),t.memoizedState=Ol,e):Dl(t,s));if(null!==(i=e.memoizedState)&&null!==(r=i.dehydrated))return function(e,t,n,r,o,i,l){if(n)return 256&t.flags?(t.flags&=-257,Bl(e,t,l,r=dl(Error(a(422))))):null!==t.memoizedState?(t.child=e.child,t.flags|=128,null):(i=r.fallback,o=t.mode,r=Fc({mode:"visible",children:r.children},o,0,null),(i=Oc(i,o,l,null)).flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,0!==(1&t.mode)&&Sa(t,e.child,null,l),t.child.memoizedState=Fl(l),t.memoizedState=Ol,i);if(0===(1&t.mode))return Bl(e,t,l,null);if("$!"===o.data){if(r=o.nextSibling&&o.nextSibling.dataset)var s=r.dgst;return r=s,Bl(e,t,l,r=dl(i=Error(a(419)),r,void 0))}if(s=0!==(l&e.childLanes),xl||s){if(null!==(r=Ts)){switch(l&-l){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}0!==(o=0!==(o&(r.suspendedLanes|l))?0:o)&&o!==i.retryLane&&(i.retryLane=o,Oa(e,o),rc(r,e,o,-1))}return gc(),Bl(e,t,l,r=dl(Error(a(421))))}return"$?"===o.data?(t.flags|=128,t.child=e.child,t=Pc.bind(null,e),o._reactRetry=t,null):(e=i.treeContext,aa=uo(o.nextSibling),oa=t,ia=!0,la=null,null!==e&&(Go[Qo++]=Jo,Go[Qo++]=Zo,Go[Qo++]=Xo,Jo=e.id,Zo=e.overflow,Xo=t),t=Dl(t,r.children),t.flags|=4096,t)}(e,t,s,o,r,i,n);if(l){l=o.fallback,s=t.mode,r=(i=e.child).sibling;var c={mode:"hidden",children:o.children};return 0===(1&s)&&t.child!==i?((o=t.child).childLanes=0,o.pendingProps=c,t.deletions=null):(o=zc(i,c)).subtreeFlags=14680064&i.subtreeFlags,null!==r?l=zc(r,l):(l=Oc(l,s,n,null)).flags|=2,l.return=t,o.return=t,o.sibling=l,t.child=o,o=l,l=t.child,s=null===(s=e.child.memoizedState)?Fl(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},l.memoizedState=s,l.childLanes=e.childLanes&~n,t.memoizedState=Ol,o}return e=(l=e.child).sibling,o=zc(l,{mode:"visible",children:o.children}),0===(1&t.mode)&&(o.lanes=n),o.return=t,o.sibling=null,null!==e&&(null===(n=t.deletions)?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=o,t.memoizedState=null,o}function Dl(e,t){return(t=Fc({mode:"visible",children:t},e.mode,0,null)).return=e,e.child=t}function Bl(e,t,n,r){return null!==r&&ga(r),Sa(t,e.child,null,n),(e=Dl(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Wl(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),Ta(e.return,t,n)}function Hl(e,t,n,r,o){var a=e.memoizedState;null===a?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(a.isBackwards=t,a.rendering=null,a.renderingStartTime=0,a.last=r,a.tail=n,a.tailMode=o)}function Ul(e,t,n){var r=t.pendingProps,o=r.revealOrder,a=r.tail;if(wl(e,t,r.children,n),0!==(2&(r=ti.current)))r=1&r|2,t.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Wl(e,n,t);else if(19===e.tag)Wl(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(Po(ti,r),0===(1&t.mode))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;null!==n;)null!==(e=n.alternate)&&null===ni(e)&&(o=n),n=n.sibling;null===(n=o)?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),Hl(t,!1,o,n,a);break;case"backwards":for(n=null,o=t.child,t.child=null;null!==o;){if(null!==(e=o.alternate)&&null===ni(e)){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}Hl(t,!0,n,null,a);break;case"together":Hl(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Vl(e,t){0===(1&t.mode)&&null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2)}function $l(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),Fs|=t.lanes,0===(n&t.childLanes))return null;if(null!==e&&t.child!==e.child)throw Error(a(153));if(null!==t.child){for(n=zc(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=zc(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Kl(e,t){if(!ia)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function ql(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;null!==o;)n|=o.lanes|o.childLanes,r|=14680064&o.subtreeFlags,r|=14680064&o.flags,o.return=e,o=o.sibling;else for(o=e.child;null!==o;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Yl(e,t,n){var r=t.pendingProps;switch(ra(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ql(t),null;case 1:case 17:return zo(t.type)&&Io(),ql(t),null;case 3:return r=t.stateNode,Ja(),No(_o),No(To),oi(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),null!==e&&null!==e.child||(fa(t)?t.flags|=4:null===e||e.memoizedState.isDehydrated&&0===(256&t.flags)||(t.flags|=1024,null!==la&&(lc(la),la=null))),Ml(e,t),ql(t),null;case 5:ei(t);var o=Qa(Ga.current);if(n=t.type,null!==e&&null!=t.stateNode)zl(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(null===t.stateNode)throw Error(a(166));return ql(t),null}if(e=Qa(qa.current),fa(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[ho]=t,r[mo]=i,e=0!==(1&t.mode),n){case"dialog":Br("cancel",r),Br("close",r);break;case"iframe":case"object":case"embed":Br("load",r);break;case"video":case"audio":for(o=0;o<Or.length;o++)Br(Or[o],r);break;case"source":Br("error",r);break;case"img":case"image":case"link":Br("error",r),Br("load",r);break;case"details":Br("toggle",r);break;case"input":Q(r,i),Br("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},Br("invalid",r);break;case"textarea":oe(r,i),Br("invalid",r)}for(var s in ye(n,i),o=null,i)if(i.hasOwnProperty(s)){var c=i[s];"children"===s?"string"===typeof c?r.textContent!==c&&(!0!==i.suppressHydrationWarning&&Zr(r.textContent,c,e),o=["children",c]):"number"===typeof c&&r.textContent!==""+c&&(!0!==i.suppressHydrationWarning&&Zr(r.textContent,c,e),o=["children",""+c]):l.hasOwnProperty(s)&&null!=c&&"onScroll"===s&&Br("scroll",r)}switch(n){case"input":K(r),Z(r,i,!0);break;case"textarea":K(r),ie(r);break;case"select":case"option":break;default:"function"===typeof i.onClick&&(r.onclick=eo)}r=o,t.updateQueue=r,null!==r&&(t.flags|=4)}else{s=9===o.nodeType?o:o.ownerDocument,"http://www.w3.org/1999/xhtml"===e&&(e=le(n)),"http://www.w3.org/1999/xhtml"===e?"script"===n?((e=s.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"===typeof r.is?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),"select"===n&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[ho]=t,e[mo]=r,Al(e,t,!1,!1),t.stateNode=e;e:{switch(s=be(n,r),n){case"dialog":Br("cancel",e),Br("close",e),o=r;break;case"iframe":case"object":case"embed":Br("load",e),o=r;break;case"video":case"audio":for(o=0;o<Or.length;o++)Br(Or[o],e);o=r;break;case"source":Br("error",e),o=r;break;case"img":case"image":case"link":Br("error",e),Br("load",e),o=r;break;case"details":Br("toggle",e),o=r;break;case"input":Q(e,r),o=G(e,r),Br("invalid",e);break;case"option":default:o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=F({},r,{value:void 0}),Br("invalid",e);break;case"textarea":oe(e,r),o=re(e,r),Br("invalid",e)}for(i in ye(n,o),c=o)if(c.hasOwnProperty(i)){var u=c[i];"style"===i?ge(e,u):"dangerouslySetInnerHTML"===i?null!=(u=u?u.__html:void 0)&&de(e,u):"children"===i?"string"===typeof u?("textarea"!==n||""!==u)&&pe(e,u):"number"===typeof u&&pe(e,""+u):"suppressContentEditableWarning"!==i&&"suppressHydrationWarning"!==i&&"autoFocus"!==i&&(l.hasOwnProperty(i)?null!=u&&"onScroll"===i&&Br("scroll",e):null!=u&&b(e,i,u,s))}switch(n){case"input":K(e),Z(e,r,!1);break;case"textarea":K(e),ie(e);break;case"option":null!=r.value&&e.setAttribute("value",""+V(r.value));break;case"select":e.multiple=!!r.multiple,null!=(i=r.value)?ne(e,!!r.multiple,i,!1):null!=r.defaultValue&&ne(e,!!r.multiple,r.defaultValue,!0);break;default:"function"===typeof o.onClick&&(e.onclick=eo)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}null!==t.ref&&(t.flags|=512,t.flags|=2097152)}return ql(t),null;case 6:if(e&&null!=t.stateNode)Il(e,t,e.memoizedProps,r);else{if("string"!==typeof r&&null===t.stateNode)throw Error(a(166));if(n=Qa(Ga.current),Qa(qa.current),fa(t)){if(r=t.stateNode,n=t.memoizedProps,r[ho]=t,(i=r.nodeValue!==n)&&null!==(e=oa))switch(e.tag){case 3:Zr(r.nodeValue,n,0!==(1&e.mode));break;case 5:!0!==e.memoizedProps.suppressHydrationWarning&&Zr(r.nodeValue,n,0!==(1&e.mode))}i&&(t.flags|=4)}else(r=(9===n.nodeType?n:n.ownerDocument).createTextNode(r))[ho]=t,t.stateNode=r}return ql(t),null;case 13:if(No(ti),r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(ia&&null!==aa&&0!==(1&t.mode)&&0===(128&t.flags))ha(),ma(),t.flags|=98560,i=!1;else if(i=fa(t),null!==r&&null!==r.dehydrated){if(null===e){if(!i)throw Error(a(318));if(!(i=null!==(i=t.memoizedState)?i.dehydrated:null))throw Error(a(317));i[ho]=t}else ma(),0===(128&t.flags)&&(t.memoizedState=null),t.flags|=4;ql(t),i=!1}else null!==la&&(lc(la),la=null),i=!0;if(!i)return 65536&t.flags?t:null}return 0!==(128&t.flags)?(t.lanes=n,t):((r=null!==r)!==(null!==e&&null!==e.memoizedState)&&r&&(t.child.flags|=8192,0!==(1&t.mode)&&(null===e||0!==(1&ti.current)?0===Is&&(Is=3):gc())),null!==t.updateQueue&&(t.flags|=4),ql(t),null);case 4:return Ja(),Ml(e,t),null===e&&Ur(t.stateNode.containerInfo),ql(t),null;case 10:return Ra(t.type._context),ql(t),null;case 19:if(No(ti),null===(i=t.memoizedState))return ql(t),null;if(r=0!==(128&t.flags),null===(s=i.rendering))if(r)Kl(i,!1);else{if(0!==Is||null!==e&&0!==(128&e.flags))for(e=t.child;null!==e;){if(null!==(s=ni(e))){for(t.flags|=128,Kl(i,!1),null!==(r=s.updateQueue)&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;null!==n;)e=r,(i=n).flags&=14680066,null===(s=i.alternate)?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=s.childLanes,i.lanes=s.lanes,i.child=s.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=s.memoizedProps,i.memoizedState=s.memoizedState,i.updateQueue=s.updateQueue,i.type=s.type,e=s.dependencies,i.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Po(ti,1&ti.current|2),t.child}e=e.sibling}null!==i.tail&&Xe()>Us&&(t.flags|=128,r=!0,Kl(i,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=ni(s))){if(t.flags|=128,r=!0,null!==(n=e.updateQueue)&&(t.updateQueue=n,t.flags|=4),Kl(i,!0),null===i.tail&&"hidden"===i.tailMode&&!s.alternate&&!ia)return ql(t),null}else 2*Xe()-i.renderingStartTime>Us&&1073741824!==n&&(t.flags|=128,r=!0,Kl(i,!1),t.lanes=4194304);i.isBackwards?(s.sibling=t.child,t.child=s):(null!==(n=i.last)?n.sibling=s:t.child=s,i.last=s)}return null!==i.tail?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=Xe(),t.sibling=null,n=ti.current,Po(ti,r?1&n|2:1&n),t):(ql(t),null);case 22:case 23:return pc(),r=null!==t.memoizedState,null!==e&&null!==e.memoizedState!==r&&(t.flags|=8192),r&&0!==(1&t.mode)?0!==(1073741824&Ms)&&(ql(t),6&t.subtreeFlags&&(t.flags|=8192)):ql(t),null;case 24:case 25:return null}throw Error(a(156,t.tag))}function Gl(e,t){switch(ra(t),t.tag){case 1:return zo(t.type)&&Io(),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return Ja(),No(_o),No(To),oi(),0!==(65536&(e=t.flags))&&0===(128&e)?(t.flags=-65537&e|128,t):null;case 5:return ei(t),null;case 13:if(No(ti),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(a(340));ma()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return No(ti),null;case 4:return Ja(),null;case 10:return Ra(t.type._context),null;case 22:case 23:return pc(),null;default:return null}}Al=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)e.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},Ml=function(){},zl=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,Qa(qa.current);var a,i=null;switch(n){case"input":o=G(e,o),r=G(e,r),i=[];break;case"select":o=F({},o,{value:void 0}),r=F({},r,{value:void 0}),i=[];break;case"textarea":o=re(e,o),r=re(e,r),i=[];break;default:"function"!==typeof o.onClick&&"function"===typeof r.onClick&&(e.onclick=eo)}for(u in ye(n,r),n=null,o)if(!r.hasOwnProperty(u)&&o.hasOwnProperty(u)&&null!=o[u])if("style"===u){var s=o[u];for(a in s)s.hasOwnProperty(a)&&(n||(n={}),n[a]="")}else"dangerouslySetInnerHTML"!==u&&"children"!==u&&"suppressContentEditableWarning"!==u&&"suppressHydrationWarning"!==u&&"autoFocus"!==u&&(l.hasOwnProperty(u)?i||(i=[]):(i=i||[]).push(u,null));for(u in r){var c=r[u];if(s=null!=o?o[u]:void 0,r.hasOwnProperty(u)&&c!==s&&(null!=c||null!=s))if("style"===u)if(s){for(a in s)!s.hasOwnProperty(a)||c&&c.hasOwnProperty(a)||(n||(n={}),n[a]="");for(a in c)c.hasOwnProperty(a)&&s[a]!==c[a]&&(n||(n={}),n[a]=c[a])}else n||(i||(i=[]),i.push(u,n)),n=c;else"dangerouslySetInnerHTML"===u?(c=c?c.__html:void 0,s=s?s.__html:void 0,null!=c&&s!==c&&(i=i||[]).push(u,c)):"children"===u?"string"!==typeof c&&"number"!==typeof c||(i=i||[]).push(u,""+c):"suppressContentEditableWarning"!==u&&"suppressHydrationWarning"!==u&&(l.hasOwnProperty(u)?(null!=c&&"onScroll"===u&&Br("scroll",e),i||s===c||(i=[])):(i=i||[]).push(u,c))}n&&(i=i||[]).push("style",n);var u=i;(t.updateQueue=u)&&(t.flags|=4)}},Il=function(e,t,n,r){n!==r&&(t.flags|=4)};var Ql=!1,Xl=!1,Jl="function"===typeof WeakSet?WeakSet:Set,Zl=null;function es(e,t){var n=e.ref;if(null!==n)if("function"===typeof n)try{n(null)}catch(r){Ec(e,t,r)}else n.current=null}function ts(e,t,n){try{n()}catch(r){Ec(e,t,r)}}var ns=!1;function rs(e,t,n){var r=t.updateQueue;if(null!==(r=null!==r?r.lastEffect:null)){var o=r=r.next;do{if((o.tag&e)===e){var a=o.destroy;o.destroy=void 0,void 0!==a&&ts(t,n,a)}o=o.next}while(o!==r)}}function os(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function as(e){var t=e.ref;if(null!==t){var n=e.stateNode;e.tag,e=n,"function"===typeof t?t(e):t.current=e}}function is(e){var t=e.alternate;null!==t&&(e.alternate=null,is(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&(delete t[ho],delete t[mo],delete t[vo],delete t[yo],delete t[bo])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function ls(e){return 5===e.tag||3===e.tag||4===e.tag}function ss(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||ls(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function cs(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?8===n.nodeType?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(8===n.nodeType?(t=n.parentNode).insertBefore(e,n):(t=n).appendChild(e),null!==(n=n._reactRootContainer)&&void 0!==n||null!==t.onclick||(t.onclick=eo));else if(4!==r&&null!==(e=e.child))for(cs(e,t,n),e=e.sibling;null!==e;)cs(e,t,n),e=e.sibling}function us(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&null!==(e=e.child))for(us(e,t,n),e=e.sibling;null!==e;)us(e,t,n),e=e.sibling}var ds=null,ps=!1;function fs(e,t,n){for(n=n.child;null!==n;)hs(e,t,n),n=n.sibling}function hs(e,t,n){if(at&&"function"===typeof at.onCommitFiberUnmount)try{at.onCommitFiberUnmount(ot,n)}catch(l){}switch(n.tag){case 5:Xl||es(n,t);case 6:var r=ds,o=ps;ds=null,fs(e,t,n),ps=o,null!==(ds=r)&&(ps?(e=ds,n=n.stateNode,8===e.nodeType?e.parentNode.removeChild(n):e.removeChild(n)):ds.removeChild(n.stateNode));break;case 18:null!==ds&&(ps?(e=ds,n=n.stateNode,8===e.nodeType?co(e.parentNode,n):1===e.nodeType&&co(e,n),Ut(e)):co(ds,n.stateNode));break;case 4:r=ds,o=ps,ds=n.stateNode.containerInfo,ps=!0,fs(e,t,n),ds=r,ps=o;break;case 0:case 11:case 14:case 15:if(!Xl&&(null!==(r=n.updateQueue)&&null!==(r=r.lastEffect))){o=r=r.next;do{var a=o,i=a.destroy;a=a.tag,void 0!==i&&(0!==(2&a)||0!==(4&a))&&ts(n,t,i),o=o.next}while(o!==r)}fs(e,t,n);break;case 1:if(!Xl&&(es(n,t),"function"===typeof(r=n.stateNode).componentWillUnmount))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(l){Ec(n,t,l)}fs(e,t,n);break;case 21:fs(e,t,n);break;case 22:1&n.mode?(Xl=(r=Xl)||null!==n.memoizedState,fs(e,t,n),Xl=r):fs(e,t,n);break;default:fs(e,t,n)}}function ms(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new Jl),t.forEach((function(t){var r=Rc.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))}))}}function gs(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var o=n[r];try{var i=e,l=t,s=l;e:for(;null!==s;){switch(s.tag){case 5:ds=s.stateNode,ps=!1;break e;case 3:case 4:ds=s.stateNode.containerInfo,ps=!0;break e}s=s.return}if(null===ds)throw Error(a(160));hs(i,l,o),ds=null,ps=!1;var c=o.alternate;null!==c&&(c.return=null),o.return=null}catch(u){Ec(o,t,u)}}if(12854&t.subtreeFlags)for(t=t.child;null!==t;)vs(t,e),t=t.sibling}function vs(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(gs(t,e),ys(e),4&r){try{rs(3,e,e.return),os(3,e)}catch(g){Ec(e,e.return,g)}try{rs(5,e,e.return)}catch(g){Ec(e,e.return,g)}}break;case 1:gs(t,e),ys(e),512&r&&null!==n&&es(n,n.return);break;case 5:if(gs(t,e),ys(e),512&r&&null!==n&&es(n,n.return),32&e.flags){var o=e.stateNode;try{pe(o,"")}catch(g){Ec(e,e.return,g)}}if(4&r&&null!=(o=e.stateNode)){var i=e.memoizedProps,l=null!==n?n.memoizedProps:i,s=e.type,c=e.updateQueue;if(e.updateQueue=null,null!==c)try{"input"===s&&"radio"===i.type&&null!=i.name&&X(o,i),be(s,l);var u=be(s,i);for(l=0;l<c.length;l+=2){var d=c[l],p=c[l+1];"style"===d?ge(o,p):"dangerouslySetInnerHTML"===d?de(o,p):"children"===d?pe(o,p):b(o,d,p,u)}switch(s){case"input":J(o,i);break;case"textarea":ae(o,i);break;case"select":var f=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!i.multiple;var h=i.value;null!=h?ne(o,!!i.multiple,h,!1):f!==!!i.multiple&&(null!=i.defaultValue?ne(o,!!i.multiple,i.defaultValue,!0):ne(o,!!i.multiple,i.multiple?[]:"",!1))}o[mo]=i}catch(g){Ec(e,e.return,g)}}break;case 6:if(gs(t,e),ys(e),4&r){if(null===e.stateNode)throw Error(a(162));o=e.stateNode,i=e.memoizedProps;try{o.nodeValue=i}catch(g){Ec(e,e.return,g)}}break;case 3:if(gs(t,e),ys(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{Ut(t.containerInfo)}catch(g){Ec(e,e.return,g)}break;case 4:default:gs(t,e),ys(e);break;case 13:gs(t,e),ys(e),8192&(o=e.child).flags&&(i=null!==o.memoizedState,o.stateNode.isHidden=i,!i||null!==o.alternate&&null!==o.alternate.memoizedState||(Hs=Xe())),4&r&&ms(e);break;case 22:if(d=null!==n&&null!==n.memoizedState,1&e.mode?(Xl=(u=Xl)||d,gs(t,e),Xl=u):gs(t,e),ys(e),8192&r){if(u=null!==e.memoizedState,(e.stateNode.isHidden=u)&&!d&&0!==(1&e.mode))for(Zl=e,d=e.child;null!==d;){for(p=Zl=d;null!==Zl;){switch(h=(f=Zl).child,f.tag){case 0:case 11:case 14:case 15:rs(4,f,f.return);break;case 1:es(f,f.return);var m=f.stateNode;if("function"===typeof m.componentWillUnmount){r=f,n=f.return;try{t=r,m.props=t.memoizedProps,m.state=t.memoizedState,m.componentWillUnmount()}catch(g){Ec(r,n,g)}}break;case 5:es(f,f.return);break;case 22:if(null!==f.memoizedState){Ss(p);continue}}null!==h?(h.return=f,Zl=h):Ss(p)}d=d.sibling}e:for(d=null,p=e;;){if(5===p.tag){if(null===d){d=p;try{o=p.stateNode,u?"function"===typeof(i=o.style).setProperty?i.setProperty("display","none","important"):i.display="none":(s=p.stateNode,l=void 0!==(c=p.memoizedProps.style)&&null!==c&&c.hasOwnProperty("display")?c.display:null,s.style.display=me("display",l))}catch(g){Ec(e,e.return,g)}}}else if(6===p.tag){if(null===d)try{p.stateNode.nodeValue=u?"":p.memoizedProps}catch(g){Ec(e,e.return,g)}}else if((22!==p.tag&&23!==p.tag||null===p.memoizedState||p===e)&&null!==p.child){p.child.return=p,p=p.child;continue}if(p===e)break e;for(;null===p.sibling;){if(null===p.return||p.return===e)break e;d===p&&(d=null),p=p.return}d===p&&(d=null),p.sibling.return=p.return,p=p.sibling}}break;case 19:gs(t,e),ys(e),4&r&&ms(e);case 21:}}function ys(e){var t=e.flags;if(2&t){try{e:{for(var n=e.return;null!==n;){if(ls(n)){var r=n;break e}n=n.return}throw Error(a(160))}switch(r.tag){case 5:var o=r.stateNode;32&r.flags&&(pe(o,""),r.flags&=-33),us(e,ss(e),o);break;case 3:case 4:var i=r.stateNode.containerInfo;cs(e,ss(e),i);break;default:throw Error(a(161))}}catch(l){Ec(e,e.return,l)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function bs(e,t,n){Zl=e,xs(e,t,n)}function xs(e,t,n){for(var r=0!==(1&e.mode);null!==Zl;){var o=Zl,a=o.child;if(22===o.tag&&r){var i=null!==o.memoizedState||Ql;if(!i){var l=o.alternate,s=null!==l&&null!==l.memoizedState||Xl;l=Ql;var c=Xl;if(Ql=i,(Xl=s)&&!c)for(Zl=o;null!==Zl;)s=(i=Zl).child,22===i.tag&&null!==i.memoizedState?ks(o):null!==s?(s.return=i,Zl=s):ks(o);for(;null!==a;)Zl=a,xs(a,t,n),a=a.sibling;Zl=o,Ql=l,Xl=c}ws(e)}else 0!==(8772&o.subtreeFlags)&&null!==a?(a.return=o,Zl=a):ws(e)}}function ws(e){for(;null!==Zl;){var t=Zl;if(0!==(8772&t.flags)){var n=t.alternate;try{if(0!==(8772&t.flags))switch(t.tag){case 0:case 11:case 15:Xl||os(5,t);break;case 1:var r=t.stateNode;if(4&t.flags&&!Xl)if(null===n)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:rl(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;null!==i&&$a(t,i,r);break;case 3:var l=t.updateQueue;if(null!==l){if(n=null,null!==t.child)switch(t.child.tag){case 5:case 1:n=t.child.stateNode}$a(t,l,n)}break;case 5:var s=t.stateNode;if(null===n&&4&t.flags){n=s;var c=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":c.autoFocus&&n.focus();break;case"img":c.src&&(n.src=c.src)}}break;case 6:case 4:case 12:case 19:case 17:case 21:case 22:case 23:case 25:break;case 13:if(null===t.memoizedState){var u=t.alternate;if(null!==u){var d=u.memoizedState;if(null!==d){var p=d.dehydrated;null!==p&&Ut(p)}}}break;default:throw Error(a(163))}Xl||512&t.flags&&as(t)}catch(jt){Ec(t,t.return,jt)}}if(t===e){Zl=null;break}if(null!==(n=t.sibling)){n.return=t.return,Zl=n;break}Zl=t.return}}function Ss(e){for(;null!==Zl;){var t=Zl;if(t===e){Zl=null;break}var n=t.sibling;if(null!==n){n.return=t.return,Zl=n;break}Zl=t.return}}function ks(e){for(;null!==Zl;){var t=Zl;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{os(4,t)}catch(s){Ec(t,n,s)}break;case 1:var r=t.stateNode;if("function"===typeof r.componentDidMount){var o=t.return;try{r.componentDidMount()}catch(s){Ec(t,o,s)}}var a=t.return;try{as(t)}catch(s){Ec(t,a,s)}break;case 5:var i=t.return;try{as(t)}catch(s){Ec(t,i,s)}}}catch(s){Ec(t,t.return,s)}if(t===e){Zl=null;break}var l=t.sibling;if(null!==l){l.return=t.return,Zl=l;break}Zl=t.return}}var Cs,Es=Math.ceil,js=x.ReactCurrentDispatcher,Ns=x.ReactCurrentOwner,Ps=x.ReactCurrentBatchConfig,Rs=0,Ts=null,_s=null,As=0,Ms=0,zs=jo(0),Is=0,Os=null,Fs=0,Ls=0,Ds=0,Bs=null,Ws=null,Hs=0,Us=1/0,Vs=null,$s=!1,Ks=null,qs=null,Ys=!1,Gs=null,Qs=0,Xs=0,Js=null,Zs=-1,ec=0;function tc(){return 0!==(6&Rs)?Xe():-1!==Zs?Zs:Zs=Xe()}function nc(e){return 0===(1&e.mode)?1:0!==(2&Rs)&&0!==As?As&-As:null!==va.transition?(0===ec&&(ec=mt()),ec):0!==(e=bt)?e:e=void 0===(e=window.event)?16:Xt(e.type)}function rc(e,t,n,r){if(50<Xs)throw Xs=0,Js=null,Error(a(185));vt(e,n,r),0!==(2&Rs)&&e===Ts||(e===Ts&&(0===(2&Rs)&&(Ls|=n),4===Is&&sc(e,As)),oc(e,r),1===n&&0===Rs&&0===(1&t.mode)&&(Us=Xe()+500,Wo&&Vo()))}function oc(e,t){var n=e.callbackNode;!function(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,a=e.pendingLanes;0<a;){var i=31-it(a),l=1<<i,s=o[i];-1===s?0!==(l&n)&&0===(l&r)||(o[i]=ft(l,t)):s<=t&&(e.expiredLanes|=l),a&=~l}}(e,t);var r=pt(e,e===Ts?As:0);if(0===r)null!==n&&Ye(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(null!=n&&Ye(n),1===t)0===e.tag?function(e){Wo=!0,Uo(e)}(cc.bind(null,e)):Uo(cc.bind(null,e)),lo((function(){0===(6&Rs)&&Vo()})),n=null;else{switch(xt(r)){case 1:n=Ze;break;case 4:n=et;break;case 16:default:n=tt;break;case 536870912:n=rt}n=Tc(n,ac.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function ac(e,t){if(Zs=-1,ec=0,0!==(6&Rs))throw Error(a(327));var n=e.callbackNode;if(kc()&&e.callbackNode!==n)return null;var r=pt(e,e===Ts?As:0);if(0===r)return null;if(0!==(30&r)||0!==(r&e.expiredLanes)||t)t=vc(e,r);else{t=r;var o=Rs;Rs|=2;var i=mc();for(Ts===e&&As===t||(Vs=null,Us=Xe()+500,fc(e,t));;)try{bc();break}catch(s){hc(e,s)}Pa(),js.current=i,Rs=o,null!==_s?t=0:(Ts=null,As=0,t=Is)}if(0!==t){if(2===t&&(0!==(o=ht(e))&&(r=o,t=ic(e,o))),1===t)throw n=Os,fc(e,0),sc(e,r),oc(e,Xe()),n;if(6===t)sc(e,r);else{if(o=e.current.alternate,0===(30&r)&&!function(e){for(var t=e;;){if(16384&t.flags){var n=t.updateQueue;if(null!==n&&null!==(n=n.stores))for(var r=0;r<n.length;r++){var o=n[r],a=o.getSnapshot;o=o.value;try{if(!sr(a(),o))return!1}catch(l){return!1}}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}(o)&&(2===(t=vc(e,r))&&(0!==(i=ht(e))&&(r=i,t=ic(e,i))),1===t))throw n=Os,fc(e,0),sc(e,r),oc(e,Xe()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(a(345));case 2:case 5:Sc(e,Ws,Vs);break;case 3:if(sc(e,r),(130023424&r)===r&&10<(t=Hs+500-Xe())){if(0!==pt(e,0))break;if(((o=e.suspendedLanes)&r)!==r){tc(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=oo(Sc.bind(null,e,Ws,Vs),t);break}Sc(e,Ws,Vs);break;case 4:if(sc(e,r),(4194240&r)===r)break;for(t=e.eventTimes,o=-1;0<r;){var l=31-it(r);i=1<<l,(l=t[l])>o&&(o=l),r&=~i}if(r=o,10<(r=(120>(r=Xe()-r)?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Es(r/1960))-r)){e.timeoutHandle=oo(Sc.bind(null,e,Ws,Vs),r);break}Sc(e,Ws,Vs);break;default:throw Error(a(329))}}}return oc(e,Xe()),e.callbackNode===n?ac.bind(null,e):null}function ic(e,t){var n=Bs;return e.current.memoizedState.isDehydrated&&(fc(e,t).flags|=256),2!==(e=vc(e,t))&&(t=Ws,Ws=n,null!==t&&lc(t)),e}function lc(e){null===Ws?Ws=e:Ws.push.apply(Ws,e)}function sc(e,t){for(t&=~Ds,t&=~Ls,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-it(t),r=1<<n;e[n]=-1,t&=~r}}function cc(e){if(0!==(6&Rs))throw Error(a(327));kc();var t=pt(e,0);if(0===(1&t))return oc(e,Xe()),null;var n=vc(e,t);if(0!==e.tag&&2===n){var r=ht(e);0!==r&&(t=r,n=ic(e,r))}if(1===n)throw n=Os,fc(e,0),sc(e,t),oc(e,Xe()),n;if(6===n)throw Error(a(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Sc(e,Ws,Vs),oc(e,Xe()),null}function uc(e,t){var n=Rs;Rs|=1;try{return e(t)}finally{0===(Rs=n)&&(Us=Xe()+500,Wo&&Vo())}}function dc(e){null!==Gs&&0===Gs.tag&&0===(6&Rs)&&kc();var t=Rs;Rs|=1;var n=Ps.transition,r=bt;try{if(Ps.transition=null,bt=1,e)return e()}finally{bt=r,Ps.transition=n,0===(6&(Rs=t))&&Vo()}}function pc(){Ms=zs.current,No(zs)}function fc(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,ao(n)),null!==_s)for(n=_s.return;null!==n;){var r=n;switch(ra(r),r.tag){case 1:null!==(r=r.type.childContextTypes)&&void 0!==r&&Io();break;case 3:Ja(),No(_o),No(To),oi();break;case 5:ei(r);break;case 4:Ja();break;case 13:case 19:No(ti);break;case 10:Ra(r.type._context);break;case 22:case 23:pc()}n=n.return}if(Ts=e,_s=e=zc(e.current,null),As=Ms=t,Is=0,Os=null,Ds=Ls=Fs=0,Ws=Bs=null,null!==Ma){for(t=0;t<Ma.length;t++)if(null!==(r=(n=Ma[t]).interleaved)){n.interleaved=null;var o=r.next,a=n.pending;if(null!==a){var i=a.next;a.next=o,r.next=i}n.pending=r}Ma=null}return e}function hc(e,t){for(;;){var n=_s;try{if(Pa(),ai.current=Zi,di){for(var r=si.memoizedState;null!==r;){var o=r.queue;null!==o&&(o.pending=null),r=r.next}di=!1}if(li=0,ui=ci=si=null,pi=!1,fi=0,Ns.current=null,null===n||null===n.return){Is=1,Os=t,_s=null;break}e:{var i=e,l=n.return,s=n,c=t;if(t=As,s.flags|=32768,null!==c&&"object"===typeof c&&"function"===typeof c.then){var u=c,d=s,p=d.tag;if(0===(1&d.mode)&&(0===p||11===p||15===p)){var f=d.alternate;f?(d.updateQueue=f.updateQueue,d.memoizedState=f.memoizedState,d.lanes=f.lanes):(d.updateQueue=null,d.memoizedState=null)}var h=vl(l);if(null!==h){h.flags&=-257,yl(h,l,s,0,t),1&h.mode&&gl(i,u,t),c=u;var m=(t=h).updateQueue;if(null===m){var g=new Set;g.add(c),t.updateQueue=g}else m.add(c);break e}if(0===(1&t)){gl(i,u,t),gc();break e}c=Error(a(426))}else if(ia&&1&s.mode){var v=vl(l);if(null!==v){0===(65536&v.flags)&&(v.flags|=256),yl(v,l,s,0,t),ga(ul(c,s));break e}}i=c=ul(c,s),4!==Is&&(Is=2),null===Bs?Bs=[i]:Bs.push(i),i=l;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t,Ua(i,hl(0,c,t));break e;case 1:s=c;var y=i.type,b=i.stateNode;if(0===(128&i.flags)&&("function"===typeof y.getDerivedStateFromError||null!==b&&"function"===typeof b.componentDidCatch&&(null===qs||!qs.has(b)))){i.flags|=65536,t&=-t,i.lanes|=t,Ua(i,ml(i,s,t));break e}}i=i.return}while(null!==i)}wc(n)}catch(x){t=x,_s===n&&null!==n&&(_s=n=n.return);continue}break}}function mc(){var e=js.current;return js.current=Zi,null===e?Zi:e}function gc(){0!==Is&&3!==Is&&2!==Is||(Is=4),null===Ts||0===(268435455&Fs)&&0===(268435455&Ls)||sc(Ts,As)}function vc(e,t){var n=Rs;Rs|=2;var r=mc();for(Ts===e&&As===t||(Vs=null,fc(e,t));;)try{yc();break}catch(o){hc(e,o)}if(Pa(),Rs=n,js.current=r,null!==_s)throw Error(a(261));return Ts=null,As=0,Is}function yc(){for(;null!==_s;)xc(_s)}function bc(){for(;null!==_s&&!Ge();)xc(_s)}function xc(e){var t=Cs(e.alternate,e,Ms);e.memoizedProps=e.pendingProps,null===t?wc(e):_s=t,Ns.current=null}function wc(e){var t=e;do{var n=t.alternate;if(e=t.return,0===(32768&t.flags)){if(null!==(n=Yl(n,t,Ms)))return void(_s=n)}else{if(null!==(n=Gl(n,t)))return n.flags&=32767,void(_s=n);if(null===e)return Is=6,void(_s=null);e.flags|=32768,e.subtreeFlags=0,e.deletions=null}if(null!==(t=t.sibling))return void(_s=t);_s=t=e}while(null!==t);0===Is&&(Is=5)}function Sc(e,t,n){var r=bt,o=Ps.transition;try{Ps.transition=null,bt=1,function(e,t,n,r){do{kc()}while(null!==Gs);if(0!==(6&Rs))throw Error(a(327));n=e.finishedWork;var o=e.finishedLanes;if(null===n)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(a(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(function(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-it(n),a=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~a}}(e,i),e===Ts&&(_s=Ts=null,As=0),0===(2064&n.subtreeFlags)&&0===(2064&n.flags)||Ys||(Ys=!0,Tc(tt,(function(){return kc(),null}))),i=0!==(15990&n.flags),0!==(15990&n.subtreeFlags)||i){i=Ps.transition,Ps.transition=null;var l=bt;bt=1;var s=Rs;Rs|=4,Ns.current=null,function(e,t){if(to=$t,hr(e=fr())){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var o=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch(w){n=null;break e}var l=0,s=-1,c=-1,u=0,d=0,p=e,f=null;t:for(;;){for(var h;p!==n||0!==o&&3!==p.nodeType||(s=l+o),p!==i||0!==r&&3!==p.nodeType||(c=l+r),3===p.nodeType&&(l+=p.nodeValue.length),null!==(h=p.firstChild);)f=p,p=h;for(;;){if(p===e)break t;if(f===n&&++u===o&&(s=l),f===i&&++d===r&&(c=l),null!==(h=p.nextSibling))break;f=(p=f).parentNode}p=h}n=-1===s||-1===c?null:{start:s,end:c}}else n=null}n=n||{start:0,end:0}}else n=null;for(no={focusedElem:e,selectionRange:n},$t=!1,Zl=t;null!==Zl;)if(e=(t=Zl).child,0!==(1028&t.subtreeFlags)&&null!==e)e.return=t,Zl=e;else for(;null!==Zl;){t=Zl;try{var m=t.alternate;if(0!==(1024&t.flags))switch(t.tag){case 0:case 11:case 15:case 5:case 6:case 4:case 17:break;case 1:if(null!==m){var g=m.memoizedProps,v=m.memoizedState,y=t.stateNode,b=y.getSnapshotBeforeUpdate(t.elementType===t.type?g:rl(t.type,g),v);y.__reactInternalSnapshotBeforeUpdate=b}break;case 3:var x=t.stateNode.containerInfo;1===x.nodeType?x.textContent="":9===x.nodeType&&x.documentElement&&x.removeChild(x.documentElement);break;default:throw Error(a(163))}}catch(w){Ec(t,t.return,w)}if(null!==(e=t.sibling)){e.return=t.return,Zl=e;break}Zl=t.return}m=ns,ns=!1}(e,n),vs(n,e),mr(no),$t=!!to,no=to=null,e.current=n,bs(n,e,o),Qe(),Rs=s,bt=l,Ps.transition=i}else e.current=n;if(Ys&&(Ys=!1,Gs=e,Qs=o),i=e.pendingLanes,0===i&&(qs=null),function(e){if(at&&"function"===typeof at.onCommitFiberRoot)try{at.onCommitFiberRoot(ot,e,void 0,128===(128&e.current.flags))}catch(t){}}(n.stateNode),oc(e,Xe()),null!==t)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if($s)throw $s=!1,e=Ks,Ks=null,e;0!==(1&Qs)&&0!==e.tag&&kc(),i=e.pendingLanes,0!==(1&i)?e===Js?Xs++:(Xs=0,Js=e):Xs=0,Vo()}(e,t,n,r)}finally{Ps.transition=o,bt=r}return null}function kc(){if(null!==Gs){var e=xt(Qs),t=Ps.transition,n=bt;try{if(Ps.transition=null,bt=16>e?16:e,null===Gs)var r=!1;else{if(e=Gs,Gs=null,Qs=0,0!==(6&Rs))throw Error(a(331));var o=Rs;for(Rs|=4,Zl=e.current;null!==Zl;){var i=Zl,l=i.child;if(0!==(16&Zl.flags)){var s=i.deletions;if(null!==s){for(var c=0;c<s.length;c++){var u=s[c];for(Zl=u;null!==Zl;){var d=Zl;switch(d.tag){case 0:case 11:case 15:rs(8,d,i)}var p=d.child;if(null!==p)p.return=d,Zl=p;else for(;null!==Zl;){var f=(d=Zl).sibling,h=d.return;if(is(d),d===u){Zl=null;break}if(null!==f){f.return=h,Zl=f;break}Zl=h}}}var m=i.alternate;if(null!==m){var g=m.child;if(null!==g){m.child=null;do{var v=g.sibling;g.sibling=null,g=v}while(null!==g)}}Zl=i}}if(0!==(2064&i.subtreeFlags)&&null!==l)l.return=i,Zl=l;else e:for(;null!==Zl;){if(0!==(2048&(i=Zl).flags))switch(i.tag){case 0:case 11:case 15:rs(9,i,i.return)}var y=i.sibling;if(null!==y){y.return=i.return,Zl=y;break e}Zl=i.return}}var b=e.current;for(Zl=b;null!==Zl;){var x=(l=Zl).child;if(0!==(2064&l.subtreeFlags)&&null!==x)x.return=l,Zl=x;else e:for(l=b;null!==Zl;){if(0!==(2048&(s=Zl).flags))try{switch(s.tag){case 0:case 11:case 15:os(9,s)}}catch(S){Ec(s,s.return,S)}if(s===l){Zl=null;break e}var w=s.sibling;if(null!==w){w.return=s.return,Zl=w;break e}Zl=s.return}}if(Rs=o,Vo(),at&&"function"===typeof at.onPostCommitFiberRoot)try{at.onPostCommitFiberRoot(ot,e)}catch(S){}r=!0}return r}finally{bt=n,Ps.transition=t}}return!1}function Cc(e,t,n){e=Wa(e,t=hl(0,t=ul(n,t),1),1),t=tc(),null!==e&&(vt(e,1,t),oc(e,t))}function Ec(e,t,n){if(3===e.tag)Cc(e,e,n);else for(;null!==t;){if(3===t.tag){Cc(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"===typeof t.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===qs||!qs.has(r))){t=Wa(t,e=ml(t,e=ul(n,e),1),1),e=tc(),null!==t&&(vt(t,1,e),oc(t,e));break}}t=t.return}}function jc(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),t=tc(),e.pingedLanes|=e.suspendedLanes&n,Ts===e&&(As&n)===n&&(4===Is||3===Is&&(130023424&As)===As&&500>Xe()-Hs?fc(e,0):Ds|=n),oc(e,t)}function Nc(e,t){0===t&&(0===(1&e.mode)?t=1:(t=ut,0===(130023424&(ut<<=1))&&(ut=4194304)));var n=tc();null!==(e=Oa(e,t))&&(vt(e,t,n),oc(e,n))}function Pc(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),Nc(e,n)}function Rc(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;null!==o&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(a(314))}null!==r&&r.delete(t),Nc(e,n)}function Tc(e,t){return qe(e,t)}function _c(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ac(e,t,n,r){return new _c(e,t,n,r)}function Mc(e){return!(!(e=e.prototype)||!e.isReactComponent)}function zc(e,t){var n=e.alternate;return null===n?((n=Ac(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=14680064&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Ic(e,t,n,r,o,i){var l=2;if(r=e,"function"===typeof e)Mc(e)&&(l=1);else if("string"===typeof e)l=5;else e:switch(e){case k:return Oc(n.children,o,i,t);case C:l=8,o|=8;break;case E:return(e=Ac(12,n,t,2|o)).elementType=E,e.lanes=i,e;case R:return(e=Ac(13,n,t,o)).elementType=R,e.lanes=i,e;case T:return(e=Ac(19,n,t,o)).elementType=T,e.lanes=i,e;case M:return Fc(n,o,i,t);default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case j:l=10;break e;case N:l=9;break e;case P:l=11;break e;case _:l=14;break e;case A:l=16,r=null;break e}throw Error(a(130,null==e?e:typeof e,""))}return(t=Ac(l,n,t,o)).elementType=e,t.type=r,t.lanes=i,t}function Oc(e,t,n,r){return(e=Ac(7,e,r,t)).lanes=n,e}function Fc(e,t,n,r){return(e=Ac(22,e,r,t)).elementType=M,e.lanes=n,e.stateNode={isHidden:!1},e}function Lc(e,t,n){return(e=Ac(6,e,null,t)).lanes=n,e}function Dc(e,t,n){return(t=Ac(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Bc(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=gt(0),this.expirationTimes=gt(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=gt(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function Wc(e,t,n,r,o,a,i,l,s){return e=new Bc(e,t,n,l,s),1===t?(t=1,!0===a&&(t|=8)):t=0,a=Ac(3,null,null,t),e.current=a,a.stateNode=e,a.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},La(a),e}function Hc(e){if(!e)return Ro;e:{if(He(e=e._reactInternals)!==e||1!==e.tag)throw Error(a(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(zo(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(null!==t);throw Error(a(171))}if(1===e.tag){var n=e.type;if(zo(n))return Fo(e,n,t)}return t}function Uc(e,t,n,r,o,a,i,l,s){return(e=Wc(n,r,!0,e,0,a,0,l,s)).context=Hc(null),n=e.current,(a=Ba(r=tc(),o=nc(n))).callback=void 0!==t&&null!==t?t:null,Wa(n,a,o),e.current.lanes=o,vt(e,o,r),oc(e,r),e}function Vc(e,t,n,r){var o=t.current,a=tc(),i=nc(o);return n=Hc(n),null===t.context?t.context=n:t.pendingContext=n,(t=Ba(a,i)).payload={element:e},null!==(r=void 0===r?null:r)&&(t.callback=r),null!==(e=Wa(o,t,i))&&(rc(e,o,i,a),Ha(e,o,i)),i}function $c(e){return(e=e.current).child?(e.child.tag,e.child.stateNode):null}function Kc(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function qc(e,t){Kc(e,t),(e=e.alternate)&&Kc(e,t)}Cs=function(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps||_o.current)xl=!0;else{if(0===(e.lanes&n)&&0===(128&t.flags))return xl=!1,function(e,t,n){switch(t.tag){case 3:Tl(t),ma();break;case 5:Za(t);break;case 1:zo(t.type)&&Lo(t);break;case 4:Xa(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;Po(Ca,r._currentValue),r._currentValue=o;break;case 13:if(null!==(r=t.memoizedState))return null!==r.dehydrated?(Po(ti,1&ti.current),t.flags|=128,null):0!==(n&t.child.childLanes)?Ll(e,t,n):(Po(ti,1&ti.current),null!==(e=$l(e,t,n))?e.sibling:null);Po(ti,1&ti.current);break;case 19:if(r=0!==(n&t.childLanes),0!==(128&e.flags)){if(r)return Ul(e,t,n);t.flags|=128}if(null!==(o=t.memoizedState)&&(o.rendering=null,o.tail=null,o.lastEffect=null),Po(ti,ti.current),r)break;return null;case 22:case 23:return t.lanes=0,El(e,t,n)}return $l(e,t,n)}(e,t,n);xl=0!==(131072&e.flags)}else xl=!1,ia&&0!==(1048576&t.flags)&&ta(t,Yo,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Vl(e,t),e=t.pendingProps;var o=Mo(t,To.current);_a(t,n),o=vi(null,t,r,e,o,n);var i=yi();return t.flags|=1,"object"===typeof o&&null!==o&&"function"===typeof o.render&&void 0===o.$$typeof?(t.tag=1,t.memoizedState=null,t.updateQueue=null,zo(r)?(i=!0,Lo(t)):i=!1,t.memoizedState=null!==o.state&&void 0!==o.state?o.state:null,La(t),o.updater=al,t.stateNode=o,o._reactInternals=t,cl(t,r,e,n),t=Rl(null,t,r,!0,i,n)):(t.tag=0,ia&&i&&na(t),wl(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Vl(e,t),e=t.pendingProps,r=(o=r._init)(r._payload),t.type=r,o=t.tag=function(e){if("function"===typeof e)return Mc(e)?1:0;if(void 0!==e&&null!==e){if((e=e.$$typeof)===P)return 11;if(e===_)return 14}return 2}(r),e=rl(r,e),o){case 0:t=Nl(null,t,r,e,n);break e;case 1:t=Pl(null,t,r,e,n);break e;case 11:t=Sl(null,t,r,e,n);break e;case 14:t=kl(null,t,r,rl(r.type,e),n);break e}throw Error(a(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,Nl(e,t,r,o=t.elementType===r?o:rl(r,o),n);case 1:return r=t.type,o=t.pendingProps,Pl(e,t,r,o=t.elementType===r?o:rl(r,o),n);case 3:e:{if(Tl(t),null===e)throw Error(a(387));r=t.pendingProps,o=(i=t.memoizedState).element,Da(e,t),Va(t,r,null,n);var l=t.memoizedState;if(r=l.element,i.isDehydrated){if(i={element:r,isDehydrated:!1,cache:l.cache,pendingSuspenseBoundaries:l.pendingSuspenseBoundaries,transitions:l.transitions},t.updateQueue.baseState=i,t.memoizedState=i,256&t.flags){t=_l(e,t,r,n,o=ul(Error(a(423)),t));break e}if(r!==o){t=_l(e,t,r,n,o=ul(Error(a(424)),t));break e}for(aa=uo(t.stateNode.containerInfo.firstChild),oa=t,ia=!0,la=null,n=ka(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(ma(),r===o){t=$l(e,t,n);break e}wl(e,t,r,n)}t=t.child}return t;case 5:return Za(t),null===e&&da(t),r=t.type,o=t.pendingProps,i=null!==e?e.memoizedProps:null,l=o.children,ro(r,o)?l=null:null!==i&&ro(r,i)&&(t.flags|=32),jl(e,t),wl(e,t,l,n),t.child;case 6:return null===e&&da(t),null;case 13:return Ll(e,t,n);case 4:return Xa(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=Sa(t,null,r,n):wl(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,Sl(e,t,r,o=t.elementType===r?o:rl(r,o),n);case 7:return wl(e,t,t.pendingProps,n),t.child;case 8:case 12:return wl(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,i=t.memoizedProps,l=o.value,Po(Ca,r._currentValue),r._currentValue=l,null!==i)if(sr(i.value,l)){if(i.children===o.children&&!_o.current){t=$l(e,t,n);break e}}else for(null!==(i=t.child)&&(i.return=t);null!==i;){var s=i.dependencies;if(null!==s){l=i.child;for(var c=s.firstContext;null!==c;){if(c.context===r){if(1===i.tag){(c=Ba(-1,n&-n)).tag=2;var u=i.updateQueue;if(null!==u){var d=(u=u.shared).pending;null===d?c.next=c:(c.next=d.next,d.next=c),u.pending=c}}i.lanes|=n,null!==(c=i.alternate)&&(c.lanes|=n),Ta(i.return,n,t),s.lanes|=n;break}c=c.next}}else if(10===i.tag)l=i.type===t.type?null:i.child;else if(18===i.tag){if(null===(l=i.return))throw Error(a(341));l.lanes|=n,null!==(s=l.alternate)&&(s.lanes|=n),Ta(l,n,t),l=i.sibling}else l=i.child;if(null!==l)l.return=i;else for(l=i;null!==l;){if(l===t){l=null;break}if(null!==(i=l.sibling)){i.return=l.return,l=i;break}l=l.return}i=l}wl(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,_a(t,n),r=r(o=Aa(o)),t.flags|=1,wl(e,t,r,n),t.child;case 14:return o=rl(r=t.type,t.pendingProps),kl(e,t,r,o=rl(r.type,o),n);case 15:return Cl(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:rl(r,o),Vl(e,t),t.tag=1,zo(r)?(e=!0,Lo(t)):e=!1,_a(t,n),ll(t,r,o),cl(t,r,o,n),Rl(null,t,r,!0,e,n);case 19:return Ul(e,t,n);case 22:return El(e,t,n)}throw Error(a(156,t.tag))};var Yc="function"===typeof reportError?reportError:function(e){console.error(e)};function Gc(e){this._internalRoot=e}function Qc(e){this._internalRoot=e}function Xc(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function Jc(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function Zc(){}function eu(e,t,n,r,o){var a=n._reactRootContainer;if(a){var i=a;if("function"===typeof o){var l=o;o=function(){var e=$c(i);l.call(e)}}Vc(t,i,e,o)}else i=function(e,t,n,r,o){if(o){if("function"===typeof r){var a=r;r=function(){var e=$c(i);a.call(e)}}var i=Uc(t,r,e,0,null,!1,0,"",Zc);return e._reactRootContainer=i,e[go]=i.current,Ur(8===e.nodeType?e.parentNode:e),dc(),i}for(;o=e.lastChild;)e.removeChild(o);if("function"===typeof r){var l=r;r=function(){var e=$c(s);l.call(e)}}var s=Wc(e,0,!1,null,0,!1,0,"",Zc);return e._reactRootContainer=s,e[go]=s.current,Ur(8===e.nodeType?e.parentNode:e),dc((function(){Vc(t,s,n,r)})),s}(n,t,e,o,r);return $c(i)}Qc.prototype.render=Gc.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(a(409));Vc(e,t,null,null)},Qc.prototype.unmount=Gc.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;dc((function(){Vc(null,e,null,null)})),t[go]=null}},Qc.prototype.unstable_scheduleHydration=function(e){if(e){var t=Ct();e={blockedOn:null,target:e,priority:t};for(var n=0;n<zt.length&&0!==t&&t<zt[n].priority;n++);zt.splice(n,0,e),0===n&&Lt(e)}},wt=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=dt(t.pendingLanes);0!==n&&(yt(t,1|n),oc(t,Xe()),0===(6&Rs)&&(Us=Xe()+500,Vo()))}break;case 13:dc((function(){var t=Oa(e,1);if(null!==t){var n=tc();rc(t,e,1,n)}})),qc(e,1)}},St=function(e){if(13===e.tag){var t=Oa(e,134217728);if(null!==t)rc(t,e,134217728,tc());qc(e,134217728)}},kt=function(e){if(13===e.tag){var t=nc(e),n=Oa(e,t);if(null!==n)rc(n,e,t,tc());qc(e,t)}},Ct=function(){return bt},Et=function(e,t){var n=bt;try{return bt=e,t()}finally{bt=n}},Se=function(e,t,n){switch(t){case"input":if(J(e,n),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=ko(r);if(!o)throw Error(a(90));q(r),J(r,o)}}}break;case"textarea":ae(e,n);break;case"select":null!=(t=n.value)&&ne(e,!!n.multiple,t,!1)}},Pe=uc,Re=dc;var tu={usingClientEntryPoint:!1,Events:[wo,So,ko,je,Ne,uc]},nu={findFiberByHostInstance:xo,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},ru={bundleType:nu.bundleType,version:nu.version,rendererPackageName:nu.rendererPackageName,rendererConfig:nu.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:x.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=$e(e))?null:e.stateNode},findFiberByHostInstance:nu.findFiberByHostInstance||function(){return null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var ou=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ou.isDisabled&&ou.supportsFiber)try{ot=ou.inject(ru),at=ou}catch(ue){}}t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=tu,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!Xc(t))throw Error(a(200));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:S,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.createRoot=function(e,t){if(!Xc(e))throw Error(a(299));var n=!1,r="",o=Yc;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onRecoverableError&&(o=t.onRecoverableError)),t=Wc(e,1,!1,null,0,n,0,r,o),e[go]=t.current,Ur(8===e.nodeType?e.parentNode:e),new Gc(t)},t.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternals;if(void 0===t){if("function"===typeof e.render)throw Error(a(188));throw e=Object.keys(e).join(","),Error(a(268,e))}return e=null===(e=$e(t))?null:e.stateNode},t.flushSync=function(e){return dc(e)},t.hydrate=function(e,t,n){if(!Jc(t))throw Error(a(200));return eu(null,e,t,!0,n)},t.hydrateRoot=function(e,t,n){if(!Xc(e))throw Error(a(405));var r=null!=n&&n.hydratedSources||null,o=!1,i="",l=Yc;if(null!==n&&void 0!==n&&(!0===n.unstable_strictMode&&(o=!0),void 0!==n.identifierPrefix&&(i=n.identifierPrefix),void 0!==n.onRecoverableError&&(l=n.onRecoverableError)),t=Uc(t,null,e,1,null!=n?n:null,o,0,i,l),e[go]=t.current,Ur(e),r)for(e=0;e<r.length;e++)o=(o=(n=r[e])._getVersion)(n._source),null==t.mutableSourceEagerHydrationData?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new Qc(t)},t.render=function(e,t,n){if(!Jc(t))throw Error(a(200));return eu(null,e,t,!1,n)},t.unmountComponentAtNode=function(e){if(!Jc(e))throw Error(a(40));return!!e._reactRootContainer&&(dc((function(){eu(null,null,e,!1,(function(){e._reactRootContainer=null,e[go]=null}))})),!0)},t.unstable_batchedUpdates=uc,t.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Jc(n))throw Error(a(200));if(null==e||void 0===e._reactInternals)throw Error(a(38));return eu(e,t,n,!1,r)},t.version="18.3.1-next-f1338f8080-20240426"},763:(e,t,n)=>{e.exports=n(983)},853:(e,t,n)=>{e.exports=n(234)},950:(e,t,n)=>{!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(730)},983:(e,t)=>{var n="function"===typeof Symbol&&Symbol.for,r=n?Symbol.for("react.element"):60103,o=n?Symbol.for("react.portal"):60106,a=n?Symbol.for("react.fragment"):60107,i=n?Symbol.for("react.strict_mode"):60108,l=n?Symbol.for("react.profiler"):60114,s=n?Symbol.for("react.provider"):60109,c=n?Symbol.for("react.context"):60110,u=n?Symbol.for("react.async_mode"):60111,d=n?Symbol.for("react.concurrent_mode"):60111,p=n?Symbol.for("react.forward_ref"):60112,f=n?Symbol.for("react.suspense"):60113,h=n?Symbol.for("react.suspense_list"):60120,m=n?Symbol.for("react.memo"):60115,g=n?Symbol.for("react.lazy"):60116,v=n?Symbol.for("react.block"):60121,y=n?Symbol.for("react.fundamental"):60117,b=n?Symbol.for("react.responder"):60118,x=n?Symbol.for("react.scope"):60119;function w(e){if("object"===typeof e&&null!==e){var t=e.$$typeof;switch(t){case r:switch(e=e.type){case u:case d:case a:case l:case i:case f:return e;default:switch(e=e&&e.$$typeof){case c:case p:case g:case m:case s:return e;default:return t}}case o:return t}}}function S(e){return w(e)===d}t.AsyncMode=u,t.ConcurrentMode=d,t.ContextConsumer=c,t.ContextProvider=s,t.Element=r,t.ForwardRef=p,t.Fragment=a,t.Lazy=g,t.Memo=m,t.Portal=o,t.Profiler=l,t.StrictMode=i,t.Suspense=f,t.isAsyncMode=function(e){return S(e)||w(e)===u},t.isConcurrentMode=S,t.isContextConsumer=function(e){return w(e)===c},t.isContextProvider=function(e){return w(e)===s},t.isElement=function(e){return"object"===typeof e&&null!==e&&e.$$typeof===r},t.isForwardRef=function(e){return w(e)===p},t.isFragment=function(e){return w(e)===a},t.isLazy=function(e){return w(e)===g},t.isMemo=function(e){return w(e)===m},t.isPortal=function(e){return w(e)===o},t.isProfiler=function(e){return w(e)===l},t.isStrictMode=function(e){return w(e)===i},t.isSuspense=function(e){return w(e)===f},t.isValidElementType=function(e){return"string"===typeof e||"function"===typeof e||e===a||e===d||e===l||e===i||e===f||e===h||"object"===typeof e&&null!==e&&(e.$$typeof===g||e.$$typeof===m||e.$$typeof===s||e.$$typeof===c||e.$$typeof===p||e.$$typeof===y||e.$$typeof===b||e.$$typeof===x||e.$$typeof===v)},t.typeOf=w}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var a=t[r]={exports:{}};return e[r](a,a.exports,n),a.exports}(()=>{var e,t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__;n.t=function(r,o){if(1&o&&(r=this(r)),8&o)return r;if("object"===typeof r&&r){if(4&o&&r.__esModule)return r;if(16&o&&"function"===typeof r.then)return r}var a=Object.create(null);n.r(a);var i={};e=e||[null,t({}),t([]),t(t)];for(var l=2&o&&r;"object"==typeof l&&!~e.indexOf(l);l=t(l))Object.getOwnPropertyNames(l).forEach((e=>i[e]=()=>r[e]));return i.default=()=>r,n.d(a,i),a}})(),n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r=n(43),o=n.t(r,2),a=n(391);function i(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;n[r]=e[r]}return n}function l(e,t){if(null==e)return{};var n,r,o=i(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],-1===t.indexOf(n)&&{}.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}function s(e){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},s(e)}function c(e){var t=function(e,t){if("object"!=s(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=s(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==s(t)?t:t+""}function u(e,t,n){return(t=c(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function d(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function p(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?d(Object(n),!0).forEach((function(t){u(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):d(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}n(175);const f=["sri"],h=["page"],m=["page","matches"],g=["onClick","discover","prefetch","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],v=["aria-current","caseSensitive","className","end","style","to","viewTransition","children"],y=["discover","fetcherKey","navigate","reloadDocument","replace","state","method","action","onSubmit","relative","preventScrollReset","viewTransition"];var b="popstate";function x(){return N((function(e,t){let{pathname:n,search:r,hash:o}=e.location;return C("",{pathname:n,search:r,hash:o},t.state&&t.state.usr||null,t.state&&t.state.key||"default")}),(function(e,t){return"string"===typeof t?t:E(t)}),null,arguments.length>0&&void 0!==arguments[0]?arguments[0]:{})}function w(e,t){if(!1===e||null===e||"undefined"===typeof e)throw new Error(t)}function S(e,t){if(!e){"undefined"!==typeof console&&console.warn(t);try{throw new Error(t)}catch(n){}}}function k(e,t){return{usr:e.state,key:e.key,idx:t}}function C(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=arguments.length>3?arguments[3]:void 0;return p(p({pathname:"string"===typeof e?e:e.pathname,search:"",hash:""},"string"===typeof t?j(t):t),{},{state:n,key:t&&t.key||r||Math.random().toString(36).substring(2,10)})}function E(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&"?"!==n&&(t+="?"===n.charAt(0)?n:"?"+n),r&&"#"!==r&&(t+="#"===r.charAt(0)?r:"#"+r),t}function j(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substring(n),e=e.substring(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substring(r),e=e.substring(0,r)),e&&(t.pathname=e)}return t}function N(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},{window:o=document.defaultView,v5Compat:a=!1}=r,i=o.history,l="POP",s=null,c=u();function u(){return(i.state||{idx:null}).idx}function d(){l="POP";let e=u(),t=null==e?null:e-c;c=e,s&&s({action:l,location:h.location,delta:t})}function f(e){return P(e)}null==c&&(c=0,i.replaceState(p(p({},i.state),{},{idx:c}),""));let h={get action(){return l},get location(){return e(o,i)},listen(e){if(s)throw new Error("A history only accepts one active listener");return o.addEventListener(b,d),s=e,()=>{o.removeEventListener(b,d),s=null}},createHref:e=>t(o,e),createURL:f,encodeLocation(e){let t=f(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:function(e,t){l="PUSH";let r=C(h.location,e,t);n&&n(r,e),c=u()+1;let d=k(r,c),p=h.createHref(r);try{i.pushState(d,"",p)}catch(f){if(f instanceof DOMException&&"DataCloneError"===f.name)throw f;o.location.assign(p)}a&&s&&s({action:l,location:h.location,delta:1})},replace:function(e,t){l="REPLACE";let r=C(h.location,e,t);n&&n(r,e),c=u();let o=k(r,c),d=h.createHref(r);i.replaceState(o,"",d),a&&s&&s({action:l,location:h.location,delta:0})},go:e=>i.go(e)};return h}function P(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n="http://localhost";"undefined"!==typeof window&&(n="null"!==window.location.origin?window.location.origin:window.location.href),w(n,"No window.location.(origin|href) available to create URL");let r="string"===typeof e?e:E(e);return r=r.replace(/ $/,"%20"),!t&&r.startsWith("//")&&(r=n+r),new URL(r,n)}new WeakMap;function R(e,t){return T(e,t,arguments.length>2&&void 0!==arguments[2]?arguments[2]:"/",!1)}function T(e,t,n,r){let o=$(("string"===typeof t?j(t):t).pathname||"/",n);if(null==o)return null;let a=_(e);!function(e){e.sort(((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let n=e.length===t.length&&e.slice(0,-1).every(((e,n)=>e===t[n]));return n?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map((e=>e.childrenIndex)),t.routesMeta.map((e=>e.childrenIndex)))))}(a);let i=null;for(let l=0;null==i&&l<a.length;++l){let e=V(o);i=W(a[l],e,r)}return i}function _(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",o=(e,o,a)=>{let i={relativePath:void 0===a?e.path||"":a,caseSensitive:!0===e.caseSensitive,childrenIndex:o,route:e};i.relativePath.startsWith("/")&&(w(i.relativePath.startsWith(r),'Absolute route path "'.concat(i.relativePath,'" nested under path "').concat(r,'" is not valid. An absolute child route path must start with the combined path of all its parent routes.')),i.relativePath=i.relativePath.slice(r.length));let l=Q([r,i.relativePath]),s=n.concat(i);e.children&&e.children.length>0&&(w(!0!==e.index,'Index routes must not have child routes. Please remove all child routes from route path "'.concat(l,'".')),_(e.children,t,s,l)),(null!=e.path||e.index)&&t.push({path:l,score:B(l,e.index),routesMeta:s})};return e.forEach(((e,t)=>{var n;if(""!==e.path&&null!==(n=e.path)&&void 0!==n&&n.includes("?"))for(let r of A(e.path))o(e,t,r);else o(e,t)})),t}function A(e){let t=e.split("/");if(0===t.length)return[];let[n,...r]=t,o=n.endsWith("?"),a=n.replace(/\?$/,"");if(0===r.length)return o?[a,""]:[a];let i=A(r.join("/")),l=[];return l.push(...i.map((e=>""===e?a:[a,e].join("/")))),o&&l.push(...i),l.map((t=>e.startsWith("/")&&""===t?"/":t))}var M=/^:[\w-]+$/,z=3,I=2,O=1,F=10,L=-2,D=e=>"*"===e;function B(e,t){let n=e.split("/"),r=n.length;return n.some(D)&&(r+=L),t&&(r+=I),n.filter((e=>!D(e))).reduce(((e,t)=>e+(M.test(t)?z:""===t?O:F)),r)}function W(e,t){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],{routesMeta:r}=e,o={},a="/",i=[];for(let l=0;l<r.length;++l){let e=r[l],s=l===r.length-1,c="/"===a?t:t.slice(a.length)||"/",u=H({path:e.relativePath,caseSensitive:e.caseSensitive,end:s},c),d=e.route;if(!u&&s&&n&&!r[r.length-1].route.index&&(u=H({path:e.relativePath,caseSensitive:e.caseSensitive,end:!1},c)),!u)return null;Object.assign(o,u.params),i.push({params:o,pathname:Q([a,u.pathname]),pathnameBase:X(Q([a,u.pathnameBase])),route:d}),"/"!==u.pathnameBase&&(a=Q([a,u.pathnameBase]))}return i}function H(e,t){"string"===typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=U(e.path,e.caseSensitive,e.end),o=t.match(n);if(!o)return null;let a=o[0],i=a.replace(/(.)\/+$/,"$1"),l=o.slice(1);return{params:r.reduce(((e,t,n)=>{let{paramName:r,isOptional:o}=t;if("*"===r){let e=l[n]||"";i=a.slice(0,a.length-e.length).replace(/(.)\/+$/,"$1")}const s=l[n];return e[r]=o&&!s?void 0:(s||"").replace(/%2F/g,"/"),e}),{}),pathname:a,pathnameBase:i,pattern:e}}function U(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];S("*"===e||!e.endsWith("*")||e.endsWith("/*"),'Route path "'.concat(e,'" will be treated as if it were "').concat(e.replace(/\*$/,"/*"),'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "').concat(e.replace(/\*$/,"/*"),'".'));let r=[],o="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,((e,t,n)=>(r.push({paramName:t,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)")));return e.endsWith("*")?(r.push({paramName:"*"}),o+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?o+="\\/*$":""!==e&&"/"!==e&&(o+="(?:(?=\\/|$))"),[new RegExp(o,t?void 0:"i"),r]}function V(e){try{return e.split("/").map((e=>decodeURIComponent(e).replace(/\//g,"%2F"))).join("/")}catch(t){return S(!1,'The URL path "'.concat(e,'" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (').concat(t,").")),e}}function $(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&"/"!==r?null:e.slice(n)||"/"}function K(e,t,n,r){return"Cannot include a '".concat(e,"' character in a manually specified `to.").concat(t,"` field [").concat(JSON.stringify(r),"].  Please separate it out to the `to.").concat(n,'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.')}function q(e){return e.filter(((e,t)=>0===t||e.route.path&&e.route.path.length>0))}function Y(e){let t=q(e);return t.map(((e,n)=>n===t.length-1?e.pathname:e.pathnameBase))}function G(e,t,n){let r,o=arguments.length>3&&void 0!==arguments[3]&&arguments[3];"string"===typeof e?r=j(e):(r=p({},e),w(!r.pathname||!r.pathname.includes("?"),K("?","pathname","search",r)),w(!r.pathname||!r.pathname.includes("#"),K("#","pathname","hash",r)),w(!r.search||!r.search.includes("#"),K("#","search","hash",r)));let a,i=""===e||""===r.pathname,l=i?"/":r.pathname;if(null==l)a=n;else{let e=t.length-1;if(!o&&l.startsWith("..")){let t=l.split("/");for(;".."===t[0];)t.shift(),e-=1;r.pathname=t.join("/")}a=e>=0?t[e]:"/"}let s=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/",{pathname:n,search:r="",hash:o=""}="string"===typeof e?j(e):e,a=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach((e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)})),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:a,search:J(r),hash:Z(o)}}(r,a),c=l&&"/"!==l&&l.endsWith("/"),u=(i||"."===l)&&n.endsWith("/");return s.pathname.endsWith("/")||!c&&!u||(s.pathname+="/"),s}var Q=e=>e.join("/").replace(/\/\/+/g,"/"),X=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),J=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",Z=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";function ee(e){return null!=e&&"number"===typeof e.status&&"string"===typeof e.statusText&&"boolean"===typeof e.internal&&"data"in e}var te=["POST","PUT","PATCH","DELETE"],ne=(new Set(te),["GET",...te]);new Set(ne),Symbol("ResetLoaderData");var re=r.createContext(null);re.displayName="DataRouter";var oe=r.createContext(null);oe.displayName="DataRouterState";var ae=r.createContext({isTransitioning:!1});ae.displayName="ViewTransition";var ie=r.createContext(new Map);ie.displayName="Fetchers";var le=r.createContext(null);le.displayName="Await";var se=r.createContext(null);se.displayName="Navigation";var ce=r.createContext(null);ce.displayName="Location";var ue=r.createContext({outlet:null,matches:[],isDataRoute:!1});ue.displayName="Route";var de=r.createContext(null);de.displayName="RouteError";function pe(){return null!=r.useContext(ce)}function fe(){return w(pe(),"useLocation() may be used only in the context of a <Router> component."),r.useContext(ce).location}var he="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function me(e){r.useContext(se).static||r.useLayoutEffect(e)}function ge(){let{isDataRoute:e}=r.useContext(ue);return e?function(){let{router:e}=je("useNavigate"),t=Pe("useNavigate"),n=r.useRef(!1);me((()=>{n.current=!0}));let o=r.useCallback((async function(r){let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};S(n.current,he),n.current&&("number"===typeof r?e.navigate(r):await e.navigate(r,p({fromRouteId:t},o)))}),[e,t]);return o}():function(){w(pe(),"useNavigate() may be used only in the context of a <Router> component.");let e=r.useContext(re),{basename:t,navigator:n}=r.useContext(se),{matches:o}=r.useContext(ue),{pathname:a}=fe(),i=JSON.stringify(Y(o)),l=r.useRef(!1);me((()=>{l.current=!0}));let s=r.useCallback((function(r){let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(S(l.current,he),!l.current)return;if("number"===typeof r)return void n.go(r);let s=G(r,JSON.parse(i),a,"path"===o.relative);null==e&&"/"!==t&&(s.pathname="/"===s.pathname?t:Q([t,s.pathname])),(o.replace?n.replace:n.push)(s,o.state,o)}),[t,n,i,a,e]);return s}()}r.createContext(null);function ve(){let{matches:e}=r.useContext(ue),t=e[e.length-1];return t?t.params:{}}function ye(e){let{relative:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{matches:n}=r.useContext(ue),{pathname:o}=fe(),a=JSON.stringify(Y(n));return r.useMemo((()=>G(e,JSON.parse(a),o,"path"===t)),[e,a,o,t])}function be(e,t,n,o){w(pe(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:a,static:i}=r.useContext(se),{matches:l}=r.useContext(ue),s=l[l.length-1],c=s?s.params:{},u=s?s.pathname:"/",d=s?s.pathnameBase:"/",f=s&&s.route;{let e=f&&f.path||"";_e(u,!f||e.endsWith("*")||e.endsWith("*?"),'You rendered descendant <Routes> (or called `useRoutes()`) at "'.concat(u,'" (under <Route path="').concat(e,'">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won\'t match anymore and therefore the child routes will never render.\n\nPlease change the parent <Route path="').concat(e,'"> to <Route path="').concat("/"===e?"*":"".concat(e,"/*"),'">.'))}let h,m=fe();if(t){var g;let e="string"===typeof t?j(t):t;w("/"===d||(null===(g=e.pathname)||void 0===g?void 0:g.startsWith(d)),'When overriding the location using `<Routes location>` or `useRoutes(routes, location)`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "'.concat(d,'" but pathname "').concat(e.pathname,'" was given in the `location` prop.')),h=e}else h=m;let v=h.pathname||"/",y=v;if("/"!==d){let e=d.replace(/^\//,"").split("/");y="/"+v.replace(/^\//,"").split("/").slice(e.length).join("/")}let b=!i&&n&&n.matches&&n.matches.length>0?n.matches:R(e,{pathname:y});S(f||null!=b,'No routes matched location "'.concat(h.pathname).concat(h.search).concat(h.hash,'" ')),S(null==b||void 0!==b[b.length-1].route.element||void 0!==b[b.length-1].route.Component||void 0!==b[b.length-1].route.lazy,'Matched leaf route at location "'.concat(h.pathname).concat(h.search).concat(h.hash,'" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.'));let x=Ce(b&&b.map((e=>Object.assign({},e,{params:Object.assign({},c,e.params),pathname:Q([d,a.encodeLocation?a.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?d:Q([d,a.encodeLocation?a.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])}))),l,n,o);return t&&x?r.createElement(ce.Provider,{value:{location:p({pathname:"/",search:"",hash:"",state:null,key:"default"},h),navigationType:"POP"}},x):x}function xe(){let e=Re(),t=ee(e)?"".concat(e.status," ").concat(e.statusText):e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,o="rgba(200,200,200, 0.5)",a={padding:"0.5rem",backgroundColor:o},i={padding:"2px 4px",backgroundColor:o},l=null;return console.error("Error handled by React Router default ErrorBoundary:",e),l=r.createElement(r.Fragment,null,r.createElement("p",null,"\ud83d\udcbf Hey developer \ud83d\udc4b"),r.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",r.createElement("code",{style:i},"ErrorBoundary")," or"," ",r.createElement("code",{style:i},"errorElement")," prop on your route.")),r.createElement(r.Fragment,null,r.createElement("h2",null,"Unexpected Application Error!"),r.createElement("h3",{style:{fontStyle:"italic"}},t),n?r.createElement("pre",{style:a},n):null,l)}var we=r.createElement(xe,null),Se=class extends r.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?r.createElement(ue.Provider,{value:this.props.routeContext},r.createElement(de.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function ke(e){let{routeContext:t,match:n,children:o}=e,a=r.useContext(re);return a&&a.static&&a.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=n.route.id),r.createElement(ue.Provider,{value:t},o)}function Ce(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;if(null==e){if(!n)return null;if(n.errors)e=n.matches;else{if(0!==t.length||n.initialized||!(n.matches.length>0))return null;e=n.matches}}let o=e,a=null===n||void 0===n?void 0:n.errors;if(null!=a){let e=o.findIndex((e=>e.route.id&&void 0!==(null===a||void 0===a?void 0:a[e.route.id])));w(e>=0,"Could not find a matching route for errors on route IDs: ".concat(Object.keys(a).join(","))),o=o.slice(0,Math.min(o.length,e+1))}let i=!1,l=-1;if(n)for(let r=0;r<o.length;r++){let e=o[r];if((e.route.HydrateFallback||e.route.hydrateFallbackElement)&&(l=r),e.route.id){let{loaderData:t,errors:r}=n,a=e.route.loader&&!t.hasOwnProperty(e.route.id)&&(!r||void 0===r[e.route.id]);if(e.route.lazy||a){i=!0,o=l>=0?o.slice(0,l+1):[o[0]];break}}}return o.reduceRight(((e,s,c)=>{let u,d=!1,p=null,f=null;n&&(u=a&&s.route.id?a[s.route.id]:void 0,p=s.route.errorElement||we,i&&(l<0&&0===c?(_e("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),d=!0,f=null):l===c&&(d=!0,f=s.route.hydrateFallbackElement||null)));let h=t.concat(o.slice(0,c+1)),m=()=>{let t;return t=u?p:d?f:s.route.Component?r.createElement(s.route.Component,null):s.route.element?s.route.element:e,r.createElement(ke,{match:s,routeContext:{outlet:e,matches:h,isDataRoute:null!=n},children:t})};return n&&(s.route.ErrorBoundary||s.route.errorElement||0===c)?r.createElement(Se,{location:n.location,revalidation:n.revalidation,component:p,error:u,children:m(),routeContext:{outlet:null,matches:h,isDataRoute:!0}}):m()}),null)}function Ee(e){return"".concat(e," must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.")}function je(e){let t=r.useContext(re);return w(t,Ee(e)),t}function Ne(e){let t=r.useContext(oe);return w(t,Ee(e)),t}function Pe(e){let t=function(e){let t=r.useContext(ue);return w(t,Ee(e)),t}(e),n=t.matches[t.matches.length-1];return w(n.route.id,"".concat(e,' can only be used on routes that contain a unique "id"')),n.route.id}function Re(){var e;let t=r.useContext(de),n=Ne("useRouteError"),o=Pe("useRouteError");return void 0!==t?t:null===(e=n.errors)||void 0===e?void 0:e[o]}var Te={};function _e(e,t,n){t||Te[e]||(Te[e]=!0,S(!1,n))}r.memo((function(e){let{routes:t,future:n,state:r}=e;return be(t,void 0,r,n)}));function Ae(e){let{to:t,replace:n,state:o,relative:a}=e;w(pe(),"<Navigate> may be used only in the context of a <Router> component.");let{static:i}=r.useContext(se);S(!i,"<Navigate> must not be used on the initial render in a <StaticRouter>. This is a no-op, but you should modify your code so the <Navigate> is only ever rendered in response to some user interaction or state change.");let{matches:l}=r.useContext(ue),{pathname:s}=fe(),c=ge(),u=G(t,Y(l),s,"path"===a),d=JSON.stringify(u);return r.useEffect((()=>{c(JSON.parse(d),{replace:n,state:o,relative:a})}),[c,d,a,n,o]),null}function Me(e){w(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function ze(e){let{basename:t="/",children:n=null,location:o,navigationType:a="POP",navigator:i,static:l=!1}=e;w(!pe(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let s=t.replace(/^\/*/,"/"),c=r.useMemo((()=>({basename:s,navigator:i,static:l,future:{}})),[s,i,l]);"string"===typeof o&&(o=j(o));let{pathname:u="/",search:d="",hash:p="",state:f=null,key:h="default"}=o,m=r.useMemo((()=>{let e=$(u,s);return null==e?null:{location:{pathname:e,search:d,hash:p,state:f,key:h},navigationType:a}}),[s,u,d,p,f,h,a]);return S(null!=m,'<Router basename="'.concat(s,'"> is not able to match the URL "').concat(u).concat(d).concat(p,"\" because it does not start with the basename, so the <Router> won't render anything.")),null==m?null:r.createElement(se.Provider,{value:c},r.createElement(ce.Provider,{children:n,value:m}))}function Ie(e){let{children:t,location:n}=e;return be(Oe(t),n)}r.Component;function Oe(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=[];return r.Children.forEach(e,((e,o)=>{if(!r.isValidElement(e))return;let a=[...t,o];if(e.type===r.Fragment)return void n.push.apply(n,Oe(e.props.children,a));w(e.type===Me,"[".concat("string"===typeof e.type?e.type:e.type.name,"] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>")),w(!e.props.index||!e.props.children,"An index route cannot have child routes.");let i={id:e.props.id||a.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,hydrateFallbackElement:e.props.hydrateFallbackElement,HydrateFallback:e.props.HydrateFallback,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:!0===e.props.hasErrorBoundary||null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(i.children=Oe(e.props.children,a)),n.push(i)})),n}var Fe="get",Le="application/x-www-form-urlencoded";function De(e){return null!=e&&"string"===typeof e.tagName}var Be=null;var We=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function He(e){return null==e||We.has(e)?e:(S(!1,'"'.concat(e,'" is not a valid `encType` for `<Form>`/`<fetcher.Form>` and will default to "').concat(Le,'"')),null)}function Ue(e,t){let n,r,o,a,i;if(De(l=e)&&"form"===l.tagName.toLowerCase()){let i=e.getAttribute("action");r=i?$(i,t):null,n=e.getAttribute("method")||Fe,o=He(e.getAttribute("enctype"))||Le,a=new FormData(e)}else if(function(e){return De(e)&&"button"===e.tagName.toLowerCase()}(e)||function(e){return De(e)&&"input"===e.tagName.toLowerCase()}(e)&&("submit"===e.type||"image"===e.type)){let i=e.form;if(null==i)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let l=e.getAttribute("formaction")||i.getAttribute("action");if(r=l?$(l,t):null,n=e.getAttribute("formmethod")||i.getAttribute("method")||Fe,o=He(e.getAttribute("formenctype"))||He(i.getAttribute("enctype"))||Le,a=new FormData(i,e),!function(){if(null===Be)try{new FormData(document.createElement("form"),0),Be=!1}catch(e){Be=!0}return Be}()){let{name:t,type:n,value:r}=e;if("image"===n){let e=t?"".concat(t,"."):"";a.append("".concat(e,"x"),"0"),a.append("".concat(e,"y"),"0")}else t&&a.append(t,r)}}else{if(De(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');n=Fe,r=null,o=Le,i=e}var l;return a&&"text/plain"===o&&(i=a,a=void 0),{action:r,method:n.toLowerCase(),encType:o,formData:a,body:i}}function Ve(e,t){if(!1===e||null===e||"undefined"===typeof e)throw new Error(t)}async function $e(e,t){if(e.id in t)return t[e.id];try{let n=await import(e.module);return t[e.id]=n,n}catch(n){return console.error("Error loading route module `".concat(e.module,"`, reloading page...")),console.error(n),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise((()=>{}))}}function Ke(e){return null!=e&&"string"===typeof e.page}function qe(e){return null!=e&&(null==e.href?"preload"===e.rel&&"string"===typeof e.imageSrcSet&&"string"===typeof e.imageSizes:"string"===typeof e.rel&&"string"===typeof e.href)}function Ye(e,t,n,r,o,a){let i=(e,t)=>!n[t]||e.route.id!==n[t].route.id,l=(e,t)=>{var r;return n[t].pathname!==e.pathname||(null===(r=n[t].route.path)||void 0===r?void 0:r.endsWith("*"))&&n[t].params["*"]!==e.params["*"]};return"assets"===a?t.filter(((e,t)=>i(e,t)||l(e,t))):"data"===a?t.filter(((t,a)=>{let s=r.routes[t.route.id];if(!s||!s.hasLoader)return!1;if(i(t,a)||l(t,a))return!0;if(t.route.shouldRevalidate){var c;let r=t.route.shouldRevalidate({currentUrl:new URL(o.pathname+o.search+o.hash,window.origin),currentParams:(null===(c=n[0])||void 0===c?void 0:c.params)||{},nextUrl:new URL(e,window.origin),nextParams:t.params,defaultShouldRevalidate:!0});if("boolean"===typeof r)return r}return!0})):[]}function Ge(e,t){let{includeHydrateFallback:n}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return r=e.map((e=>{let r=t.routes[e.route.id];if(!r)return[];let o=[r.module];return r.clientActionModule&&(o=o.concat(r.clientActionModule)),r.clientLoaderModule&&(o=o.concat(r.clientLoaderModule)),n&&r.hydrateFallbackModule&&(o=o.concat(r.hydrateFallbackModule)),r.imports&&(o=o.concat(r.imports)),o})).flat(1),[...new Set(r)];var r}function Qe(e,t){let n=new Set,r=new Set(t);return e.reduce(((e,o)=>{if(t&&!Ke(o)&&"script"===o.as&&o.href&&r.has(o.href))return e;let a=JSON.stringify(function(e){let t={},n=Object.keys(e).sort();for(let r of n)t[r]=e[r];return t}(o));return n.has(a)||(n.add(a),e.push({key:a,link:o})),e}),[])}function Xe(e){return{__html:e}}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");"undefined"!==typeof window?window:"undefined"!==typeof globalThis&&globalThis;Symbol("SingleFetchRedirect");var Je=new Set([100,101,204,205]);function Ze(e,t){let n="string"===typeof e?new URL(e,"undefined"===typeof window?"server://singlefetch/":window.location.origin):e;return"/"===n.pathname?n.pathname="_root.data":t&&"/"===$(n.pathname,t)?n.pathname="".concat(t.replace(/\/$/,""),"/_root.data"):n.pathname="".concat(n.pathname.replace(/\/$/,""),".data"),n}r.Component;function et(e){let{error:t,isOutsideRemixApp:n}=e;console.error(t);let o,a=r.createElement("script",{dangerouslySetInnerHTML:{__html:'\n        console.log(\n          "\ud83d\udcbf Hey developer \ud83d\udc4b. You can provide a way better UX than this when your app throws errors. Check out https://reactrouter.com/how-to/error-boundary for more information."\n        );\n      '}});if(ee(t))return r.createElement(tt,{title:"Unhandled Thrown Response!"},r.createElement("h1",{style:{fontSize:"24px"}},t.status," ",t.statusText),a);if(t instanceof Error)o=t;else{let e=null==t?"Unknown Error":"object"===typeof t&&"toString"in t?t.toString():JSON.stringify(t);o=new Error(e)}return r.createElement(tt,{title:"Application Error!",isOutsideRemixApp:n},r.createElement("h1",{style:{fontSize:"24px"}},"Application Error"),r.createElement("pre",{style:{padding:"2rem",background:"hsla(10, 50%, 50%, 0.1)",color:"red",overflow:"auto"}},o.stack),a)}function tt(e){var t;let{title:n,renderScripts:o,isOutsideRemixApp:a,children:i}=e,{routeModules:l}=it();return null!==(t=l.root)&&void 0!==t&&t.Layout&&!a?i:r.createElement("html",{lang:"en"},r.createElement("head",null,r.createElement("meta",{charSet:"utf-8"}),r.createElement("meta",{name:"viewport",content:"width=device-width,initial-scale=1,viewport-fit=cover"}),r.createElement("title",null,n)),r.createElement("body",null,r.createElement("main",{style:{fontFamily:"system-ui, sans-serif",padding:"2rem"}},i,o?r.createElement(ft,null):null)))}function nt(e,t){return"lazy"===e.mode&&!0===t}function rt(){let e=r.useContext(re);return Ve(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}function ot(){let e=r.useContext(oe);return Ve(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}var at=r.createContext(void 0);function it(){let e=r.useContext(at);return Ve(e,"You must render this element inside a <HydratedRouter> element"),e}function lt(e,t){return n=>{e&&e(n),n.defaultPrevented||t(n)}}function st(e,t,n){if(n&&!pt)return[e[0]];if(t){let n=e.findIndex((e=>void 0!==t[e.route.id]));return e.slice(0,n+1)}return e}function ct(e){let{page:t}=e,n=l(e,h),{router:o}=rt(),a=r.useMemo((()=>R(o.routes,t,o.basename)),[o.routes,t,o.basename]);return a?r.createElement(dt,p({page:t,matches:a},n)):null}function ut(e){let{manifest:t,routeModules:n}=it(),[o,a]=r.useState([]);return r.useEffect((()=>{let r=!1;return async function(e,t,n){return Qe((await Promise.all(e.map((async e=>{let r=t.routes[e.route.id];if(r){let e=await $e(r,n);return e.links?e.links():[]}return[]})))).flat(1).filter(qe).filter((e=>"stylesheet"===e.rel||"preload"===e.rel)).map((e=>"stylesheet"===e.rel?p(p({},e),{},{rel:"prefetch",as:"style"}):p(p({},e),{},{rel:"prefetch"}))))}(e,t,n).then((e=>{r||a(e)})),()=>{r=!0}}),[e,t,n]),o}function dt(e){let{page:t,matches:n}=e,o=l(e,m),a=fe(),{manifest:i,routeModules:s}=it(),{basename:c}=rt(),{loaderData:u,matches:d}=ot(),f=r.useMemo((()=>Ye(t,n,d,i,a,"data")),[t,n,d,i,a]),h=r.useMemo((()=>Ye(t,n,d,i,a,"assets")),[t,n,d,i,a]),g=r.useMemo((()=>{if(t===a.pathname+a.search+a.hash)return[];let e=new Set,r=!1;if(n.forEach((t=>{var n;let o=i.routes[t.route.id];o&&o.hasLoader&&(!f.some((e=>e.route.id===t.route.id))&&t.route.id in u&&null!==(n=s[t.route.id])&&void 0!==n&&n.shouldRevalidate||o.hasClientLoader?r=!0:e.add(t.route.id))})),0===e.size)return[];let o=Ze(t,c);return r&&e.size>0&&o.searchParams.set("_routes",n.filter((t=>e.has(t.route.id))).map((e=>e.route.id)).join(",")),[o.pathname+o.search]}),[c,u,a,i,f,n,t,s]),v=r.useMemo((()=>Ge(h,i)),[h,i]),y=ut(h);return r.createElement(r.Fragment,null,g.map((e=>r.createElement("link",p({key:e,rel:"prefetch",as:"fetch",href:e},o)))),v.map((e=>r.createElement("link",p({key:e,rel:"modulepreload",href:e},o)))),y.map((e=>{let{key:t,link:n}=e;return r.createElement("link",p({key:t},n))})))}at.displayName="FrameworkContext";var pt=!1;function ft(e){let{manifest:t,serverHandoffString:n,isSpaMode:o,renderMeta:a,routeDiscovery:i,ssr:s}=it(),{router:c,static:u,staticContext:d}=rt(),{matches:h}=ot(),m=nt(i,s);a&&(a.didRenderScripts=!0);let g=st(h,null,o);r.useEffect((()=>{pt=!0}),[]);let v=r.useMemo((()=>{var o;let a=d?"window.__reactRouterContext = ".concat(n,";").concat("window.__reactRouterContext.stream = new ReadableStream({start(controller){window.__reactRouterContext.streamController = controller;}}).pipeThrough(new TextEncoderStream());"):" ",i=u?"".concat(null!==(o=t.hmr)&&void 0!==o&&o.runtime?"import ".concat(JSON.stringify(t.hmr.runtime),";"):"").concat(m?"":"import ".concat(JSON.stringify(t.url)),";\n").concat(g.map(((e,n)=>{let r="route".concat(n),o=t.routes[e.route.id];Ve(o,"Route ".concat(e.route.id," not found in manifest"));let{clientActionModule:a,clientLoaderModule:i,clientMiddlewareModule:l,hydrateFallbackModule:s,module:c}=o,u=[...a?[{module:a,varName:"".concat(r,"_clientAction")}]:[],...i?[{module:i,varName:"".concat(r,"_clientLoader")}]:[],...l?[{module:l,varName:"".concat(r,"_clientMiddleware")}]:[],...s?[{module:s,varName:"".concat(r,"_HydrateFallback")}]:[],{module:c,varName:"".concat(r,"_main")}];return 1===u.length?"import * as ".concat(r," from ").concat(JSON.stringify(c),";"):[u.map((e=>"import * as ".concat(e.varName,' from "').concat(e.module,'";'))).join("\n"),"const ".concat(r," = {").concat(u.map((e=>"...".concat(e.varName))).join(","),"};")].join("\n")})).join("\n"),"\n  ").concat(m?"window.__reactRouterManifest = ".concat(JSON.stringify(function(e,t){let{sri:n}=e,r=l(e,f),o=new Set(t.state.matches.map((e=>e.route.id))),a=t.state.location.pathname.split("/").filter(Boolean),i=["/"];for(a.pop();a.length>0;)i.push("/".concat(a.join("/"))),a.pop();i.forEach((e=>{let n=R(t.routes,e,t.basename);n&&n.forEach((e=>o.add(e.route.id)))}));let s=[...o].reduce(((e,t)=>Object.assign(e,{[t]:r.routes[t]})),{});return p(p({},r),{},{routes:s,sri:!!n||void 0})}(t,c),null,2),";"):"","\n  window.__reactRouterRouteModules = {").concat(g.map(((e,t)=>"".concat(JSON.stringify(e.route.id),":route").concat(t))).join(","),"};\n\nimport(").concat(JSON.stringify(t.entry.module),");"):" ";return r.createElement(r.Fragment,null,r.createElement("script",p(p({},e),{},{suppressHydrationWarning:!0,dangerouslySetInnerHTML:Xe(a),type:void 0})),r.createElement("script",p(p({},e),{},{suppressHydrationWarning:!0,dangerouslySetInnerHTML:Xe(i),type:"module",async:!0})))}),[]),y=pt?[]:(b=t.entry.imports.concat(Ge(g,t,{includeHydrateFallback:!0})),[...new Set(b)]);var b;let x="object"===typeof t.sri?t.sri:{};return pt?null:r.createElement(r.Fragment,null,"object"===typeof t.sri?r.createElement("script",{"rr-importmap":"",type:"importmap",suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:JSON.stringify({integrity:x})}}):null,m?null:r.createElement("link",{rel:"modulepreload",href:t.url,crossOrigin:e.crossOrigin,integrity:x[t.url],suppressHydrationWarning:!0}),r.createElement("link",{rel:"modulepreload",href:t.entry.module,crossOrigin:e.crossOrigin,integrity:x[t.entry.module],suppressHydrationWarning:!0}),y.map((t=>r.createElement("link",{key:t,rel:"modulepreload",href:t,crossOrigin:e.crossOrigin,integrity:x[t],suppressHydrationWarning:!0}))),v)}function ht(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return e=>{t.forEach((t=>{"function"===typeof t?t(e):null!=t&&(t.current=e)}))}}var mt="undefined"!==typeof window&&"undefined"!==typeof window.document&&"undefined"!==typeof window.document.createElement;try{mt&&(window.__reactRouterVersion="7.6.0")}catch(lh){}function gt(e){let{basename:t,children:n,window:o}=e,a=r.useRef();null==a.current&&(a.current=x({window:o,v5Compat:!0}));let i=a.current,[l,s]=r.useState({action:i.action,location:i.location}),c=r.useCallback((e=>{r.startTransition((()=>s(e)))}),[s]);return r.useLayoutEffect((()=>i.listen(c)),[i,c]),r.createElement(ze,{basename:t,children:n,location:l.location,navigationType:l.action,navigator:i})}var vt=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,yt=r.forwardRef((function(e,t){let n,{onClick:o,discover:a="render",prefetch:i="none",relative:s,reloadDocument:c,replace:u,state:d,target:f,to:h,preventScrollReset:m,viewTransition:v}=e,y=l(e,g),{basename:b}=r.useContext(se),x="string"===typeof h&&vt.test(h),k=!1;if("string"===typeof h&&x&&(n=h,mt))try{let e=new URL(window.location.href),t=h.startsWith("//")?new URL(e.protocol+h):new URL(h),n=$(t.pathname,b);t.origin===e.origin&&null!=n?h=n+t.search+t.hash:k=!0}catch(lh){S(!1,'<Link to="'.concat(h,'"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.'))}let C=function(e){let{relative:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};w(pe(),"useHref() may be used only in the context of a <Router> component.");let{basename:n,navigator:o}=r.useContext(se),{hash:a,pathname:i,search:l}=ye(e,{relative:t}),s=i;return"/"!==n&&(s="/"===i?n:Q([n,i])),o.createHref({pathname:s,search:l,hash:a})}(h,{relative:s}),[j,N,P]=function(e,t){let n=r.useContext(at),[o,a]=r.useState(!1),[i,l]=r.useState(!1),{onFocus:s,onBlur:c,onMouseEnter:u,onMouseLeave:d,onTouchStart:p}=t,f=r.useRef(null);r.useEffect((()=>{if("render"===e&&l(!0),"viewport"===e){let e=new IntersectionObserver((e=>{e.forEach((e=>{l(e.isIntersecting)}))}),{threshold:.5});return f.current&&e.observe(f.current),()=>{e.disconnect()}}}),[e]),r.useEffect((()=>{if(o){let e=setTimeout((()=>{l(!0)}),100);return()=>{clearTimeout(e)}}}),[o]);let h=()=>{a(!0)},m=()=>{a(!1),l(!1)};return n?"intent"!==e?[i,f,{}]:[i,f,{onFocus:lt(s,h),onBlur:lt(c,m),onMouseEnter:lt(u,h),onMouseLeave:lt(d,m),onTouchStart:lt(p,h)}]:[!1,f,{}]}(i,y),R=function(e){let{target:t,replace:n,state:o,preventScrollReset:a,relative:i,viewTransition:l}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=ge(),c=fe(),u=ye(e,{relative:i});return r.useCallback((r=>{if(function(e,t){return 0===e.button&&(!t||"_self"===t)&&!function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e)}(r,t)){r.preventDefault();let t=void 0!==n?n:E(c)===E(u);s(e,{replace:t,state:o,preventScrollReset:a,relative:i,viewTransition:l})}}),[c,s,u,n,o,t,e,a,i,l])}(h,{replace:u,state:d,target:f,preventScrollReset:m,relative:s,viewTransition:v});let T=r.createElement("a",p(p(p({},y),P),{},{href:n||C,onClick:k||c?o:function(e){o&&o(e),e.defaultPrevented||R(e)},ref:ht(t,N),target:f,"data-discover":x||"render"!==a?void 0:"true"}));return j&&!x?r.createElement(r.Fragment,null,T,r.createElement(ct,{page:C})):T}));yt.displayName="Link";var bt=r.forwardRef((function(e,t){let{"aria-current":n="page",caseSensitive:o=!1,className:a="",end:i=!1,style:s,to:c,viewTransition:u,children:d}=e,f=l(e,v),h=ye(c,{relative:f.relative}),m=fe(),g=r.useContext(oe),{navigator:y,basename:b}=r.useContext(se),x=null!=g&&function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.useContext(ae);w(null!=n,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:o}=St("useViewTransitionState"),a=ye(e,{relative:t.relative});if(!n.isTransitioning)return!1;let i=$(n.currentLocation.pathname,o)||n.currentLocation.pathname,l=$(n.nextLocation.pathname,o)||n.nextLocation.pathname;return null!=H(a.pathname,l)||null!=H(a.pathname,i)}(h)&&!0===u,S=y.encodeLocation?y.encodeLocation(h).pathname:h.pathname,k=m.pathname,C=g&&g.navigation&&g.navigation.location?g.navigation.location.pathname:null;o||(k=k.toLowerCase(),C=C?C.toLowerCase():null,S=S.toLowerCase()),C&&b&&(C=$(C,b)||C);const E="/"!==S&&S.endsWith("/")?S.length-1:S.length;let j,N=k===S||!i&&k.startsWith(S)&&"/"===k.charAt(E),P=null!=C&&(C===S||!i&&C.startsWith(S)&&"/"===C.charAt(S.length)),R={isActive:N,isPending:P,isTransitioning:x},T=N?n:void 0;j="function"===typeof a?a(R):[a,N?"active":null,P?"pending":null,x?"transitioning":null].filter(Boolean).join(" ");let _="function"===typeof s?s(R):s;return r.createElement(yt,p(p({},f),{},{"aria-current":T,className:j,ref:t,style:_,to:c,viewTransition:u}),"function"===typeof d?d(R):d)}));bt.displayName="NavLink";var xt=r.forwardRef(((e,t)=>{let{discover:n="render",fetcherKey:o,navigate:a,reloadDocument:i,replace:s,state:c,method:u=Fe,action:d,onSubmit:f,relative:h,preventScrollReset:m,viewTransition:g}=e,v=l(e,y),b=Et(),x=function(e){let{relative:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{basename:n}=r.useContext(se),o=r.useContext(ue);w(o,"useFormAction must be used inside a RouteContext");let[a]=o.matches.slice(-1),i=p({},ye(e||".",{relative:t})),l=fe();if(null==e){i.search=l.search;let e=new URLSearchParams(i.search),t=e.getAll("index");if(t.some((e=>""===e))){e.delete("index"),t.filter((e=>e)).forEach((t=>e.append("index",t)));let n=e.toString();i.search=n?"?".concat(n):""}}e&&"."!==e||!a.route.index||(i.search=i.search?i.search.replace(/^\?/,"?index&"):"?index");"/"!==n&&(i.pathname="/"===i.pathname?n:Q([n,i.pathname]));return E(i)}(d,{relative:h}),S="get"===u.toLowerCase()?"get":"post",k="string"===typeof d&&vt.test(d);return r.createElement("form",p(p({ref:t,method:S,action:x,onSubmit:i?f:e=>{if(f&&f(e),e.defaultPrevented)return;e.preventDefault();let t=e.nativeEvent.submitter,n=(null===t||void 0===t?void 0:t.getAttribute("formmethod"))||u;b(t||e.currentTarget,{fetcherKey:o,method:n,navigate:a,replace:s,state:c,relative:h,preventScrollReset:m,viewTransition:g})}},v),{},{"data-discover":k||"render"!==n?void 0:"true"}))}));function wt(e){return"".concat(e," must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.")}function St(e){let t=r.useContext(re);return w(t,wt(e)),t}xt.displayName="Form";var kt=0,Ct=()=>"__".concat(String(++kt),"__");function Et(){let{router:e}=St("useSubmit"),{basename:t}=r.useContext(se),n=Pe("useRouteId");return r.useCallback((async function(r){let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{action:a,method:i,encType:l,formData:s,body:c}=Ue(r,t);if(!1===o.navigate){let t=o.fetcherKey||Ct();await e.fetch(t,n,o.action||a,{preventScrollReset:o.preventScrollReset,formData:s,body:c,formMethod:o.method||i,formEncType:o.encType||l,flushSync:o.flushSync})}else await e.navigate(o.action||a,{preventScrollReset:o.preventScrollReset,formData:s,body:c,formMethod:o.method||i,formEncType:o.encType||l,replace:o.replace,state:o.state,fromRouteId:n,flushSync:o.flushSync,viewTransition:o.viewTransition})}),[e,t,n])}function jt(e){var t,n,r="";if("string"==typeof e||"number"==typeof e)r+=e;else if("object"==typeof e)if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=jt(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}const Nt=function(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=jt(e))&&(r&&(r+=" "),r+=t);return r};function Pt(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0;const r={};for(const o in e){const a=e[o];let i="",l=!0;for(let e=0;e<a.length;e+=1){const r=a[e];r&&(i+=(!0===l?"":" ")+t(r),l=!1,n&&n[r]&&(i+=" "+n[r]))}r[o]=i}return r}function Rt(e){const t=new URL("https://mui.com/production-error/?code=".concat(e));for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return r.forEach((e=>t.searchParams.append("args[]",e))),"Minified MUI error #".concat(e,"; visit ").concat(t," for the full message.")}function Tt(e){if("string"!==typeof e)throw new Error(Rt(7));return e.charAt(0).toUpperCase()+e.slice(1)}const _t=Tt;function At(){return At=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},At.apply(null,arguments)}var Mt=function(){function e(e){var t=this;this._insertTag=function(e){var n;n=0===t.tags.length?t.insertionPoint?t.insertionPoint.nextSibling:t.prepend?t.container.firstChild:t.before:t.tags[t.tags.length-1].nextSibling,t.container.insertBefore(e,n),t.tags.push(e)},this.isSpeedy=void 0===e.speedy||e.speedy,this.tags=[],this.ctr=0,this.nonce=e.nonce,this.key=e.key,this.container=e.container,this.prepend=e.prepend,this.insertionPoint=e.insertionPoint,this.before=null}var t=e.prototype;return t.hydrate=function(e){e.forEach(this._insertTag)},t.insert=function(e){this.ctr%(this.isSpeedy?65e3:1)===0&&this._insertTag(function(e){var t=document.createElement("style");return t.setAttribute("data-emotion",e.key),void 0!==e.nonce&&t.setAttribute("nonce",e.nonce),t.appendChild(document.createTextNode("")),t.setAttribute("data-s",""),t}(this));var t=this.tags[this.tags.length-1];if(this.isSpeedy){var n=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]}(t);try{n.insertRule(e,n.cssRules.length)}catch(lh){}}else t.appendChild(document.createTextNode(e));this.ctr++},t.flush=function(){this.tags.forEach((function(e){var t;return null==(t=e.parentNode)?void 0:t.removeChild(e)})),this.tags=[],this.ctr=0},e}(),zt=Math.abs,It=String.fromCharCode,Ot=Object.assign;function Ft(e){return e.trim()}function Lt(e,t,n){return e.replace(t,n)}function Dt(e,t){return e.indexOf(t)}function Bt(e,t){return 0|e.charCodeAt(t)}function Wt(e,t,n){return e.slice(t,n)}function Ht(e){return e.length}function Ut(e){return e.length}function Vt(e,t){return t.push(e),e}var $t=1,Kt=1,qt=0,Yt=0,Gt=0,Qt="";function Xt(e,t,n,r,o,a,i){return{value:e,root:t,parent:n,type:r,props:o,children:a,line:$t,column:Kt,length:i,return:""}}function Jt(e,t){return Ot(Xt("",null,null,"",null,null,0),e,{length:-e.length},t)}function Zt(){return Gt=Yt>0?Bt(Qt,--Yt):0,Kt--,10===Gt&&(Kt=1,$t--),Gt}function en(){return Gt=Yt<qt?Bt(Qt,Yt++):0,Kt++,10===Gt&&(Kt=1,$t++),Gt}function tn(){return Bt(Qt,Yt)}function nn(){return Yt}function rn(e,t){return Wt(Qt,e,t)}function on(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function an(e){return $t=Kt=1,qt=Ht(Qt=e),Yt=0,[]}function ln(e){return Qt="",e}function sn(e){return Ft(rn(Yt-1,dn(91===e?e+2:40===e?e+1:e)))}function cn(e){for(;(Gt=tn())&&Gt<33;)en();return on(e)>2||on(Gt)>3?"":" "}function un(e,t){for(;--t&&en()&&!(Gt<48||Gt>102||Gt>57&&Gt<65||Gt>70&&Gt<97););return rn(e,nn()+(t<6&&32==tn()&&32==en()))}function dn(e){for(;en();)switch(Gt){case e:return Yt;case 34:case 39:34!==e&&39!==e&&dn(Gt);break;case 40:41===e&&dn(e);break;case 92:en()}return Yt}function pn(e,t){for(;en()&&e+Gt!==57&&(e+Gt!==84||47!==tn()););return"/*"+rn(t,Yt-1)+"*"+It(47===e?e:en())}function fn(e){for(;!on(tn());)en();return rn(e,Yt)}var hn="-ms-",mn="-moz-",gn="-webkit-",vn="comm",yn="rule",bn="decl",xn="@keyframes";function wn(e,t){for(var n="",r=Ut(e),o=0;o<r;o++)n+=t(e[o],o,e,t)||"";return n}function Sn(e,t,n,r){switch(e.type){case"@layer":if(e.children.length)break;case"@import":case bn:return e.return=e.return||e.value;case vn:return"";case xn:return e.return=e.value+"{"+wn(e.children,r)+"}";case yn:e.value=e.props.join(",")}return Ht(n=wn(e.children,r))?e.return=e.value+"{"+n+"}":""}function kn(e){return ln(Cn("",null,null,null,[""],e=an(e),0,[0],e))}function Cn(e,t,n,r,o,a,i,l,s){for(var c=0,u=0,d=i,p=0,f=0,h=0,m=1,g=1,v=1,y=0,b="",x=o,w=a,S=r,k=b;g;)switch(h=y,y=en()){case 40:if(108!=h&&58==Bt(k,d-1)){-1!=Dt(k+=Lt(sn(y),"&","&\f"),"&\f")&&(v=-1);break}case 34:case 39:case 91:k+=sn(y);break;case 9:case 10:case 13:case 32:k+=cn(h);break;case 92:k+=un(nn()-1,7);continue;case 47:switch(tn()){case 42:case 47:Vt(jn(pn(en(),nn()),t,n),s);break;default:k+="/"}break;case 123*m:l[c++]=Ht(k)*v;case 125*m:case 59:case 0:switch(y){case 0:case 125:g=0;case 59+u:-1==v&&(k=Lt(k,/\f/g,"")),f>0&&Ht(k)-d&&Vt(f>32?Nn(k+";",r,n,d-1):Nn(Lt(k," ","")+";",r,n,d-2),s);break;case 59:k+=";";default:if(Vt(S=En(k,t,n,c,u,o,l,b,x=[],w=[],d),a),123===y)if(0===u)Cn(k,t,S,S,x,a,d,l,w);else switch(99===p&&110===Bt(k,3)?100:p){case 100:case 108:case 109:case 115:Cn(e,S,S,r&&Vt(En(e,S,S,0,0,o,l,b,o,x=[],d),w),o,w,d,l,r?x:w);break;default:Cn(k,S,S,S,[""],w,0,l,w)}}c=u=f=0,m=v=1,b=k="",d=i;break;case 58:d=1+Ht(k),f=h;default:if(m<1)if(123==y)--m;else if(125==y&&0==m++&&125==Zt())continue;switch(k+=It(y),y*m){case 38:v=u>0?1:(k+="\f",-1);break;case 44:l[c++]=(Ht(k)-1)*v,v=1;break;case 64:45===tn()&&(k+=sn(en())),p=tn(),u=d=Ht(b=k+=fn(nn())),y++;break;case 45:45===h&&2==Ht(k)&&(m=0)}}return a}function En(e,t,n,r,o,a,i,l,s,c,u){for(var d=o-1,p=0===o?a:[""],f=Ut(p),h=0,m=0,g=0;h<r;++h)for(var v=0,y=Wt(e,d+1,d=zt(m=i[h])),b=e;v<f;++v)(b=Ft(m>0?p[v]+" "+y:Lt(y,/&\f/g,p[v])))&&(s[g++]=b);return Xt(e,t,n,0===o?yn:l,s,c,u)}function jn(e,t,n){return Xt(e,t,n,vn,It(Gt),Wt(e,2,-2),0)}function Nn(e,t,n,r){return Xt(e,t,n,bn,Wt(e,0,r),Wt(e,r+1,-1),r)}var Pn=function(e,t,n){for(var r=0,o=0;r=o,o=tn(),38===r&&12===o&&(t[n]=1),!on(o);)en();return rn(e,Yt)},Rn=function(e,t){return ln(function(e,t){var n=-1,r=44;do{switch(on(r)){case 0:38===r&&12===tn()&&(t[n]=1),e[n]+=Pn(Yt-1,t,n);break;case 2:e[n]+=sn(r);break;case 4:if(44===r){e[++n]=58===tn()?"&\f":"",t[n]=e[n].length;break}default:e[n]+=It(r)}}while(r=en());return e}(an(e),t))},Tn=new WeakMap,_n=function(e){if("rule"===e.type&&e.parent&&!(e.length<1)){for(var t=e.value,n=e.parent,r=e.column===n.column&&e.line===n.line;"rule"!==n.type;)if(!(n=n.parent))return;if((1!==e.props.length||58===t.charCodeAt(0)||Tn.get(n))&&!r){Tn.set(e,!0);for(var o=[],a=Rn(t,o),i=n.props,l=0,s=0;l<a.length;l++)for(var c=0;c<i.length;c++,s++)e.props[s]=o[l]?a[l].replace(/&\f/g,i[c]):i[c]+" "+a[l]}}},An=function(e){if("decl"===e.type){var t=e.value;108===t.charCodeAt(0)&&98===t.charCodeAt(2)&&(e.return="",e.value="")}};function Mn(e,t){switch(function(e,t){return 45^Bt(e,0)?(((t<<2^Bt(e,0))<<2^Bt(e,1))<<2^Bt(e,2))<<2^Bt(e,3):0}(e,t)){case 5103:return gn+"print-"+e+e;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return gn+e+e;case 5349:case 4246:case 4810:case 6968:case 2756:return gn+e+mn+e+hn+e+e;case 6828:case 4268:return gn+e+hn+e+e;case 6165:return gn+e+hn+"flex-"+e+e;case 5187:return gn+e+Lt(e,/(\w+).+(:[^]+)/,gn+"box-$1$2"+hn+"flex-$1$2")+e;case 5443:return gn+e+hn+"flex-item-"+Lt(e,/flex-|-self/,"")+e;case 4675:return gn+e+hn+"flex-line-pack"+Lt(e,/align-content|flex-|-self/,"")+e;case 5548:return gn+e+hn+Lt(e,"shrink","negative")+e;case 5292:return gn+e+hn+Lt(e,"basis","preferred-size")+e;case 6060:return gn+"box-"+Lt(e,"-grow","")+gn+e+hn+Lt(e,"grow","positive")+e;case 4554:return gn+Lt(e,/([^-])(transform)/g,"$1"+gn+"$2")+e;case 6187:return Lt(Lt(Lt(e,/(zoom-|grab)/,gn+"$1"),/(image-set)/,gn+"$1"),e,"")+e;case 5495:case 3959:return Lt(e,/(image-set\([^]*)/,gn+"$1$`$1");case 4968:return Lt(Lt(e,/(.+:)(flex-)?(.*)/,gn+"box-pack:$3"+hn+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+gn+e+e;case 4095:case 3583:case 4068:case 2532:return Lt(e,/(.+)-inline(.+)/,gn+"$1$2")+e;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(Ht(e)-1-t>6)switch(Bt(e,t+1)){case 109:if(45!==Bt(e,t+4))break;case 102:return Lt(e,/(.+:)(.+)-([^]+)/,"$1"+gn+"$2-$3$1"+mn+(108==Bt(e,t+3)?"$3":"$2-$3"))+e;case 115:return~Dt(e,"stretch")?Mn(Lt(e,"stretch","fill-available"),t)+e:e}break;case 4949:if(115!==Bt(e,t+1))break;case 6444:switch(Bt(e,Ht(e)-3-(~Dt(e,"!important")&&10))){case 107:return Lt(e,":",":"+gn)+e;case 101:return Lt(e,/(.+:)([^;!]+)(;|!.+)?/,"$1"+gn+(45===Bt(e,14)?"inline-":"")+"box$3$1"+gn+"$2$3$1"+hn+"$2box$3")+e}break;case 5936:switch(Bt(e,t+11)){case 114:return gn+e+hn+Lt(e,/[svh]\w+-[tblr]{2}/,"tb")+e;case 108:return gn+e+hn+Lt(e,/[svh]\w+-[tblr]{2}/,"tb-rl")+e;case 45:return gn+e+hn+Lt(e,/[svh]\w+-[tblr]{2}/,"lr")+e}return gn+e+hn+e+e}return e}var zn=[function(e,t,n,r){if(e.length>-1&&!e.return)switch(e.type){case bn:e.return=Mn(e.value,e.length);break;case xn:return wn([Jt(e,{value:Lt(e.value,"@","@"+gn)})],r);case yn:if(e.length)return function(e,t){return e.map(t).join("")}(e.props,(function(t){switch(function(e,t){return(e=t.exec(e))?e[0]:e}(t,/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":return wn([Jt(e,{props:[Lt(t,/:(read-\w+)/,":-moz-$1")]})],r);case"::placeholder":return wn([Jt(e,{props:[Lt(t,/:(plac\w+)/,":"+gn+"input-$1")]}),Jt(e,{props:[Lt(t,/:(plac\w+)/,":-moz-$1")]}),Jt(e,{props:[Lt(t,/:(plac\w+)/,hn+"input-$1")]})],r)}return""}))}}],In=function(e){var t=e.key;if("css"===t){var n=document.querySelectorAll("style[data-emotion]:not([data-s])");Array.prototype.forEach.call(n,(function(e){-1!==e.getAttribute("data-emotion").indexOf(" ")&&(document.head.appendChild(e),e.setAttribute("data-s",""))}))}var r,o,a=e.stylisPlugins||zn,i={},l=[];r=e.container||document.head,Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="'+t+' "]'),(function(e){for(var t=e.getAttribute("data-emotion").split(" "),n=1;n<t.length;n++)i[t[n]]=!0;l.push(e)}));var s,c,u=[Sn,(c=function(e){s.insert(e)},function(e){e.root||(e=e.return)&&c(e)})],d=function(e){var t=Ut(e);return function(n,r,o,a){for(var i="",l=0;l<t;l++)i+=e[l](n,r,o,a)||"";return i}}([_n,An].concat(a,u));o=function(e,t,n,r){s=n,function(e){wn(kn(e),d)}(e?e+"{"+t.styles+"}":t.styles),r&&(p.inserted[t.name]=!0)};var p={key:t,sheet:new Mt({key:t,container:r,nonce:e.nonce,speedy:e.speedy,prepend:e.prepend,insertionPoint:e.insertionPoint}),nonce:e.nonce,inserted:i,registered:{},insert:o};return p.sheet.hydrate(l),p};function On(e,t,n){var r="";return n.split(" ").forEach((function(n){void 0!==e[n]?t.push(e[n]+";"):n&&(r+=n+" ")})),r}var Fn=function(e,t,n){var r=e.key+"-"+t.name;!1===n&&void 0===e.registered[r]&&(e.registered[r]=t.styles)},Ln=function(e,t,n){Fn(e,t,n);var r=e.key+"-"+t.name;if(void 0===e.inserted[t.name]){var o=t;do{e.insert(t===o?"."+r:"",o,e.sheet,!0),o=o.next}while(void 0!==o)}};var Dn={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,scale:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1};function Bn(e){var t=Object.create(null);return function(n){return void 0===t[n]&&(t[n]=e(n)),t[n]}}var Wn=/[A-Z]|^ms/g,Hn=/_EMO_([^_]+?)_([^]*?)_EMO_/g,Un=function(e){return 45===e.charCodeAt(1)},Vn=function(e){return null!=e&&"boolean"!==typeof e},$n=Bn((function(e){return Un(e)?e:e.replace(Wn,"-$&").toLowerCase()})),Kn=function(e,t){switch(e){case"animation":case"animationName":if("string"===typeof t)return t.replace(Hn,(function(e,t,n){return Yn={name:t,styles:n,next:Yn},t}))}return 1===Dn[e]||Un(e)||"number"!==typeof t||0===t?t:t+"px"};function qn(e,t,n){if(null==n)return"";var r=n;if(void 0!==r.__emotion_styles)return r;switch(typeof n){case"boolean":return"";case"object":var o=n;if(1===o.anim)return Yn={name:o.name,styles:o.styles,next:Yn},o.name;var a=n;if(void 0!==a.styles){var i=a.next;if(void 0!==i)for(;void 0!==i;)Yn={name:i.name,styles:i.styles,next:Yn},i=i.next;return a.styles+";"}return function(e,t,n){var r="";if(Array.isArray(n))for(var o=0;o<n.length;o++)r+=qn(e,t,n[o])+";";else for(var a in n){var i=n[a];if("object"!==typeof i){var l=i;null!=t&&void 0!==t[l]?r+=a+"{"+t[l]+"}":Vn(l)&&(r+=$n(a)+":"+Kn(a,l)+";")}else if(!Array.isArray(i)||"string"!==typeof i[0]||null!=t&&void 0!==t[i[0]]){var s=qn(e,t,i);switch(a){case"animation":case"animationName":r+=$n(a)+":"+s+";";break;default:r+=a+"{"+s+"}"}}else for(var c=0;c<i.length;c++)Vn(i[c])&&(r+=$n(a)+":"+Kn(a,i[c])+";")}return r}(e,t,n);case"function":if(void 0!==e){var l=Yn,s=n(e);return Yn=l,qn(e,t,s)}}var c=n;if(null==t)return c;var u=t[c];return void 0!==u?u:c}var Yn,Gn=/label:\s*([^\s;{]+)\s*(;|$)/g;function Qn(e,t,n){if(1===e.length&&"object"===typeof e[0]&&null!==e[0]&&void 0!==e[0].styles)return e[0];var r=!0,o="";Yn=void 0;var a=e[0];null==a||void 0===a.raw?(r=!1,o+=qn(n,t,a)):o+=a[0];for(var i=1;i<e.length;i++){if(o+=qn(n,t,e[i]),r)o+=a[i]}Gn.lastIndex=0;for(var l,s="";null!==(l=Gn.exec(o));)s+="-"+l[1];var c=function(e){for(var t,n=0,r=0,o=e.length;o>=4;++r,o-=4)t=***********(65535&(t=255&e.charCodeAt(r)|(255&e.charCodeAt(++r))<<8|(255&e.charCodeAt(++r))<<16|(255&e.charCodeAt(++r))<<24))+(59797*(t>>>16)<<16),n=***********(65535&(t^=t>>>24))+(59797*(t>>>16)<<16)^***********(65535&n)+(59797*(n>>>16)<<16);switch(o){case 3:n^=(255&e.charCodeAt(r+2))<<16;case 2:n^=(255&e.charCodeAt(r+1))<<8;case 1:n=***********(65535&(n^=255&e.charCodeAt(r)))+(59797*(n>>>16)<<16)}return(((n=***********(65535&(n^=n>>>13))+(59797*(n>>>16)<<16))^n>>>15)>>>0).toString(36)}(o)+s;return{name:c,styles:o,next:Yn}}var Xn=!!o.useInsertionEffect&&o.useInsertionEffect,Jn=Xn||function(e){return e()},Zn=Xn||r.useLayoutEffect,er=r.createContext("undefined"!==typeof HTMLElement?In({key:"css"}):null),tr=(er.Provider,function(e){return(0,r.forwardRef)((function(t,n){var o=(0,r.useContext)(er);return e(t,o,n)}))}),nr=r.createContext({});var rr={}.hasOwnProperty,or="__EMOTION_TYPE_PLEASE_DO_NOT_USE__",ar=function(e){var t=e.cache,n=e.serialized,r=e.isStringTag;return Fn(t,n,r),Jn((function(){return Ln(t,n,r)})),null},ir=tr((function(e,t,n){var o=e.css;"string"===typeof o&&void 0!==t.registered[o]&&(o=t.registered[o]);var a=e[or],i=[o],l="";"string"===typeof e.className?l=On(t.registered,i,e.className):null!=e.className&&(l=e.className+" ");var s=Qn(i,void 0,r.useContext(nr));l+=t.key+"-"+s.name;var c={};for(var u in e)rr.call(e,u)&&"css"!==u&&u!==or&&(c[u]=e[u]);return c.className=l,n&&(c.ref=n),r.createElement(r.Fragment,null,r.createElement(ar,{cache:t,serialized:s,isStringTag:"string"===typeof a}),r.createElement(a,c))})),lr=/^((children|dangerouslySetInnerHTML|key|ref|autoFocus|defaultValue|defaultChecked|innerHTML|suppressContentEditableWarning|suppressHydrationWarning|valueLink|abbr|accept|acceptCharset|accessKey|action|allow|allowUserMedia|allowPaymentRequest|allowFullScreen|allowTransparency|alt|async|autoComplete|autoPlay|capture|cellPadding|cellSpacing|challenge|charSet|checked|cite|classID|className|cols|colSpan|content|contentEditable|contextMenu|controls|controlsList|coords|crossOrigin|data|dateTime|decoding|default|defer|dir|disabled|disablePictureInPicture|disableRemotePlayback|download|draggable|encType|enterKeyHint|fetchpriority|fetchPriority|form|formAction|formEncType|formMethod|formNoValidate|formTarget|frameBorder|headers|height|hidden|high|href|hrefLang|htmlFor|httpEquiv|id|inputMode|integrity|is|keyParams|keyType|kind|label|lang|list|loading|loop|low|marginHeight|marginWidth|max|maxLength|media|mediaGroup|method|min|minLength|multiple|muted|name|nonce|noValidate|open|optimum|pattern|placeholder|playsInline|poster|preload|profile|radioGroup|readOnly|referrerPolicy|rel|required|reversed|role|rows|rowSpan|sandbox|scope|scoped|scrolling|seamless|selected|shape|size|sizes|slot|span|spellCheck|src|srcDoc|srcLang|srcSet|start|step|style|summary|tabIndex|target|title|translate|type|useMap|value|width|wmode|wrap|about|datatype|inlist|prefix|property|resource|typeof|vocab|autoCapitalize|autoCorrect|autoSave|color|incremental|fallback|inert|itemProp|itemScope|itemType|itemID|itemRef|on|option|results|security|unselectable|accentHeight|accumulate|additive|alignmentBaseline|allowReorder|alphabetic|amplitude|arabicForm|ascent|attributeName|attributeType|autoReverse|azimuth|baseFrequency|baselineShift|baseProfile|bbox|begin|bias|by|calcMode|capHeight|clip|clipPathUnits|clipPath|clipRule|colorInterpolation|colorInterpolationFilters|colorProfile|colorRendering|contentScriptType|contentStyleType|cursor|cx|cy|d|decelerate|descent|diffuseConstant|direction|display|divisor|dominantBaseline|dur|dx|dy|edgeMode|elevation|enableBackground|end|exponent|externalResourcesRequired|fill|fillOpacity|fillRule|filter|filterRes|filterUnits|floodColor|floodOpacity|focusable|fontFamily|fontSize|fontSizeAdjust|fontStretch|fontStyle|fontVariant|fontWeight|format|from|fr|fx|fy|g1|g2|glyphName|glyphOrientationHorizontal|glyphOrientationVertical|glyphRef|gradientTransform|gradientUnits|hanging|horizAdvX|horizOriginX|ideographic|imageRendering|in|in2|intercept|k|k1|k2|k3|k4|kernelMatrix|kernelUnitLength|kerning|keyPoints|keySplines|keyTimes|lengthAdjust|letterSpacing|lightingColor|limitingConeAngle|local|markerEnd|markerMid|markerStart|markerHeight|markerUnits|markerWidth|mask|maskContentUnits|maskUnits|mathematical|mode|numOctaves|offset|opacity|operator|order|orient|orientation|origin|overflow|overlinePosition|overlineThickness|panose1|paintOrder|pathLength|patternContentUnits|patternTransform|patternUnits|pointerEvents|points|pointsAtX|pointsAtY|pointsAtZ|preserveAlpha|preserveAspectRatio|primitiveUnits|r|radius|refX|refY|renderingIntent|repeatCount|repeatDur|requiredExtensions|requiredFeatures|restart|result|rotate|rx|ry|scale|seed|shapeRendering|slope|spacing|specularConstant|specularExponent|speed|spreadMethod|startOffset|stdDeviation|stemh|stemv|stitchTiles|stopColor|stopOpacity|strikethroughPosition|strikethroughThickness|string|stroke|strokeDasharray|strokeDashoffset|strokeLinecap|strokeLinejoin|strokeMiterlimit|strokeOpacity|strokeWidth|surfaceScale|systemLanguage|tableValues|targetX|targetY|textAnchor|textDecoration|textRendering|textLength|to|transform|u1|u2|underlinePosition|underlineThickness|unicode|unicodeBidi|unicodeRange|unitsPerEm|vAlphabetic|vHanging|vIdeographic|vMathematical|values|vectorEffect|version|vertAdvY|vertOriginX|vertOriginY|viewBox|viewTarget|visibility|widths|wordSpacing|writingMode|x|xHeight|x1|x2|xChannelSelector|xlinkActuate|xlinkArcrole|xlinkHref|xlinkRole|xlinkShow|xlinkTitle|xlinkType|xmlBase|xmlns|xmlnsXlink|xmlLang|xmlSpace|y|y1|y2|yChannelSelector|z|zoomAndPan|for|class|autofocus)|(([Dd][Aa][Tt][Aa]|[Aa][Rr][Ii][Aa]|x)-.*))$/,sr=Bn((function(e){return lr.test(e)||111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&e.charCodeAt(2)<91})),cr=function(e){return"theme"!==e},ur=function(e){return"string"===typeof e&&e.charCodeAt(0)>96?sr:cr},dr=function(e,t,n){var r;if(t){var o=t.shouldForwardProp;r=e.__emotion_forwardProp&&o?function(t){return e.__emotion_forwardProp(t)&&o(t)}:o}return"function"!==typeof r&&n&&(r=e.__emotion_forwardProp),r},pr=function(e){var t=e.cache,n=e.serialized,r=e.isStringTag;return Fn(t,n,r),Jn((function(){return Ln(t,n,r)})),null},fr=function e(t,n){var o,a,i=t.__emotion_real===t,l=i&&t.__emotion_base||t;void 0!==n&&(o=n.label,a=n.target);var s=dr(t,n,i),c=s||ur(l),u=!c("as");return function(){var d=arguments,p=i&&void 0!==t.__emotion_styles?t.__emotion_styles.slice(0):[];if(void 0!==o&&p.push("label:"+o+";"),null==d[0]||void 0===d[0].raw)p.push.apply(p,d);else{var f=d[0];p.push(f[0]);for(var h=d.length,m=1;m<h;m++)p.push(d[m],f[m])}var g=tr((function(e,t,n){var o=u&&e.as||l,i="",d=[],f=e;if(null==e.theme){for(var h in f={},e)f[h]=e[h];f.theme=r.useContext(nr)}"string"===typeof e.className?i=On(t.registered,d,e.className):null!=e.className&&(i=e.className+" ");var m=Qn(p.concat(d),t.registered,f);i+=t.key+"-"+m.name,void 0!==a&&(i+=" "+a);var g=u&&void 0===s?ur(o):c,v={};for(var y in e)u&&"as"===y||g(y)&&(v[y]=e[y]);return v.className=i,n&&(v.ref=n),r.createElement(r.Fragment,null,r.createElement(pr,{cache:t,serialized:m,isStringTag:"string"===typeof o}),r.createElement(o,v))}));return g.displayName=void 0!==o?o:"Styled("+("string"===typeof l?l:l.displayName||l.name||"Component")+")",g.defaultProps=t.defaultProps,g.__emotion_real=g,g.__emotion_base=l,g.__emotion_styles=p,g.__emotion_forwardProp=s,Object.defineProperty(g,"toString",{value:function(){return"."+a}}),g.withComponent=function(t,r){return e(t,At({},n,r,{shouldForwardProp:dr(g,r,!0)})).apply(void 0,p)},g}}.bind(null);["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","marquee","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","title","tr","track","u","ul","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","tspan"].forEach((function(e){fr[e]=fr(e)}));const hr=[];function mr(e){return hr[0]=e,Qn(hr)}var gr=n(528);function vr(e){if("object"!==typeof e||null===e)return!1;const t=Object.getPrototypeOf(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}function yr(e){if(r.isValidElement(e)||(0,gr.Hy)(e)||!vr(e))return e;const t={};return Object.keys(e).forEach((n=>{t[n]=yr(e[n])})),t}function br(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{clone:!0};const o=n.clone?p({},e):e;return vr(e)&&vr(t)&&Object.keys(t).forEach((a=>{r.isValidElement(t[a])||(0,gr.Hy)(t[a])?o[a]=t[a]:vr(t[a])&&Object.prototype.hasOwnProperty.call(e,a)&&vr(e[a])?o[a]=br(e[a],t[a],n):n.clone?o[a]=vr(t[a])?yr(t[a]):t[a]:o[a]=t[a]})),o}const xr=["values","unit","step"];function wr(e){const{values:t={xs:0,sm:600,md:900,lg:1200,xl:1536},unit:n="px",step:r=5}=e,o=l(e,xr),a=(e=>{const t=Object.keys(e).map((t=>({key:t,val:e[t]})))||[];return t.sort(((e,t)=>e.val-t.val)),t.reduce(((e,t)=>p(p({},e),{},{[t.key]:t.val})),{})})(t),i=Object.keys(a);function s(e){const r="number"===typeof t[e]?t[e]:e;return"@media (min-width:".concat(r).concat(n,")")}function c(e){const o="number"===typeof t[e]?t[e]:e;return"@media (max-width:".concat(o-r/100).concat(n,")")}function u(e,o){const a=i.indexOf(o);return"@media (min-width:".concat("number"===typeof t[e]?t[e]:e).concat(n,") and ")+"(max-width:".concat((-1!==a&&"number"===typeof t[i[a]]?t[i[a]]:o)-r/100).concat(n,")")}return p({keys:i,values:a,up:s,down:c,between:u,only:function(e){return i.indexOf(e)+1<i.length?u(e,i[i.indexOf(e)+1]):s(e)},not:function(e){const t=i.indexOf(e);return 0===t?s(i[1]):t===i.length-1?c(i[t]):u(e,i[i.indexOf(e)+1]).replace("@media","@media not all and")},unit:n},o)}const Sr={borderRadius:4},kr={xs:0,sm:600,md:900,lg:1200,xl:1536},Cr={keys:["xs","sm","md","lg","xl"],up:e=>"@media (min-width:".concat(kr[e],"px)")},Er={containerQueries:e=>({up:t=>{let n="number"===typeof t?t:kr[t]||t;return"number"===typeof n&&(n="".concat(n,"px")),e?"@container ".concat(e," (min-width:").concat(n,")"):"@container (min-width:".concat(n,")")}})};function jr(e,t,n){const r=e.theme||{};if(Array.isArray(t)){const e=r.breakpoints||Cr;return t.reduce(((r,o,a)=>(r[e.up(e.keys[a])]=n(t[a]),r)),{})}if("object"===typeof t){const e=r.breakpoints||Cr;return Object.keys(t).reduce(((o,a)=>{if(function(e,t){return"@"===t||t.startsWith("@")&&(e.some((e=>t.startsWith("@".concat(e))))||!!t.match(/^@\d/))}(e.keys,a)){const e=function(e,t){const n=t.match(/^@([^/]+)?\/?(.+)?$/);if(!n)return null;const[,r,o]=n,a=Number.isNaN(+r)?r||0:+r;return e.containerQueries(o).up(a)}(r.containerQueries?r:Er,a);e&&(o[e]=n(t[a],a))}else if(Object.keys(e.values||kr).includes(a)){o[e.up(a)]=n(t[a],a)}else{const e=a;o[e]=t[e]}return o}),{})}return n(t)}function Nr(){var e;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return(null===(e=t.keys)||void 0===e?void 0:e.reduce(((e,n)=>(e[t.up(n)]={},e)),{}))||{}}function Pr(e,t){return e.reduce(((e,t)=>{const n=e[t];return(!n||0===Object.keys(n).length)&&delete e[t],e}),t)}function Rr(e,t){let n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(!t||"string"!==typeof t)return null;if(e&&e.vars&&n){const n="vars.".concat(t).split(".").reduce(((e,t)=>e&&e[t]?e[t]:null),e);if(null!=n)return n}return t.split(".").reduce(((e,t)=>e&&null!=e[t]?e[t]:null),e)}function Tr(e,t,n){let r,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:n;return r="function"===typeof e?e(n):Array.isArray(e)?e[n]||o:Rr(e,n)||o,t&&(r=t(r,o,e)),r}const _r=function(e){const{prop:t,cssProperty:n=e.prop,themeKey:r,transform:o}=e,a=e=>{if(null==e[t])return null;const a=e[t],i=Rr(e.theme,r)||{};return jr(e,a,(e=>{let r=Tr(i,o,e);return e===r&&"string"===typeof e&&(r=Tr(i,o,"".concat(t).concat("default"===e?"":Tt(e)),e)),!1===n?r:{[n]:r}}))};return a.propTypes={},a.filterProps=[t],a};const Ar=function(e,t){return t?br(e,t,{clone:!1}):e};const Mr={m:"margin",p:"padding"},zr={t:"Top",r:"Right",b:"Bottom",l:"Left",x:["Left","Right"],y:["Top","Bottom"]},Ir={marginX:"mx",marginY:"my",paddingX:"px",paddingY:"py"},Or=function(e){const t={};return n=>(void 0===t[n]&&(t[n]=e(n)),t[n])}((e=>{if(e.length>2){if(!Ir[e])return[e];e=Ir[e]}const[t,n]=e.split(""),r=Mr[t],o=zr[n]||"";return Array.isArray(o)?o.map((e=>r+e)):[r+o]})),Fr=["m","mt","mr","mb","ml","mx","my","margin","marginTop","marginRight","marginBottom","marginLeft","marginX","marginY","marginInline","marginInlineStart","marginInlineEnd","marginBlock","marginBlockStart","marginBlockEnd"],Lr=["p","pt","pr","pb","pl","px","py","padding","paddingTop","paddingRight","paddingBottom","paddingLeft","paddingX","paddingY","paddingInline","paddingInlineStart","paddingInlineEnd","paddingBlock","paddingBlockStart","paddingBlockEnd"],Dr=[...Fr,...Lr];function Br(e,t,n,r){var o;const a=null!==(o=Rr(e,t,!0))&&void 0!==o?o:n;return"number"===typeof a||"string"===typeof a?e=>"string"===typeof e?e:"string"===typeof a?a.startsWith("var(")&&0===e?0:a.startsWith("var(")&&1===e?a:"calc(".concat(e," * ").concat(a,")"):a*e:Array.isArray(a)?e=>{if("string"===typeof e)return e;const t=Math.abs(e);const n=a[t];return e>=0?n:"number"===typeof n?-n:"string"===typeof n&&n.startsWith("var(")?"calc(-1 * ".concat(n,")"):"-".concat(n)}:"function"===typeof a?a:()=>{}}function Wr(e){return Br(e,"spacing",8)}function Hr(e,t){return"string"===typeof t||null==t?t:e(t)}function Ur(e,t,n,r){if(!t.includes(n))return null;const o=function(e,t){return n=>e.reduce(((e,r)=>(e[r]=Hr(t,n),e)),{})}(Or(n),r);return jr(e,e[n],o)}function Vr(e,t){const n=Wr(e.theme);return Object.keys(e).map((r=>Ur(e,t,r,n))).reduce(Ar,{})}function $r(e){return Vr(e,Fr)}function Kr(e){return Vr(e,Lr)}function qr(e){return Vr(e,Dr)}$r.propTypes={},$r.filterProps=Fr,Kr.propTypes={},Kr.filterProps=Lr,qr.propTypes={},qr.filterProps=Dr;function Yr(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:8,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Wr({spacing:e});if(e.mui)return e;const n=function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return(0===n.length?[1]:n).map((e=>{const n=t(e);return"number"===typeof n?"".concat(n,"px"):n})).join(" ")};return n.mui=!0,n}const Gr=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];const r=t.reduce(((e,t)=>(t.filterProps.forEach((n=>{e[n]=t})),e)),{}),o=e=>Object.keys(e).reduce(((t,n)=>r[n]?Ar(t,r[n](e)):t),{});return o.propTypes={},o.filterProps=t.reduce(((e,t)=>e.concat(t.filterProps)),[]),o};function Qr(e){return"number"!==typeof e?e:"".concat(e,"px solid")}function Xr(e,t){return _r({prop:e,themeKey:"borders",transform:t})}const Jr=Xr("border",Qr),Zr=Xr("borderTop",Qr),eo=Xr("borderRight",Qr),to=Xr("borderBottom",Qr),no=Xr("borderLeft",Qr),ro=Xr("borderColor"),oo=Xr("borderTopColor"),ao=Xr("borderRightColor"),io=Xr("borderBottomColor"),lo=Xr("borderLeftColor"),so=Xr("outline",Qr),co=Xr("outlineColor"),uo=e=>{if(void 0!==e.borderRadius&&null!==e.borderRadius){const t=Br(e.theme,"shape.borderRadius",4),n=e=>({borderRadius:Hr(t,e)});return jr(e,e.borderRadius,n)}return null};uo.propTypes={},uo.filterProps=["borderRadius"];Gr(Jr,Zr,eo,to,no,ro,oo,ao,io,lo,uo,so,co);const po=e=>{if(void 0!==e.gap&&null!==e.gap){const t=Br(e.theme,"spacing",8),n=e=>({gap:Hr(t,e)});return jr(e,e.gap,n)}return null};po.propTypes={},po.filterProps=["gap"];const fo=e=>{if(void 0!==e.columnGap&&null!==e.columnGap){const t=Br(e.theme,"spacing",8),n=e=>({columnGap:Hr(t,e)});return jr(e,e.columnGap,n)}return null};fo.propTypes={},fo.filterProps=["columnGap"];const ho=e=>{if(void 0!==e.rowGap&&null!==e.rowGap){const t=Br(e.theme,"spacing",8),n=e=>({rowGap:Hr(t,e)});return jr(e,e.rowGap,n)}return null};ho.propTypes={},ho.filterProps=["rowGap"];Gr(po,fo,ho,_r({prop:"gridColumn"}),_r({prop:"gridRow"}),_r({prop:"gridAutoFlow"}),_r({prop:"gridAutoColumns"}),_r({prop:"gridAutoRows"}),_r({prop:"gridTemplateColumns"}),_r({prop:"gridTemplateRows"}),_r({prop:"gridTemplateAreas"}),_r({prop:"gridArea"}));function mo(e,t){return"grey"===t?t:e}Gr(_r({prop:"color",themeKey:"palette",transform:mo}),_r({prop:"bgcolor",cssProperty:"backgroundColor",themeKey:"palette",transform:mo}),_r({prop:"backgroundColor",themeKey:"palette",transform:mo}));function go(e){return e<=1&&0!==e?"".concat(100*e,"%"):e}const vo=_r({prop:"width",transform:go}),yo=e=>{if(void 0!==e.maxWidth&&null!==e.maxWidth){const t=t=>{var n,r;const o=(null===(n=e.theme)||void 0===n||null===(n=n.breakpoints)||void 0===n||null===(n=n.values)||void 0===n?void 0:n[t])||kr[t];return o?"px"!==(null===(r=e.theme)||void 0===r||null===(r=r.breakpoints)||void 0===r?void 0:r.unit)?{maxWidth:"".concat(o).concat(e.theme.breakpoints.unit)}:{maxWidth:o}:{maxWidth:go(t)}};return jr(e,e.maxWidth,t)}return null};yo.filterProps=["maxWidth"];const bo=_r({prop:"minWidth",transform:go}),xo=_r({prop:"height",transform:go}),wo=_r({prop:"maxHeight",transform:go}),So=_r({prop:"minHeight",transform:go}),ko=(_r({prop:"size",cssProperty:"width",transform:go}),_r({prop:"size",cssProperty:"height",transform:go}),Gr(vo,yo,bo,xo,wo,So,_r({prop:"boxSizing"})),{border:{themeKey:"borders",transform:Qr},borderTop:{themeKey:"borders",transform:Qr},borderRight:{themeKey:"borders",transform:Qr},borderBottom:{themeKey:"borders",transform:Qr},borderLeft:{themeKey:"borders",transform:Qr},borderColor:{themeKey:"palette"},borderTopColor:{themeKey:"palette"},borderRightColor:{themeKey:"palette"},borderBottomColor:{themeKey:"palette"},borderLeftColor:{themeKey:"palette"},outline:{themeKey:"borders",transform:Qr},outlineColor:{themeKey:"palette"},borderRadius:{themeKey:"shape.borderRadius",style:uo},color:{themeKey:"palette",transform:mo},bgcolor:{themeKey:"palette",cssProperty:"backgroundColor",transform:mo},backgroundColor:{themeKey:"palette",transform:mo},p:{style:Kr},pt:{style:Kr},pr:{style:Kr},pb:{style:Kr},pl:{style:Kr},px:{style:Kr},py:{style:Kr},padding:{style:Kr},paddingTop:{style:Kr},paddingRight:{style:Kr},paddingBottom:{style:Kr},paddingLeft:{style:Kr},paddingX:{style:Kr},paddingY:{style:Kr},paddingInline:{style:Kr},paddingInlineStart:{style:Kr},paddingInlineEnd:{style:Kr},paddingBlock:{style:Kr},paddingBlockStart:{style:Kr},paddingBlockEnd:{style:Kr},m:{style:$r},mt:{style:$r},mr:{style:$r},mb:{style:$r},ml:{style:$r},mx:{style:$r},my:{style:$r},margin:{style:$r},marginTop:{style:$r},marginRight:{style:$r},marginBottom:{style:$r},marginLeft:{style:$r},marginX:{style:$r},marginY:{style:$r},marginInline:{style:$r},marginInlineStart:{style:$r},marginInlineEnd:{style:$r},marginBlock:{style:$r},marginBlockStart:{style:$r},marginBlockEnd:{style:$r},displayPrint:{cssProperty:!1,transform:e=>({"@media print":{display:e}})},display:{},overflow:{},textOverflow:{},visibility:{},whiteSpace:{},flexBasis:{},flexDirection:{},flexWrap:{},justifyContent:{},alignItems:{},alignContent:{},order:{},flex:{},flexGrow:{},flexShrink:{},alignSelf:{},justifyItems:{},justifySelf:{},gap:{style:po},rowGap:{style:ho},columnGap:{style:fo},gridColumn:{},gridRow:{},gridAutoFlow:{},gridAutoColumns:{},gridAutoRows:{},gridTemplateColumns:{},gridTemplateRows:{},gridTemplateAreas:{},gridArea:{},position:{},zIndex:{themeKey:"zIndex"},top:{},right:{},bottom:{},left:{},boxShadow:{themeKey:"shadows"},width:{transform:go},maxWidth:{style:yo},minWidth:{transform:go},height:{transform:go},maxHeight:{transform:go},minHeight:{transform:go},boxSizing:{},font:{themeKey:"font"},fontFamily:{themeKey:"typography"},fontSize:{themeKey:"typography"},fontStyle:{themeKey:"typography"},fontWeight:{themeKey:"typography"},letterSpacing:{},textTransform:{},lineHeight:{},textAlign:{},typography:{cssProperty:!1,themeKey:"typography"}});const Co=function(){function e(e,t,n,r){const o={[e]:t,theme:n},a=r[e];if(!a)return{[e]:t};const{cssProperty:i=e,themeKey:l,transform:s,style:c}=a;if(null==t)return null;if("typography"===l&&"inherit"===t)return{[e]:t};const u=Rr(n,l)||{};if(c)return c(o);return jr(o,t,(t=>{let n=Tr(u,s,t);return t===n&&"string"===typeof t&&(n=Tr(u,s,"".concat(e).concat("default"===t?"":Tt(t)),t)),!1===i?n:{[i]:n}}))}return function t(n){var r;const{sx:o,theme:a={}}=n||{};if(!o)return null;const i=null!==(r=a.unstable_sxConfig)&&void 0!==r?r:ko;function l(n){let r=n;if("function"===typeof n)r=n(a);else if("object"!==typeof n)return n;if(!r)return null;const o=Nr(a.breakpoints),l=Object.keys(o);let s=o;return Object.keys(r).forEach((n=>{const o=function(e,t){return"function"===typeof e?e(t):e}(r[n],a);if(null!==o&&void 0!==o)if("object"===typeof o)if(i[n])s=Ar(s,e(n,o,a,i));else{const e=jr({theme:a},o,(e=>({[n]:e})));!function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];const r=t.reduce(((e,t)=>e.concat(Object.keys(t))),[]),o=new Set(r);return t.every((e=>o.size===Object.keys(e).length))}(e,o)?s=Ar(s,e):s[n]=t({sx:o,theme:a})}else s=Ar(s,e(n,o,a,i))})),function(e,t){if(!e.containerQueries)return t;const n=Object.keys(t).filter((e=>e.startsWith("@container"))).sort(((e,t)=>{var n,r;const o=/min-width:\s*([0-9.]+)/;return+((null===(n=e.match(o))||void 0===n?void 0:n[1])||0)-+((null===(r=t.match(o))||void 0===r?void 0:r[1])||0)}));return n.length?n.reduce(((e,n)=>{const r=t[n];return delete e[n],e[n]=r,e}),p({},t)):t}(a,Pr(l,s))}return Array.isArray(o)?o.map(l):l(o)}}();Co.filterProps=["sx"];const Eo=Co;function jo(e,t){const n=this;if(n.vars){var r;if(null===(r=n.colorSchemes)||void 0===r||!r[e]||"function"!==typeof n.getColorSchemeSelector)return{};let o=n.getColorSchemeSelector(e);return"&"===o?t:((o.includes("data-")||o.includes("."))&&(o="*:where(".concat(o.replace(/\s*&$/,""),") &")),{[o]:t})}return n.palette.mode===e?t:{}}const No=["breakpoints","palette","spacing","shape"];const Po=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{breakpoints:t={},palette:n={},spacing:r,shape:o={}}=e,a=l(e,No),i=wr(t),s=Yr(r);let c=br({breakpoints:i,direction:"ltr",components:{},palette:p({mode:"light"},n),spacing:s,shape:p(p({},Sr),o)},a);c=function(e){const t=(e,t)=>e.replace("@media",t?"@container ".concat(t):"@container");function n(n,r){n.up=function(){return t(e.breakpoints.up(...arguments),r)},n.down=function(){return t(e.breakpoints.down(...arguments),r)},n.between=function(){return t(e.breakpoints.between(...arguments),r)},n.only=function(){return t(e.breakpoints.only(...arguments),r)},n.not=function(){const n=t(e.breakpoints.not(...arguments),r);return n.includes("not all and")?n.replace("not all and ","").replace("min-width:","width<").replace("max-width:","width>").replace("and","or"):n}}const r={},o=e=>(n(r,e),r);return n(o),p(p({},e),{},{containerQueries:o})}(c),c.applyStyles=jo;for(var u=arguments.length,d=new Array(u>1?u-1:0),f=1;f<u;f++)d[f-1]=arguments[f];return c=d.reduce(((e,t)=>br(e,t)),c),c.unstable_sxConfig=p(p({},ko),null===a||void 0===a?void 0:a.unstable_sxConfig),c.unstable_sx=function(e){return Eo({sx:e,theme:this})},c},Ro=["variants"];function To(e){const{variants:t}=e,n=l(e,Ro),r={variants:t,style:mr(n),isProcessed:!0};return r.style===n||t&&t.forEach((e=>{"function"!==typeof e.style&&(e.style=mr(e.style))})),r}const _o=["variants"],Ao=["name","slot","skipVariantsResolver","skipSx","overridesResolver"],Mo=Po();function zo(e){return"ownerState"!==e&&"theme"!==e&&"sx"!==e&&"as"!==e}function Io(e){return e?(t,n)=>n[e]:null}function Oo(e,t){const n="function"===typeof t?t(e):t;if(Array.isArray(n))return n.flatMap((t=>Oo(e,t)));if(Array.isArray(null===n||void 0===n?void 0:n.variants)){let t;if(n.isProcessed)t=n.style;else{const{variants:e}=n;t=l(n,_o)}return Fo(e,n.variants,[t])}return null!==n&&void 0!==n&&n.isProcessed?n.style:n}function Fo(e,t){let n,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];e:for(let a=0;a<t.length;a+=1){const i=t[a];if("function"===typeof i.props){if(null!==n&&void 0!==n||(n=p(p(p({},e),e.ownerState),{},{ownerState:e.ownerState})),!i.props(n))continue}else for(const t in i.props){var o;if(e[t]!==i.props[t]&&(null===(o=e.ownerState)||void 0===o?void 0:o[t])!==i.props[t])continue e}"function"===typeof i.style?(null!==n&&void 0!==n||(n=p(p(p({},e),e.ownerState),{},{ownerState:e.ownerState})),r.push(i.style(n))):r.push(i.style)}return r}function Lo(e,t){}function Do(e){return e?e.charAt(0).toLowerCase()+e.slice(1):e}const Bo=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Number.MIN_SAFE_INTEGER,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Number.MAX_SAFE_INTEGER;return Math.max(t,Math.min(e,n))};function Wo(e){return Bo(e,arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,arguments.length>2&&void 0!==arguments[2]?arguments[2]:1)}function Ho(e){if(e.type)return e;if("#"===e.charAt(0))return Ho(function(e){e=e.slice(1);const t=new RegExp(".{1,".concat(e.length>=6?2:1,"}"),"g");let n=e.match(t);return n&&1===n[0].length&&(n=n.map((e=>e+e))),n?"rgb".concat(4===n.length?"a":"","(").concat(n.map(((e,t)=>t<3?parseInt(e,16):Math.round(parseInt(e,16)/255*1e3)/1e3)).join(", "),")"):""}(e));const t=e.indexOf("("),n=e.substring(0,t);if(!["rgb","rgba","hsl","hsla","color"].includes(n))throw new Error(Rt(9,e));let r,o=e.substring(t+1,e.length-1);if("color"===n){if(o=o.split(" "),r=o.shift(),4===o.length&&"/"===o[3].charAt(0)&&(o[3]=o[3].slice(1)),!["srgb","display-p3","a98-rgb","prophoto-rgb","rec-2020"].includes(r))throw new Error(Rt(10,r))}else o=o.split(",");return o=o.map((e=>parseFloat(e))),{type:n,values:o,colorSpace:r}}const Uo=(e,t)=>{try{return(e=>{const t=Ho(e);return t.values.slice(0,3).map(((e,n)=>t.type.includes("hsl")&&0!==n?"".concat(e,"%"):e)).join(" ")})(e)}catch(n){return e}};function Vo(e){const{type:t,colorSpace:n}=e;let{values:r}=e;return t.includes("rgb")?r=r.map(((e,t)=>t<3?parseInt(e,10):e)):t.includes("hsl")&&(r[1]="".concat(r[1],"%"),r[2]="".concat(r[2],"%")),r=t.includes("color")?"".concat(n," ").concat(r.join(" ")):"".concat(r.join(", ")),"".concat(t,"(").concat(r,")")}function $o(e){e=Ho(e);const{values:t}=e,n=t[0],r=t[1]/100,o=t[2]/100,a=r*Math.min(o,1-o),i=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:(e+n/30)%12;return o-a*Math.max(Math.min(t-3,9-t,1),-1)};let l="rgb";const s=[Math.round(255*i(0)),Math.round(255*i(8)),Math.round(255*i(4))];return"hsla"===e.type&&(l+="a",s.push(t[3])),Vo({type:l,values:s})}function Ko(e){let t="hsl"===(e=Ho(e)).type||"hsla"===e.type?Ho($o(e)).values:e.values;return t=t.map((t=>("color"!==e.type&&(t/=255),t<=.03928?t/12.92:((t+.055)/1.055)**2.4))),Number((.2126*t[0]+.7152*t[1]+.0722*t[2]).toFixed(3))}function qo(e,t){return e=Ho(e),t=Wo(t),"rgb"!==e.type&&"hsl"!==e.type||(e.type+="a"),"color"===e.type?e.values[3]="/".concat(t):e.values[3]=t,Vo(e)}function Yo(e,t,n){try{return qo(e,t)}catch(r){return e}}function Go(e,t){if(e=Ho(e),t=Wo(t),e.type.includes("hsl"))e.values[2]*=1-t;else if(e.type.includes("rgb")||e.type.includes("color"))for(let n=0;n<3;n+=1)e.values[n]*=1-t;return Vo(e)}function Qo(e,t,n){try{return Go(e,t)}catch(r){return e}}function Xo(e,t){if(e=Ho(e),t=Wo(t),e.type.includes("hsl"))e.values[2]+=(100-e.values[2])*t;else if(e.type.includes("rgb"))for(let n=0;n<3;n+=1)e.values[n]+=(255-e.values[n])*t;else if(e.type.includes("color"))for(let n=0;n<3;n+=1)e.values[n]+=(1-e.values[n])*t;return Vo(e)}function Jo(e,t,n){try{return Xo(e,t)}catch(r){return e}}function Zo(e,t,n){try{return function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:.15;return Ko(e)>.5?Go(e,t):Xo(e,t)}(e,t)}catch(r){return e}}const ea={black:"#000",white:"#fff"},ta={50:"#fafafa",100:"#f5f5f5",200:"#eeeeee",300:"#e0e0e0",400:"#bdbdbd",500:"#9e9e9e",600:"#757575",700:"#616161",800:"#424242",900:"#212121",A100:"#f5f5f5",A200:"#eeeeee",A400:"#bdbdbd",A700:"#616161"},na={50:"#f3e5f5",100:"#e1bee7",200:"#ce93d8",300:"#ba68c8",400:"#ab47bc",500:"#9c27b0",600:"#8e24aa",700:"#7b1fa2",800:"#6a1b9a",900:"#4a148c",A100:"#ea80fc",A200:"#e040fb",A400:"#d500f9",A700:"#aa00ff"},ra={50:"#ffebee",100:"#ffcdd2",200:"#ef9a9a",300:"#e57373",400:"#ef5350",500:"#f44336",600:"#e53935",700:"#d32f2f",800:"#c62828",900:"#b71c1c",A100:"#ff8a80",A200:"#ff5252",A400:"#ff1744",A700:"#d50000"},oa={50:"#fff3e0",100:"#ffe0b2",200:"#ffcc80",300:"#ffb74d",400:"#ffa726",500:"#ff9800",600:"#fb8c00",700:"#f57c00",800:"#ef6c00",900:"#e65100",A100:"#ffd180",A200:"#ffab40",A400:"#ff9100",A700:"#ff6d00"},aa={50:"#e3f2fd",100:"#bbdefb",200:"#90caf9",300:"#64b5f6",400:"#42a5f5",500:"#2196f3",600:"#1e88e5",700:"#1976d2",800:"#1565c0",900:"#0d47a1",A100:"#82b1ff",A200:"#448aff",A400:"#2979ff",A700:"#2962ff"},ia={50:"#e1f5fe",100:"#b3e5fc",200:"#81d4fa",300:"#4fc3f7",400:"#29b6f6",500:"#03a9f4",600:"#039be5",700:"#0288d1",800:"#0277bd",900:"#01579b",A100:"#80d8ff",A200:"#40c4ff",A400:"#00b0ff",A700:"#0091ea"},la={50:"#e8f5e9",100:"#c8e6c9",200:"#a5d6a7",300:"#81c784",400:"#66bb6a",500:"#4caf50",600:"#43a047",700:"#388e3c",800:"#2e7d32",900:"#1b5e20",A100:"#b9f6ca",A200:"#69f0ae",A400:"#00e676",A700:"#00c853"},sa=["mode","contrastThreshold","tonalOffset"];function ca(){return{text:{primary:"rgba(0, 0, 0, 0.87)",secondary:"rgba(0, 0, 0, 0.6)",disabled:"rgba(0, 0, 0, 0.38)"},divider:"rgba(0, 0, 0, 0.12)",background:{paper:ea.white,default:ea.white},action:{active:"rgba(0, 0, 0, 0.54)",hover:"rgba(0, 0, 0, 0.04)",hoverOpacity:.04,selected:"rgba(0, 0, 0, 0.08)",selectedOpacity:.08,disabled:"rgba(0, 0, 0, 0.26)",disabledBackground:"rgba(0, 0, 0, 0.12)",disabledOpacity:.38,focus:"rgba(0, 0, 0, 0.12)",focusOpacity:.12,activatedOpacity:.12}}}const ua=ca();function da(){return{text:{primary:ea.white,secondary:"rgba(255, 255, 255, 0.7)",disabled:"rgba(255, 255, 255, 0.5)",icon:"rgba(255, 255, 255, 0.5)"},divider:"rgba(255, 255, 255, 0.12)",background:{paper:"#121212",default:"#121212"},action:{active:ea.white,hover:"rgba(255, 255, 255, 0.08)",hoverOpacity:.08,selected:"rgba(255, 255, 255, 0.16)",selectedOpacity:.16,disabled:"rgba(255, 255, 255, 0.3)",disabledBackground:"rgba(255, 255, 255, 0.12)",disabledOpacity:.38,focus:"rgba(255, 255, 255, 0.12)",focusOpacity:.12,activatedOpacity:.24}}}const pa=da();function fa(e,t,n,r){const o=r.light||r,a=r.dark||1.5*r;e[t]||(e.hasOwnProperty(n)?e[t]=e[n]:"light"===t?e.light=Xo(e.main,o):"dark"===t&&(e.dark=Go(e.main,a)))}function ha(e){const{mode:t="light",contrastThreshold:n=3,tonalOffset:r=.2}=e,o=l(e,sa),a=e.primary||function(){return"dark"===(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"light")?{main:aa[200],light:aa[50],dark:aa[400]}:{main:aa[700],light:aa[400],dark:aa[800]}}(t),i=e.secondary||function(){return"dark"===(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"light")?{main:na[200],light:na[50],dark:na[400]}:{main:na[500],light:na[300],dark:na[700]}}(t),s=e.error||function(){return"dark"===(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"light")?{main:ra[500],light:ra[300],dark:ra[700]}:{main:ra[700],light:ra[400],dark:ra[800]}}(t),c=e.info||function(){return"dark"===(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"light")?{main:ia[400],light:ia[300],dark:ia[700]}:{main:ia[700],light:ia[500],dark:ia[900]}}(t),u=e.success||function(){return"dark"===(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"light")?{main:la[400],light:la[300],dark:la[700]}:{main:la[800],light:la[500],dark:la[900]}}(t),d=e.warning||function(){return"dark"===(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"light")?{main:oa[400],light:oa[300],dark:oa[700]}:{main:"#ed6c02",light:oa[500],dark:oa[900]}}(t);function f(e){const t=function(e,t){const n=Ko(e),r=Ko(t);return(Math.max(n,r)+.05)/(Math.min(n,r)+.05)}(e,pa.text.primary)>=n?pa.text.primary:ua.text.primary;return t}const h=e=>{let{color:t,name:n,mainShade:o=500,lightShade:a=300,darkShade:i=700}=e;if(t=p({},t),!t.main&&t[o]&&(t.main=t[o]),!t.hasOwnProperty("main"))throw new Error(Rt(11,n?" (".concat(n,")"):"",o));if("string"!==typeof t.main)throw new Error(Rt(12,n?" (".concat(n,")"):"",JSON.stringify(t.main)));return fa(t,"light",a,r),fa(t,"dark",i,r),t.contrastText||(t.contrastText=f(t.main)),t};let m;"light"===t?m=ca():"dark"===t&&(m=da());return br(p({common:p({},ea),mode:t,primary:h({color:a,name:"primary"}),secondary:h({color:i,name:"secondary",mainShade:"A400",lightShade:"A200",darkShade:"A700"}),error:h({color:s,name:"error"}),warning:h({color:d,name:"warning"}),info:h({color:c,name:"info"}),success:h({color:u,name:"success"}),grey:ta,contrastThreshold:n,getContrastText:f,augmentColor:h,tonalOffset:r},m),o)}function ma(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";function t(){for(var n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];if(!r.length)return"";const a=r[0];return"string"!==typeof a||a.match(/(#|\(|\)|(-?(\d*\.)?\d+)(px|em|%|ex|ch|rem|vw|vh|vmin|vmax|cm|mm|in|pt|pc))|^(-?(\d*\.)?\d+)$|(\d+ \d+ \d+)/)?", ".concat(a):", var(--".concat(e?"".concat(e,"-"):"").concat(a).concat(t(...r.slice(1)),")")}return function(n){for(var r=arguments.length,o=new Array(r>1?r-1:0),a=1;a<r;a++)o[a-1]=arguments[a];return"var(--".concat(e?"".concat(e,"-"):"").concat(n).concat(t(...o),")")}}function ga(e){const t={};return Object.entries(e).forEach((e=>{const[n,r]=e;"object"===typeof r&&(t[n]="".concat(r.fontStyle?"".concat(r.fontStyle," "):"").concat(r.fontVariant?"".concat(r.fontVariant," "):"").concat(r.fontWeight?"".concat(r.fontWeight," "):"").concat(r.fontStretch?"".concat(r.fontStretch," "):"").concat(r.fontSize||"").concat(r.lineHeight?"/".concat(r.lineHeight," "):"").concat(r.fontFamily||""))})),t}const va=function(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:[],o=e;t.forEach(((e,a)=>{a===t.length-1?Array.isArray(o)?o[Number(e)]=n:o&&"object"===typeof o&&(o[e]=n):o&&"object"===typeof o&&(o[e]||(o[e]=r.includes(e)?[]:{}),o=o[e])}))};function ya(e,t){const{prefix:n,shouldSkipGeneratingVar:r}=t||{},o={},a={},i={};var l,s;return l=(e,t,l)=>{if(("string"===typeof t||"number"===typeof t)&&(!r||!r(e,t))){const r="--".concat(n?"".concat(n,"-"):"").concat(e.join("-")),s=((e,t)=>"number"===typeof t?["lineHeight","fontWeight","opacity","zIndex"].some((t=>e.includes(t)))||e[e.length-1].toLowerCase().includes("opacity")?t:"".concat(t,"px"):t)(e,t);Object.assign(o,{[r]:s}),va(a,e,"var(".concat(r,")"),l),va(i,e,"var(".concat(r,", ").concat(s,")"),l)}},s=e=>"vars"===e[0],function e(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];Object.entries(t).forEach((t=>{let[o,a]=t;(!s||s&&!s([...n,o]))&&void 0!==a&&null!==a&&("object"===typeof a&&Object.keys(a).length>0?e(a,[...n,o],Array.isArray(a)?[...r,o]:r):l([...n,o],a,r))}))}(e),{css:o,vars:a,varsWithDefaults:i}}const ba=["colorSchemes","components","defaultColorScheme"];const xa=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{getSelector:n=b,disableCssColorScheme:r,colorSchemeSelector:o}=t,{colorSchemes:a={},components:i,defaultColorScheme:s="light"}=e,u=l(e,ba),{vars:d,css:f,varsWithDefaults:h}=ya(u,t);let m=h;const g={},{[s]:v}=a,y=l(a,[s].map(c));if(Object.entries(y||{}).forEach((e=>{let[n,r]=e;const{vars:o,css:a,varsWithDefaults:i}=ya(r,t);m=br(m,i),g[n]={css:a,vars:o}})),v){const{css:e,vars:n,varsWithDefaults:r}=ya(v,t);m=br(m,r),g[s]={css:e,vars:n}}function b(t,n){let r=o;if("class"===o&&(r=".%s"),"data"===o&&(r="[data-%s]"),null!==o&&void 0!==o&&o.startsWith("data-")&&!o.includes("%s")&&(r="[".concat(o,'="%s"]')),t){if("media"===r){var i;if(e.defaultColorScheme===t)return":root";const r=(null===(i=a[t])||void 0===i||null===(i=i.palette)||void 0===i?void 0:i.mode)||t;return{["@media (prefers-color-scheme: ".concat(r,")")]:{":root":n}}}if(r)return e.defaultColorScheme===t?":root, ".concat(r.replace("%s",String(t))):r.replace("%s",String(t))}return":root"}return{vars:m,generateThemeVars:()=>{let e=p({},d);return Object.entries(g).forEach((t=>{let[,{vars:n}]=t;e=br(e,n)})),e},generateStyleSheets:()=>{const t=[],o=e.defaultColorScheme||"light";function i(e,n){Object.keys(n).length&&t.push("string"===typeof e?{[e]:p({},n)}:e)}i(n(void 0,p({},f)),f);const{[o]:s}=g,u=l(g,[o].map(c));if(s){var d;const{css:e}=s,t=null===(d=a[o])||void 0===d||null===(d=d.palette)||void 0===d?void 0:d.mode,l=p(!r&&t?{colorScheme:t}:{},e);i(n(o,p({},l)),l)}return Object.entries(u).forEach((e=>{var t;let[o,{css:l}]=e;const s=null===(t=a[o])||void 0===t||null===(t=t.palette)||void 0===t?void 0:t.mode,c=p(!r&&s?{colorScheme:s}:{},l);i(n(o,p({},c)),c)})),t}}};function wa(e,t){return p({toolbar:{minHeight:56,[e.up("xs")]:{"@media (orientation: landscape)":{minHeight:48}},[e.up("sm")]:{minHeight:64}}},t)}const Sa=["fontFamily","fontSize","fontWeightLight","fontWeightRegular","fontWeightMedium","fontWeightBold","htmlFontSize","allVariants","pxToRem"];const ka={textTransform:"uppercase"},Ca='"Roboto", "Helvetica", "Arial", sans-serif';function Ea(e,t){const n="function"===typeof t?t(e):t,{fontFamily:r=Ca,fontSize:o=14,fontWeightLight:a=300,fontWeightRegular:i=400,fontWeightMedium:s=500,fontWeightBold:c=700,htmlFontSize:u=16,allVariants:d,pxToRem:f}=n,h=l(n,Sa);const m=o/14,g=f||(e=>"".concat(e/u*m,"rem")),v=(e,t,n,o,a)=>{return p(p(p({fontFamily:r,fontWeight:e,fontSize:g(t),lineHeight:n},r===Ca?{letterSpacing:"".concat((i=o/t,Math.round(1e5*i)/1e5),"em")}:{}),a),d);var i},y={h1:v(a,96,1.167,-1.5),h2:v(a,60,1.2,-.5),h3:v(i,48,1.167,0),h4:v(i,34,1.235,.25),h5:v(i,24,1.334,0),h6:v(s,20,1.6,.15),subtitle1:v(i,16,1.75,.15),subtitle2:v(s,14,1.57,.1),body1:v(i,16,1.5,.15),body2:v(i,14,1.43,.15),button:v(s,14,1.75,.4,ka),caption:v(i,12,1.66,.4),overline:v(i,12,2.66,1,ka),inherit:{fontFamily:"inherit",fontWeight:"inherit",fontSize:"inherit",lineHeight:"inherit",letterSpacing:"inherit"}};return br(p({htmlFontSize:u,pxToRem:g,fontFamily:r,fontSize:o,fontWeightLight:a,fontWeightRegular:i,fontWeightMedium:s,fontWeightBold:c},y),h,{clone:!1})}function ja(){return["".concat(arguments.length<=0?void 0:arguments[0],"px ").concat(arguments.length<=1?void 0:arguments[1],"px ").concat(arguments.length<=2?void 0:arguments[2],"px ").concat(arguments.length<=3?void 0:arguments[3],"px rgba(0,0,0,").concat(.2,")"),"".concat(arguments.length<=4?void 0:arguments[4],"px ").concat(arguments.length<=5?void 0:arguments[5],"px ").concat(arguments.length<=6?void 0:arguments[6],"px ").concat(arguments.length<=7?void 0:arguments[7],"px rgba(0,0,0,").concat(.14,")"),"".concat(arguments.length<=8?void 0:arguments[8],"px ").concat(arguments.length<=9?void 0:arguments[9],"px ").concat(arguments.length<=10?void 0:arguments[10],"px ").concat(arguments.length<=11?void 0:arguments[11],"px rgba(0,0,0,").concat(.12,")")].join(",")}const Na=["none",ja(0,2,1,-1,0,1,1,0,0,1,3,0),ja(0,3,1,-2,0,2,2,0,0,1,5,0),ja(0,3,3,-2,0,3,4,0,0,1,8,0),ja(0,2,4,-1,0,4,5,0,0,1,10,0),ja(0,3,5,-1,0,5,8,0,0,1,14,0),ja(0,3,5,-1,0,6,10,0,0,1,18,0),ja(0,4,5,-2,0,7,10,1,0,2,16,1),ja(0,5,5,-3,0,8,10,1,0,3,14,2),ja(0,5,6,-3,0,9,12,1,0,3,16,2),ja(0,6,6,-3,0,10,14,1,0,4,18,3),ja(0,6,7,-4,0,11,15,1,0,4,20,3),ja(0,7,8,-4,0,12,17,2,0,5,22,4),ja(0,7,8,-4,0,13,19,2,0,5,24,4),ja(0,7,9,-4,0,14,21,2,0,5,26,4),ja(0,8,9,-5,0,15,22,2,0,6,28,5),ja(0,8,10,-5,0,16,24,2,0,6,30,5),ja(0,8,11,-5,0,17,26,2,0,6,32,5),ja(0,9,11,-5,0,18,28,2,0,7,34,6),ja(0,9,12,-6,0,19,29,2,0,7,36,6),ja(0,10,13,-6,0,20,31,3,0,8,38,7),ja(0,10,13,-6,0,21,33,3,0,8,40,7),ja(0,10,14,-6,0,22,35,3,0,8,42,7),ja(0,11,14,-7,0,23,36,3,0,9,44,8),ja(0,11,15,-7,0,24,38,3,0,9,46,8)],Pa=["duration","easing","delay"],Ra={easeInOut:"cubic-bezier(0.4, 0, 0.2, 1)",easeOut:"cubic-bezier(0.0, 0, 0.2, 1)",easeIn:"cubic-bezier(0.4, 0, 1, 1)",sharp:"cubic-bezier(0.4, 0, 0.6, 1)"},Ta={shortest:150,shorter:200,short:250,standard:300,complex:375,enteringScreen:225,leavingScreen:195};function _a(e){return"".concat(Math.round(e),"ms")}function Aa(e){if(!e)return 0;const t=e/36;return Math.min(Math.round(10*(4+15*t**.25+t/5)),3e3)}function Ma(e){const t=p(p({},Ra),e.easing),n=p(p({},Ta),e.duration);return p(p({getAutoHeightDuration:Aa,create:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:["all"],r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{duration:o=n.standard,easing:a=t.easeInOut,delay:i=0}=r;l(r,Pa);return(Array.isArray(e)?e:[e]).map((e=>"".concat(e," ").concat("string"===typeof o?o:_a(o)," ").concat(a," ").concat("string"===typeof i?i:_a(i)))).join(",")}},e),{},{easing:t,duration:n})}const za={mobileStepper:1e3,fab:1050,speedDial:1050,appBar:1100,drawer:1200,modal:1300,snackbar:1400,tooltip:1500};function Ia(){const e=p({},arguments.length>0&&void 0!==arguments[0]?arguments[0]:{});return function e(t){const n=Object.entries(t);for(let o=0;o<n.length;o++){const[a,i]=n[o];!vr(r=i)&&"undefined"!==typeof r&&"string"!==typeof r&&"boolean"!==typeof r&&"number"!==typeof r&&!Array.isArray(r)||a.startsWith("unstable_")?delete t[a]:vr(i)&&(t[a]=p({},i),e(t[a]))}var r}(e),"import { unstable_createBreakpoints as createBreakpoints, createTransitions } from '@mui/material/styles';\n\nconst theme = ".concat(JSON.stringify(e,null,2),";\n\ntheme.breakpoints = createBreakpoints(theme.breakpoints || {});\ntheme.transitions = createTransitions(theme.transitions || {});\n\nexport default theme;")}const Oa=["breakpoints","mixins","spacing","palette","transitions","typography","shape"];const Fa=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{breakpoints:t,mixins:n={},spacing:r,palette:o={},transitions:a={},typography:i={},shape:s}=e,c=l(e,Oa);if(e.vars&&void 0===e.generateThemeVars)throw new Error(Rt(20));const u=ha(o),d=Po(e);let f=br(d,{mixins:wa(d.breakpoints,n),palette:u,shadows:Na.slice(),typography:Ea(u,i),transitions:Ma(a),zIndex:p({},za)});f=br(f,c);for(var h=arguments.length,m=new Array(h>1?h-1:0),g=1;g<h;g++)m[g-1]=arguments[g];return f=m.reduce(((e,t)=>br(e,t)),f),f.unstable_sxConfig=p(p({},ko),null===c||void 0===c?void 0:c.unstable_sxConfig),f.unstable_sx=function(e){return Eo({sx:e,theme:this})},f.toRuntimeSource=Ia,f};function La(e){let t;return t=e<1?5.11916*e**2:4.5*Math.log(e+1)+2,Math.round(10*t)/1e3}const Da=["palette","opacity","overlays"],Ba=[...Array(25)].map(((e,t)=>{if(0===t)return"none";const n=La(t);return"linear-gradient(rgba(255 255 255 / ".concat(n,"), rgba(255 255 255 / ").concat(n,"))")}));function Wa(e){return{inputPlaceholder:"dark"===e?.5:.42,inputUnderline:"dark"===e?.7:.42,switchTrackDisabled:"dark"===e?.2:.12,switchTrack:"dark"===e?.3:.38}}function Ha(e){return"dark"===e?Ba:[]}function Ua(e){var t;return!!e[0].match(/(cssVarPrefix|colorSchemeSelector|rootSelector|typography|mixins|breakpoints|direction|transitions)/)||!!e[0].match(/sxConfig$/)||"palette"===e[0]&&!(null===(t=e[1])||void 0===t||!t.match(/(mode|contrastThreshold|tonalOffset)/))}const Va=e=>[...[...Array(25)].map(((t,n)=>"--".concat(e?"".concat(e,"-"):"","overlays-").concat(n))),"--".concat(e?"".concat(e,"-"):"","palette-AppBar-darkBg"),"--".concat(e?"".concat(e,"-"):"","palette-AppBar-darkColor")],$a=e=>(t,n)=>{const r=e.rootSelector||":root",o=e.colorSchemeSelector;let a=o;if("class"===o&&(a=".%s"),"data"===o&&(a="[data-%s]"),null!==o&&void 0!==o&&o.startsWith("data-")&&!o.includes("%s")&&(a="[".concat(o,'="%s"]')),e.defaultColorScheme===t){if("dark"===t){const o={};return Va(e.cssVarPrefix).forEach((e=>{o[e]=n[e],delete n[e]})),"media"===a?{[r]:n,"@media (prefers-color-scheme: dark)":{[r]:o}}:a?{[a.replace("%s",t)]:o,["".concat(r,", ").concat(a.replace("%s",t))]:n}:{[r]:p(p({},n),o)}}if(a&&"media"!==a)return"".concat(r,", ").concat(a.replace("%s",String(t)))}else if(t){if("media"===a)return{["@media (prefers-color-scheme: ".concat(String(t),")")]:{[r]:n}};if(a)return a.replace("%s",String(t))}return r},Ka=["palette"],qa=["colorSchemes","defaultColorScheme","disableCssColorScheme","cssVarPrefix","shouldSkipGeneratingVar","colorSchemeSelector","rootSelector"];function Ya(e,t,n){!e[t]&&n&&(e[t]=n)}function Ga(e){return"string"===typeof e&&e.startsWith("hsl")?$o(e):e}function Qa(e,t){"".concat(t,"Channel")in e||(e["".concat(t,"Channel")]=Uo(Ga(e[t]),("MUI: Can't create `palette.".concat(t,"Channel` because `palette.").concat(t,"` is not one of these formats: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color()."),"To suppress this warning, you need to explicitly provide the `palette.".concat(t,'Channel` as a string (in rgb format, for example "12 12 12") or undefined if you want to remove the channel token.'))))}const Xa=e=>{try{return e()}catch(t){}};function Ja(e,t,n,r){var o,a,i;if(!t)return;t=!0===t?{}:t;const s="dark"===r?"dark":"light";var c;if(!n)return void(e[r]=function(e){const{palette:t={mode:"light"},opacity:n,overlays:r}=e,o=l(e,Da),a=ha(t);return p({palette:a,opacity:p(p({},Wa(a.mode)),n),overlays:r||Ha(a.mode)},o)}(p(p({},t),{},{palette:p({mode:s},null===(c=t)||void 0===c?void 0:c.palette)})));const u=Fa(p(p({},n),{},{palette:p({mode:s},null===(o=t)||void 0===o?void 0:o.palette)})),{palette:d}=u,f=l(u,Ka);return e[r]=p(p({},t),{},{palette:d,opacity:p(p({},Wa(s)),null===(a=t)||void 0===a?void 0:a.opacity),overlays:(null===(i=t)||void 0===i?void 0:i.overlays)||Ha(s)}),f}function Za(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{colorSchemes:t={light:!0},defaultColorScheme:n,disableCssColorScheme:r=!1,cssVarPrefix:o="mui",shouldSkipGeneratingVar:a=Ua,colorSchemeSelector:i=(t.light&&t.dark?"media":void 0),rootSelector:s=":root"}=e,u=l(e,qa),d=Object.keys(t)[0],f=n||(t.light&&"light"!==d?"light":d),h=function(){return ma(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"mui")}(o),{[f]:m,light:g,dark:v}=t,y=p({},l(t,[f,"light","dark"].map(c)));let b=m;if(("dark"===f&&!("dark"in t)||"light"===f&&!("light"in t))&&(b=!0),!b)throw new Error(Rt(21,f));const x=Ja(y,b,u,f);g&&!y.light&&Ja(y,g,void 0,"light"),v&&!y.dark&&Ja(y,v,void 0,"dark");let w=p(p({defaultColorScheme:f},x),{},{cssVarPrefix:o,colorSchemeSelector:i,rootSelector:s,getCssVar:h,colorSchemes:y,font:p(p({},ga(x.typography)),x.font),spacing:(S=u.spacing,"number"===typeof S?"".concat(S,"px"):"string"===typeof S||"function"===typeof S||Array.isArray(S)?S:"8px")});var S;Object.keys(w.colorSchemes).forEach((e=>{const t=w.colorSchemes[e].palette,n=e=>{const n=e.split("-"),r=n[1],o=n[2];return h(e,t[r][o])};var r;if("light"===t.mode&&(Ya(t.common,"background","#fff"),Ya(t.common,"onBackground","#000")),"dark"===t.mode&&(Ya(t.common,"background","#000"),Ya(t.common,"onBackground","#fff")),r=t,["Alert","AppBar","Avatar","Button","Chip","FilledInput","LinearProgress","Skeleton","Slider","SnackbarContent","SpeedDialAction","StepConnector","StepContent","Switch","TableCell","Tooltip"].forEach((e=>{r[e]||(r[e]={})})),"light"===t.mode){Ya(t.Alert,"errorColor",Qo(t.error.light,.6)),Ya(t.Alert,"infoColor",Qo(t.info.light,.6)),Ya(t.Alert,"successColor",Qo(t.success.light,.6)),Ya(t.Alert,"warningColor",Qo(t.warning.light,.6)),Ya(t.Alert,"errorFilledBg",n("palette-error-main")),Ya(t.Alert,"infoFilledBg",n("palette-info-main")),Ya(t.Alert,"successFilledBg",n("palette-success-main")),Ya(t.Alert,"warningFilledBg",n("palette-warning-main")),Ya(t.Alert,"errorFilledColor",Xa((()=>t.getContrastText(t.error.main)))),Ya(t.Alert,"infoFilledColor",Xa((()=>t.getContrastText(t.info.main)))),Ya(t.Alert,"successFilledColor",Xa((()=>t.getContrastText(t.success.main)))),Ya(t.Alert,"warningFilledColor",Xa((()=>t.getContrastText(t.warning.main)))),Ya(t.Alert,"errorStandardBg",Jo(t.error.light,.9)),Ya(t.Alert,"infoStandardBg",Jo(t.info.light,.9)),Ya(t.Alert,"successStandardBg",Jo(t.success.light,.9)),Ya(t.Alert,"warningStandardBg",Jo(t.warning.light,.9)),Ya(t.Alert,"errorIconColor",n("palette-error-main")),Ya(t.Alert,"infoIconColor",n("palette-info-main")),Ya(t.Alert,"successIconColor",n("palette-success-main")),Ya(t.Alert,"warningIconColor",n("palette-warning-main")),Ya(t.AppBar,"defaultBg",n("palette-grey-100")),Ya(t.Avatar,"defaultBg",n("palette-grey-400")),Ya(t.Button,"inheritContainedBg",n("palette-grey-300")),Ya(t.Button,"inheritContainedHoverBg",n("palette-grey-A100")),Ya(t.Chip,"defaultBorder",n("palette-grey-400")),Ya(t.Chip,"defaultAvatarColor",n("palette-grey-700")),Ya(t.Chip,"defaultIconColor",n("palette-grey-700")),Ya(t.FilledInput,"bg","rgba(0, 0, 0, 0.06)"),Ya(t.FilledInput,"hoverBg","rgba(0, 0, 0, 0.09)"),Ya(t.FilledInput,"disabledBg","rgba(0, 0, 0, 0.12)"),Ya(t.LinearProgress,"primaryBg",Jo(t.primary.main,.62)),Ya(t.LinearProgress,"secondaryBg",Jo(t.secondary.main,.62)),Ya(t.LinearProgress,"errorBg",Jo(t.error.main,.62)),Ya(t.LinearProgress,"infoBg",Jo(t.info.main,.62)),Ya(t.LinearProgress,"successBg",Jo(t.success.main,.62)),Ya(t.LinearProgress,"warningBg",Jo(t.warning.main,.62)),Ya(t.Skeleton,"bg","rgba(".concat(n("palette-text-primaryChannel")," / 0.11)")),Ya(t.Slider,"primaryTrack",Jo(t.primary.main,.62)),Ya(t.Slider,"secondaryTrack",Jo(t.secondary.main,.62)),Ya(t.Slider,"errorTrack",Jo(t.error.main,.62)),Ya(t.Slider,"infoTrack",Jo(t.info.main,.62)),Ya(t.Slider,"successTrack",Jo(t.success.main,.62)),Ya(t.Slider,"warningTrack",Jo(t.warning.main,.62));const e=Zo(t.background.default,.8);Ya(t.SnackbarContent,"bg",e),Ya(t.SnackbarContent,"color",Xa((()=>t.getContrastText(e)))),Ya(t.SpeedDialAction,"fabHoverBg",Zo(t.background.paper,.15)),Ya(t.StepConnector,"border",n("palette-grey-400")),Ya(t.StepContent,"border",n("palette-grey-400")),Ya(t.Switch,"defaultColor",n("palette-common-white")),Ya(t.Switch,"defaultDisabledColor",n("palette-grey-100")),Ya(t.Switch,"primaryDisabledColor",Jo(t.primary.main,.62)),Ya(t.Switch,"secondaryDisabledColor",Jo(t.secondary.main,.62)),Ya(t.Switch,"errorDisabledColor",Jo(t.error.main,.62)),Ya(t.Switch,"infoDisabledColor",Jo(t.info.main,.62)),Ya(t.Switch,"successDisabledColor",Jo(t.success.main,.62)),Ya(t.Switch,"warningDisabledColor",Jo(t.warning.main,.62)),Ya(t.TableCell,"border",Jo(Yo(t.divider,1),.88)),Ya(t.Tooltip,"bg",Yo(t.grey[700],.92))}if("dark"===t.mode){Ya(t.Alert,"errorColor",Jo(t.error.light,.6)),Ya(t.Alert,"infoColor",Jo(t.info.light,.6)),Ya(t.Alert,"successColor",Jo(t.success.light,.6)),Ya(t.Alert,"warningColor",Jo(t.warning.light,.6)),Ya(t.Alert,"errorFilledBg",n("palette-error-dark")),Ya(t.Alert,"infoFilledBg",n("palette-info-dark")),Ya(t.Alert,"successFilledBg",n("palette-success-dark")),Ya(t.Alert,"warningFilledBg",n("palette-warning-dark")),Ya(t.Alert,"errorFilledColor",Xa((()=>t.getContrastText(t.error.dark)))),Ya(t.Alert,"infoFilledColor",Xa((()=>t.getContrastText(t.info.dark)))),Ya(t.Alert,"successFilledColor",Xa((()=>t.getContrastText(t.success.dark)))),Ya(t.Alert,"warningFilledColor",Xa((()=>t.getContrastText(t.warning.dark)))),Ya(t.Alert,"errorStandardBg",Qo(t.error.light,.9)),Ya(t.Alert,"infoStandardBg",Qo(t.info.light,.9)),Ya(t.Alert,"successStandardBg",Qo(t.success.light,.9)),Ya(t.Alert,"warningStandardBg",Qo(t.warning.light,.9)),Ya(t.Alert,"errorIconColor",n("palette-error-main")),Ya(t.Alert,"infoIconColor",n("palette-info-main")),Ya(t.Alert,"successIconColor",n("palette-success-main")),Ya(t.Alert,"warningIconColor",n("palette-warning-main")),Ya(t.AppBar,"defaultBg",n("palette-grey-900")),Ya(t.AppBar,"darkBg",n("palette-background-paper")),Ya(t.AppBar,"darkColor",n("palette-text-primary")),Ya(t.Avatar,"defaultBg",n("palette-grey-600")),Ya(t.Button,"inheritContainedBg",n("palette-grey-800")),Ya(t.Button,"inheritContainedHoverBg",n("palette-grey-700")),Ya(t.Chip,"defaultBorder",n("palette-grey-700")),Ya(t.Chip,"defaultAvatarColor",n("palette-grey-300")),Ya(t.Chip,"defaultIconColor",n("palette-grey-300")),Ya(t.FilledInput,"bg","rgba(255, 255, 255, 0.09)"),Ya(t.FilledInput,"hoverBg","rgba(255, 255, 255, 0.13)"),Ya(t.FilledInput,"disabledBg","rgba(255, 255, 255, 0.12)"),Ya(t.LinearProgress,"primaryBg",Qo(t.primary.main,.5)),Ya(t.LinearProgress,"secondaryBg",Qo(t.secondary.main,.5)),Ya(t.LinearProgress,"errorBg",Qo(t.error.main,.5)),Ya(t.LinearProgress,"infoBg",Qo(t.info.main,.5)),Ya(t.LinearProgress,"successBg",Qo(t.success.main,.5)),Ya(t.LinearProgress,"warningBg",Qo(t.warning.main,.5)),Ya(t.Skeleton,"bg","rgba(".concat(n("palette-text-primaryChannel")," / 0.13)")),Ya(t.Slider,"primaryTrack",Qo(t.primary.main,.5)),Ya(t.Slider,"secondaryTrack",Qo(t.secondary.main,.5)),Ya(t.Slider,"errorTrack",Qo(t.error.main,.5)),Ya(t.Slider,"infoTrack",Qo(t.info.main,.5)),Ya(t.Slider,"successTrack",Qo(t.success.main,.5)),Ya(t.Slider,"warningTrack",Qo(t.warning.main,.5));const e=Zo(t.background.default,.98);Ya(t.SnackbarContent,"bg",e),Ya(t.SnackbarContent,"color",Xa((()=>t.getContrastText(e)))),Ya(t.SpeedDialAction,"fabHoverBg",Zo(t.background.paper,.15)),Ya(t.StepConnector,"border",n("palette-grey-600")),Ya(t.StepContent,"border",n("palette-grey-600")),Ya(t.Switch,"defaultColor",n("palette-grey-300")),Ya(t.Switch,"defaultDisabledColor",n("palette-grey-600")),Ya(t.Switch,"primaryDisabledColor",Qo(t.primary.main,.55)),Ya(t.Switch,"secondaryDisabledColor",Qo(t.secondary.main,.55)),Ya(t.Switch,"errorDisabledColor",Qo(t.error.main,.55)),Ya(t.Switch,"infoDisabledColor",Qo(t.info.main,.55)),Ya(t.Switch,"successDisabledColor",Qo(t.success.main,.55)),Ya(t.Switch,"warningDisabledColor",Qo(t.warning.main,.55)),Ya(t.TableCell,"border",Qo(Yo(t.divider,1),.68)),Ya(t.Tooltip,"bg",Yo(t.grey[700],.92))}Qa(t.background,"default"),Qa(t.background,"paper"),Qa(t.common,"background"),Qa(t.common,"onBackground"),Qa(t,"divider"),Object.keys(t).forEach((e=>{const n=t[e];"tonalOffset"!==e&&n&&"object"===typeof n&&(n.main&&Ya(t[e],"mainChannel",Uo(Ga(n.main))),n.light&&Ya(t[e],"lightChannel",Uo(Ga(n.light))),n.dark&&Ya(t[e],"darkChannel",Uo(Ga(n.dark))),n.contrastText&&Ya(t[e],"contrastTextChannel",Uo(Ga(n.contrastText))),"text"===e&&(Qa(t[e],"primary"),Qa(t[e],"secondary")),"action"===e&&(n.active&&Qa(t[e],"active"),n.selected&&Qa(t[e],"selected")))}))}));for(var k=arguments.length,C=new Array(k>1?k-1:0),E=1;E<k;E++)C[E-1]=arguments[E];w=C.reduce(((e,t)=>br(e,t)),w);const j={prefix:o,disableCssColorScheme:r,shouldSkipGeneratingVar:a,getSelector:$a(w)},{vars:N,generateThemeVars:P,generateStyleSheets:R}=xa(w,j);return w.vars=N,Object.entries(w.colorSchemes[w.defaultColorScheme]).forEach((e=>{let[t,n]=e;w[t]=n})),w.generateThemeVars=P,w.generateStyleSheets=R,w.generateSpacing=function(){return Yr(u.spacing,Wr(this))},w.getColorSchemeSelector=function(e){return function(t){return"media"===e?"@media (prefers-color-scheme: ".concat(t,")"):e?e.startsWith("data-")&&!e.includes("%s")?"[".concat(e,'="').concat(t,'"] &'):"class"===e?".".concat(t," &"):"data"===e?"[data-".concat(t,"] &"):"".concat(e.replace("%s",t)," &"):"&"}}(i),w.spacing=w.generateSpacing(),w.shouldSkipGeneratingVar=a,w.unstable_sxConfig=p(p({},ko),null===u||void 0===u?void 0:u.unstable_sxConfig),w.unstable_sx=function(e){return Eo({sx:e,theme:this})},w.toRuntimeSource=Ia,w}const ei=["palette","cssVariables","colorSchemes","defaultColorScheme"];function ti(e,t,n){e.colorSchemes&&n&&(e.colorSchemes[t]=p(p({},!0!==n&&n),{},{palette:ha(p(p({},!0===n?{}:n.palette),{},{mode:t}))}))}const ni=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{palette:t,cssVariables:n=!1,colorSchemes:r=(t?void 0:{light:!0}),defaultColorScheme:o=(null===t||void 0===t?void 0:t.mode)}=e,a=l(e,ei),i=o||"light",s=null===r||void 0===r?void 0:r[i],c=p(p({},r),t?{[i]:p(p({},"boolean"!==typeof s&&s),{},{palette:t})}:void 0);for(var u=arguments.length,d=new Array(u>1?u-1:0),f=1;f<u;f++)d[f-1]=arguments[f];if(!1===n){if(!("colorSchemes"in e))return Fa(e,...d);let n=t;"palette"in e||c[i]&&(!0!==c[i]?n=c[i].palette:"dark"===i&&(n={mode:"dark"}));const r=Fa(p(p({},e),{},{palette:n}),...d);return r.defaultColorScheme=i,r.colorSchemes=c,"light"===r.palette.mode&&(r.colorSchemes.light=p(p({},!0!==c.light&&c.light),{},{palette:r.palette}),ti(r,"dark",c.dark)),"dark"===r.palette.mode&&(r.colorSchemes.dark=p(p({},!0!==c.dark&&c.dark),{},{palette:r.palette}),ti(r,"light",c.light)),r}return t||"light"in c||"light"!==i||(c.light=!0),Za(p(p({},a),{},{colorSchemes:c,defaultColorScheme:i},"boolean"!==typeof n&&n),...d)}(),ri=ni,oi="$$material";const ai=function(e){return"ownerState"!==e&&"theme"!==e&&"sx"!==e&&"as"!==e},ii=e=>ai(e)&&"classes"!==e,li=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{themeId:t,defaultTheme:n=Mo,rootShouldForwardProp:r=zo,slotShouldForwardProp:o=zo}=e;function a(e){!function(e,t,n){e.theme=function(e){for(const t in e)return!1;return!0}(e.theme)?n:e.theme[t]||e.theme}(e,t,n)}return function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};!function(e,t){Array.isArray(e.__emotion_styles)&&(e.__emotion_styles=t(e.__emotion_styles))}(e,(e=>e.filter((e=>e!==Eo))));const{name:n,slot:i,skipVariantsResolver:s,skipSx:c,overridesResolver:u=Io(Do(i))}=t,d=l(t,Ao),f=void 0!==s?s:i&&"Root"!==i&&"root"!==i||!1,h=c||!1;let m=zo;"Root"===i||"root"===i?m=r:i?m=o:function(e){return"string"===typeof e&&e.charCodeAt(0)>96}(e)&&(m=void 0);const g=function(e,t){return fr(e,t)}(e,p({shouldForwardProp:m,label:Lo(n,i)},d)),v=e=>{if("function"===typeof e&&e.__emotion_real!==e)return function(t){return Oo(t,e)};if(vr(e)){const t=To(e);return t.variants?function(e){return Oo(e,t)}:t.style}return e},y=function(){const t=[];for(var r=arguments.length,o=new Array(r),i=0;i<r;i++)o[i]=arguments[i];const l=o.map(v),s=[];if(t.push(a),n&&u&&s.push((function(e){var t;const r=null===(t=e.theme.components)||void 0===t||null===(t=t[n])||void 0===t?void 0:t.styleOverrides;if(!r)return null;const o={};for(const n in r)o[n]=Oo(e,r[n]);return u(e,o)})),n&&!f&&s.push((function(e){var t;const r=e.theme,o=null===r||void 0===r||null===(t=r.components)||void 0===t||null===(t=t[n])||void 0===t?void 0:t.variants;return o?Fo(e,o):null})),h||s.push(Eo),Array.isArray(l[0])){const e=l.shift(),n=new Array(t.length).fill(""),r=new Array(s.length).fill("");let o;o=[...n,...e,...r],o.raw=[...n,...e.raw,...r],t.unshift(o)}const c=[...t,...l,...s],d=g(...c);return e.muiName&&(d.muiName=e.muiName),d};return g.withConfig&&(y.withConfig=g.withConfig),y}}({themeId:oi,defaultTheme:ri,rootShouldForwardProp:ii}),si=li,ci={theme:void 0};const ui=function(e){let t,n;return function(r){let o=t;return void 0!==o&&r.theme===n||(ci.theme=r.theme,o=To(e(ci)),t=o,n=r.theme),o}};function di(e,t){const n=p({},t);for(const r in e)if(Object.prototype.hasOwnProperty.call(e,r)){const o=r;if("components"===o||"slots"===o)n[o]=p(p({},e[o]),n[o]);else if("componentsProps"===o||"slotProps"===o){const r=e[o],a=t[o];if(a)if(r){n[o]=p({},a);for(const e in r)if(Object.prototype.hasOwnProperty.call(r,e)){const t=e;n[o][t]=di(r[t],a[t])}}else n[o]=a;else n[o]=r||{}}else void 0===n[o]&&(n[o]=e[o])}return n}var pi=n(579);const fi=r.createContext(void 0);function hi(e){let{props:t,name:n}=e;return function(e){const{theme:t,name:n,props:r}=e;if(!t||!t.components||!t.components[n])return r;const o=t.components[n];return o.defaultProps?di(o.defaultProps,r):o.styleOverrides||o.variants?r:di(o,r)}({props:t,name:n,theme:{components:r.useContext(fi)}})}function mi(e){return hi(e)}const gi=e=>e,vi=(()=>{let e=gi;return{configure(t){e=t},generate:t=>e(t),reset(){e=gi}}})(),yi={active:"active",checked:"checked",completed:"completed",disabled:"disabled",error:"error",expanded:"expanded",focused:"focused",focusVisible:"focusVisible",open:"open",readOnly:"readOnly",required:"required",selected:"selected"};function bi(e,t){const n=yi[t];return n?"".concat(arguments.length>2&&void 0!==arguments[2]?arguments[2]:"Mui","-").concat(n):"".concat(vi.generate(e),"-").concat(t)}function xi(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"Mui";const r={};return t.forEach((t=>{r[t]=bi(e,t,n)})),r}function wi(e){return bi("MuiSvgIcon",e)}xi("MuiSvgIcon",["root","colorPrimary","colorSecondary","colorAction","colorError","colorDisabled","fontSizeInherit","fontSizeSmall","fontSizeMedium","fontSizeLarge"]);const Si=["children","className","color","component","fontSize","htmlColor","inheritViewBox","titleAccess","viewBox"],ki=si("svg",{name:"MuiSvgIcon",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,"inherit"!==n.color&&t["color".concat(_t(n.color))],t["fontSize".concat(_t(n.fontSize))]]}})(ui((e=>{var t,n,r,o,a,i,l,s,c,u,d,p,f,h,m;let{theme:g}=e;return{userSelect:"none",width:"1em",height:"1em",display:"inline-block",flexShrink:0,transition:null===(t=g.transitions)||void 0===t||null===(n=t.create)||void 0===n?void 0:n.call(t,"fill",{duration:null===(r=(null!==(o=g.vars)&&void 0!==o?o:g).transitions)||void 0===r||null===(r=r.duration)||void 0===r?void 0:r.shorter}),variants:[{props:e=>!e.hasSvgAsChild,style:{fill:"currentColor"}},{props:{fontSize:"inherit"},style:{fontSize:"inherit"}},{props:{fontSize:"small"},style:{fontSize:(null===(a=g.typography)||void 0===a||null===(i=a.pxToRem)||void 0===i?void 0:i.call(a,20))||"1.25rem"}},{props:{fontSize:"medium"},style:{fontSize:(null===(l=g.typography)||void 0===l||null===(s=l.pxToRem)||void 0===s?void 0:s.call(l,24))||"1.5rem"}},{props:{fontSize:"large"},style:{fontSize:(null===(c=g.typography)||void 0===c||null===(u=c.pxToRem)||void 0===u?void 0:u.call(c,35))||"2.1875rem"}},...Object.entries((null!==(d=g.vars)&&void 0!==d?d:g).palette).filter((e=>{let[,t]=e;return t&&t.main})).map((e=>{var t,n;let[r]=e;return{props:{color:r},style:{color:null===(t=(null!==(n=g.vars)&&void 0!==n?n:g).palette)||void 0===t||null===(t=t[r])||void 0===t?void 0:t.main}}})),{props:{color:"action"},style:{color:null===(p=(null!==(f=g.vars)&&void 0!==f?f:g).palette)||void 0===p||null===(p=p.action)||void 0===p?void 0:p.active}},{props:{color:"disabled"},style:{color:null===(h=(null!==(m=g.vars)&&void 0!==m?m:g).palette)||void 0===h||null===(h=h.action)||void 0===h?void 0:h.disabled}},{props:{color:"inherit"},style:{color:void 0}}]}}))),Ci=r.forwardRef((function(e,t){const n=mi({props:e,name:"MuiSvgIcon"}),{children:o,className:a,color:i="inherit",component:s="svg",fontSize:c="medium",htmlColor:u,inheritViewBox:d=!1,titleAccess:f,viewBox:h="0 0 24 24"}=n,m=l(n,Si),g=r.isValidElement(o)&&"svg"===o.type,v=p(p({},n),{},{color:i,component:s,fontSize:c,instanceFontSize:e.fontSize,inheritViewBox:d,viewBox:h,hasSvgAsChild:g}),y={};d||(y.viewBox=h);const b=(e=>{const{color:t,fontSize:n,classes:r}=e;return Pt({root:["root","inherit"!==t&&"color".concat(_t(t)),"fontSize".concat(_t(n))]},wi,r)})(v);return(0,pi.jsxs)(ki,p(p(p(p({as:s,className:Nt(b.root,a),focusable:"false",color:u,"aria-hidden":!f||void 0,role:f?"img":void 0,ref:t},y),m),g&&o.props),{},{ownerState:v,children:[g?o.props.children:o,f?(0,pi.jsx)("title",{children:f}):null]}))}));Ci.muiName="SvgIcon";const Ei=Ci;function ji(e,t){function n(t,n){return(0,pi.jsx)(Ei,p(p({"data-testid":void 0,ref:n},t),{},{children:e}))}return n.muiName=Ei.muiName,r.memo(r.forwardRef(n))}const Ni=ji((0,pi.jsx)("path",{d:"M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"})),Pi=ji((0,pi.jsx)("path",{d:"m17 7-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.58L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4z"})),Ri=ji((0,pi.jsx)("path",{d:"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4m0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4"}));const Ti=function(){const[e,t]=(0,r.useState)(!1),[n,o]=(0,r.useState)(!1),a=ge(),i=fe();(0,r.useEffect)((()=>{localStorage.getItem("userDetails")&&o(!0)}),[]);const l=e=>{if(t(!1),"/"===i.pathname){const t=document.getElementById(e);t&&t.scrollIntoView({behavior:"smooth"})}else a("/",{state:{scrollTo:e}})};return(0,pi.jsx)("header",{className:"site-header",children:(0,pi.jsxs)("div",{className:"container",children:[(0,pi.jsxs)("div",{className:"top-bar",children:[(0,pi.jsxs)("div",{className:"contact-info",children:[(0,pi.jsxs)("a",{href:"mailto:<EMAIL>",className:"contact-item",onClick:e=>{window.location.href="mailto:<EMAIL>"},children:[(0,pi.jsx)("i",{className:"fas fa-envelope"})," <EMAIL>"]}),(0,pi.jsxs)("a",{href:"tel:+19092359247",className:"contact-item",children:[(0,pi.jsx)("i",{className:"fas fa-phone"})," ******-235-9247"]})]}),(0,pi.jsx)("div",{className:"social-links",children:(0,pi.jsx)("a",{href:"https://www.linkedin.com/company/interviewsupport-ai/",target:"_blank",rel:"noopener noreferrer",className:"social-icon linkedin",children:(0,pi.jsx)("i",{className:"fab fa-linkedin"})})})]}),(0,pi.jsxs)("nav",{className:"main-nav",children:[(0,pi.jsx)("div",{className:"logo",children:(0,pi.jsxs)(yt,{to:"/",className:"logo-link",children:[(0,pi.jsx)(Ni,{className:"home-icon"}),(0,pi.jsx)("span",{children:"Interview AI"})]})}),(0,pi.jsx)("button",{className:"menu-toggle",onClick:()=>{t(!e)},children:(0,pi.jsx)("i",{className:"fas ".concat(e?"fa-times":"fa-bars")})}),(0,pi.jsxs)("ul",{className:"nav-links ".concat(e?"active":""),children:[(0,pi.jsx)("li",{children:(0,pi.jsx)(yt,{to:"/",children:"Home"})}),(0,pi.jsx)("li",{children:(0,pi.jsx)(yt,{to:"/#features",className:"nav-link",onClick:e=>{e.preventDefault(),l("features")},children:"Features"})}),(0,pi.jsx)("li",{children:(0,pi.jsx)(yt,{to:"/#pricing",className:"nav-link",onClick:e=>{e.preventDefault(),l("pricing")},children:"Pricing"})}),(0,pi.jsx)("li",{children:(0,pi.jsx)(yt,{to:"/#faq",className:"nav-link",onClick:e=>{e.preventDefault(),l("faq")},children:"FAQ"})}),(0,pi.jsx)("li",{children:(0,pi.jsx)(yt,{to:"/#contact",className:"nav-link",onClick:e=>{e.preventDefault(),l("contact")},children:"Contact"})}),n&&(0,pi.jsx)("li",{className:"nav-button",children:(0,pi.jsxs)(yt,{to:"/myprofile",title:"My Profile",className:"profile-link",children:[(0,pi.jsx)(Ri,{style:{verticalAlign:"middle",marginRight:4}}),"My Profile"]})}),n?(0,pi.jsx)("li",{className:"nav-button logout-button",children:(0,pi.jsxs)(yt,{to:"/",onClick:e=>{e.preventDefault(),localStorage.removeItem("userDetails"),localStorage.removeItem("token"),o(!1),a("/")},className:"logout-link",children:[(0,pi.jsx)(Pi,{className:"logout-icon"}),(0,pi.jsx)("span",{children:"Logout"})]})}):(0,pi.jsx)("li",{className:"nav-button",children:(0,pi.jsx)(yt,{to:"/login",className:"highlight",children:"Login"})})]})]})]})})};const _i=function(){const e=ge(),t=fe(),n=n=>{if("/"===t.pathname){const e=document.getElementById(n);e&&e.scrollIntoView({behavior:"smooth"})}else e("/",{state:{scrollTo:n}})};return(0,pi.jsx)("footer",{className:"site-footer",children:(0,pi.jsxs)("div",{className:"container",children:[(0,pi.jsxs)("div",{className:"footer-content",children:[(0,pi.jsxs)("div",{className:"footer-section",children:[(0,pi.jsx)("h3",{children:"Interview AI"}),(0,pi.jsx)("p",{children:"Interview ready. Get Instant answers for your interview questions. Ace your interview. Your dream job is right there."})]}),(0,pi.jsxs)("div",{className:"footer-section",children:[(0,pi.jsx)("h3",{children:"Quick Links"}),(0,pi.jsxs)("ul",{className:"footer-links",children:[(0,pi.jsx)("li",{children:(0,pi.jsx)(yt,{to:"/",children:"Home"})}),(0,pi.jsx)("li",{children:(0,pi.jsx)(yt,{to:"/#features",onClick:e=>{e.preventDefault(),n("features")},children:"Features"})}),(0,pi.jsx)("li",{children:(0,pi.jsx)(yt,{to:"/#pricing",onClick:e=>{e.preventDefault(),n("pricing")},children:"Pricing"})}),(0,pi.jsx)("li",{children:(0,pi.jsx)(yt,{to:"/#faq",onClick:e=>{e.preventDefault(),n("faq")},children:"FAQ"})})]})]}),(0,pi.jsxs)("div",{className:"footer-section",children:[(0,pi.jsx)("h3",{children:"Contact Us"}),(0,pi.jsx)("p",{children:"<EMAIL>"}),(0,pi.jsx)("p",{children:"Phone: ******-235-9247"}),(0,pi.jsx)("p",{children:"201 E Center St Suite 112, Anaheim CA 92805"})]}),(0,pi.jsxs)("div",{className:"footer-section",children:[(0,pi.jsx)("h3",{children:"Follow Us"}),(0,pi.jsx)("div",{className:"social-links",children:(0,pi.jsx)("a",{href:"https://www.linkedin.com/company/interviewsupport-ai/",target:"_blank",rel:"noopener noreferrer",className:"social-icon linkedin",children:(0,pi.jsx)("i",{className:"fab fa-linkedin"})})})]})]}),(0,pi.jsxs)("div",{className:"footer-bottom",children:[(0,pi.jsxs)("p",{children:["\xa9 ",(new Date).getFullYear()," Interview AI. All rights reserved."]}),(0,pi.jsxs)("div",{className:"footer-bottom-links",children:[(0,pi.jsx)(yt,{to:"/#features",onClick:e=>{e.preventDefault(),n("features")},children:"Features"}),(0,pi.jsx)(yt,{to:"/#pricing",onClick:e=>{e.preventDefault(),n("pricing")},children:"Pricing"}),(0,pi.jsx)(yt,{to:"/#faq",onClick:e=>{e.preventDefault(),n("faq")},children:"FAQ"}),(0,pi.jsx)(yt,{to:"/privacy",children:"Privacy Policy"}),(0,pi.jsx)(yt,{to:"/terms",children:"Terms of Service"})]})]})]})})};const Ai=function(){const e=(0,r.useRef)(null),[t,n]=(0,r.useState)(!1),[o,a]=(0,r.useState)(null),i=(ge(),fe()),l=(0,r.useRef)(null),s=(0,r.useRef)(null);return(0,r.useEffect)((()=>{const e=localStorage.getItem("userDetails");if(e){n(!0);try{const t=JSON.parse(e);a(t)}catch(t){console.error("Error parsing user data:",t)}}i.state&&i.state.scrollTo&&setTimeout((()=>{"features"===i.state.scrollTo&&l.current?l.current.scrollIntoView({behavior:"smooth"}):"contact"===i.state.scrollTo&&s.current&&s.current.scrollIntoView({behavior:"smooth"})}),100)}),[i]),(0,pi.jsxs)("div",{className:"home-page",children:[(0,pi.jsx)(Ti,{}),(0,pi.jsxs)("main",{className:"home-content",children:[(0,pi.jsx)("section",{className:"hero-section",children:(0,pi.jsxs)("div",{className:"container",children:[(0,pi.jsx)("h1",{children:"Interview ready."}),(0,pi.jsx)("p",{className:"subtitle",children:"Get Instant answers for your interview questions. Ace your interview. Your dream job is right there"}),(0,pi.jsx)("div",{className:"cta-buttons",children:t?(0,pi.jsxs)(pi.Fragment,{children:[(0,pi.jsx)(yt,{to:"/home",className:"cta-button primary",children:"Start Interview"}),(0,pi.jsxs)("button",{onClick:()=>{localStorage.removeItem("userDetails"),localStorage.removeItem("token"),n(!1),a(null),window.location.reload()},className:"cta-button logout",children:[(0,pi.jsx)(Pi,{className:"logout-icon"}),"Logout"]})]}):(0,pi.jsxs)(pi.Fragment,{children:[(0,pi.jsx)(yt,{to:"/login",className:"cta-button primary",children:"Try now"}),(0,pi.jsx)("a",{href:"#pricing",className:"cta-button secondary",onClick:t=>{var n;t.preventDefault(),null===(n=e.current)||void 0===n||n.scrollIntoView({behavior:"smooth"})},children:"View Plans"})]})})]})}),(0,pi.jsx)("section",{ref:l,id:"features",className:"features-section",children:(0,pi.jsxs)("div",{className:"container",children:[(0,pi.jsx)("h2",{children:"Features"}),(0,pi.jsxs)("div",{className:"features-grid",children:[(0,pi.jsxs)("div",{className:"feature-card",children:[(0,pi.jsx)("div",{className:"feature-icon",children:"\ud83c\udf10"}),(0,pi.jsx)("h3",{children:"Universal Meeting Access"}),(0,pi.jsx)("p",{children:"Our app supports both browser-based and app-based meetings, giving you the flexibility to connect anytime, anywhere\u2014no installations required."})]}),(0,pi.jsxs)("div",{className:"feature-card",children:[(0,pi.jsx)("div",{className:"feature-icon",children:"\ud83d\udd17"}),(0,pi.jsx)("h3",{children:"Seamless Platform Integration"}),(0,pi.jsx)("p",{children:"Seamless integration with all leading meeting platforms, including Zoom, Microsoft Teams, Google Meet, Webex, Skype, Slack, BlueJeans, GoTo Meeting, Whereby, Jitsi Meet, BigBlueButton, Zoho Meeting, Amazon Chime, Adobe Connect, ClickMeeting, Livestorm, RingCentral Video, 8x8 Meet, Pexip, TrueConf and more."})]}),(0,pi.jsxs)("div",{className:"feature-card",children:[(0,pi.jsx)("div",{className:"feature-icon",children:"\ud83c\udfe2"}),(0,pi.jsx)("h3",{children:"All Industries Supported"}),(0,pi.jsx)("p",{children:"Our interview-focused app is proven effective for all industries, including traditional, emerging, and niche sectors like Technology & IT, Healthcare & Medical, Finance & Banking, Education & Academia, Legal Services, Retail & E-commerce, Manufacturing & Production, Construction & Real Estate, Transportation & Logistics, Telecommunications, and more."})]}),(0,pi.jsxs)("div",{className:"feature-card",children:[(0,pi.jsx)("div",{className:"feature-icon",children:"\ud83d\udcbb"}),(0,pi.jsx)("h3",{children:"IT & Tech Interview Mastery"}),(0,pi.jsx)("p",{children:"Specialized for interviews across all IT technologies: Software Development (Frontend, Backend, Full Stack), DevOps & Cloud (AWS, Azure, GCP, CI/CD), Data Science & Analytics (ML, DL, NLP, BI), Cybersecurity, Database Management, Mobile & Web Development, AI/ML, Blockchain, IT Support, ERP/CRM (SAP, Oracle, Salesforce), QA & Testing, Networking, UI/UX, and more."})]}),(0,pi.jsxs)("div",{className:"feature-card",children:[(0,pi.jsx)("div",{className:"feature-icon",children:"\u26a1"}),(0,pi.jsx)("h3",{children:"Instant Coding Solutions"}),(0,pi.jsx)("p",{children:"Ace any coding test\u2014our AI delivers instant, proven, and reliable coding solutions across all technologies. Fast, accurate, and always available."})]}),(0,pi.jsxs)("div",{className:"feature-card highlight",children:[(0,pi.jsx)("div",{className:"feature-icon",children:"\ud83d\ude80"}),(0,pi.jsx)("h3",{children:"Ultra-Low Latency AI"}),(0,pi.jsx)("p",{children:"With ultra-low latency, our AI delivers instant answers\u2014no delays, no waiting. You get real-time support exactly when you need it."})]})]})]})}),(0,pi.jsx)("section",{id:"pricing",className:"pricing-section",ref:e,children:(0,pi.jsxs)("div",{className:"container",children:[(0,pi.jsx)("h2",{children:"Pick your plan"}),(0,pi.jsxs)("div",{className:"pricing-grid",children:[(0,pi.jsxs)("div",{className:"pricing-card",children:[(0,pi.jsxs)("div",{className:"pricing-header",children:[(0,pi.jsx)("h3",{children:"FREE"}),(0,pi.jsxs)("div",{className:"price",children:[(0,pi.jsx)("span",{className:"currency",children:"$"}),(0,pi.jsx)("span",{className:"amount",children:"0"}),(0,pi.jsx)("span",{className:"period",children:"/ 5 mins session"})]})]}),(0,pi.jsxs)("div",{className:"pricing-features",children:[(0,pi.jsxs)("div",{className:"feature-item available",children:[(0,pi.jsx)("span",{className:"check",children:"\u2713"}),(0,pi.jsx)("span",{children:"5 mins session"})]}),(0,pi.jsxs)("div",{className:"feature-item available",children:[(0,pi.jsx)("span",{className:"check",children:"\u2713"}),(0,pi.jsx)("span",{children:"Browser based/app based meeting"})]}),(0,pi.jsxs)("div",{className:"feature-item available",children:[(0,pi.jsx)("span",{className:"check",children:"\u2713"}),(0,pi.jsx)("span",{children:"All meeting platform integration"})]}),(0,pi.jsxs)("div",{className:"feature-item available",children:[(0,pi.jsx)("span",{className:"check",children:"\u2713"}),(0,pi.jsx)("span",{children:"All industry/All Technology"})]}),(0,pi.jsxs)("div",{className:"feature-item available",children:[(0,pi.jsx)("span",{className:"check",children:"\u2713"}),(0,pi.jsx)("span",{children:"Instant Coding solution"})]}),(0,pi.jsxs)("div",{className:"feature-item available",children:[(0,pi.jsx)("span",{className:"check",children:"\u2713"}),(0,pi.jsx)("span",{children:"Instant Image creation"})]}),(0,pi.jsxs)("div",{className:"feature-item available",children:[(0,pi.jsx)("span",{className:"check",children:"\u2713"}),(0,pi.jsx)("span",{children:"Highly Customized AI playground"})]})]}),(0,pi.jsx)(yt,{to:"/signup",className:"pricing-cta",children:"Try for free"})]}),(0,pi.jsxs)("div",{className:"pricing-card",children:[(0,pi.jsxs)("div",{className:"pricing-header",children:[(0,pi.jsx)("h3",{children:"PRO"}),(0,pi.jsxs)("div",{className:"price",children:[(0,pi.jsx)("span",{className:"currency",children:"$"}),(0,pi.jsx)("span",{className:"amount",children:"4.99"}),(0,pi.jsx)("span",{className:"period",children:"/ 1 hour session"})]})]}),(0,pi.jsxs)("div",{className:"pricing-features",children:[(0,pi.jsxs)("div",{className:"feature-item available",children:[(0,pi.jsx)("span",{className:"check",children:"\u2713"}),(0,pi.jsx)("span",{children:"1 hour session"})]}),(0,pi.jsxs)("div",{className:"feature-item available",children:[(0,pi.jsx)("span",{className:"check",children:"\u2713"}),(0,pi.jsx)("span",{children:"Browser based/app based meeting"})]}),(0,pi.jsxs)("div",{className:"feature-item available",children:[(0,pi.jsx)("span",{className:"check",children:"\u2713"}),(0,pi.jsx)("span",{children:"All meeting platform integration"})]}),(0,pi.jsxs)("div",{className:"feature-item available",children:[(0,pi.jsx)("span",{className:"check",children:"\u2713"}),(0,pi.jsx)("span",{children:"All industry/All Technology"})]}),(0,pi.jsxs)("div",{className:"feature-item available",children:[(0,pi.jsx)("span",{className:"check",children:"\u2713"}),(0,pi.jsx)("span",{children:"Instant Coding solution"})]}),(0,pi.jsxs)("div",{className:"feature-item available",children:[(0,pi.jsx)("span",{className:"check",children:"\u2713"}),(0,pi.jsx)("span",{children:"Instant Image creation"})]}),(0,pi.jsxs)("div",{className:"feature-item available",children:[(0,pi.jsx)("span",{className:"check",children:"\u2713"}),(0,pi.jsx)("span",{children:"Highly Customized AI playground"})]})]}),(0,pi.jsx)(yt,{to:"/signup",className:"pricing-cta",children:"Get the plan"})]})]})]})}),(0,pi.jsx)("section",{id:"contact",ref:s,className:"contact-section",children:(0,pi.jsx)(_i,{})})]})]})};let Mi=0;const zi=p({},o).useId;function Ii(e){if(void 0!==zi){const t=zi();return null!==e&&void 0!==e?e:t}return function(e){const[t,n]=r.useState(e),o=e||t;return r.useEffect((()=>{null==t&&(Mi+=1,n("mui-".concat(Mi)))}),[t]),o}(e)}function Oi(e){let t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:166;function r(){for(var r=arguments.length,o=new Array(r),a=0;a<r;a++)o[a]=arguments[a];clearTimeout(t),t=setTimeout((()=>{e.apply(this,o)}),n)}return r.clear=()=>{clearTimeout(t)},r}function Fi(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];const o=r.useRef(void 0),a=r.useCallback((e=>{const n=t.map((t=>{if(null==t)return null;if("function"===typeof t){const n=t,r=n(e);return"function"===typeof r?r:()=>{n(null)}}return t.current=e,()=>{t.current=null}}));return()=>{n.forEach((e=>null===e||void 0===e?void 0:e()))}}),t);return r.useMemo((()=>t.every((e=>null==e))?null:e=>{o.current&&(o.current(),o.current=void 0),null!=e&&(o.current=a(e))}),t)}const Li="undefined"!==typeof window?r.useLayoutEffect:r.useEffect;const Di=function(e){const t=r.useRef(e);return Li((()=>{t.current=e})),r.useRef((function(){return(0,t.current)(...arguments)})).current};function Bi(e){return e&&e.ownerDocument||document}function Wi(e){return Bi(e).defaultView||window}const Hi=["onChange","maxRows","minRows","style","value"];function Ui(e){return parseInt(e,10)||0}const Vi={visibility:"hidden",position:"absolute",overflow:"hidden",height:0,top:0,left:0,transform:"translateZ(0)"};function $i(e){return function(e){for(const t in e)return!1;return!0}(e)||0===e.outerHeightStyle&&!e.overflowing}const Ki=r.forwardRef((function(e,t){const{onChange:n,maxRows:o,minRows:a=1,style:i,value:s}=e,c=l(e,Hi),{current:u}=r.useRef(null!=s),d=r.useRef(null),f=Fi(t,d),h=r.useRef(null),m=r.useRef(null),g=r.useCallback((()=>{const t=d.current,n=m.current;if(!t||!n)return;const r=Wi(t).getComputedStyle(t);if("0px"===r.width)return{outerHeightStyle:0,overflowing:!1};n.style.width=r.width,n.value=t.value||e.placeholder||"x","\n"===n.value.slice(-1)&&(n.value+=" ");const i=r.boxSizing,l=Ui(r.paddingBottom)+Ui(r.paddingTop),s=Ui(r.borderBottomWidth)+Ui(r.borderTopWidth),c=n.scrollHeight;n.value="x";const u=n.scrollHeight;let p=c;a&&(p=Math.max(Number(a)*u,p)),o&&(p=Math.min(Number(o)*u,p)),p=Math.max(p,u);return{outerHeightStyle:p+("border-box"===i?l+s:0),overflowing:Math.abs(p-c)<=1}}),[o,a,e.placeholder]),v=Di((()=>{const e=d.current,t=g();if(!e||!t||$i(t))return!1;const n=t.outerHeightStyle;return null!=h.current&&h.current!==n})),y=r.useCallback((()=>{const e=d.current,t=g();if(!e||!t||$i(t))return;const n=t.outerHeightStyle;h.current!==n&&(h.current=n,e.style.height="".concat(n,"px")),e.style.overflow=t.overflowing?"hidden":""}),[g]),b=r.useRef(-1);Li((()=>{const e=Oi(y),t=null===d||void 0===d?void 0:d.current;if(!t)return;const n=Wi(t);let r;return n.addEventListener("resize",e),"undefined"!==typeof ResizeObserver&&(r=new ResizeObserver((()=>{v()&&(r.unobserve(t),cancelAnimationFrame(b.current),y(),b.current=requestAnimationFrame((()=>{r.observe(t)})))})),r.observe(t)),()=>{e.clear(),cancelAnimationFrame(b.current),n.removeEventListener("resize",e),r&&r.disconnect()}}),[g,y,v]),Li((()=>{y()}));return(0,pi.jsxs)(r.Fragment,{children:[(0,pi.jsx)("textarea",p({value:s,onChange:e=>{u||y();const t=e.target,r=t.value.length,o=t.value.endsWith("\n"),a=t.selectionStart===r;o&&a&&t.setSelectionRange(r,r),n&&n(e)},ref:f,rows:a,style:i},c)),(0,pi.jsx)("textarea",{"aria-hidden":!0,className:e.className,readOnly:!0,ref:m,tabIndex:-1,style:p(p(p({},Vi),i),{},{paddingTop:0,paddingBottom:0})})]})})),qi=Ki;const Yi=function(e){return"string"===typeof e};function Gi(e){let{props:t,states:n,muiFormControl:r}=e;return n.reduce(((e,n)=>(e[n]=t[n],r&&"undefined"===typeof t[n]&&(e[n]=r[n]),e)),{})}const Qi=r.createContext(void 0);function Xi(){return r.useContext(Qi)}const Ji=["sx"];function Zi(e){const{sx:t}=e,n=l(e,Ji),{systemProps:r,otherProps:o}=(e=>{var t,n;const r={systemProps:{},otherProps:{}},o=null!==(t=null===e||void 0===e||null===(n=e.theme)||void 0===n?void 0:n.unstable_sxConfig)&&void 0!==t?t:ko;return Object.keys(e).forEach((t=>{o[t]?r.systemProps[t]=e[t]:r.otherProps[t]=e[t]})),r})(n);let a;return a=Array.isArray(t)?[r,...t]:"function"===typeof t?function(){const e=t(...arguments);return vr(e)?p(p({},r),e):r}:p(p({},r),t),p(p({},o),{},{sx:a})}n(219);var el=function(e,t){var n=arguments;if(null==t||!rr.call(t,"css"))return r.createElement.apply(void 0,n);var o=n.length,a=new Array(o);a[0]=ir,a[1]=function(e,t){var n={};for(var r in t)rr.call(t,r)&&(n[r]=t[r]);return n[or]=e,n}(e,t);for(var i=2;i<o;i++)a[i]=n[i];return r.createElement.apply(null,a)};!function(e){var t;t||(t=e.JSX||(e.JSX={}))}(el||(el={}));var tl=tr((function(e,t){var n=Qn([e.styles],void 0,r.useContext(nr)),o=r.useRef();return Zn((function(){var e=t.key+"-global",r=new t.sheet.constructor({key:e,nonce:t.sheet.nonce,container:t.sheet.container,speedy:t.sheet.isSpeedy}),a=!1,i=document.querySelector('style[data-emotion="'+e+" "+n.name+'"]');return t.sheet.tags.length&&(r.before=t.sheet.tags[0]),null!==i&&(a=!0,i.setAttribute("data-emotion",e),r.hydrate([i])),o.current=[r,a],function(){r.flush()}}),[t]),Zn((function(){var e=o.current,r=e[0];if(e[1])e[1]=!1;else{if(void 0!==n.next&&Ln(t,n.next,!0),r.tags.length){var a=r.tags[r.tags.length-1].nextElementSibling;r.before=a,r.flush()}t.insert("",n,r,!1)}}),[t,n.name]),null}));function nl(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return Qn(t)}function rl(){var e=nl.apply(void 0,arguments),t="animation-"+e.name;return{name:t,styles:"@keyframes "+t+"{"+e.styles+"}",anim:1,toString:function(){return"_EMO_"+this.name+"_"+this.styles+"_EMO_"}}}function ol(e){const{styles:t,defaultTheme:n={}}=e,r="function"===typeof t?e=>{return t(void 0===(r=e)||null===r||0===Object.keys(r).length?n:e);var r}:t;return(0,pi.jsx)(tl,{styles:r})}const al=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;const t=r.useContext(nr);return t&&(n=t,0!==Object.keys(n).length)?t:e;var n},il=Po();const ll=function(){return al(arguments.length>0&&void 0!==arguments[0]?arguments[0]:il)};const sl=function(e){let{styles:t,themeId:n,defaultTheme:r={}}=e;const o=ll(r),a="function"===typeof t?t(n&&o[n]||o):t;return(0,pi.jsx)(ol,{styles:a})};const cl=function(e){return(0,pi.jsx)(sl,p(p({},e),{},{defaultTheme:ri,themeId:oi}))};const ul=Fi,dl=Li;function pl(e){return null!=e&&!(Array.isArray(e)&&0===e.length)}function fl(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return e&&(pl(e.value)&&""!==e.value||t&&pl(e.defaultValue)&&""!==e.defaultValue)}function hl(e){return bi("MuiInputBase",e)}const ml=xi("MuiInputBase",["root","formControl","focused","disabled","adornedStart","adornedEnd","error","sizeSmall","multiline","colorSecondary","fullWidth","hiddenLabel","readOnly","input","inputSizeSmall","inputMultiline","inputTypeSearch","inputAdornedStart","inputAdornedEnd","inputHiddenLabel"]),gl=["aria-describedby","autoComplete","autoFocus","className","color","components","componentsProps","defaultValue","disabled","disableInjectingGlobalStyles","endAdornment","error","fullWidth","id","inputComponent","inputProps","inputRef","margin","maxRows","minRows","multiline","name","onBlur","onChange","onClick","onFocus","onKeyDown","onKeyUp","placeholder","readOnly","renderSuffix","rows","size","slotProps","slots","startAdornment","type","value"];var vl;const yl=(e,t)=>{const{ownerState:n}=e;return[t.root,n.formControl&&t.formControl,n.startAdornment&&t.adornedStart,n.endAdornment&&t.adornedEnd,n.error&&t.error,"small"===n.size&&t.sizeSmall,n.multiline&&t.multiline,n.color&&t["color".concat(_t(n.color))],n.fullWidth&&t.fullWidth,n.hiddenLabel&&t.hiddenLabel]},bl=(e,t)=>{const{ownerState:n}=e;return[t.input,"small"===n.size&&t.inputSizeSmall,n.multiline&&t.inputMultiline,"search"===n.type&&t.inputTypeSearch,n.startAdornment&&t.inputAdornedStart,n.endAdornment&&t.inputAdornedEnd,n.hiddenLabel&&t.inputHiddenLabel]},xl=si("div",{name:"MuiInputBase",slot:"Root",overridesResolver:yl})(ui((e=>{let{theme:t}=e;return p(p({},t.typography.body1),{},{color:(t.vars||t).palette.text.primary,lineHeight:"1.4375em",boxSizing:"border-box",position:"relative",cursor:"text",display:"inline-flex",alignItems:"center",["&.".concat(ml.disabled)]:{color:(t.vars||t).palette.text.disabled,cursor:"default"},variants:[{props:e=>{let{ownerState:t}=e;return t.multiline},style:{padding:"4px 0 5px"}},{props:e=>{let{ownerState:t,size:n}=e;return t.multiline&&"small"===n},style:{paddingTop:1}},{props:e=>{let{ownerState:t}=e;return t.fullWidth},style:{width:"100%"}}]})}))),wl=si("input",{name:"MuiInputBase",slot:"Input",overridesResolver:bl})(ui((e=>{let{theme:t}=e;const n="light"===t.palette.mode,r=p(p({color:"currentColor"},t.vars?{opacity:t.vars.opacity.inputPlaceholder}:{opacity:n?.42:.5}),{},{transition:t.transitions.create("opacity",{duration:t.transitions.duration.shorter})}),o={opacity:"0 !important"},a=t.vars?{opacity:t.vars.opacity.inputPlaceholder}:{opacity:n?.42:.5};return{font:"inherit",letterSpacing:"inherit",color:"currentColor",padding:"4px 0 5px",border:0,boxSizing:"content-box",background:"none",height:"1.4375em",margin:0,WebkitTapHighlightColor:"transparent",display:"block",minWidth:0,width:"100%","&::-webkit-input-placeholder":r,"&::-moz-placeholder":r,"&::-ms-input-placeholder":r,"&:focus":{outline:0},"&:invalid":{boxShadow:"none"},"&::-webkit-search-decoration":{WebkitAppearance:"none"},["label[data-shrink=false] + .".concat(ml.formControl," &")]:{"&::-webkit-input-placeholder":o,"&::-moz-placeholder":o,"&::-ms-input-placeholder":o,"&:focus::-webkit-input-placeholder":a,"&:focus::-moz-placeholder":a,"&:focus::-ms-input-placeholder":a},["&.".concat(ml.disabled)]:{opacity:1,WebkitTextFillColor:(t.vars||t).palette.text.disabled},variants:[{props:e=>{let{ownerState:t}=e;return!t.disableInjectingGlobalStyles},style:{animationName:"mui-auto-fill-cancel",animationDuration:"10ms","&:-webkit-autofill":{animationDuration:"5000s",animationName:"mui-auto-fill"}}},{props:{size:"small"},style:{paddingTop:1}},{props:e=>{let{ownerState:t}=e;return t.multiline},style:{height:"auto",resize:"none",padding:0,paddingTop:0}},{props:{type:"search"},style:{MozAppearance:"textfield"}}]}}))),Sl=function(e){return function(t){return(0,pi.jsx)(cl,{styles:"function"===typeof e?n=>e(p({theme:n},t)):e})}}({"@keyframes mui-auto-fill":{from:{display:"block"}},"@keyframes mui-auto-fill-cancel":{from:{display:"block"}}}),kl=r.forwardRef((function(e,t){var n;const o=mi({props:e,name:"MuiInputBase"}),{"aria-describedby":a,autoComplete:i,autoFocus:s,className:c,color:u,components:d={},componentsProps:f={},defaultValue:h,disabled:m,disableInjectingGlobalStyles:g,endAdornment:v,error:y,fullWidth:b=!1,id:x,inputComponent:w="input",inputProps:S={},inputRef:k,margin:C,maxRows:E,minRows:j,multiline:N=!1,name:P,onBlur:R,onChange:T,onClick:_,onFocus:A,onKeyDown:M,onKeyUp:z,placeholder:I,readOnly:O,renderSuffix:F,rows:L,size:D,slotProps:B={},slots:W={},startAdornment:H,type:U="text",value:V}=o,$=l(o,gl),K=null!=S.value?S.value:V,{current:q}=r.useRef(null!=K),Y=r.useRef(),G=r.useCallback((e=>{0}),[]),Q=ul(Y,k,S.ref,G),[X,J]=r.useState(!1),Z=Xi();const ee=Gi({props:o,muiFormControl:Z,states:["color","disabled","error","hiddenLabel","size","required","filled"]});ee.focused=Z?Z.focused:X,r.useEffect((()=>{!Z&&m&&X&&(J(!1),R&&R())}),[Z,m,X,R]);const te=Z&&Z.onFilled,ne=Z&&Z.onEmpty,re=r.useCallback((e=>{fl(e)?te&&te():ne&&ne()}),[te,ne]);dl((()=>{q&&re({value:K})}),[K,re,q]);r.useEffect((()=>{re(Y.current)}),[]);let oe=w,ae=S;N&&"input"===oe&&(ae=p(L?{type:void 0,minRows:L,maxRows:L}:{type:void 0,maxRows:E,minRows:j},ae),oe=qi);r.useEffect((()=>{Z&&Z.setAdornedStart(Boolean(H))}),[Z,H]);const ie=p(p({},o),{},{color:ee.color||"primary",disabled:ee.disabled,endAdornment:v,error:ee.error,focused:ee.focused,formControl:Z,fullWidth:b,hiddenLabel:ee.hiddenLabel,multiline:N,size:ee.size,startAdornment:H,type:U}),le=(e=>{const{classes:t,color:n,disabled:r,error:o,endAdornment:a,focused:i,formControl:l,fullWidth:s,hiddenLabel:c,multiline:u,readOnly:d,size:p,startAdornment:f,type:h}=e;return Pt({root:["root","color".concat(_t(n)),r&&"disabled",o&&"error",s&&"fullWidth",i&&"focused",l&&"formControl",p&&"medium"!==p&&"size".concat(_t(p)),u&&"multiline",f&&"adornedStart",a&&"adornedEnd",c&&"hiddenLabel",d&&"readOnly"],input:["input",r&&"disabled","search"===h&&"inputTypeSearch",u&&"inputMultiline","small"===p&&"inputSizeSmall",c&&"inputHiddenLabel",f&&"inputAdornedStart",a&&"inputAdornedEnd",d&&"readOnly"]},hl,t)})(ie),se=W.root||d.Root||xl,ce=B.root||f.root||{},ue=W.input||d.Input||wl;return ae=p(p({},ae),null!==(n=B.input)&&void 0!==n?n:f.input),(0,pi.jsxs)(r.Fragment,{children:[!g&&"function"===typeof Sl&&(vl||(vl=(0,pi.jsx)(Sl,{}))),(0,pi.jsxs)(se,p(p(p(p({},ce),{},{ref:t,onClick:e=>{Y.current&&e.currentTarget===e.target&&Y.current.focus(),_&&_(e)}},$),!Yi(se)&&{ownerState:p(p({},ie),ce.ownerState)}),{},{className:Nt(le.root,ce.className,c,O&&"MuiInputBase-readOnly"),children:[H,(0,pi.jsx)(Qi.Provider,{value:null,children:(0,pi.jsx)(ue,p(p(p({"aria-invalid":ee.error,"aria-describedby":a,autoComplete:i,autoFocus:s,defaultValue:h,disabled:ee.disabled,id:x,onAnimationStart:e=>{re("mui-auto-fill-cancel"===e.animationName?Y.current:{value:"x"})},name:P,placeholder:I,readOnly:O,required:ee.required,rows:L,value:K,onKeyDown:M,onKeyUp:z,type:U},ae),!Yi(ue)&&{as:oe,ownerState:p(p({},ie),ae.ownerState)}),{},{ref:Q,className:Nt(le.input,ae.className,O&&"MuiInputBase-readOnly"),onBlur:e=>{R&&R(e),S.onBlur&&S.onBlur(e),Z&&Z.onBlur?Z.onBlur(e):J(!1)},onChange:function(e){if(!q){const t=e.target||Y.current;if(null==t)throw new Error(Rt(1));re({value:t.value})}for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];S.onChange&&S.onChange(e,...n),T&&T(e,...n)},onFocus:e=>{A&&A(e),S.onFocus&&S.onFocus(e),Z&&Z.onFocus?Z.onFocus(e):J(!0)}}))}),v,F?F(p(p({},ee),{},{startAdornment:H})):null]}))]})})),Cl=kl;function El(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t=>{let[,n]=t;return n&&function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];if(!function(e){return"string"===typeof e.main}(e))return!1;for(const n of t)if(!e.hasOwnProperty(n)||"string"!==typeof e[n])return!1;return!0}(n,e)}}function jl(e){return bi("MuiInput",e)}const Nl=p(p({},ml),xi("MuiInput",["root","underline","input"])),Pl=["disableUnderline","components","componentsProps","fullWidth","inputComponent","multiline","slotProps","slots","type"],Rl=si(xl,{shouldForwardProp:e=>ii(e)||"classes"===e,name:"MuiInput",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[...yl(e,t),!n.disableUnderline&&t.underline]}})(ui((e=>{let{theme:t}=e;let n="light"===t.palette.mode?"rgba(0, 0, 0, 0.42)":"rgba(255, 255, 255, 0.7)";return t.vars&&(n="rgba(".concat(t.vars.palette.common.onBackgroundChannel," / ").concat(t.vars.opacity.inputUnderline,")")),{position:"relative",variants:[{props:e=>{let{ownerState:t}=e;return t.formControl},style:{"label + &":{marginTop:16}}},{props:e=>{let{ownerState:t}=e;return!t.disableUnderline},style:{"&::after":{left:0,bottom:0,content:'""',position:"absolute",right:0,transform:"scaleX(0)",transition:t.transitions.create("transform",{duration:t.transitions.duration.shorter,easing:t.transitions.easing.easeOut}),pointerEvents:"none"},["&.".concat(Nl.focused,":after")]:{transform:"scaleX(1) translateX(0)"},["&.".concat(Nl.error)]:{"&::before, &::after":{borderBottomColor:(t.vars||t).palette.error.main}},"&::before":{borderBottom:"1px solid ".concat(n),left:0,bottom:0,content:'"\\00a0"',position:"absolute",right:0,transition:t.transitions.create("border-bottom-color",{duration:t.transitions.duration.shorter}),pointerEvents:"none"},["&:hover:not(.".concat(Nl.disabled,", .").concat(Nl.error,"):before")]:{borderBottom:"2px solid ".concat((t.vars||t).palette.text.primary),"@media (hover: none)":{borderBottom:"1px solid ".concat(n)}},["&.".concat(Nl.disabled,":before")]:{borderBottomStyle:"dotted"}}},...Object.entries(t.palette).filter(El()).map((e=>{let[n]=e;return{props:{color:n,disableUnderline:!1},style:{"&::after":{borderBottom:"2px solid ".concat((t.vars||t).palette[n].main)}}}}))]}}))),Tl=si(wl,{name:"MuiInput",slot:"Input",overridesResolver:bl})({}),_l=r.forwardRef((function(e,t){var n,r,o,a;const i=mi({props:e,name:"MuiInput"}),{disableUnderline:s=!1,components:c={},componentsProps:u,fullWidth:d=!1,inputComponent:f="input",multiline:h=!1,slotProps:m,slots:g={},type:v="text"}=i,y=l(i,Pl),b=(e=>{const{classes:t,disableUnderline:n}=e,r=Pt({root:["root",!n&&"underline"],input:["input"]},jl,t);return p(p({},t),r)})(i),x={root:{ownerState:{disableUnderline:s}}},w=(null!==m&&void 0!==m?m:u)?br(null!==m&&void 0!==m?m:u,x):x,S=null!==(n=null!==(r=g.root)&&void 0!==r?r:c.Root)&&void 0!==n?n:Rl,k=null!==(o=null!==(a=g.input)&&void 0!==a?a:c.Input)&&void 0!==o?o:Tl;return(0,pi.jsx)(Cl,p(p({slots:{root:S,input:k},slotProps:w,fullWidth:d,inputComponent:f,multiline:h,ref:t,type:v},y),{},{classes:b}))}));_l.muiName="Input";const Al=_l;function Ml(e){return bi("MuiFilledInput",e)}const zl=p(p({},ml),xi("MuiFilledInput",["root","underline","input","adornedStart","adornedEnd","sizeSmall","multiline","hiddenLabel"])),Il=["disableUnderline","components","componentsProps","fullWidth","hiddenLabel","inputComponent","multiline","slotProps","slots","type"],Ol=si(xl,{shouldForwardProp:e=>ii(e)||"classes"===e,name:"MuiFilledInput",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[...yl(e,t),!n.disableUnderline&&t.underline]}})(ui((e=>{let{theme:t}=e;const n="light"===t.palette.mode,r=n?"rgba(0, 0, 0, 0.42)":"rgba(255, 255, 255, 0.7)",o=n?"rgba(0, 0, 0, 0.06)":"rgba(255, 255, 255, 0.09)",a=n?"rgba(0, 0, 0, 0.09)":"rgba(255, 255, 255, 0.13)",i=n?"rgba(0, 0, 0, 0.12)":"rgba(255, 255, 255, 0.12)";return{position:"relative",backgroundColor:t.vars?t.vars.palette.FilledInput.bg:o,borderTopLeftRadius:(t.vars||t).shape.borderRadius,borderTopRightRadius:(t.vars||t).shape.borderRadius,transition:t.transitions.create("background-color",{duration:t.transitions.duration.shorter,easing:t.transitions.easing.easeOut}),"&:hover":{backgroundColor:t.vars?t.vars.palette.FilledInput.hoverBg:a,"@media (hover: none)":{backgroundColor:t.vars?t.vars.palette.FilledInput.bg:o}},["&.".concat(zl.focused)]:{backgroundColor:t.vars?t.vars.palette.FilledInput.bg:o},["&.".concat(zl.disabled)]:{backgroundColor:t.vars?t.vars.palette.FilledInput.disabledBg:i},variants:[{props:e=>{let{ownerState:t}=e;return!t.disableUnderline},style:{"&::after":{left:0,bottom:0,content:'""',position:"absolute",right:0,transform:"scaleX(0)",transition:t.transitions.create("transform",{duration:t.transitions.duration.shorter,easing:t.transitions.easing.easeOut}),pointerEvents:"none"},["&.".concat(zl.focused,":after")]:{transform:"scaleX(1) translateX(0)"},["&.".concat(zl.error)]:{"&::before, &::after":{borderBottomColor:(t.vars||t).palette.error.main}},"&::before":{borderBottom:"1px solid ".concat(t.vars?"rgba(".concat(t.vars.palette.common.onBackgroundChannel," / ").concat(t.vars.opacity.inputUnderline,")"):r),left:0,bottom:0,content:'"\\00a0"',position:"absolute",right:0,transition:t.transitions.create("border-bottom-color",{duration:t.transitions.duration.shorter}),pointerEvents:"none"},["&:hover:not(.".concat(zl.disabled,", .").concat(zl.error,"):before")]:{borderBottom:"1px solid ".concat((t.vars||t).palette.text.primary)},["&.".concat(zl.disabled,":before")]:{borderBottomStyle:"dotted"}}},...Object.entries(t.palette).filter(El()).map((e=>{var n;let[r]=e;return{props:{disableUnderline:!1,color:r},style:{"&::after":{borderBottom:"2px solid ".concat(null===(n=(t.vars||t).palette[r])||void 0===n?void 0:n.main)}}}})),{props:e=>{let{ownerState:t}=e;return t.startAdornment},style:{paddingLeft:12}},{props:e=>{let{ownerState:t}=e;return t.endAdornment},style:{paddingRight:12}},{props:e=>{let{ownerState:t}=e;return t.multiline},style:{padding:"25px 12px 8px"}},{props:e=>{let{ownerState:t,size:n}=e;return t.multiline&&"small"===n},style:{paddingTop:21,paddingBottom:4}},{props:e=>{let{ownerState:t}=e;return t.multiline&&t.hiddenLabel},style:{paddingTop:16,paddingBottom:17}},{props:e=>{let{ownerState:t}=e;return t.multiline&&t.hiddenLabel&&"small"===t.size},style:{paddingTop:8,paddingBottom:9}}]}}))),Fl=si(wl,{name:"MuiFilledInput",slot:"Input",overridesResolver:bl})(ui((e=>{let{theme:t}=e;return p(p(p({paddingTop:25,paddingRight:12,paddingBottom:8,paddingLeft:12},!t.vars&&{"&:-webkit-autofill":{WebkitBoxShadow:"light"===t.palette.mode?null:"0 0 0 100px #266798 inset",WebkitTextFillColor:"light"===t.palette.mode?null:"#fff",caretColor:"light"===t.palette.mode?null:"#fff",borderTopLeftRadius:"inherit",borderTopRightRadius:"inherit"}}),t.vars&&{"&:-webkit-autofill":{borderTopLeftRadius:"inherit",borderTopRightRadius:"inherit"},[t.getColorSchemeSelector("dark")]:{"&:-webkit-autofill":{WebkitBoxShadow:"0 0 0 100px #266798 inset",WebkitTextFillColor:"#fff",caretColor:"#fff"}}}),{},{variants:[{props:{size:"small"},style:{paddingTop:21,paddingBottom:4}},{props:e=>{let{ownerState:t}=e;return t.hiddenLabel},style:{paddingTop:16,paddingBottom:17}},{props:e=>{let{ownerState:t}=e;return t.startAdornment},style:{paddingLeft:0}},{props:e=>{let{ownerState:t}=e;return t.endAdornment},style:{paddingRight:0}},{props:e=>{let{ownerState:t}=e;return t.hiddenLabel&&"small"===t.size},style:{paddingTop:8,paddingBottom:9}},{props:e=>{let{ownerState:t}=e;return t.multiline},style:{paddingTop:0,paddingBottom:0,paddingLeft:0,paddingRight:0}}]})}))),Ll=r.forwardRef((function(e,t){var n,r,o,a;const i=mi({props:e,name:"MuiFilledInput"}),{disableUnderline:s=!1,components:c={},componentsProps:u,fullWidth:d=!1,hiddenLabel:f,inputComponent:h="input",multiline:m=!1,slotProps:g,slots:v={},type:y="text"}=i,b=l(i,Il),x=p(p({},i),{},{disableUnderline:s,fullWidth:d,inputComponent:h,multiline:m,type:y}),w=(e=>{const{classes:t,disableUnderline:n,startAdornment:r,endAdornment:o,size:a,hiddenLabel:i,multiline:l}=e,s=Pt({root:["root",!n&&"underline",r&&"adornedStart",o&&"adornedEnd","small"===a&&"size".concat(_t(a)),i&&"hiddenLabel",l&&"multiline"],input:["input"]},Ml,t);return p(p({},t),s)})(i),S={root:{ownerState:x},input:{ownerState:x}},k=(null!==g&&void 0!==g?g:u)?br(S,null!==g&&void 0!==g?g:u):S,C=null!==(n=null!==(r=v.root)&&void 0!==r?r:c.Root)&&void 0!==n?n:Ol,E=null!==(o=null!==(a=v.input)&&void 0!==a?a:c.Input)&&void 0!==o?o:Fl;return(0,pi.jsx)(Cl,p(p({slots:{root:C,input:E},slotProps:k,fullWidth:d,inputComponent:h,multiline:m,ref:t,type:y},b),{},{classes:w}))}));Ll.muiName="Input";const Dl=Ll,Bl=["children","classes","className","label","notched"];var Wl;const Hl=si("fieldset",{shouldForwardProp:ii})({textAlign:"left",position:"absolute",bottom:0,right:0,top:-5,left:0,margin:0,padding:"0 8px",pointerEvents:"none",borderRadius:"inherit",borderStyle:"solid",borderWidth:1,overflow:"hidden",minWidth:"0%"}),Ul=si("legend",{shouldForwardProp:ii})(ui((e=>{let{theme:t}=e;return{float:"unset",width:"auto",overflow:"hidden",variants:[{props:e=>{let{ownerState:t}=e;return!t.withLabel},style:{padding:0,lineHeight:"11px",transition:t.transitions.create("width",{duration:150,easing:t.transitions.easing.easeOut})}},{props:e=>{let{ownerState:t}=e;return t.withLabel},style:{display:"block",padding:0,height:11,fontSize:"0.75em",visibility:"hidden",maxWidth:.01,transition:t.transitions.create("max-width",{duration:50,easing:t.transitions.easing.easeOut}),whiteSpace:"nowrap","& > span":{paddingLeft:5,paddingRight:5,display:"inline-block",opacity:0,visibility:"visible"}}},{props:e=>{let{ownerState:t}=e;return t.withLabel&&t.notched},style:{maxWidth:"100%",transition:t.transitions.create("max-width",{duration:100,easing:t.transitions.easing.easeOut,delay:50})}}]}})));function Vl(e){return bi("MuiOutlinedInput",e)}const $l=p(p({},ml),xi("MuiOutlinedInput",["root","notchedOutline","input"]));const Kl=function(e){return"string"===typeof e};const ql=function(e,t,n){return void 0===e||Kl(e)?t:p(p({},t),{},{ownerState:p(p({},t.ownerState),n)})};const Yl=function(e,t,n){return"function"===typeof e?e(t,n):e};const Gl=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];if(void 0===e)return{};const n={};return Object.keys(e).filter((n=>n.match(/^on[A-Z]/)&&"function"===typeof e[n]&&!t.includes(n))).forEach((t=>{n[t]=e[t]})),n};const Ql=function(e){if(void 0===e)return{};const t={};return Object.keys(e).filter((t=>!(t.match(/^on[A-Z]/)&&"function"===typeof e[t]))).forEach((n=>{t[n]=e[n]})),t};const Xl=function(e){const{getSlotProps:t,additionalProps:n,externalSlotProps:r,externalForwardedProps:o,className:a}=e;if(!t){const e=Nt(null===n||void 0===n?void 0:n.className,a,null===o||void 0===o?void 0:o.className,null===r||void 0===r?void 0:r.className),t=p(p(p({},null===n||void 0===n?void 0:n.style),null===o||void 0===o?void 0:o.style),null===r||void 0===r?void 0:r.style),i=p(p(p({},n),o),r);return e.length>0&&(i.className=e),Object.keys(t).length>0&&(i.style=t),{props:i,internalRef:void 0}}const i=Gl(p(p({},o),r)),l=Ql(r),s=Ql(o),c=t(i),u=Nt(null===c||void 0===c?void 0:c.className,null===n||void 0===n?void 0:n.className,a,null===o||void 0===o?void 0:o.className,null===r||void 0===r?void 0:r.className),d=p(p(p(p({},null===c||void 0===c?void 0:c.style),null===n||void 0===n?void 0:n.style),null===o||void 0===o?void 0:o.style),null===r||void 0===r?void 0:r.style),f=p(p(p(p({},c),n),s),l);return u.length>0&&(f.className=u),Object.keys(d).length>0&&(f.style=d),{props:f,internalRef:c.ref}},Jl=["className","elementType","ownerState","externalForwardedProps","internalForwardedProps","shouldForwardComponentProp"],Zl=["component","slots","slotProps"],es=["component"];function ts(e,t){const{className:n,elementType:r,ownerState:o,externalForwardedProps:a,internalForwardedProps:i,shouldForwardComponentProp:s=!1}=t,c=l(t,Jl),{component:u,slots:d={[e]:void 0},slotProps:f={[e]:void 0}}=a,h=l(a,Zl),m=d[e]||r,g=Yl(f[e],o),v=Xl(p(p({className:n},c),{},{externalForwardedProps:"root"===e?h:void 0,externalSlotProps:g})),{props:{component:y},internalRef:b}=v,x=l(v.props,es),w=Fi(b,null===g||void 0===g?void 0:g.ref,t.ref),S="root"===e?y||u:y;return[m,ql(m,p(p(p(p(p(p({},"root"===e&&!u&&!d[e]&&i),"root"!==e&&!d[e]&&i),x),S&&!s&&{as:S}),S&&s&&{component:S}),{},{ref:w}),o)]}const ns=["components","fullWidth","inputComponent","label","multiline","notched","slots","slotProps","type"],rs=si(xl,{shouldForwardProp:e=>ii(e)||"classes"===e,name:"MuiOutlinedInput",slot:"Root",overridesResolver:yl})(ui((e=>{let{theme:t}=e;const n="light"===t.palette.mode?"rgba(0, 0, 0, 0.23)":"rgba(255, 255, 255, 0.23)";return{position:"relative",borderRadius:(t.vars||t).shape.borderRadius,["&:hover .".concat($l.notchedOutline)]:{borderColor:(t.vars||t).palette.text.primary},"@media (hover: none)":{["&:hover .".concat($l.notchedOutline)]:{borderColor:t.vars?"rgba(".concat(t.vars.palette.common.onBackgroundChannel," / 0.23)"):n}},["&.".concat($l.focused," .").concat($l.notchedOutline)]:{borderWidth:2},variants:[...Object.entries(t.palette).filter(El()).map((e=>{let[n]=e;return{props:{color:n},style:{["&.".concat($l.focused," .").concat($l.notchedOutline)]:{borderColor:(t.vars||t).palette[n].main}}}})),{props:{},style:{["&.".concat($l.error," .").concat($l.notchedOutline)]:{borderColor:(t.vars||t).palette.error.main},["&.".concat($l.disabled," .").concat($l.notchedOutline)]:{borderColor:(t.vars||t).palette.action.disabled}}},{props:e=>{let{ownerState:t}=e;return t.startAdornment},style:{paddingLeft:14}},{props:e=>{let{ownerState:t}=e;return t.endAdornment},style:{paddingRight:14}},{props:e=>{let{ownerState:t}=e;return t.multiline},style:{padding:"16.5px 14px"}},{props:e=>{let{ownerState:t,size:n}=e;return t.multiline&&"small"===n},style:{padding:"8.5px 14px"}}]}}))),os=si((function(e){const{children:t,classes:n,className:r,label:o,notched:a}=e,i=l(e,Bl),s=null!=o&&""!==o,c=p(p({},e),{},{notched:a,withLabel:s});return(0,pi.jsx)(Hl,p(p({"aria-hidden":!0,className:r,ownerState:c},i),{},{children:(0,pi.jsx)(Ul,{ownerState:c,children:s?(0,pi.jsx)("span",{children:o}):Wl||(Wl=(0,pi.jsx)("span",{className:"notranslate","aria-hidden":!0,children:"\u200b"}))})}))}),{name:"MuiOutlinedInput",slot:"NotchedOutline"})(ui((e=>{let{theme:t}=e;const n="light"===t.palette.mode?"rgba(0, 0, 0, 0.23)":"rgba(255, 255, 255, 0.23)";return{borderColor:t.vars?"rgba(".concat(t.vars.palette.common.onBackgroundChannel," / 0.23)"):n}}))),as=si(wl,{name:"MuiOutlinedInput",slot:"Input",overridesResolver:bl})(ui((e=>{let{theme:t}=e;return p(p(p({padding:"16.5px 14px"},!t.vars&&{"&:-webkit-autofill":{WebkitBoxShadow:"light"===t.palette.mode?null:"0 0 0 100px #266798 inset",WebkitTextFillColor:"light"===t.palette.mode?null:"#fff",caretColor:"light"===t.palette.mode?null:"#fff",borderRadius:"inherit"}}),t.vars&&{"&:-webkit-autofill":{borderRadius:"inherit"},[t.getColorSchemeSelector("dark")]:{"&:-webkit-autofill":{WebkitBoxShadow:"0 0 0 100px #266798 inset",WebkitTextFillColor:"#fff",caretColor:"#fff"}}}),{},{variants:[{props:{size:"small"},style:{padding:"8.5px 14px"}},{props:e=>{let{ownerState:t}=e;return t.multiline},style:{padding:0}},{props:e=>{let{ownerState:t}=e;return t.startAdornment},style:{paddingLeft:0}},{props:e=>{let{ownerState:t}=e;return t.endAdornment},style:{paddingRight:0}}]})}))),is=r.forwardRef((function(e,t){var n,o,a,i;const s=mi({props:e,name:"MuiOutlinedInput"}),{components:c={},fullWidth:u=!1,inputComponent:d="input",label:f,multiline:h=!1,notched:m,slots:g={},slotProps:v={},type:y="text"}=s,b=l(s,ns),x=(e=>{const{classes:t}=e,n=Pt({root:["root"],notchedOutline:["notchedOutline"],input:["input"]},Vl,t);return p(p({},t),n)})(s),w=Xi(),S=Gi({props:s,muiFormControl:w,states:["color","disabled","error","focused","hiddenLabel","size","required"]}),k=p(p({},s),{},{color:S.color||"primary",disabled:S.disabled,error:S.error,focused:S.focused,formControl:w,fullWidth:u,hiddenLabel:S.hiddenLabel,multiline:h,size:S.size,type:y}),C=null!==(n=null!==(o=g.root)&&void 0!==o?o:c.Root)&&void 0!==n?n:rs,E=null!==(a=null!==(i=g.input)&&void 0!==i?i:c.Input)&&void 0!==a?a:as,[j,N]=ts("notchedOutline",{elementType:os,className:x.notchedOutline,shouldForwardComponentProp:!0,ownerState:k,externalForwardedProps:{slots:g,slotProps:v},additionalProps:{label:null!=f&&""!==f&&S.required?(0,pi.jsxs)(r.Fragment,{children:[f,"\u2009","*"]}):f}});return(0,pi.jsx)(Cl,p(p({slots:{root:C,input:E},slotProps:v,renderSuffix:e=>(0,pi.jsx)(j,p(p({},N),{},{notched:"undefined"!==typeof m?m:Boolean(e.startAdornment||e.filled||e.focused)})),fullWidth:u,inputComponent:d,multiline:h,ref:t,type:y},b),{},{classes:p(p({},x),{},{notchedOutline:null})}))}));is.muiName="Input";const ls=is;function ss(e){return bi("MuiFormLabel",e)}const cs=xi("MuiFormLabel",["root","colorSecondary","focused","disabled","error","filled","required","asterisk"]),us=["children","className","color","component","disabled","error","filled","focused","required"],ds=si("label",{name:"MuiFormLabel",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,"secondary"===n.color&&t.colorSecondary,n.filled&&t.filled]}})(ui((e=>{let{theme:t}=e;return p(p({color:(t.vars||t).palette.text.secondary},t.typography.body1),{},{lineHeight:"1.4375em",padding:0,position:"relative",variants:[...Object.entries(t.palette).filter(El()).map((e=>{let[n]=e;return{props:{color:n},style:{["&.".concat(cs.focused)]:{color:(t.vars||t).palette[n].main}}}})),{props:{},style:{["&.".concat(cs.disabled)]:{color:(t.vars||t).palette.text.disabled},["&.".concat(cs.error)]:{color:(t.vars||t).palette.error.main}}}]})}))),ps=si("span",{name:"MuiFormLabel",slot:"Asterisk"})(ui((e=>{let{theme:t}=e;return{["&.".concat(cs.error)]:{color:(t.vars||t).palette.error.main}}}))),fs=r.forwardRef((function(e,t){const n=mi({props:e,name:"MuiFormLabel"}),{children:r,className:o,color:a,component:i="label",disabled:s,error:c,filled:u,focused:d,required:f}=n,h=l(n,us),m=Gi({props:n,muiFormControl:Xi(),states:["color","required","focused","disabled","error","filled"]}),g=p(p({},n),{},{color:m.color||"primary",component:i,disabled:m.disabled,error:m.error,filled:m.filled,focused:m.focused,required:m.required}),v=(e=>{const{classes:t,color:n,focused:r,disabled:o,error:a,filled:i,required:l}=e;return Pt({root:["root","color".concat(_t(n)),o&&"disabled",a&&"error",i&&"filled",r&&"focused",l&&"required"],asterisk:["asterisk",a&&"error"]},ss,t)})(g);return(0,pi.jsxs)(ds,p(p({as:i,ownerState:g,className:Nt(v.root,o),ref:t},h),{},{children:[r,m.required&&(0,pi.jsxs)(ps,{ownerState:g,"aria-hidden":!0,className:v.asterisk,children:["\u2009","*"]})]}))})),hs=fs;function ms(e){return bi("MuiInputLabel",e)}xi("MuiInputLabel",["root","focused","disabled","error","required","asterisk","formControl","sizeSmall","shrink","animated","standard","filled","outlined"]);const gs=["disableAnimation","margin","shrink","variant","className"],vs=si(hs,{shouldForwardProp:e=>ii(e)||"classes"===e,name:"MuiInputLabel",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[{["& .".concat(cs.asterisk)]:t.asterisk},t.root,n.formControl&&t.formControl,"small"===n.size&&t.sizeSmall,n.shrink&&t.shrink,!n.disableAnimation&&t.animated,n.focused&&t.focused,t[n.variant]]}})(ui((e=>{let{theme:t}=e;return{display:"block",transformOrigin:"top left",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",maxWidth:"100%",variants:[{props:e=>{let{ownerState:t}=e;return t.formControl},style:{position:"absolute",left:0,top:0,transform:"translate(0, 20px) scale(1)"}},{props:{size:"small"},style:{transform:"translate(0, 17px) scale(1)"}},{props:e=>{let{ownerState:t}=e;return t.shrink},style:{transform:"translate(0, -1.5px) scale(0.75)",transformOrigin:"top left",maxWidth:"133%"}},{props:e=>{let{ownerState:t}=e;return!t.disableAnimation},style:{transition:t.transitions.create(["color","transform","max-width"],{duration:t.transitions.duration.shorter,easing:t.transitions.easing.easeOut})}},{props:{variant:"filled"},style:{zIndex:1,pointerEvents:"none",transform:"translate(12px, 16px) scale(1)",maxWidth:"calc(100% - 24px)"}},{props:{variant:"filled",size:"small"},style:{transform:"translate(12px, 13px) scale(1)"}},{props:e=>{let{variant:t,ownerState:n}=e;return"filled"===t&&n.shrink},style:{userSelect:"none",pointerEvents:"auto",transform:"translate(12px, 7px) scale(0.75)",maxWidth:"calc(133% - 24px)"}},{props:e=>{let{variant:t,ownerState:n,size:r}=e;return"filled"===t&&n.shrink&&"small"===r},style:{transform:"translate(12px, 4px) scale(0.75)"}},{props:{variant:"outlined"},style:{zIndex:1,pointerEvents:"none",transform:"translate(14px, 16px) scale(1)",maxWidth:"calc(100% - 24px)"}},{props:{variant:"outlined",size:"small"},style:{transform:"translate(14px, 9px) scale(1)"}},{props:e=>{let{variant:t,ownerState:n}=e;return"outlined"===t&&n.shrink},style:{userSelect:"none",pointerEvents:"auto",maxWidth:"calc(133% - 32px)",transform:"translate(14px, -9px) scale(0.75)"}}]}}))),ys=r.forwardRef((function(e,t){const n=mi({name:"MuiInputLabel",props:e}),{disableAnimation:r=!1,margin:o,shrink:a,variant:i,className:s}=n,c=l(n,gs),u=Xi();let d=a;"undefined"===typeof d&&u&&(d=u.filled||u.focused||u.adornedStart);const f=Gi({props:n,muiFormControl:u,states:["size","variant","required","focused"]}),h=p(p({},n),{},{disableAnimation:r,formControl:u,shrink:d,size:f.size,variant:f.variant,required:f.required,focused:f.focused}),m=(e=>{const{classes:t,formControl:n,size:r,shrink:o,disableAnimation:a,variant:i,required:l}=e,s=Pt({root:["root",n&&"formControl",!a&&"animated",o&&"shrink",r&&"medium"!==r&&"size".concat(_t(r)),i],asterisk:[l&&"asterisk"]},ms,t);return p(p({},t),s)})(h);return(0,pi.jsx)(vs,p(p({"data-shrink":d,ref:t,className:Nt(m.root,s)},c),{},{ownerState:h,classes:m}))}));const bs=function(e,t){var n,o;return r.isValidElement(e)&&-1!==t.indexOf(null!==(n=e.type.muiName)&&void 0!==n?n:null===(o=e.type)||void 0===o||null===(o=o._payload)||void 0===o||null===(o=o.value)||void 0===o?void 0:o.muiName)};function xs(e){return bi("MuiFormControl",e)}xi("MuiFormControl",["root","marginNone","marginNormal","marginDense","fullWidth","disabled"]);const ws=["children","className","color","component","disabled","error","focused","fullWidth","hiddenLabel","margin","required","size","variant"],Ss=si("div",{name:"MuiFormControl",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,t["margin".concat(_t(n.margin))],n.fullWidth&&t.fullWidth]}})({display:"inline-flex",flexDirection:"column",position:"relative",minWidth:0,padding:0,margin:0,border:0,verticalAlign:"top",variants:[{props:{margin:"normal"},style:{marginTop:16,marginBottom:8}},{props:{margin:"dense"},style:{marginTop:8,marginBottom:4}},{props:{fullWidth:!0},style:{width:"100%"}}]}),ks=r.forwardRef((function(e,t){const n=mi({props:e,name:"MuiFormControl"}),{children:o,className:a,color:i="primary",component:s="div",disabled:c=!1,error:u=!1,focused:d,fullWidth:f=!1,hiddenLabel:h=!1,margin:m="none",required:g=!1,size:v="medium",variant:y="outlined"}=n,b=l(n,ws),x=p(p({},n),{},{color:i,component:s,disabled:c,error:u,fullWidth:f,hiddenLabel:h,margin:m,required:g,size:v,variant:y}),w=(e=>{const{classes:t,margin:n,fullWidth:r}=e;return Pt({root:["root","none"!==n&&"margin".concat(_t(n)),r&&"fullWidth"]},xs,t)})(x),[S,k]=r.useState((()=>{let e=!1;return o&&r.Children.forEach(o,(t=>{if(!bs(t,["Input","Select"]))return;const n=bs(t,["Select"])?t.props.input:t;n&&n.props.startAdornment&&(e=!0)})),e})),[C,E]=r.useState((()=>{let e=!1;return o&&r.Children.forEach(o,(t=>{bs(t,["Input","Select"])&&(fl(t.props,!0)||fl(t.props.inputProps,!0))&&(e=!0)})),e})),[j,N]=r.useState(!1);c&&j&&N(!1);const P=void 0===d||c?j:d;let R;r.useRef(!1);const T=r.useCallback((()=>{E(!0)}),[]),_=r.useCallback((()=>{E(!1)}),[]),A=r.useMemo((()=>({adornedStart:S,setAdornedStart:k,color:i,disabled:c,error:u,filled:C,focused:P,fullWidth:f,hiddenLabel:h,size:v,onBlur:()=>{N(!1)},onFocus:()=>{N(!0)},onEmpty:_,onFilled:T,registerEffect:R,required:g,variant:y})),[S,i,c,u,C,P,f,h,R,_,T,g,v,y]);return(0,pi.jsx)(Qi.Provider,{value:A,children:(0,pi.jsx)(Ss,p(p({as:s,ownerState:x,className:Nt(w.root,a),ref:t},b),{},{children:o}))})})),Cs=ks;function Es(e){return bi("MuiFormHelperText",e)}const js=xi("MuiFormHelperText",["root","error","disabled","sizeSmall","sizeMedium","contained","focused","filled","required"]),Ns=["children","className","component","disabled","error","filled","focused","margin","required","variant"];var Ps;const Rs=si("p",{name:"MuiFormHelperText",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,n.size&&t["size".concat(_t(n.size))],n.contained&&t.contained,n.filled&&t.filled]}})(ui((e=>{let{theme:t}=e;return p(p({color:(t.vars||t).palette.text.secondary},t.typography.caption),{},{textAlign:"left",marginTop:3,marginRight:0,marginBottom:0,marginLeft:0,["&.".concat(js.disabled)]:{color:(t.vars||t).palette.text.disabled},["&.".concat(js.error)]:{color:(t.vars||t).palette.error.main},variants:[{props:{size:"small"},style:{marginTop:4}},{props:e=>{let{ownerState:t}=e;return t.contained},style:{marginLeft:14,marginRight:14}}]})}))),Ts=r.forwardRef((function(e,t){const n=mi({props:e,name:"MuiFormHelperText"}),{children:r,className:o,component:a="p",disabled:i,error:s,filled:c,focused:u,margin:d,required:f,variant:h}=n,m=l(n,Ns),g=Gi({props:n,muiFormControl:Xi(),states:["variant","size","disabled","error","filled","focused","required"]}),v=p(p({},n),{},{component:a,contained:"filled"===g.variant||"outlined"===g.variant,variant:g.variant,size:g.size,disabled:g.disabled,error:g.error,filled:g.filled,focused:g.focused,required:g.required});delete v.ownerState;const y=(e=>{const{classes:t,contained:n,size:r,disabled:o,error:a,filled:i,focused:l,required:s}=e;return Pt({root:["root",o&&"disabled",a&&"error",r&&"size".concat(_t(r)),n&&"contained",l&&"focused",i&&"filled",s&&"required"]},Es,t)})(v);return(0,pi.jsx)(Rs,p(p({as:a,className:Nt(y.root,o),ref:t},m),{},{ownerState:v,children:" "===r?Ps||(Ps=(0,pi.jsx)("span",{className:"notranslate","aria-hidden":!0,children:"\u200b"})):r}))}));function _s(e){var t;return parseInt(r.version,10)>=19?(null===e||void 0===e||null===(t=e.props)||void 0===t?void 0:t.ref)||null:(null===e||void 0===e?void 0:e.ref)||null}const As=Bi,Ms=r.createContext();const zs=["elementType","externalSlotProps","ownerState","skipResolvingSlotProps"];const Is=function(e){var t;const{elementType:n,externalSlotProps:r,ownerState:o,skipResolvingSlotProps:a=!1}=e,i=l(e,zs),s=a?{}:Yl(r,o),{props:c,internalRef:u}=Xl(p(p({},i),{},{externalSlotProps:s})),d=Fi(u,null===s||void 0===s?void 0:s.ref,null===(t=e.additionalProps)||void 0===t?void 0:t.ref);return ql(n,p(p({},c),{},{ref:d}),o)};const Os=r.createContext({});function Fs(e){return bi("MuiList",e)}xi("MuiList",["root","padding","dense","subheader"]);const Ls=["children","className","component","dense","disablePadding","subheader"],Ds=si("ul",{name:"MuiList",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,!n.disablePadding&&t.padding,n.dense&&t.dense,n.subheader&&t.subheader]}})({listStyle:"none",margin:0,padding:0,position:"relative",variants:[{props:e=>{let{ownerState:t}=e;return!t.disablePadding},style:{paddingTop:8,paddingBottom:8}},{props:e=>{let{ownerState:t}=e;return t.subheader},style:{paddingTop:0}}]}),Bs=r.forwardRef((function(e,t){const n=mi({props:e,name:"MuiList"}),{children:o,className:a,component:i="ul",dense:s=!1,disablePadding:c=!1,subheader:u}=n,d=l(n,Ls),f=r.useMemo((()=>({dense:s})),[s]),h=p(p({},n),{},{component:i,dense:s,disablePadding:c}),m=(e=>{const{classes:t,disablePadding:n,dense:r,subheader:o}=e;return Pt({root:["root",!n&&"padding",r&&"dense",o&&"subheader"]},Fs,t)})(h);return(0,pi.jsx)(Os.Provider,{value:f,children:(0,pi.jsxs)(Ds,p(p({as:i,className:Nt(m.root,a),ref:t,ownerState:h},d),{},{children:[u,o]}))})}));function Ws(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window;const t=e.document.documentElement.clientWidth;return e.innerWidth-t}const Hs=Ws,Us=Wi,Vs=["actions","autoFocus","autoFocusItem","children","className","disabledItemsFocusable","disableListWrap","onKeyDown","variant"];function $s(e,t,n){return e===t?e.firstChild:t&&t.nextElementSibling?t.nextElementSibling:n?null:e.firstChild}function Ks(e,t,n){return e===t?n?e.firstChild:e.lastChild:t&&t.previousElementSibling?t.previousElementSibling:n?null:e.lastChild}function qs(e,t){if(void 0===t)return!0;let n=e.innerText;return void 0===n&&(n=e.textContent),n=n.trim().toLowerCase(),0!==n.length&&(t.repeating?n[0]===t.keys[0]:n.startsWith(t.keys.join("")))}function Ys(e,t,n,r,o,a){let i=!1,l=o(e,t,!!t&&n);for(;l;){if(l===e.firstChild){if(i)return!1;i=!0}const t=!r&&(l.disabled||"true"===l.getAttribute("aria-disabled"));if(l.hasAttribute("tabindex")&&qs(l,a)&&!t)return l.focus(),!0;l=o(e,l,n)}return!1}const Gs=r.forwardRef((function(e,t){const{actions:n,autoFocus:o=!1,autoFocusItem:a=!1,children:i,className:s,disabledItemsFocusable:c=!1,disableListWrap:u=!1,onKeyDown:d,variant:f="selectedMenu"}=e,h=l(e,Vs),m=r.useRef(null),g=r.useRef({keys:[],repeating:!0,previousKeyMatched:!0,lastTime:null});dl((()=>{o&&m.current.focus()}),[o]),r.useImperativeHandle(n,(()=>({adjustStyleForScrollbar:(e,t)=>{let{direction:n}=t;const r=!m.current.style.width;if(e.clientHeight<m.current.clientHeight&&r){const t="".concat(Hs(Us(e)),"px");m.current.style["rtl"===n?"paddingLeft":"paddingRight"]=t,m.current.style.width="calc(100% + ".concat(t,")")}return m.current}})),[]);const v=ul(m,t);let y=-1;r.Children.forEach(i,((e,t)=>{r.isValidElement(e)?(e.props.disabled||("selectedMenu"===f&&e.props.selected||-1===y)&&(y=t),y===t&&(e.props.disabled||e.props.muiSkipListHighlight||e.type.muiSkipListHighlight)&&(y+=1,y>=i.length&&(y=-1))):y===t&&(y+=1,y>=i.length&&(y=-1))}));const b=r.Children.map(i,((e,t)=>{if(t===y){const t={};return a&&(t.autoFocus=!0),void 0===e.props.tabIndex&&"selectedMenu"===f&&(t.tabIndex=0),r.cloneElement(e,t)}return e}));return(0,pi.jsx)(Bs,p(p({role:"menu",ref:v,className:s,onKeyDown:e=>{const t=m.current,n=e.key;if(e.ctrlKey||e.metaKey||e.altKey)return void(d&&d(e));const r=As(t).activeElement;if("ArrowDown"===n)e.preventDefault(),Ys(t,r,u,c,$s);else if("ArrowUp"===n)e.preventDefault(),Ys(t,r,u,c,Ks);else if("Home"===n)e.preventDefault(),Ys(t,null,u,c,$s);else if("End"===n)e.preventDefault(),Ys(t,null,u,c,Ks);else if(1===n.length){const o=g.current,a=n.toLowerCase(),i=performance.now();o.keys.length>0&&(i-o.lastTime>500?(o.keys=[],o.repeating=!0,o.previousKeyMatched=!0):o.repeating&&a!==o.keys[0]&&(o.repeating=!1)),o.lastTime=i,o.keys.push(a);const l=r&&!o.repeating&&qs(r,o);o.previousKeyMatched&&(l||Ys(t,r,!1,c,$s,o))?e.preventDefault():o.previousKeyMatched=!1}d&&d(e)},tabIndex:o?0:-1},h),{},{children:b}))})),Qs=Oi,Xs={};function Js(e,t){const n=r.useRef(Xs);return n.current===Xs&&(n.current=e(t)),n}const Zs=[];class ec{constructor(){u(this,"currentId",null),u(this,"clear",(()=>{null!==this.currentId&&(clearTimeout(this.currentId),this.currentId=null)})),u(this,"disposeEffect",(()=>this.clear))}static create(){return new ec}start(e,t){this.clear(),this.currentId=setTimeout((()=>{this.currentId=null,t()}),e)}}function tc(){const e=Js(ec.create).current;var t;return t=e.disposeEffect,r.useEffect(t,Zs),e}function nc(e,t){return nc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},nc(e,t)}function rc(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,nc(e,t)}var oc=n(950);const ac=!1,ic=r.createContext(null);var lc="unmounted",sc="exited",cc="entering",uc="entered",dc="exiting",pc=function(e){function t(t,n){var r;r=e.call(this,t,n)||this;var o,a=n&&!n.isMounting?t.enter:t.appear;return r.appearStatus=null,t.in?a?(o=sc,r.appearStatus=cc):o=uc:o=t.unmountOnExit||t.mountOnEnter?lc:sc,r.state={status:o},r.nextCallback=null,r}rc(t,e),t.getDerivedStateFromProps=function(e,t){return e.in&&t.status===lc?{status:sc}:null};var n=t.prototype;return n.componentDidMount=function(){this.updateStatus(!0,this.appearStatus)},n.componentDidUpdate=function(e){var t=null;if(e!==this.props){var n=this.state.status;this.props.in?n!==cc&&n!==uc&&(t=cc):n!==cc&&n!==uc||(t=dc)}this.updateStatus(!1,t)},n.componentWillUnmount=function(){this.cancelNextCallback()},n.getTimeouts=function(){var e,t,n,r=this.props.timeout;return e=t=n=r,null!=r&&"number"!==typeof r&&(e=r.exit,t=r.enter,n=void 0!==r.appear?r.appear:t),{exit:e,enter:t,appear:n}},n.updateStatus=function(e,t){if(void 0===e&&(e=!1),null!==t)if(this.cancelNextCallback(),t===cc){if(this.props.unmountOnExit||this.props.mountOnEnter){var n=this.props.nodeRef?this.props.nodeRef.current:oc.findDOMNode(this);n&&function(e){e.scrollTop}(n)}this.performEnter(e)}else this.performExit();else this.props.unmountOnExit&&this.state.status===sc&&this.setState({status:lc})},n.performEnter=function(e){var t=this,n=this.props.enter,r=this.context?this.context.isMounting:e,o=this.props.nodeRef?[r]:[oc.findDOMNode(this),r],a=o[0],i=o[1],l=this.getTimeouts(),s=r?l.appear:l.enter;!e&&!n||ac?this.safeSetState({status:uc},(function(){t.props.onEntered(a)})):(this.props.onEnter(a,i),this.safeSetState({status:cc},(function(){t.props.onEntering(a,i),t.onTransitionEnd(s,(function(){t.safeSetState({status:uc},(function(){t.props.onEntered(a,i)}))}))})))},n.performExit=function(){var e=this,t=this.props.exit,n=this.getTimeouts(),r=this.props.nodeRef?void 0:oc.findDOMNode(this);t&&!ac?(this.props.onExit(r),this.safeSetState({status:dc},(function(){e.props.onExiting(r),e.onTransitionEnd(n.exit,(function(){e.safeSetState({status:sc},(function(){e.props.onExited(r)}))}))}))):this.safeSetState({status:sc},(function(){e.props.onExited(r)}))},n.cancelNextCallback=function(){null!==this.nextCallback&&(this.nextCallback.cancel(),this.nextCallback=null)},n.safeSetState=function(e,t){t=this.setNextCallback(t),this.setState(e,t)},n.setNextCallback=function(e){var t=this,n=!0;return this.nextCallback=function(r){n&&(n=!1,t.nextCallback=null,e(r))},this.nextCallback.cancel=function(){n=!1},this.nextCallback},n.onTransitionEnd=function(e,t){this.setNextCallback(t);var n=this.props.nodeRef?this.props.nodeRef.current:oc.findDOMNode(this),r=null==e&&!this.props.addEndListener;if(n&&!r){if(this.props.addEndListener){var o=this.props.nodeRef?[this.nextCallback]:[n,this.nextCallback],a=o[0],i=o[1];this.props.addEndListener(a,i)}null!=e&&setTimeout(this.nextCallback,e)}else setTimeout(this.nextCallback,0)},n.render=function(){var e=this.state.status;if(e===lc)return null;var t=this.props,n=t.children,o=(t.in,t.mountOnEnter,t.unmountOnExit,t.appear,t.enter,t.exit,t.timeout,t.addEndListener,t.onEnter,t.onEntering,t.onEntered,t.onExit,t.onExiting,t.onExited,t.nodeRef,i(t,["children","in","mountOnEnter","unmountOnExit","appear","enter","exit","timeout","addEndListener","onEnter","onEntering","onEntered","onExit","onExiting","onExited","nodeRef"]));return r.createElement(ic.Provider,{value:null},"function"===typeof n?n(e,o):r.cloneElement(r.Children.only(n),o))},t}(r.Component);function fc(){}pc.contextType=ic,pc.propTypes={},pc.defaultProps={in:!1,mountOnEnter:!1,unmountOnExit:!1,appear:!1,enter:!0,exit:!0,onEnter:fc,onEntering:fc,onEntered:fc,onExit:fc,onExiting:fc,onExited:fc},pc.UNMOUNTED=lc,pc.EXITED=sc,pc.ENTERING=cc,pc.ENTERED=uc,pc.EXITING=dc;const hc=pc;function mc(){const e=ll(ri);return e[oi]||e}const gc=e=>e.scrollTop;function vc(e,t){var n,r;const{timeout:o,easing:a,style:i={}}=e;return{duration:null!==(n=i.transitionDuration)&&void 0!==n?n:"number"===typeof o?o:o[t.mode]||0,easing:null!==(r=i.transitionTimingFunction)&&void 0!==r?r:"object"===typeof a?a[t.mode]:a,delay:i.transitionDelay}}const yc=["addEndListener","appear","children","easing","in","onEnter","onEntered","onEntering","onExit","onExited","onExiting","style","timeout","TransitionComponent"],bc=["ownerState"];function xc(e){return"scale(".concat(e,", ").concat(e**2,")")}const wc={entering:{opacity:1,transform:xc(1)},entered:{opacity:1,transform:"none"}},Sc="undefined"!==typeof navigator&&/^((?!chrome|android).)*(safari|mobile)/i.test(navigator.userAgent)&&/(os |version\/)15(.|_)4/i.test(navigator.userAgent),kc=r.forwardRef((function(e,t){const{addEndListener:n,appear:o=!0,children:a,easing:i,in:s,onEnter:c,onEntered:u,onEntering:d,onExit:f,onExited:h,onExiting:m,style:g,timeout:v="auto",TransitionComponent:y=hc}=e,b=l(e,yc),x=tc(),w=r.useRef(),S=mc(),k=r.useRef(null),C=ul(k,_s(a),t),E=e=>t=>{if(e){const n=k.current;void 0===t?e(n):e(n,t)}},j=E(d),N=E(((e,t)=>{gc(e);const{duration:n,delay:r,easing:o}=vc({style:g,timeout:v,easing:i},{mode:"enter"});let a;"auto"===v?(a=S.transitions.getAutoHeightDuration(e.clientHeight),w.current=a):a=n,e.style.transition=[S.transitions.create("opacity",{duration:a,delay:r}),S.transitions.create("transform",{duration:Sc?a:.666*a,delay:r,easing:o})].join(","),c&&c(e,t)})),P=E(u),R=E(m),T=E((e=>{const{duration:t,delay:n,easing:r}=vc({style:g,timeout:v,easing:i},{mode:"exit"});let o;"auto"===v?(o=S.transitions.getAutoHeightDuration(e.clientHeight),w.current=o):o=t,e.style.transition=[S.transitions.create("opacity",{duration:o,delay:n}),S.transitions.create("transform",{duration:Sc?o:.666*o,delay:Sc?n:n||.333*o,easing:r})].join(","),e.style.opacity=0,e.style.transform=xc(.75),f&&f(e)})),_=E(h);return(0,pi.jsx)(y,p(p({appear:o,in:s,nodeRef:k,onEnter:N,onEntered:P,onEntering:j,onExit:T,onExited:_,onExiting:R,addEndListener:e=>{"auto"===v&&x.start(w.current||0,e),n&&n(k.current,e)},timeout:"auto"===v?null:v},b),{},{children:(e,t)=>{let{ownerState:n}=t,o=l(t,bc);return r.cloneElement(a,p({style:p(p(p({opacity:0,transform:xc(.75),visibility:"exited"!==e||s?void 0:"hidden"},wc[e]),g),a.props.style),ref:C},o))}}))}));kc&&(kc.muiSupportAuto=!0);const Cc=kc,Ec=["input","select","textarea","a[href]","button","[tabindex]","audio[controls]","video[controls]",'[contenteditable]:not([contenteditable="false"])'].join(",");function jc(e){const t=[],n=[];return Array.from(e.querySelectorAll(Ec)).forEach(((e,r)=>{const o=function(e){const t=parseInt(e.getAttribute("tabindex")||"",10);return Number.isNaN(t)?"true"===e.contentEditable||("AUDIO"===e.nodeName||"VIDEO"===e.nodeName||"DETAILS"===e.nodeName)&&null===e.getAttribute("tabindex")?0:e.tabIndex:t}(e);-1!==o&&function(e){return!(e.disabled||"INPUT"===e.tagName&&"hidden"===e.type||function(e){if("INPUT"!==e.tagName||"radio"!==e.type)return!1;if(!e.name)return!1;const t=t=>e.ownerDocument.querySelector('input[type="radio"]'.concat(t));let n=t('[name="'.concat(e.name,'"]:checked'));return n||(n=t('[name="'.concat(e.name,'"]'))),n!==e}(e))}(e)&&(0===o?t.push(e):n.push({documentOrder:r,tabIndex:o,node:e}))})),n.sort(((e,t)=>e.tabIndex===t.tabIndex?e.documentOrder-t.documentOrder:e.tabIndex-t.tabIndex)).map((e=>e.node)).concat(t)}function Nc(){return!0}const Pc=function(e){const{children:t,disableAutoFocus:n=!1,disableEnforceFocus:o=!1,disableRestoreFocus:a=!1,getTabbable:i=jc,isEnabled:l=Nc,open:s}=e,c=r.useRef(!1),u=r.useRef(null),d=r.useRef(null),p=r.useRef(null),f=r.useRef(null),h=r.useRef(!1),m=r.useRef(null),g=Fi(_s(t),m),v=r.useRef(null);r.useEffect((()=>{s&&m.current&&(h.current=!n)}),[n,s]),r.useEffect((()=>{if(!s||!m.current)return;const e=Bi(m.current);return m.current.contains(e.activeElement)||(m.current.hasAttribute("tabIndex")||m.current.setAttribute("tabIndex","-1"),h.current&&m.current.focus()),()=>{a||(p.current&&p.current.focus&&(c.current=!0,p.current.focus()),p.current=null)}}),[s]),r.useEffect((()=>{if(!s||!m.current)return;const e=Bi(m.current),t=t=>{v.current=t,!o&&l()&&"Tab"===t.key&&e.activeElement===m.current&&t.shiftKey&&(c.current=!0,d.current&&d.current.focus())},n=()=>{const t=m.current;if(null===t)return;if(!e.hasFocus()||!l()||c.current)return void(c.current=!1);if(t.contains(e.activeElement))return;if(o&&e.activeElement!==u.current&&e.activeElement!==d.current)return;if(e.activeElement!==f.current)f.current=null;else if(null!==f.current)return;if(!h.current)return;let n=[];if(e.activeElement!==u.current&&e.activeElement!==d.current||(n=i(m.current)),n.length>0){var r,a;const e=Boolean((null===(r=v.current)||void 0===r?void 0:r.shiftKey)&&"Tab"===(null===(a=v.current)||void 0===a?void 0:a.key)),t=n[0],o=n[n.length-1];"string"!==typeof t&&"string"!==typeof o&&(e?o.focus():t.focus())}else t.focus()};e.addEventListener("focusin",n),e.addEventListener("keydown",t,!0);const r=setInterval((()=>{e.activeElement&&"BODY"===e.activeElement.tagName&&n()}),50);return()=>{clearInterval(r),e.removeEventListener("focusin",n),e.removeEventListener("keydown",t,!0)}}),[n,o,a,l,s,i]);const y=e=>{null===p.current&&(p.current=e.relatedTarget),h.current=!0};return(0,pi.jsxs)(r.Fragment,{children:[(0,pi.jsx)("div",{tabIndex:s?0:-1,onFocus:y,ref:u,"data-testid":"sentinelStart"}),r.cloneElement(t,{ref:g,onFocus:e=>{null===p.current&&(p.current=e.relatedTarget),h.current=!0,f.current=e.target;const n=t.props.onFocus;n&&n(e)}}),(0,pi.jsx)("div",{tabIndex:s?0:-1,onFocus:y,ref:d,"data-testid":"sentinelEnd"})]})};function Rc(e,t){"function"===typeof e?e(t):e&&(e.current=t)}const Tc=r.forwardRef((function(e,t){const{children:n,container:o,disablePortal:a=!1}=e,[i,l]=r.useState(null),s=Fi(r.isValidElement(n)?_s(n):null,t);if(Li((()=>{a||l(function(e){return"function"===typeof e?e():e}(o)||document.body)}),[o,a]),Li((()=>{if(i&&!a)return Rc(t,i),()=>{Rc(t,null)}}),[t,i,a]),a){if(r.isValidElement(n)){const e={ref:s};return r.cloneElement(n,e)}return n}return i?oc.createPortal(n,i):i})),_c=["addEndListener","appear","children","easing","in","onEnter","onEntered","onEntering","onExit","onExited","onExiting","style","timeout","TransitionComponent"],Ac=["ownerState"],Mc={entering:{opacity:1},entered:{opacity:1}},zc=r.forwardRef((function(e,t){const n=mc(),o={enter:n.transitions.duration.enteringScreen,exit:n.transitions.duration.leavingScreen},{addEndListener:a,appear:i=!0,children:s,easing:c,in:u,onEnter:d,onEntered:f,onEntering:h,onExit:m,onExited:g,onExiting:v,style:y,timeout:b=o,TransitionComponent:x=hc}=e,w=l(e,_c),S=r.useRef(null),k=ul(S,_s(s),t),C=e=>t=>{if(e){const n=S.current;void 0===t?e(n):e(n,t)}},E=C(h),j=C(((e,t)=>{gc(e);const r=vc({style:y,timeout:b,easing:c},{mode:"enter"});e.style.webkitTransition=n.transitions.create("opacity",r),e.style.transition=n.transitions.create("opacity",r),d&&d(e,t)})),N=C(f),P=C(v),R=C((e=>{const t=vc({style:y,timeout:b,easing:c},{mode:"exit"});e.style.webkitTransition=n.transitions.create("opacity",t),e.style.transition=n.transitions.create("opacity",t),m&&m(e)})),T=C(g);return(0,pi.jsx)(x,p(p({appear:i,in:u,nodeRef:S,onEnter:j,onEntered:N,onEntering:E,onExit:R,onExited:T,onExiting:P,addEndListener:e=>{a&&a(S.current,e)},timeout:b},w),{},{children:(e,t)=>{let{ownerState:n}=t,o=l(t,Ac);return r.cloneElement(s,p({style:p(p(p({opacity:0,visibility:"exited"!==e||u?void 0:"hidden"},Mc[e]),y),s.props.style),ref:k},o))}}))})),Ic=zc;function Oc(e){return bi("MuiBackdrop",e)}xi("MuiBackdrop",["root","invisible"]);const Fc=["children","className","component","invisible","open","components","componentsProps","slotProps","slots","TransitionComponent","transitionDuration"],Lc=si("div",{name:"MuiBackdrop",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,n.invisible&&t.invisible]}})({position:"fixed",display:"flex",alignItems:"center",justifyContent:"center",right:0,bottom:0,top:0,left:0,backgroundColor:"rgba(0, 0, 0, 0.5)",WebkitTapHighlightColor:"transparent",variants:[{props:{invisible:!0},style:{backgroundColor:"transparent"}}]}),Dc=r.forwardRef((function(e,t){const n=mi({props:e,name:"MuiBackdrop"}),{children:r,className:o,component:a="div",invisible:i=!1,open:s,components:c={},componentsProps:u={},slotProps:d={},slots:f={},TransitionComponent:h,transitionDuration:m}=n,g=l(n,Fc),v=p(p({},n),{},{component:a,invisible:i}),y=(e=>{const{classes:t,invisible:n}=e;return Pt({root:["root",n&&"invisible"]},Oc,t)})(v),b={slots:p({transition:h,root:c.Root},f),slotProps:p(p({},u),d)},[x,w]=ts("root",{elementType:Lc,externalForwardedProps:b,className:Nt(y.root,o),ownerState:v}),[S,k]=ts("transition",{elementType:Ic,externalForwardedProps:b,ownerState:v});return(0,pi.jsx)(S,p(p(p({in:s,timeout:m},g),k),{},{children:(0,pi.jsx)(x,p(p({"aria-hidden":!0},w),{},{classes:y,ref:t,children:r}))}))}));function Bc(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.reduce(((e,t)=>null==t?e:function(){for(var n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];e.apply(this,r),t.apply(this,r)}),(()=>{}))}function Wc(e,t){t?e.setAttribute("aria-hidden","true"):e.removeAttribute("aria-hidden")}function Hc(e){return parseInt(Wi(e).getComputedStyle(e).paddingRight,10)||0}function Uc(e,t,n,r,o){const a=[t,n,...r];[].forEach.call(e.children,(e=>{const t=!a.includes(e),n=!function(e){const t=["TEMPLATE","SCRIPT","STYLE","LINK","MAP","META","NOSCRIPT","PICTURE","COL","COLGROUP","PARAM","SLOT","SOURCE","TRACK"].includes(e.tagName),n="INPUT"===e.tagName&&"hidden"===e.getAttribute("type");return t||n}(e);t&&n&&Wc(e,o)}))}function Vc(e,t){let n=-1;return e.some(((e,r)=>!!t(e)&&(n=r,!0))),n}function $c(e,t){const n=[],r=e.container;if(!t.disableScrollLock){if(function(e){const t=Bi(e);return t.body===e?Wi(e).innerWidth>t.documentElement.clientWidth:e.scrollHeight>e.clientHeight}(r)){const e=Ws(Wi(r));n.push({value:r.style.paddingRight,property:"padding-right",el:r}),r.style.paddingRight="".concat(Hc(r)+e,"px");const t=Bi(r).querySelectorAll(".mui-fixed");[].forEach.call(t,(t=>{n.push({value:t.style.paddingRight,property:"padding-right",el:t}),t.style.paddingRight="".concat(Hc(t)+e,"px")}))}let e;if(r.parentNode instanceof DocumentFragment)e=Bi(r).body;else{const t=r.parentElement,n=Wi(r);e="HTML"===(null===t||void 0===t?void 0:t.nodeName)&&"scroll"===n.getComputedStyle(t).overflowY?t:r}n.push({value:e.style.overflow,property:"overflow",el:e},{value:e.style.overflowX,property:"overflow-x",el:e},{value:e.style.overflowY,property:"overflow-y",el:e}),e.style.overflow="hidden"}return()=>{n.forEach((e=>{let{value:t,el:n,property:r}=e;t?n.style.setProperty(r,t):n.style.removeProperty(r)}))}}const Kc=()=>{},qc=new class{constructor(){this.modals=[],this.containers=[]}add(e,t){let n=this.modals.indexOf(e);if(-1!==n)return n;n=this.modals.length,this.modals.push(e),e.modalRef&&Wc(e.modalRef,!1);const r=function(e){const t=[];return[].forEach.call(e.children,(e=>{"true"===e.getAttribute("aria-hidden")&&t.push(e)})),t}(t);Uc(t,e.mount,e.modalRef,r,!0);const o=Vc(this.containers,(e=>e.container===t));return-1!==o?(this.containers[o].modals.push(e),n):(this.containers.push({modals:[e],container:t,restore:null,hiddenSiblings:r}),n)}mount(e,t){const n=Vc(this.containers,(t=>t.modals.includes(e))),r=this.containers[n];r.restore||(r.restore=$c(r,t))}remove(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];const n=this.modals.indexOf(e);if(-1===n)return n;const r=Vc(this.containers,(t=>t.modals.includes(e))),o=this.containers[r];if(o.modals.splice(o.modals.indexOf(e),1),this.modals.splice(n,1),0===o.modals.length)o.restore&&o.restore(),e.modalRef&&Wc(e.modalRef,t),Uc(o.container,e.mount,e.modalRef,o.hiddenSiblings,!1),this.containers.splice(r,1);else{const e=o.modals[o.modals.length-1];e.modalRef&&Wc(e.modalRef,!1)}return n}isTopModal(e){return this.modals.length>0&&this.modals[this.modals.length-1]===e}};const Yc=function(e){const{container:t,disableEscapeKeyDown:n=!1,disableScrollLock:o=!1,closeAfterTransition:a=!1,onTransitionEnter:i,onTransitionExited:l,children:s,onClose:c,open:u,rootRef:d}=e,f=r.useRef({}),h=r.useRef(null),m=r.useRef(null),g=Fi(m,d),[v,y]=r.useState(!u),b=function(e){return!!e&&e.props.hasOwnProperty("in")}(s);let x=!0;"false"!==e["aria-hidden"]&&!1!==e["aria-hidden"]||(x=!1);const w=()=>(f.current.modalRef=m.current,f.current.mount=h.current,f.current),S=()=>{qc.mount(w(),{disableScrollLock:o}),m.current&&(m.current.scrollTop=0)},k=Di((()=>{const e=function(e){return"function"===typeof e?e():e}(t)||Bi(h.current).body;qc.add(w(),e),m.current&&S()})),C=()=>qc.isTopModal(w()),E=Di((e=>{h.current=e,e&&(u&&C()?S():m.current&&Wc(m.current,x))})),j=r.useCallback((()=>{qc.remove(w(),x)}),[x]);r.useEffect((()=>()=>{j()}),[j]),r.useEffect((()=>{u?k():b&&a||j()}),[u,j,b,a,k]);const N=e=>t=>{var r;null===(r=e.onKeyDown)||void 0===r||r.call(e,t),"Escape"===t.key&&229!==t.which&&C()&&(n||(t.stopPropagation(),c&&c(t,"escapeKeyDown")))},P=e=>t=>{var n;null===(n=e.onClick)||void 0===n||n.call(e,t),t.target===t.currentTarget&&c&&c(t,"backdropClick")};return{getRootProps:function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const n=Gl(e);delete n.onTransitionEnter,delete n.onTransitionExited;const r=p(p({},n),t);return p(p({role:"presentation"},r),{},{onKeyDown:N(r),ref:g})},getBackdropProps:function(){const e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return p(p({"aria-hidden":!0},e),{},{onClick:P(e),open:u})},getTransitionProps:()=>{var e,t;return{onEnter:Bc((()=>{y(!1),i&&i()}),null!==(e=null===s||void 0===s?void 0:s.props.onEnter)&&void 0!==e?e:Kc),onExited:Bc((()=>{y(!0),l&&l(),a&&j()}),null!==(t=null===s||void 0===s?void 0:s.props.onExited)&&void 0!==t?t:Kc)}},rootRef:g,portalRef:E,isTopModal:C,exited:v,hasTransition:b}};function Gc(e){return bi("MuiModal",e)}xi("MuiModal",["root","hidden","backdrop"]);const Qc=["BackdropComponent","BackdropProps","classes","className","closeAfterTransition","children","container","component","components","componentsProps","disableAutoFocus","disableEnforceFocus","disableEscapeKeyDown","disablePortal","disableRestoreFocus","disableScrollLock","hideBackdrop","keepMounted","onClose","onTransitionEnter","onTransitionExited","open","slotProps","slots","theme"],Xc=si("div",{name:"MuiModal",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,!n.open&&n.exited&&t.hidden]}})(ui((e=>{let{theme:t}=e;return{position:"fixed",zIndex:(t.vars||t).zIndex.modal,right:0,bottom:0,top:0,left:0,variants:[{props:e=>{let{ownerState:t}=e;return!t.open&&t.exited},style:{visibility:"hidden"}}]}}))),Jc=si(Dc,{name:"MuiModal",slot:"Backdrop"})({zIndex:-1}),Zc=r.forwardRef((function(e,t){const n=mi({name:"MuiModal",props:e}),{BackdropComponent:o=Jc,BackdropProps:a,classes:i,className:s,closeAfterTransition:c=!1,children:u,container:d,component:f,components:h={},componentsProps:m={},disableAutoFocus:g=!1,disableEnforceFocus:v=!1,disableEscapeKeyDown:y=!1,disablePortal:b=!1,disableRestoreFocus:x=!1,disableScrollLock:w=!1,hideBackdrop:S=!1,keepMounted:k=!1,onClose:C,onTransitionEnter:E,onTransitionExited:j,open:N,slotProps:P={},slots:R={},theme:T}=n,_=l(n,Qc),A=p(p({},n),{},{closeAfterTransition:c,disableAutoFocus:g,disableEnforceFocus:v,disableEscapeKeyDown:y,disablePortal:b,disableRestoreFocus:x,disableScrollLock:w,hideBackdrop:S,keepMounted:k}),{getRootProps:M,getBackdropProps:z,getTransitionProps:I,portalRef:O,isTopModal:F,exited:L,hasTransition:D}=Yc(p(p({},A),{},{rootRef:t})),B=p(p({},A),{},{exited:L}),W=(e=>{const{open:t,exited:n,classes:r}=e;return Pt({root:["root",!t&&n&&"hidden"],backdrop:["backdrop"]},Gc,r)})(B),H={};if(void 0===u.props.tabIndex&&(H.tabIndex="-1"),D){const{onEnter:e,onExited:t}=I();H.onEnter=e,H.onExited=t}const U={slots:p({root:h.Root,backdrop:h.Backdrop},R),slotProps:p(p({},m),P)},[V,$]=ts("root",{ref:t,elementType:Xc,externalForwardedProps:p(p(p({},U),_),{},{component:f}),getSlotProps:M,ownerState:B,className:Nt(s,null===W||void 0===W?void 0:W.root,!B.open&&B.exited&&(null===W||void 0===W?void 0:W.hidden))}),[K,q]=ts("backdrop",{ref:null===a||void 0===a?void 0:a.ref,elementType:o,externalForwardedProps:U,shouldForwardComponentProp:!0,additionalProps:a,getSlotProps:e=>z(p(p({},e),{},{onClick:t=>{null!==e&&void 0!==e&&e.onClick&&e.onClick(t)}})),className:Nt(null===a||void 0===a?void 0:a.className,null===W||void 0===W?void 0:W.backdrop),ownerState:B});return k||N||D&&!L?(0,pi.jsx)(Tc,{ref:O,container:d,disablePortal:b,children:(0,pi.jsxs)(V,p(p({},$),{},{children:[!S&&o?(0,pi.jsx)(K,p({},q)):null,(0,pi.jsx)(Pc,{disableEnforceFocus:v,disableAutoFocus:g,disableRestoreFocus:x,isEnabled:F,open:N,children:r.cloneElement(u,H)})]}))}):null})),eu=Zc;function tu(e){return bi("MuiPaper",e)}xi("MuiPaper",["root","rounded","outlined","elevation","elevation0","elevation1","elevation2","elevation3","elevation4","elevation5","elevation6","elevation7","elevation8","elevation9","elevation10","elevation11","elevation12","elevation13","elevation14","elevation15","elevation16","elevation17","elevation18","elevation19","elevation20","elevation21","elevation22","elevation23","elevation24"]);const nu=["className","component","elevation","square","variant"],ru=si("div",{name:"MuiPaper",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,t[n.variant],!n.square&&t.rounded,"elevation"===n.variant&&t["elevation".concat(n.elevation)]]}})(ui((e=>{let{theme:t}=e;return{backgroundColor:(t.vars||t).palette.background.paper,color:(t.vars||t).palette.text.primary,transition:t.transitions.create("box-shadow"),variants:[{props:e=>{let{ownerState:t}=e;return!t.square},style:{borderRadius:t.shape.borderRadius}},{props:{variant:"outlined"},style:{border:"1px solid ".concat((t.vars||t).palette.divider)}},{props:{variant:"elevation"},style:{boxShadow:"var(--Paper-shadow)",backgroundImage:"var(--Paper-overlay)"}}]}}))),ou=r.forwardRef((function(e,t){var n;const r=mi({props:e,name:"MuiPaper"}),o=mc(),{className:a,component:i="div",elevation:s=1,square:c=!1,variant:u="elevation"}=r,d=l(r,nu),f=p(p({},r),{},{component:i,elevation:s,square:c,variant:u}),h=(e=>{const{square:t,elevation:n,variant:r,classes:o}=e;return Pt({root:["root",r,!t&&"rounded","elevation"===r&&"elevation".concat(n)]},tu,o)})(f);return(0,pi.jsx)(ru,p(p({as:i,ownerState:f,className:Nt(h.root,a),ref:t},d),{},{style:p(p({},"elevation"===u&&p(p({"--Paper-shadow":(o.vars||o).shadows[s]},o.vars&&{"--Paper-overlay":null===(n=o.vars.overlays)||void 0===n?void 0:n[s]}),!o.vars&&"dark"===o.palette.mode&&{"--Paper-overlay":"linear-gradient(".concat(qo("#fff",La(s)),", ").concat(qo("#fff",La(s)),")")})),d.style)}))}));function au(e){return bi("MuiPopover",e)}xi("MuiPopover",["root","paper"]);function iu(e,t){if(!e)return t;function n(e,t){const n={};return Object.keys(t).forEach((r=>{(function(e,t){const n=e.charCodeAt(2);return"o"===e[0]&&"n"===e[1]&&n>=65&&n<=90&&"function"===typeof t})(r,t[r])&&"function"===typeof e[r]&&(n[r]=function(){e[r](...arguments),t[r](...arguments)})})),n}if("function"===typeof e||"function"===typeof t)return r=>{const o="function"===typeof t?t(r):t,a="function"===typeof e?e(p(p({},r),o)):e,i=Nt(null===r||void 0===r?void 0:r.className,null===o||void 0===o?void 0:o.className,null===a||void 0===a?void 0:a.className),l=n(a,o);return p(p(p(p(p(p({},o),a),l),!!i&&{className:i}),(null===o||void 0===o?void 0:o.style)&&(null===a||void 0===a?void 0:a.style)&&{style:p(p({},o.style),a.style)}),(null===o||void 0===o?void 0:o.sx)&&(null===a||void 0===a?void 0:a.sx)&&{sx:[...Array.isArray(o.sx)?o.sx:[o.sx],...Array.isArray(a.sx)?a.sx:[a.sx]]})};const r=t,o=n(e,r),a=Nt(null===r||void 0===r?void 0:r.className,null===e||void 0===e?void 0:e.className);return p(p(p(p(p(p({},t),e),o),!!a&&{className:a}),(null===r||void 0===r?void 0:r.style)&&(null===e||void 0===e?void 0:e.style)&&{style:p(p({},r.style),e.style)}),(null===r||void 0===r?void 0:r.sx)&&(null===e||void 0===e?void 0:e.sx)&&{sx:[...Array.isArray(r.sx)?r.sx:[r.sx],...Array.isArray(e.sx)?e.sx:[e.sx]]})}const lu=["action","anchorEl","anchorOrigin","anchorPosition","anchorReference","children","className","container","elevation","marginThreshold","open","PaperProps","slots","slotProps","transformOrigin","TransitionComponent","transitionDuration","TransitionProps","disableScrollLock"],su=["slots","slotProps"];function cu(e,t){let n=0;return"number"===typeof t?n=t:"center"===t?n=e.height/2:"bottom"===t&&(n=e.height),n}function uu(e,t){let n=0;return"number"===typeof t?n=t:"center"===t?n=e.width/2:"right"===t&&(n=e.width),n}function du(e){return[e.horizontal,e.vertical].map((e=>"number"===typeof e?"".concat(e,"px"):e)).join(" ")}function pu(e){return"function"===typeof e?e():e}const fu=si(eu,{name:"MuiPopover",slot:"Root"})({}),hu=si(ou,{name:"MuiPopover",slot:"Paper"})({position:"absolute",overflowY:"auto",overflowX:"hidden",minWidth:16,minHeight:16,maxWidth:"calc(100% - 32px)",maxHeight:"calc(100% - 32px)",outline:0}),mu=r.forwardRef((function(e,t){const n=mi({props:e,name:"MuiPopover"}),{action:o,anchorEl:a,anchorOrigin:i={vertical:"top",horizontal:"left"},anchorPosition:s,anchorReference:c="anchorEl",children:u,className:d,container:f,elevation:h=8,marginThreshold:m=16,open:g,PaperProps:v={},slots:y={},slotProps:b={},transformOrigin:x={vertical:"top",horizontal:"left"},TransitionComponent:w,transitionDuration:S="auto",TransitionProps:k={},disableScrollLock:C=!1}=n,E=l(n,lu),j=r.useRef(),N=p(p({},n),{},{anchorOrigin:i,anchorReference:c,elevation:h,marginThreshold:m,transformOrigin:x,TransitionComponent:w,transitionDuration:S,TransitionProps:k}),P=(e=>{const{classes:t}=e;return Pt({root:["root"],paper:["paper"]},au,t)})(N),R=r.useCallback((()=>{if("anchorPosition"===c)return s;const e=pu(a),t=(e&&1===e.nodeType?e:As(j.current).body).getBoundingClientRect();return{top:t.top+cu(t,i.vertical),left:t.left+uu(t,i.horizontal)}}),[a,i.horizontal,i.vertical,s,c]),T=r.useCallback((e=>({vertical:cu(e,x.vertical),horizontal:uu(e,x.horizontal)})),[x.horizontal,x.vertical]),_=r.useCallback((e=>{const t={width:e.offsetWidth,height:e.offsetHeight},n=T(t);if("none"===c)return{top:null,left:null,transformOrigin:du(n)};const r=R();let o=r.top-n.vertical,i=r.left-n.horizontal;const l=o+t.height,s=i+t.width,u=Us(pu(a)),d=u.innerHeight-m,p=u.innerWidth-m;if(null!==m&&o<m){const e=o-m;o-=e,n.vertical+=e}else if(null!==m&&l>d){const e=l-d;o-=e,n.vertical+=e}if(null!==m&&i<m){const e=i-m;i-=e,n.horizontal+=e}else if(s>p){const e=s-p;i-=e,n.horizontal+=e}return{top:"".concat(Math.round(o),"px"),left:"".concat(Math.round(i),"px"),transformOrigin:du(n)}}),[a,c,R,T,m]),[A,M]=r.useState(g),z=r.useCallback((()=>{const e=j.current;if(!e)return;const t=_(e);null!==t.top&&e.style.setProperty("top",t.top),null!==t.left&&(e.style.left=t.left),e.style.transformOrigin=t.transformOrigin,M(!0)}),[_]);r.useEffect((()=>(C&&window.addEventListener("scroll",z),()=>window.removeEventListener("scroll",z))),[a,C,z]);r.useEffect((()=>{g&&z()})),r.useImperativeHandle(o,(()=>g?{updatePosition:()=>{z()}}:null),[g,z]),r.useEffect((()=>{if(!g)return;const e=Qs((()=>{z()})),t=Us(pu(a));return t.addEventListener("resize",e),()=>{e.clear(),t.removeEventListener("resize",e)}}),[a,g,z]);let I=S;const O={slots:p({transition:w},y),slotProps:p({transition:k,paper:v},b)},[F,L]=ts("transition",{elementType:Cc,externalForwardedProps:O,ownerState:N,getSlotProps:e=>p(p({},e),{},{onEntering:(t,n)=>{var r;null===(r=e.onEntering)||void 0===r||r.call(e,t,n),z()},onExited:t=>{var n;null===(n=e.onExited)||void 0===n||n.call(e,t),M(!1)}}),additionalProps:{appear:!0,in:g}});"auto"!==S||F.muiSupportAuto||(I=void 0);const D=f||(a?As(pu(a)).body:void 0),[B,W]=ts("root",{ref:t,elementType:fu,externalForwardedProps:p(p({},O),E),shouldForwardComponentProp:!0,additionalProps:{slots:{backdrop:y.backdrop},slotProps:{backdrop:iu("function"===typeof b.backdrop?b.backdrop(N):b.backdrop,{invisible:!0})},container:D,open:g},ownerState:N,className:Nt(P.root,d)}),{slots:H,slotProps:U}=W,V=l(W,su),[$,K]=ts("paper",{ref:j,className:P.paper,elementType:hu,externalForwardedProps:O,shouldForwardComponentProp:!0,additionalProps:{elevation:h,style:A?void 0:{opacity:0}},ownerState:N});return(0,pi.jsx)(B,p(p(p({},V),!Yi(B)&&{slots:H,slotProps:U,disableScrollLock:C}),{},{children:(0,pi.jsx)(F,p(p({},L),{},{timeout:I,children:(0,pi.jsx)($,p(p({},K),{},{children:u}))}))}))}));function gu(e){return bi("MuiMenu",e)}xi("MuiMenu",["root","paper","list"]);const vu=["onEntering"],yu=["autoFocus","children","className","disableAutoFocusItem","MenuListProps","onClose","open","PaperProps","PopoverClasses","transitionDuration","TransitionProps","variant","slots","slotProps"],bu={vertical:"top",horizontal:"right"},xu={vertical:"top",horizontal:"left"},wu=si(mu,{shouldForwardProp:e=>ii(e)||"classes"===e,name:"MuiMenu",slot:"Root"})({}),Su=si(hu,{name:"MuiMenu",slot:"Paper"})({maxHeight:"calc(100% - 96px)",WebkitOverflowScrolling:"touch"}),ku=si(Gs,{name:"MuiMenu",slot:"List"})({outline:0}),Cu=r.forwardRef((function(e,t){const n=mi({props:e,name:"MuiMenu"}),{autoFocus:o=!0,children:a,className:i,disableAutoFocusItem:s=!1,MenuListProps:c={},onClose:u,open:d,PaperProps:f={},PopoverClasses:h,transitionDuration:m="auto",TransitionProps:{onEntering:g}={},variant:v="selectedMenu",slots:y={},slotProps:b={}}=n,x=l(n.TransitionProps,vu),w=l(n,yu),S=(()=>{const e=r.useContext(Ms);return null!==e&&void 0!==e&&e})(),k=p(p({},n),{},{autoFocus:o,disableAutoFocusItem:s,MenuListProps:c,onEntering:g,PaperProps:f,transitionDuration:m,TransitionProps:x,variant:v}),C=(e=>{const{classes:t}=e;return Pt({root:["root"],paper:["paper"],list:["list"]},gu,t)})(k),E=o&&!s&&d,j=r.useRef(null);let N=-1;r.Children.map(a,((e,t)=>{r.isValidElement(e)&&(e.props.disabled||("selectedMenu"===v&&e.props.selected||-1===N)&&(N=t))}));const P={slots:y,slotProps:p({list:c,transition:x,paper:f},b)},R=Is({elementType:y.root,externalSlotProps:b.root,ownerState:k,className:[C.root,i]}),[T,_]=ts("paper",{className:C.paper,elementType:Su,externalForwardedProps:P,shouldForwardComponentProp:!0,ownerState:k}),[A,M]=ts("list",{className:Nt(C.list,c.className),elementType:ku,shouldForwardComponentProp:!0,externalForwardedProps:P,getSlotProps:e=>p(p({},e),{},{onKeyDown:t=>{var n;(e=>{"Tab"===e.key&&(e.preventDefault(),u&&u(e,"tabKeyDown"))})(t),null===(n=e.onKeyDown)||void 0===n||n.call(e,t)}}),ownerState:k}),z="function"===typeof P.slotProps.transition?P.slotProps.transition(k):P.slotProps.transition;return(0,pi.jsx)(wu,p(p({onClose:u,anchorOrigin:{vertical:"bottom",horizontal:S?"right":"left"},transformOrigin:S?bu:xu,slots:p({root:y.root,paper:T,backdrop:y.backdrop},y.transition&&{transition:y.transition}),slotProps:{root:R,paper:_,backdrop:"function"===typeof b.backdrop?b.backdrop(k):b.backdrop,transition:p(p({},z),{},{onEntering:function(){for(var e,t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];((e,t)=>{j.current&&j.current.adjustStyleForScrollbar(e,{direction:S?"rtl":"ltr"}),g&&g(e,t)})(...n),null===z||void 0===z||null===(e=z.onEntering)||void 0===e||e.call(z,...n)}})},open:d,ref:t,transitionDuration:m,ownerState:k},w),{},{classes:h,children:(0,pi.jsx)(A,p(p({actions:j,autoFocus:o&&(-1===N||s),autoFocusItem:E,variant:v},M),{},{children:a}))}))}));function Eu(e){return bi("MuiNativeSelect",e)}const ju=xi("MuiNativeSelect",["root","select","multiple","filled","outlined","standard","disabled","icon","iconOpen","iconFilled","iconOutlined","iconStandard","nativeInput","error"]),Nu=["className","disabled","error","IconComponent","inputRef","variant"],Pu=si("select")((e=>{let{theme:t}=e;return{MozAppearance:"none",WebkitAppearance:"none",userSelect:"none",borderRadius:0,cursor:"pointer","&:focus":{borderRadius:0},["&.".concat(ju.disabled)]:{cursor:"default"},"&[multiple]":{height:"auto"},"&:not([multiple]) option, &:not([multiple]) optgroup":{backgroundColor:(t.vars||t).palette.background.paper},variants:[{props:e=>{let{ownerState:t}=e;return"filled"!==t.variant&&"outlined"!==t.variant},style:{"&&&":{paddingRight:24,minWidth:16}}},{props:{variant:"filled"},style:{"&&&":{paddingRight:32}}},{props:{variant:"outlined"},style:{borderRadius:(t.vars||t).shape.borderRadius,"&:focus":{borderRadius:(t.vars||t).shape.borderRadius},"&&&":{paddingRight:32}}}]}})),Ru=si(Pu,{name:"MuiNativeSelect",slot:"Select",shouldForwardProp:ii,overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.select,t[n.variant],n.error&&t.error,{["&.".concat(ju.multiple)]:t.multiple}]}})({}),Tu=si("svg")((e=>{let{theme:t}=e;return{position:"absolute",right:0,top:"calc(50% - .5em)",pointerEvents:"none",color:(t.vars||t).palette.action.active,["&.".concat(ju.disabled)]:{color:(t.vars||t).palette.action.disabled},variants:[{props:e=>{let{ownerState:t}=e;return t.open},style:{transform:"rotate(180deg)"}},{props:{variant:"filled"},style:{right:7}},{props:{variant:"outlined"},style:{right:7}}]}})),_u=si(Tu,{name:"MuiNativeSelect",slot:"Icon",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.icon,n.variant&&t["icon".concat(_t(n.variant))],n.open&&t.iconOpen]}})({}),Au=r.forwardRef((function(e,t){const{className:n,disabled:o,error:a,IconComponent:i,inputRef:s,variant:c="standard"}=e,u=l(e,Nu),d=p(p({},e),{},{disabled:o,variant:c,error:a}),f=(e=>{const{classes:t,variant:n,disabled:r,multiple:o,open:a,error:i}=e;return Pt({select:["select",n,r&&"disabled",o&&"multiple",i&&"error"],icon:["icon","icon".concat(_t(n)),a&&"iconOpen",r&&"disabled"]},Eu,t)})(d);return(0,pi.jsxs)(r.Fragment,{children:[(0,pi.jsx)(Ru,p({ownerState:d,className:Nt(f.select,n),disabled:o,ref:s||t},u)),e.multiple?null:(0,pi.jsx)(_u,{as:i,ownerState:d,className:f.icon})]})}));const Mu=function(e){const{controlled:t,default:n,name:o,state:a="value"}=e,{current:i}=r.useRef(void 0!==t),[l,s]=r.useState(n);return[i?t:l,r.useCallback((e=>{i||s(e)}),[])]};function zu(e){return bi("MuiSelect",e)}const Iu=xi("MuiSelect",["root","select","multiple","filled","outlined","standard","disabled","focused","icon","iconOpen","iconFilled","iconOutlined","iconStandard","nativeInput","error"]),Ou=["aria-describedby","aria-label","autoFocus","autoWidth","children","className","defaultOpen","defaultValue","disabled","displayEmpty","error","IconComponent","inputRef","labelId","MenuProps","multiple","name","onBlur","onChange","onClose","onFocus","onOpen","open","readOnly","renderValue","required","SelectDisplayProps","tabIndex","type","value","variant"];var Fu;const Lu=si(Pu,{name:"MuiSelect",slot:"Select",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[{["&.".concat(Iu.select)]:t.select},{["&.".concat(Iu.select)]:t[n.variant]},{["&.".concat(Iu.error)]:t.error},{["&.".concat(Iu.multiple)]:t.multiple}]}})({["&.".concat(Iu.select)]:{height:"auto",minHeight:"1.4375em",textOverflow:"ellipsis",whiteSpace:"nowrap",overflow:"hidden"}}),Du=si(Tu,{name:"MuiSelect",slot:"Icon",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.icon,n.variant&&t["icon".concat(_t(n.variant))],n.open&&t.iconOpen]}})({}),Bu=si("input",{shouldForwardProp:e=>ai(e)&&"classes"!==e,name:"MuiSelect",slot:"NativeInput"})({bottom:0,left:0,position:"absolute",opacity:0,pointerEvents:"none",width:"100%",boxSizing:"border-box"});function Wu(e,t){return"object"===typeof t&&null!==t?e===t:String(e)===String(t)}function Hu(e){return null==e||"string"===typeof e&&!e.trim()}const Uu=r.forwardRef((function(e,t){var n;const{"aria-describedby":o,"aria-label":a,autoFocus:i,autoWidth:s,children:c,className:u,defaultOpen:d,defaultValue:f,disabled:h,displayEmpty:m,error:g=!1,IconComponent:v,inputRef:y,labelId:b,MenuProps:x={},multiple:w,name:S,onBlur:k,onChange:C,onClose:E,onFocus:j,onOpen:N,open:P,readOnly:R,renderValue:T,required:_,SelectDisplayProps:A={},tabIndex:M,type:z,value:I,variant:O="standard"}=e,F=l(e,Ou),[L,D]=Mu({controlled:I,default:f,name:"Select"}),[B,W]=Mu({controlled:P,default:d,name:"Select"}),H=r.useRef(null),U=r.useRef(null),[V,$]=r.useState(null),{current:K}=r.useRef(null!=P),[q,Y]=r.useState(),G=ul(t,y),Q=r.useCallback((e=>{U.current=e,e&&$(e)}),[]),X=null===V||void 0===V?void 0:V.parentNode;r.useImperativeHandle(G,(()=>({focus:()=>{U.current.focus()},node:H.current,value:L})),[L]),r.useEffect((()=>{d&&B&&V&&!K&&(Y(s?null:X.clientWidth),U.current.focus())}),[V,s]),r.useEffect((()=>{i&&U.current.focus()}),[i]),r.useEffect((()=>{if(!b)return;const e=As(U.current).getElementById(b);if(e){const t=()=>{getSelection().isCollapsed&&U.current.focus()};return e.addEventListener("click",t),()=>{e.removeEventListener("click",t)}}}),[b]);const J=(e,t)=>{e?N&&N(t):E&&E(t),K||(Y(s?null:X.clientWidth),W(e))},Z=r.Children.toArray(c),ee=e=>t=>{let n;if(t.currentTarget.hasAttribute("tabindex")){if(w){n=Array.isArray(L)?L.slice():[];const t=L.indexOf(e.props.value);-1===t?n.push(e.props.value):n.splice(t,1)}else n=e.props.value;if(e.props.onClick&&e.props.onClick(t),L!==n&&(D(n),C)){const r=t.nativeEvent||t,o=new r.constructor(r.type,r);Object.defineProperty(o,"target",{writable:!0,value:{value:n,name:S}}),C(o,e)}w||J(!1,t)}},te=null!==V&&B;let ne,re;delete F["aria-invalid"];const oe=[];let ae=!1,ie=!1;(fl({value:L})||m)&&(T?ne=T(L):ae=!0);const le=Z.map((e=>{if(!r.isValidElement(e))return null;let t;if(w){if(!Array.isArray(L))throw new Error(Rt(2));t=L.some((t=>Wu(t,e.props.value))),t&&ae&&oe.push(e.props.children)}else t=Wu(L,e.props.value),t&&ae&&(re=e.props.children);return t&&(ie=!0),r.cloneElement(e,{"aria-selected":t?"true":"false",onClick:ee(e),onKeyUp:t=>{" "===t.key&&t.preventDefault(),e.props.onKeyUp&&e.props.onKeyUp(t)},role:"option",selected:t,value:void 0,"data-value":e.props.value})}));ae&&(ne=w?0===oe.length?null:oe.reduce(((e,t,n)=>(e.push(t),n<oe.length-1&&e.push(", "),e)),[]):re);let se,ce=q;!s&&K&&V&&(ce=X.clientWidth),se="undefined"!==typeof M?M:h?null:0;const ue=A.id||(S?"mui-component-select-".concat(S):void 0),de=p(p({},e),{},{variant:O,value:L,open:te,error:g}),pe=(e=>{const{classes:t,variant:n,disabled:r,multiple:o,open:a,error:i}=e;return Pt({select:["select",n,r&&"disabled",o&&"multiple",i&&"error"],icon:["icon","icon".concat(_t(n)),a&&"iconOpen",r&&"disabled"],nativeInput:["nativeInput"]},zu,t)})(de),fe=p(p({},x.PaperProps),null===(n=x.slotProps)||void 0===n?void 0:n.paper),he=Ii();return(0,pi.jsxs)(r.Fragment,{children:[(0,pi.jsx)(Lu,p(p({as:"div",ref:Q,tabIndex:se,role:"combobox","aria-controls":te?he:void 0,"aria-disabled":h?"true":void 0,"aria-expanded":te?"true":"false","aria-haspopup":"listbox","aria-label":a,"aria-labelledby":[b,ue].filter(Boolean).join(" ")||void 0,"aria-describedby":o,"aria-required":_?"true":void 0,"aria-invalid":g?"true":void 0,onKeyDown:e=>{if(!R){[" ","ArrowUp","ArrowDown","Enter"].includes(e.key)&&(e.preventDefault(),J(!0,e))}},onMouseDown:h||R?null:e=>{0===e.button&&(e.preventDefault(),U.current.focus(),J(!0,e))},onBlur:e=>{!te&&k&&(Object.defineProperty(e,"target",{writable:!0,value:{value:L,name:S}}),k(e))},onFocus:j},A),{},{ownerState:de,className:Nt(A.className,pe.select,u),id:ue,children:Hu(ne)?Fu||(Fu=(0,pi.jsx)("span",{className:"notranslate","aria-hidden":!0,children:"\u200b"})):ne})),(0,pi.jsx)(Bu,p(p({"aria-invalid":g,value:Array.isArray(L)?L.join(","):L,name:S,ref:H,"aria-hidden":!0,onChange:e=>{const t=Z.find((t=>t.props.value===e.target.value));void 0!==t&&(D(t.props.value),C&&C(e,t))},tabIndex:-1,disabled:h,className:pe.nativeInput,autoFocus:i,required:_},F),{},{ownerState:de})),(0,pi.jsx)(Du,{as:v,className:pe.icon,ownerState:de}),(0,pi.jsx)(Cu,p(p({id:"menu-".concat(S||""),anchorEl:X,open:te,onClose:e=>{J(!1,e)},anchorOrigin:{vertical:"bottom",horizontal:"center"},transformOrigin:{vertical:"top",horizontal:"center"}},x),{},{slotProps:p(p({},x.slotProps),{},{list:p({"aria-labelledby":b,role:"listbox","aria-multiselectable":w?"true":void 0,disableListWrap:!0,id:he},x.MenuListProps),paper:p(p({},fe),{},{style:p({minWidth:ce},null!=fe?fe.style:null)})}),children:le}))]})})),Vu=Uu,$u=ji((0,pi.jsx)("path",{d:"M7 10l5 5 5-5z"})),Ku=["autoWidth","children","classes","className","defaultOpen","displayEmpty","IconComponent","id","input","inputProps","label","labelId","MenuProps","multiple","native","onClose","onOpen","open","renderValue","SelectDisplayProps","variant"],qu=["root"],Yu={name:"MuiSelect",slot:"Root",shouldForwardProp:e=>ii(e)&&"variant"!==e},Gu=si(Al,Yu)(""),Qu=si(ls,Yu)(""),Xu=si(Dl,Yu)(""),Ju=r.forwardRef((function(e,t){const n=mi({name:"MuiSelect",props:e}),{autoWidth:o=!1,children:a,classes:i={},className:s,defaultOpen:c=!1,displayEmpty:u=!1,IconComponent:d=$u,id:f,input:h,inputProps:m,label:g,labelId:v,MenuProps:y,multiple:b=!1,native:x=!1,onClose:w,onOpen:S,open:k,renderValue:C,SelectDisplayProps:E,variant:j="outlined"}=n,N=l(n,Ku),P=x?Au:Vu,R=Gi({props:n,muiFormControl:Xi(),states:["variant","error"]}),T=R.variant||j,_=p(p({},n),{},{variant:T,classes:i}),A=(e=>{const{classes:t}=e,n=Pt({root:["root"]},zu,t);return p(p({},t),n)})(_),{root:M}=A,z=l(A,qu),I=h||{standard:(0,pi.jsx)(Gu,{ownerState:_}),outlined:(0,pi.jsx)(Qu,{label:g,ownerState:_}),filled:(0,pi.jsx)(Xu,{ownerState:_})}[T],O=ul(t,_s(I));return(0,pi.jsx)(r.Fragment,{children:r.cloneElement(I,p(p(p({inputComponent:P,inputProps:p(p(p({children:a,error:R.error,IconComponent:d,variant:T,type:void 0,multiple:b},x?{id:f}:{autoWidth:o,defaultOpen:c,displayEmpty:u,labelId:v,MenuProps:y,onClose:w,onOpen:S,open:k,renderValue:C,SelectDisplayProps:p({id:f},E)}),m),{},{classes:m?br(z,m.classes):z},h?h.props.inputProps:{})},(b&&x||u)&&"outlined"===T?{notched:!0}:{}),{},{ref:O,className:Nt(I.props.className,s,A.root)},!h&&{variant:T}),N))})}));Ju.muiName="Select";const Zu=Ju;function ed(e){return bi("MuiTextField",e)}xi("MuiTextField",["root"]);const td=["autoComplete","autoFocus","children","className","color","defaultValue","disabled","error","FormHelperTextProps","fullWidth","helperText","id","InputLabelProps","inputProps","InputProps","inputRef","label","maxRows","minRows","multiline","name","onBlur","onChange","onFocus","placeholder","required","rows","select","SelectProps","slots","slotProps","type","value","variant"],nd={standard:Al,filled:Dl,outlined:ls},rd=si(Cs,{name:"MuiTextField",slot:"Root"})({}),od=r.forwardRef((function(e,t){const n=mi({props:e,name:"MuiTextField"}),{autoComplete:r,autoFocus:o=!1,children:a,className:i,color:s="primary",defaultValue:c,disabled:u=!1,error:d=!1,FormHelperTextProps:f,fullWidth:h=!1,helperText:m,id:g,InputLabelProps:v,inputProps:y,InputProps:b,inputRef:x,label:w,maxRows:S,minRows:k,multiline:C=!1,name:E,onBlur:j,onChange:N,onFocus:P,placeholder:R,required:T=!1,rows:_,select:A=!1,SelectProps:M,slots:z={},slotProps:I={},type:O,value:F,variant:L="outlined"}=n,D=l(n,td),B=p(p({},n),{},{autoFocus:o,color:s,disabled:u,error:d,fullWidth:h,multiline:C,required:T,select:A,variant:L}),W=(e=>{const{classes:t}=e;return Pt({root:["root"]},ed,t)})(B);const H=Ii(g),U=m&&H?"".concat(H,"-helper-text"):void 0,V=w&&H?"".concat(H,"-label"):void 0,$=nd[L],K={slots:z,slotProps:p({input:b,inputLabel:v,htmlInput:y,formHelperText:f,select:M},I)},q={},Y=K.slotProps.inputLabel;"outlined"===L&&(Y&&"undefined"!==typeof Y.shrink&&(q.notched=Y.shrink),q.label=w),A&&(M&&M.native||(q.id=void 0),q["aria-describedby"]=void 0);const[G,Q]=ts("root",{elementType:rd,shouldForwardComponentProp:!0,externalForwardedProps:p(p({},K),D),ownerState:B,className:Nt(W.root,i),ref:t,additionalProps:{disabled:u,error:d,fullWidth:h,required:T,color:s,variant:L}}),[X,J]=ts("input",{elementType:$,externalForwardedProps:K,additionalProps:q,ownerState:B}),[Z,ee]=ts("inputLabel",{elementType:ys,externalForwardedProps:K,ownerState:B}),[te,ne]=ts("htmlInput",{elementType:"input",externalForwardedProps:K,ownerState:B}),[re,oe]=ts("formHelperText",{elementType:Ts,externalForwardedProps:K,ownerState:B}),[ae,ie]=ts("select",{elementType:Zu,externalForwardedProps:K,ownerState:B}),le=(0,pi.jsx)(X,p({"aria-describedby":U,autoComplete:r,autoFocus:o,defaultValue:c,fullWidth:h,multiline:C,name:E,rows:_,maxRows:S,minRows:k,type:O,value:F,id:H,inputRef:x,onBlur:j,onChange:N,onFocus:P,placeholder:R,inputProps:ne,slots:{input:z.htmlInput?te:void 0}},J));return(0,pi.jsxs)(G,p(p({},Q),{},{children:[null!=w&&""!==w&&(0,pi.jsx)(Z,p(p({htmlFor:H,id:V},ee),{},{children:w})),A?(0,pi.jsx)(ae,p(p({"aria-describedby":U,id:H,labelId:V,value:F,input:le},ie),{},{children:a})):le,m&&(0,pi.jsx)(re,p(p({id:U},oe),{},{children:m}))]}))})),ad=od;function id(e){return bi("MuiTypography",e)}xi("MuiTypography",["root","h1","h2","h3","h4","h5","h6","subtitle1","subtitle2","body1","body2","inherit","button","caption","overline","alignLeft","alignRight","alignCenter","alignJustify","noWrap","gutterBottom","paragraph"]);const ld=["color"],sd=["align","className","component","gutterBottom","noWrap","paragraph","variant","variantMapping"],cd={primary:!0,secondary:!0,error:!0,info:!0,success:!0,warning:!0,textPrimary:!0,textSecondary:!0,textDisabled:!0},ud=Zi,dd=si("span",{name:"MuiTypography",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,n.variant&&t[n.variant],"inherit"!==n.align&&t["align".concat(_t(n.align))],n.noWrap&&t.noWrap,n.gutterBottom&&t.gutterBottom,n.paragraph&&t.paragraph]}})(ui((e=>{var t;let{theme:n}=e;return{margin:0,variants:[{props:{variant:"inherit"},style:{font:"inherit",lineHeight:"inherit",letterSpacing:"inherit"}},...Object.entries(n.typography).filter((e=>{let[t,n]=e;return"inherit"!==t&&n&&"object"===typeof n})).map((e=>{let[t,n]=e;return{props:{variant:t},style:n}})),...Object.entries(n.palette).filter(El()).map((e=>{let[t]=e;return{props:{color:t},style:{color:(n.vars||n).palette[t].main}}})),...Object.entries((null===(t=n.palette)||void 0===t?void 0:t.text)||{}).filter((e=>{let[,t]=e;return"string"===typeof t})).map((e=>{let[t]=e;return{props:{color:"text".concat(_t(t))},style:{color:(n.vars||n).palette.text[t]}}})),{props:e=>{let{ownerState:t}=e;return"inherit"!==t.align},style:{textAlign:"var(--Typography-textAlign)"}},{props:e=>{let{ownerState:t}=e;return t.noWrap},style:{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"}},{props:e=>{let{ownerState:t}=e;return t.gutterBottom},style:{marginBottom:"0.35em"}},{props:e=>{let{ownerState:t}=e;return t.paragraph},style:{marginBottom:16}}]}}))),pd={h1:"h1",h2:"h2",h3:"h3",h4:"h4",h5:"h5",h6:"h6",subtitle1:"h6",subtitle2:"h6",body1:"p",body2:"p",inherit:"p"},fd=r.forwardRef((function(e,t){const n=mi({props:e,name:"MuiTypography"}),{color:r}=n,o=l(n,ld),a=!cd[r],i=ud(p(p({},o),a&&{color:r})),{align:s="inherit",className:c,component:u,gutterBottom:d=!1,noWrap:f=!1,paragraph:h=!1,variant:m="body1",variantMapping:g=pd}=i,v=l(i,sd),y=p(p({},i),{},{align:s,color:r,className:c,component:u,gutterBottom:d,noWrap:f,paragraph:h,variant:m,variantMapping:g}),b=u||(h?"p":g[m]||pd[m])||"span",x=(e=>{const{align:t,gutterBottom:n,noWrap:r,paragraph:o,variant:a,classes:i}=e;return Pt({root:["root",a,"inherit"!==e.align&&"align".concat(_t(t)),n&&"gutterBottom",r&&"noWrap",o&&"paragraph"]},id,i)})(y);return(0,pi.jsx)(dd,p(p({as:b,ref:t,className:Nt(x.root,c)},v),{},{ownerState:y,style:p(p({},"inherit"!==s&&{"--Typography-textAlign":s}),v.style)}))})),hd=fd;function md(e){return bi("MuiInputAdornment",e)}const gd=xi("MuiInputAdornment",["root","filled","standard","outlined","positionStart","positionEnd","disablePointerEvents","hiddenLabel","sizeSmall"]),vd=["children","className","component","disablePointerEvents","disableTypography","position","variant"];var yd;const bd=si("div",{name:"MuiInputAdornment",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,t["position".concat(_t(n.position))],!0===n.disablePointerEvents&&t.disablePointerEvents,t[n.variant]]}})(ui((e=>{let{theme:t}=e;return{display:"flex",maxHeight:"2em",alignItems:"center",whiteSpace:"nowrap",color:(t.vars||t).palette.action.active,variants:[{props:{variant:"filled"},style:{["&.".concat(gd.positionStart,"&:not(.").concat(gd.hiddenLabel,")")]:{marginTop:16}}},{props:{position:"start"},style:{marginRight:8}},{props:{position:"end"},style:{marginLeft:8}},{props:{disablePointerEvents:!0},style:{pointerEvents:"none"}}]}}))),xd=r.forwardRef((function(e,t){const n=mi({props:e,name:"MuiInputAdornment"}),{children:o,className:a,component:i="div",disablePointerEvents:s=!1,disableTypography:c=!1,position:u,variant:d}=n,f=l(n,vd),h=Xi()||{};let m=d;d&&h.variant,h&&!m&&(m=h.variant);const g=p(p({},n),{},{hiddenLabel:h.hiddenLabel,size:h.size,disablePointerEvents:s,position:u,variant:m}),v=(e=>{const{classes:t,disablePointerEvents:n,hiddenLabel:r,position:o,size:a,variant:i}=e;return Pt({root:["root",n&&"disablePointerEvents",o&&"position".concat(_t(o)),i,r&&"hiddenLabel",a&&"size".concat(_t(a))]},md,t)})(g);return(0,pi.jsx)(Qi.Provider,{value:null,children:(0,pi.jsx)(bd,p(p({as:i,ownerState:g,className:Nt(v.root,a),ref:t},f),{},{children:"string"!==typeof o||c?(0,pi.jsxs)(r.Fragment,{children:["start"===u?yd||(yd=(0,pi.jsx)("span",{className:"notranslate","aria-hidden":!0,children:"\u200b"})):null,o]}):(0,pi.jsx)(hd,{color:"textSecondary",children:o})}))})})),wd=xd,Sd=Ii;function kd(e){try{return e.matches(":focus-visible")}catch(t){0}return!1}const Cd=Di;class Ed{static create(){return new Ed}static use(){const e=Js(Ed.create).current,[t,n]=r.useState(!1);return e.shouldMount=t,e.setShouldMount=n,r.useEffect(e.mountEffect,[t]),e}constructor(){u(this,"mountEffect",(()=>{this.shouldMount&&!this.didMount&&null!==this.ref.current&&(this.didMount=!0,this.mounted.resolve())})),this.ref={current:null},this.mounted=null,this.didMount=!1,this.shouldMount=!1,this.setShouldMount=null}mount(){return this.mounted||(this.mounted=function(){let e,t;const n=new Promise(((n,r)=>{e=n,t=r}));return n.resolve=e,n.reject=t,n}(),this.shouldMount=!0,this.setShouldMount(this.shouldMount)),this.mounted}start(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];this.mount().then((()=>{var e;return null===(e=this.ref.current)||void 0===e?void 0:e.start(...t)}))}stop(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];this.mount().then((()=>{var e;return null===(e=this.ref.current)||void 0===e?void 0:e.stop(...t)}))}pulsate(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];this.mount().then((()=>{var e;return null===(e=this.ref.current)||void 0===e?void 0:e.pulsate(...t)}))}}function jd(e,t){return t||(t=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}function Nd(e,t){var n=Object.create(null);return e&&r.Children.map(e,(function(e){return e})).forEach((function(e){n[e.key]=function(e){return t&&(0,r.isValidElement)(e)?t(e):e}(e)})),n}function Pd(e,t,n){return null!=n[t]?n[t]:e.props[t]}function Rd(e,t,n){var o=Nd(e.children),a=function(e,t){function n(n){return n in t?t[n]:e[n]}e=e||{},t=t||{};var r,o=Object.create(null),a=[];for(var i in e)i in t?a.length&&(o[i]=a,a=[]):a.push(i);var l={};for(var s in t){if(o[s])for(r=0;r<o[s].length;r++){var c=o[s][r];l[o[s][r]]=n(c)}l[s]=n(s)}for(r=0;r<a.length;r++)l[a[r]]=n(a[r]);return l}(t,o);return Object.keys(a).forEach((function(i){var l=a[i];if((0,r.isValidElement)(l)){var s=i in t,c=i in o,u=t[i],d=(0,r.isValidElement)(u)&&!u.props.in;!c||s&&!d?c||!s||d?c&&s&&(0,r.isValidElement)(u)&&(a[i]=(0,r.cloneElement)(l,{onExited:n.bind(null,l),in:u.props.in,exit:Pd(l,"exit",e),enter:Pd(l,"enter",e)})):a[i]=(0,r.cloneElement)(l,{in:!1}):a[i]=(0,r.cloneElement)(l,{onExited:n.bind(null,l),in:!0,exit:Pd(l,"exit",e),enter:Pd(l,"enter",e)})}})),a}var Td=Object.values||function(e){return Object.keys(e).map((function(t){return e[t]}))},_d=function(e){function t(t,n){var r,o=(r=e.call(this,t,n)||this).handleExited.bind(function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(r));return r.state={contextValue:{isMounting:!0},handleExited:o,firstRender:!0},r}rc(t,e);var n=t.prototype;return n.componentDidMount=function(){this.mounted=!0,this.setState({contextValue:{isMounting:!1}})},n.componentWillUnmount=function(){this.mounted=!1},t.getDerivedStateFromProps=function(e,t){var n,o,a=t.children,i=t.handleExited;return{children:t.firstRender?(n=e,o=i,Nd(n.children,(function(e){return(0,r.cloneElement)(e,{onExited:o.bind(null,e),in:!0,appear:Pd(e,"appear",n),enter:Pd(e,"enter",n),exit:Pd(e,"exit",n)})}))):Rd(e,a,i),firstRender:!1}},n.handleExited=function(e,t){var n=Nd(this.props.children);e.key in n||(e.props.onExited&&e.props.onExited(t),this.mounted&&this.setState((function(t){var n=At({},t.children);return delete n[e.key],{children:n}})))},n.render=function(){var e=this.props,t=e.component,n=e.childFactory,o=i(e,["component","childFactory"]),a=this.state.contextValue,l=Td(this.state.children).map(n);return delete o.appear,delete o.enter,delete o.exit,null===t?r.createElement(ic.Provider,{value:a},l):r.createElement(ic.Provider,{value:a},r.createElement(t,o,l))},t}(r.Component);_d.propTypes={},_d.defaultProps={component:"div",childFactory:function(e){return e}};const Ad=_d;const Md=function(e){const{className:t,classes:n,pulsate:o=!1,rippleX:a,rippleY:i,rippleSize:l,in:s,onExited:c,timeout:u}=e,[d,p]=r.useState(!1),f=Nt(t,n.ripple,n.rippleVisible,o&&n.ripplePulsate),h={width:l,height:l,top:-l/2+i,left:-l/2+a},m=Nt(n.child,d&&n.childLeaving,o&&n.childPulsate);return s||d||p(!0),r.useEffect((()=>{if(!s&&null!=c){const e=setTimeout(c,u);return()=>{clearTimeout(e)}}}),[c,s,u]),(0,pi.jsx)("span",{className:f,style:h,children:(0,pi.jsx)("span",{className:m})})};const zd=xi("MuiTouchRipple",["root","ripple","rippleVisible","ripplePulsate","child","childLeaving","childPulsate"]),Id=["center","classes","className"];var Od,Fd,Ld,Dd;const Bd=rl(Od||(Od=jd(["\n  0% {\n    transform: scale(0);\n    opacity: 0.1;\n  }\n\n  100% {\n    transform: scale(1);\n    opacity: 0.3;\n  }\n"]))),Wd=rl(Fd||(Fd=jd(["\n  0% {\n    opacity: 1;\n  }\n\n  100% {\n    opacity: 0;\n  }\n"]))),Hd=rl(Ld||(Ld=jd(["\n  0% {\n    transform: scale(1);\n  }\n\n  50% {\n    transform: scale(0.92);\n  }\n\n  100% {\n    transform: scale(1);\n  }\n"]))),Ud=si("span",{name:"MuiTouchRipple",slot:"Root"})({overflow:"hidden",pointerEvents:"none",position:"absolute",zIndex:0,top:0,right:0,bottom:0,left:0,borderRadius:"inherit"}),Vd=si(Md,{name:"MuiTouchRipple",slot:"Ripple"})(Dd||(Dd=jd(["\n  opacity: 0;\n  position: absolute;\n\n  &."," {\n    opacity: 0.3;\n    transform: scale(1);\n    animation-name: ",";\n    animation-duration: ","ms;\n    animation-timing-function: ",";\n  }\n\n  &."," {\n    animation-duration: ","ms;\n  }\n\n  & ."," {\n    opacity: 1;\n    display: block;\n    width: 100%;\n    height: 100%;\n    border-radius: 50%;\n    background-color: currentColor;\n  }\n\n  & ."," {\n    opacity: 0;\n    animation-name: ",";\n    animation-duration: ","ms;\n    animation-timing-function: ",";\n  }\n\n  & ."," {\n    position: absolute;\n    /* @noflip */\n    left: 0px;\n    top: 0;\n    animation-name: ",";\n    animation-duration: 2500ms;\n    animation-timing-function: ",";\n    animation-iteration-count: infinite;\n    animation-delay: 200ms;\n  }\n"])),zd.rippleVisible,Bd,550,(e=>{let{theme:t}=e;return t.transitions.easing.easeInOut}),zd.ripplePulsate,(e=>{let{theme:t}=e;return t.transitions.duration.shorter}),zd.child,zd.childLeaving,Wd,550,(e=>{let{theme:t}=e;return t.transitions.easing.easeInOut}),zd.childPulsate,Hd,(e=>{let{theme:t}=e;return t.transitions.easing.easeInOut})),$d=r.forwardRef((function(e,t){const n=mi({props:e,name:"MuiTouchRipple"}),{center:o=!1,classes:a={},className:i}=n,s=l(n,Id),[c,u]=r.useState([]),d=r.useRef(0),f=r.useRef(null);r.useEffect((()=>{f.current&&(f.current(),f.current=null)}),[c]);const h=r.useRef(!1),m=tc(),g=r.useRef(null),v=r.useRef(null),y=r.useCallback((e=>{const{pulsate:t,rippleX:n,rippleY:r,rippleSize:o,cb:i}=e;u((e=>[...e,(0,pi.jsx)(Vd,{classes:{ripple:Nt(a.ripple,zd.ripple),rippleVisible:Nt(a.rippleVisible,zd.rippleVisible),ripplePulsate:Nt(a.ripplePulsate,zd.ripplePulsate),child:Nt(a.child,zd.child),childLeaving:Nt(a.childLeaving,zd.childLeaving),childPulsate:Nt(a.childPulsate,zd.childPulsate)},timeout:550,pulsate:t,rippleX:n,rippleY:r,rippleSize:o},d.current)])),d.current+=1,f.current=i}),[a]),b=r.useCallback((function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:()=>{};const{pulsate:r=!1,center:a=o||t.pulsate,fakeElement:i=!1}=t;if("mousedown"===(null===e||void 0===e?void 0:e.type)&&h.current)return void(h.current=!1);"touchstart"===(null===e||void 0===e?void 0:e.type)&&(h.current=!0);const l=i?null:v.current,s=l?l.getBoundingClientRect():{width:0,height:0,left:0,top:0};let c,u,d;if(a||void 0===e||0===e.clientX&&0===e.clientY||!e.clientX&&!e.touches)c=Math.round(s.width/2),u=Math.round(s.height/2);else{const{clientX:t,clientY:n}=e.touches&&e.touches.length>0?e.touches[0]:e;c=Math.round(t-s.left),u=Math.round(n-s.top)}if(a)d=Math.sqrt((2*s.width**2+s.height**2)/3),d%2===0&&(d+=1);else{const e=2*Math.max(Math.abs((l?l.clientWidth:0)-c),c)+2,t=2*Math.max(Math.abs((l?l.clientHeight:0)-u),u)+2;d=Math.sqrt(e**2+t**2)}null!==e&&void 0!==e&&e.touches?null===g.current&&(g.current=()=>{y({pulsate:r,rippleX:c,rippleY:u,rippleSize:d,cb:n})},m.start(80,(()=>{g.current&&(g.current(),g.current=null)}))):y({pulsate:r,rippleX:c,rippleY:u,rippleSize:d,cb:n})}),[o,y,m]),x=r.useCallback((()=>{b({},{pulsate:!0})}),[b]),w=r.useCallback(((e,t)=>{if(m.clear(),"touchend"===(null===e||void 0===e?void 0:e.type)&&g.current)return g.current(),g.current=null,void m.start(0,(()=>{w(e,t)}));g.current=null,u((e=>e.length>0?e.slice(1):e)),f.current=t}),[m]);return r.useImperativeHandle(t,(()=>({pulsate:x,start:b,stop:w})),[x,b,w]),(0,pi.jsx)(Ud,p(p({className:Nt(zd.root,a.root,i),ref:v},s),{},{children:(0,pi.jsx)(Ad,{component:null,exit:!0,children:c})}))}));function Kd(e){return bi("MuiButtonBase",e)}const qd=xi("MuiButtonBase",["root","disabled","focusVisible"]),Yd=["action","centerRipple","children","className","component","disabled","disableRipple","disableTouchRipple","focusRipple","focusVisibleClassName","LinkComponent","onBlur","onClick","onContextMenu","onDragLeave","onFocus","onFocusVisible","onKeyDown","onKeyUp","onMouseDown","onMouseLeave","onMouseUp","onTouchEnd","onTouchMove","onTouchStart","tabIndex","TouchRippleProps","touchRippleRef","type"],Gd=si("button",{name:"MuiButtonBase",slot:"Root"})({display:"inline-flex",alignItems:"center",justifyContent:"center",position:"relative",boxSizing:"border-box",WebkitTapHighlightColor:"transparent",backgroundColor:"transparent",outline:0,border:0,margin:0,borderRadius:0,padding:0,cursor:"pointer",userSelect:"none",verticalAlign:"middle",MozAppearance:"none",WebkitAppearance:"none",textDecoration:"none",color:"inherit","&::-moz-focus-inner":{borderStyle:"none"},["&.".concat(qd.disabled)]:{pointerEvents:"none",cursor:"default"},"@media print":{colorAdjust:"exact"}});function Qd(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];return Cd((o=>(n&&n(o),r||e[t](o),!0)))}const Xd=r.forwardRef((function(e,t){const n=mi({props:e,name:"MuiButtonBase"}),{action:o,centerRipple:a=!1,children:i,className:s,component:c="button",disabled:u=!1,disableRipple:d=!1,disableTouchRipple:f=!1,focusRipple:h=!1,focusVisibleClassName:m,LinkComponent:g="a",onBlur:v,onClick:y,onContextMenu:b,onDragLeave:x,onFocus:w,onFocusVisible:S,onKeyDown:k,onKeyUp:C,onMouseDown:E,onMouseLeave:j,onMouseUp:N,onTouchEnd:P,onTouchMove:R,onTouchStart:T,tabIndex:_=0,TouchRippleProps:A,touchRippleRef:M,type:z}=n,I=l(n,Yd),O=r.useRef(null),F=Ed.use(),L=ul(F.ref,M),[D,B]=r.useState(!1);u&&D&&B(!1),r.useImperativeHandle(o,(()=>({focusVisible:()=>{B(!0),O.current.focus()}})),[]);const W=F.shouldMount&&!d&&!u;r.useEffect((()=>{D&&h&&!d&&F.pulsate()}),[d,h,D,F]);const H=Qd(F,"start",E,f),U=Qd(F,"stop",b,f),V=Qd(F,"stop",x,f),$=Qd(F,"stop",N,f),K=Qd(F,"stop",(e=>{D&&e.preventDefault(),j&&j(e)}),f),q=Qd(F,"start",T,f),Y=Qd(F,"stop",P,f),G=Qd(F,"stop",R,f),Q=Qd(F,"stop",(e=>{kd(e.target)||B(!1),v&&v(e)}),!1),X=Cd((e=>{O.current||(O.current=e.currentTarget),kd(e.target)&&(B(!0),S&&S(e)),w&&w(e)})),J=()=>{const e=O.current;return c&&"button"!==c&&!("A"===e.tagName&&e.href)},Z=Cd((e=>{h&&!e.repeat&&D&&" "===e.key&&F.stop(e,(()=>{F.start(e)})),e.target===e.currentTarget&&J()&&" "===e.key&&e.preventDefault(),k&&k(e),e.target===e.currentTarget&&J()&&"Enter"===e.key&&!u&&(e.preventDefault(),y&&y(e))})),ee=Cd((e=>{h&&" "===e.key&&D&&!e.defaultPrevented&&F.stop(e,(()=>{F.pulsate(e)})),C&&C(e),y&&e.target===e.currentTarget&&J()&&" "===e.key&&!e.defaultPrevented&&y(e)}));let te=c;"button"===te&&(I.href||I.to)&&(te=g);const ne={};"button"===te?(ne.type=void 0===z?"button":z,ne.disabled=u):(I.href||I.to||(ne.role="button"),u&&(ne["aria-disabled"]=u));const re=ul(t,O),oe=p(p({},n),{},{centerRipple:a,component:c,disabled:u,disableRipple:d,disableTouchRipple:f,focusRipple:h,tabIndex:_,focusVisible:D}),ae=(e=>{const{disabled:t,focusVisible:n,focusVisibleClassName:r,classes:o}=e,a=Pt({root:["root",t&&"disabled",n&&"focusVisible"]},Kd,o);return n&&r&&(a.root+=" ".concat(r)),a})(oe);return(0,pi.jsxs)(Gd,p(p(p({as:te,className:Nt(ae.root,s),ownerState:oe,onBlur:Q,onClick:y,onContextMenu:U,onFocus:X,onKeyDown:Z,onKeyUp:ee,onMouseDown:H,onMouseLeave:K,onMouseUp:$,onDragLeave:V,onTouchEnd:Y,onTouchMove:G,onTouchStart:q,ref:re,tabIndex:u?-1:_,type:z},ne),I),{},{children:[i,W?(0,pi.jsx)($d,p({ref:L,center:a},A)):null]}))}));function Jd(e){return bi("MuiCircularProgress",e)}xi("MuiCircularProgress",["root","determinate","indeterminate","colorPrimary","colorSecondary","svg","circle","circleDeterminate","circleIndeterminate","circleDisableShrink"]);const Zd=["className","color","disableShrink","size","style","thickness","value","variant"];var ep,tp,np,rp;const op=44,ap=rl(ep||(ep=jd(["\n  0% {\n    transform: rotate(0deg);\n  }\n\n  100% {\n    transform: rotate(360deg);\n  }\n"]))),ip=rl(tp||(tp=jd(["\n  0% {\n    stroke-dasharray: 1px, 200px;\n    stroke-dashoffset: 0;\n  }\n\n  50% {\n    stroke-dasharray: 100px, 200px;\n    stroke-dashoffset: -15px;\n  }\n\n  100% {\n    stroke-dasharray: 1px, 200px;\n    stroke-dashoffset: -126px;\n  }\n"]))),lp="string"!==typeof ap?nl(np||(np=jd(["\n        animation: "," 1.4s linear infinite;\n      "])),ap):null,sp="string"!==typeof ip?nl(rp||(rp=jd(["\n        animation: "," 1.4s ease-in-out infinite;\n      "])),ip):null,cp=si("span",{name:"MuiCircularProgress",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,t[n.variant],t["color".concat(_t(n.color))]]}})(ui((e=>{let{theme:t}=e;return{display:"inline-block",variants:[{props:{variant:"determinate"},style:{transition:t.transitions.create("transform")}},{props:{variant:"indeterminate"},style:lp||{animation:"".concat(ap," 1.4s linear infinite")}},...Object.entries(t.palette).filter(El()).map((e=>{let[n]=e;return{props:{color:n},style:{color:(t.vars||t).palette[n].main}}}))]}}))),up=si("svg",{name:"MuiCircularProgress",slot:"Svg"})({display:"block"}),dp=si("circle",{name:"MuiCircularProgress",slot:"Circle",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.circle,t["circle".concat(_t(n.variant))],n.disableShrink&&t.circleDisableShrink]}})(ui((e=>{let{theme:t}=e;return{stroke:"currentColor",variants:[{props:{variant:"determinate"},style:{transition:t.transitions.create("stroke-dashoffset")}},{props:{variant:"indeterminate"},style:{strokeDasharray:"80px, 200px",strokeDashoffset:0}},{props:e=>{let{ownerState:t}=e;return"indeterminate"===t.variant&&!t.disableShrink},style:sp||{animation:"".concat(ip," 1.4s ease-in-out infinite")}}]}}))),pp=r.forwardRef((function(e,t){const n=mi({props:e,name:"MuiCircularProgress"}),{className:r,color:o="primary",disableShrink:a=!1,size:i=40,style:s,thickness:c=3.6,value:u=0,variant:d="indeterminate"}=n,f=l(n,Zd),h=p(p({},n),{},{color:o,disableShrink:a,size:i,thickness:c,value:u,variant:d}),m=(e=>{const{classes:t,variant:n,color:r,disableShrink:o}=e;return Pt({root:["root",n,"color".concat(_t(r))],svg:["svg"],circle:["circle","circle".concat(_t(n)),o&&"circleDisableShrink"]},Jd,t)})(h),g={},v={},y={};if("determinate"===d){const e=2*Math.PI*((op-c)/2);g.strokeDasharray=e.toFixed(3),y["aria-valuenow"]=Math.round(u),g.strokeDashoffset="".concat(((100-u)/100*e).toFixed(3),"px"),v.transform="rotate(-90deg)"}return(0,pi.jsx)(cp,p(p(p({className:Nt(m.root,r),style:p(p({width:i,height:i},v),s),ownerState:h,ref:t,role:"progressbar"},y),f),{},{children:(0,pi.jsx)(up,{className:m.svg,ownerState:h,viewBox:"".concat(22," ").concat(22," ").concat(op," ").concat(op),children:(0,pi.jsx)(dp,{className:m.circle,style:g,ownerState:h,cx:op,cy:op,r:(op-c)/2,fill:"none",strokeWidth:c})})}))})),fp=pp;function hp(e){return bi("MuiIconButton",e)}const mp=xi("MuiIconButton",["root","disabled","colorInherit","colorPrimary","colorSecondary","colorError","colorInfo","colorSuccess","colorWarning","edgeStart","edgeEnd","sizeSmall","sizeMedium","sizeLarge","loading","loadingIndicator","loadingWrapper"]),gp=["edge","children","className","color","disabled","disableFocusRipple","size","id","loading","loadingIndicator"],vp=si(Xd,{name:"MuiIconButton",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,n.loading&&t.loading,"default"!==n.color&&t["color".concat(_t(n.color))],n.edge&&t["edge".concat(_t(n.edge))],t["size".concat(_t(n.size))]]}})(ui((e=>{let{theme:t}=e;return{textAlign:"center",flex:"0 0 auto",fontSize:t.typography.pxToRem(24),padding:8,borderRadius:"50%",color:(t.vars||t).palette.action.active,transition:t.transitions.create("background-color",{duration:t.transitions.duration.shortest}),variants:[{props:e=>!e.disableRipple,style:{"--IconButton-hoverBg":t.vars?"rgba(".concat(t.vars.palette.action.activeChannel," / ").concat(t.vars.palette.action.hoverOpacity,")"):qo(t.palette.action.active,t.palette.action.hoverOpacity),"&:hover":{backgroundColor:"var(--IconButton-hoverBg)","@media (hover: none)":{backgroundColor:"transparent"}}}},{props:{edge:"start"},style:{marginLeft:-12}},{props:{edge:"start",size:"small"},style:{marginLeft:-3}},{props:{edge:"end"},style:{marginRight:-12}},{props:{edge:"end",size:"small"},style:{marginRight:-3}}]}})),ui((e=>{let{theme:t}=e;return{variants:[{props:{color:"inherit"},style:{color:"inherit"}},...Object.entries(t.palette).filter(El()).map((e=>{let[n]=e;return{props:{color:n},style:{color:(t.vars||t).palette[n].main}}})),...Object.entries(t.palette).filter(El()).map((e=>{let[n]=e;return{props:{color:n},style:{"--IconButton-hoverBg":t.vars?"rgba(".concat((t.vars||t).palette[n].mainChannel," / ").concat(t.vars.palette.action.hoverOpacity,")"):qo((t.vars||t).palette[n].main,t.palette.action.hoverOpacity)}}})),{props:{size:"small"},style:{padding:5,fontSize:t.typography.pxToRem(18)}},{props:{size:"large"},style:{padding:12,fontSize:t.typography.pxToRem(28)}}],["&.".concat(mp.disabled)]:{backgroundColor:"transparent",color:(t.vars||t).palette.action.disabled},["&.".concat(mp.loading)]:{color:"transparent"}}}))),yp=si("span",{name:"MuiIconButton",slot:"LoadingIndicator"})((e=>{let{theme:t}=e;return{display:"none",position:"absolute",visibility:"visible",top:"50%",left:"50%",transform:"translate(-50%, -50%)",color:(t.vars||t).palette.action.disabled,variants:[{props:{loading:!0},style:{display:"flex"}}]}})),bp=r.forwardRef((function(e,t){const n=mi({props:e,name:"MuiIconButton"}),{edge:r=!1,children:o,className:a,color:i="default",disabled:s=!1,disableFocusRipple:c=!1,size:u="medium",id:d,loading:f=null,loadingIndicator:h}=n,m=l(n,gp),g=Sd(d),v=null!==h&&void 0!==h?h:(0,pi.jsx)(fp,{"aria-labelledby":g,color:"inherit",size:16}),y=p(p({},n),{},{edge:r,color:i,disabled:s,disableFocusRipple:c,loading:f,loadingIndicator:v,size:u}),b=(e=>{const{classes:t,disabled:n,color:r,edge:o,size:a,loading:i}=e;return Pt({root:["root",i&&"loading",n&&"disabled","default"!==r&&"color".concat(_t(r)),o&&"edge".concat(_t(o)),"size".concat(_t(a))],loadingIndicator:["loadingIndicator"],loadingWrapper:["loadingWrapper"]},hp,t)})(y);return(0,pi.jsxs)(vp,p(p({id:f?g:d,className:Nt(b.root,a),centerRipple:!0,focusRipple:!c,disabled:s||f,ref:t},m),{},{ownerState:y,children:["boolean"===typeof f&&(0,pi.jsx)("span",{className:b.loadingWrapper,style:{display:"contents"},children:(0,pi.jsx)(yp,{className:b.loadingIndicator,ownerState:y,children:f&&v})}),o]}))})),xp=bp;function wp(e){return bi("MuiButton",e)}const Sp=xi("MuiButton",["root","text","textInherit","textPrimary","textSecondary","textSuccess","textError","textInfo","textWarning","outlined","outlinedInherit","outlinedPrimary","outlinedSecondary","outlinedSuccess","outlinedError","outlinedInfo","outlinedWarning","contained","containedInherit","containedPrimary","containedSecondary","containedSuccess","containedError","containedInfo","containedWarning","disableElevation","focusVisible","disabled","colorInherit","colorPrimary","colorSecondary","colorSuccess","colorError","colorInfo","colorWarning","textSizeSmall","textSizeMedium","textSizeLarge","outlinedSizeSmall","outlinedSizeMedium","outlinedSizeLarge","containedSizeSmall","containedSizeMedium","containedSizeLarge","sizeMedium","sizeSmall","sizeLarge","fullWidth","startIcon","endIcon","icon","iconSizeSmall","iconSizeMedium","iconSizeLarge","loading","loadingWrapper","loadingIconPlaceholder","loadingIndicator","loadingPositionCenter","loadingPositionStart","loadingPositionEnd"]);const kp=r.createContext({});const Cp=r.createContext(void 0),Ep=["children","color","component","className","disabled","disableElevation","disableFocusRipple","endIcon","focusVisibleClassName","fullWidth","id","loading","loadingIndicator","loadingPosition","size","startIcon","type","variant"],jp=[{props:{size:"small"},style:{"& > *:nth-of-type(1)":{fontSize:18}}},{props:{size:"medium"},style:{"& > *:nth-of-type(1)":{fontSize:20}}},{props:{size:"large"},style:{"& > *:nth-of-type(1)":{fontSize:22}}}],Np=si(Xd,{shouldForwardProp:e=>ii(e)||"classes"===e,name:"MuiButton",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,t[n.variant],t["".concat(n.variant).concat(_t(n.color))],t["size".concat(_t(n.size))],t["".concat(n.variant,"Size").concat(_t(n.size))],"inherit"===n.color&&t.colorInherit,n.disableElevation&&t.disableElevation,n.fullWidth&&t.fullWidth,n.loading&&t.loading]}})(ui((e=>{let{theme:t}=e;const n="light"===t.palette.mode?t.palette.grey[300]:t.palette.grey[800],r="light"===t.palette.mode?t.palette.grey.A100:t.palette.grey[700];return p(p({},t.typography.button),{},{minWidth:64,padding:"6px 16px",border:0,borderRadius:(t.vars||t).shape.borderRadius,transition:t.transitions.create(["background-color","box-shadow","border-color","color"],{duration:t.transitions.duration.short}),"&:hover":{textDecoration:"none"},["&.".concat(Sp.disabled)]:{color:(t.vars||t).palette.action.disabled},variants:[{props:{variant:"contained"},style:{color:"var(--variant-containedColor)",backgroundColor:"var(--variant-containedBg)",boxShadow:(t.vars||t).shadows[2],"&:hover":{boxShadow:(t.vars||t).shadows[4],"@media (hover: none)":{boxShadow:(t.vars||t).shadows[2]}},"&:active":{boxShadow:(t.vars||t).shadows[8]},["&.".concat(Sp.focusVisible)]:{boxShadow:(t.vars||t).shadows[6]},["&.".concat(Sp.disabled)]:{color:(t.vars||t).palette.action.disabled,boxShadow:(t.vars||t).shadows[0],backgroundColor:(t.vars||t).palette.action.disabledBackground}}},{props:{variant:"outlined"},style:{padding:"5px 15px",border:"1px solid currentColor",borderColor:"var(--variant-outlinedBorder, currentColor)",backgroundColor:"var(--variant-outlinedBg)",color:"var(--variant-outlinedColor)",["&.".concat(Sp.disabled)]:{border:"1px solid ".concat((t.vars||t).palette.action.disabledBackground)}}},{props:{variant:"text"},style:{padding:"6px 8px",color:"var(--variant-textColor)",backgroundColor:"var(--variant-textBg)"}},...Object.entries(t.palette).filter(El()).map((e=>{let[n]=e;return{props:{color:n},style:{"--variant-textColor":(t.vars||t).palette[n].main,"--variant-outlinedColor":(t.vars||t).palette[n].main,"--variant-outlinedBorder":t.vars?"rgba(".concat(t.vars.palette[n].mainChannel," / 0.5)"):qo(t.palette[n].main,.5),"--variant-containedColor":(t.vars||t).palette[n].contrastText,"--variant-containedBg":(t.vars||t).palette[n].main,"@media (hover: hover)":{"&:hover":{"--variant-containedBg":(t.vars||t).palette[n].dark,"--variant-textBg":t.vars?"rgba(".concat(t.vars.palette[n].mainChannel," / ").concat(t.vars.palette.action.hoverOpacity,")"):qo(t.palette[n].main,t.palette.action.hoverOpacity),"--variant-outlinedBorder":(t.vars||t).palette[n].main,"--variant-outlinedBg":t.vars?"rgba(".concat(t.vars.palette[n].mainChannel," / ").concat(t.vars.palette.action.hoverOpacity,")"):qo(t.palette[n].main,t.palette.action.hoverOpacity)}}}}})),{props:{color:"inherit"},style:{color:"inherit",borderColor:"currentColor","--variant-containedBg":t.vars?t.vars.palette.Button.inheritContainedBg:n,"@media (hover: hover)":{"&:hover":{"--variant-containedBg":t.vars?t.vars.palette.Button.inheritContainedHoverBg:r,"--variant-textBg":t.vars?"rgba(".concat(t.vars.palette.text.primaryChannel," / ").concat(t.vars.palette.action.hoverOpacity,")"):qo(t.palette.text.primary,t.palette.action.hoverOpacity),"--variant-outlinedBg":t.vars?"rgba(".concat(t.vars.palette.text.primaryChannel," / ").concat(t.vars.palette.action.hoverOpacity,")"):qo(t.palette.text.primary,t.palette.action.hoverOpacity)}}}},{props:{size:"small",variant:"text"},style:{padding:"4px 5px",fontSize:t.typography.pxToRem(13)}},{props:{size:"large",variant:"text"},style:{padding:"8px 11px",fontSize:t.typography.pxToRem(15)}},{props:{size:"small",variant:"outlined"},style:{padding:"3px 9px",fontSize:t.typography.pxToRem(13)}},{props:{size:"large",variant:"outlined"},style:{padding:"7px 21px",fontSize:t.typography.pxToRem(15)}},{props:{size:"small",variant:"contained"},style:{padding:"4px 10px",fontSize:t.typography.pxToRem(13)}},{props:{size:"large",variant:"contained"},style:{padding:"8px 22px",fontSize:t.typography.pxToRem(15)}},{props:{disableElevation:!0},style:{boxShadow:"none","&:hover":{boxShadow:"none"},["&.".concat(Sp.focusVisible)]:{boxShadow:"none"},"&:active":{boxShadow:"none"},["&.".concat(Sp.disabled)]:{boxShadow:"none"}}},{props:{fullWidth:!0},style:{width:"100%"}},{props:{loadingPosition:"center"},style:{transition:t.transitions.create(["background-color","box-shadow","border-color"],{duration:t.transitions.duration.short}),["&.".concat(Sp.loading)]:{color:"transparent"}}}]})}))),Pp=si("span",{name:"MuiButton",slot:"StartIcon",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.startIcon,n.loading&&t.startIconLoadingStart,t["iconSize".concat(_t(n.size))]]}})((e=>{let{theme:t}=e;return{display:"inherit",marginRight:8,marginLeft:-4,variants:[{props:{size:"small"},style:{marginLeft:-2}},{props:{loadingPosition:"start",loading:!0},style:{transition:t.transitions.create(["opacity"],{duration:t.transitions.duration.short}),opacity:0}},{props:{loadingPosition:"start",loading:!0,fullWidth:!0},style:{marginRight:-8}},...jp]}})),Rp=si("span",{name:"MuiButton",slot:"EndIcon",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.endIcon,n.loading&&t.endIconLoadingEnd,t["iconSize".concat(_t(n.size))]]}})((e=>{let{theme:t}=e;return{display:"inherit",marginRight:-4,marginLeft:8,variants:[{props:{size:"small"},style:{marginRight:-2}},{props:{loadingPosition:"end",loading:!0},style:{transition:t.transitions.create(["opacity"],{duration:t.transitions.duration.short}),opacity:0}},{props:{loadingPosition:"end",loading:!0,fullWidth:!0},style:{marginLeft:-8}},...jp]}})),Tp=si("span",{name:"MuiButton",slot:"LoadingIndicator"})((e=>{let{theme:t}=e;return{display:"none",position:"absolute",visibility:"visible",variants:[{props:{loading:!0},style:{display:"flex"}},{props:{loadingPosition:"start"},style:{left:14}},{props:{loadingPosition:"start",size:"small"},style:{left:10}},{props:{variant:"text",loadingPosition:"start"},style:{left:6}},{props:{loadingPosition:"center"},style:{left:"50%",transform:"translate(-50%)",color:(t.vars||t).palette.action.disabled}},{props:{loadingPosition:"end"},style:{right:14}},{props:{loadingPosition:"end",size:"small"},style:{right:10}},{props:{variant:"text",loadingPosition:"end"},style:{right:6}},{props:{loadingPosition:"start",fullWidth:!0},style:{position:"relative",left:-10}},{props:{loadingPosition:"end",fullWidth:!0},style:{position:"relative",right:-10}}]}})),_p=si("span",{name:"MuiButton",slot:"LoadingIconPlaceholder"})({display:"inline-block",width:"1em",height:"1em"}),Ap=r.forwardRef((function(e,t){const n=r.useContext(kp),o=r.useContext(Cp),a=mi({props:di(n,e),name:"MuiButton"}),{children:i,color:s="primary",component:c="button",className:u,disabled:d=!1,disableElevation:f=!1,disableFocusRipple:h=!1,endIcon:m,focusVisibleClassName:g,fullWidth:v=!1,id:y,loading:b=null,loadingIndicator:x,loadingPosition:w="center",size:S="medium",startIcon:k,type:C,variant:E="text"}=a,j=l(a,Ep),N=Sd(y),P=null!==x&&void 0!==x?x:(0,pi.jsx)(fp,{"aria-labelledby":N,color:"inherit",size:16}),R=p(p({},a),{},{color:s,component:c,disabled:d,disableElevation:f,disableFocusRipple:h,fullWidth:v,loading:b,loadingIndicator:P,loadingPosition:w,size:S,type:C,variant:E}),T=(e=>{const{color:t,disableElevation:n,fullWidth:r,size:o,variant:a,loading:i,loadingPosition:l,classes:s}=e,c=Pt({root:["root",i&&"loading",a,"".concat(a).concat(_t(t)),"size".concat(_t(o)),"".concat(a,"Size").concat(_t(o)),"color".concat(_t(t)),n&&"disableElevation",r&&"fullWidth",i&&"loadingPosition".concat(_t(l))],startIcon:["icon","startIcon","iconSize".concat(_t(o))],endIcon:["icon","endIcon","iconSize".concat(_t(o))],loadingIndicator:["loadingIndicator"],loadingWrapper:["loadingWrapper"]},wp,s);return p(p({},s),c)})(R),_=(k||b&&"start"===w)&&(0,pi.jsx)(Pp,{className:T.startIcon,ownerState:R,children:k||(0,pi.jsx)(_p,{className:T.loadingIconPlaceholder,ownerState:R})}),A=(m||b&&"end"===w)&&(0,pi.jsx)(Rp,{className:T.endIcon,ownerState:R,children:m||(0,pi.jsx)(_p,{className:T.loadingIconPlaceholder,ownerState:R})}),M=o||"",z="boolean"===typeof b?(0,pi.jsx)("span",{className:T.loadingWrapper,style:{display:"contents"},children:b&&(0,pi.jsx)(Tp,{className:T.loadingIndicator,ownerState:R,children:P})}):null;return(0,pi.jsxs)(Np,p(p({ownerState:R,className:Nt(n.className,T.root,u,M),component:c,disabled:d||b,focusRipple:!h,focusVisibleClassName:Nt(T.focusVisible,g),ref:t,type:C,id:b?N:y},j),{},{classes:T,children:[_,"end"!==w&&z,i,"end"===w&&z,A]}))})),Mp=Ap,zp=ji((0,pi.jsx)("path",{d:"M12 7c2.76 0 5 2.24 5 5 0 .65-.13 1.26-.36 1.83l2.92 2.92c1.51-1.26 2.7-2.89 3.43-4.75-1.73-4.39-6-7.5-11-7.5-1.4 0-2.74.25-3.98.7l2.16 2.16C10.74 7.13 11.35 7 12 7M2 4.27l2.28 2.28.46.46C3.08 8.3 1.78 10.02 1 12c1.73 4.39 6 7.5 11 7.5 1.55 0 3.03-.3 4.38-.84l.42.42L19.73 22 21 20.73 3.27 3zM7.53 9.8l1.55 1.55c-.05.21-.08.43-.08.65 0 1.66 1.34 3 3 3 .22 0 .44-.03.65-.08l1.55 1.55c-.67.33-1.41.53-2.2.53-2.76 0-5-2.24-5-5 0-.79.2-1.53.53-2.2m4.31-.78 3.15 3.15.02-.16c0-1.66-1.34-3-3-3z"})),Ip=ji((0,pi.jsx)("path",{d:"M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5M12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5m0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3"})),Op={OPENAI_API_KEY:"********************************************************************************************************************************************************************",GOOGLE_SPEECH_API_KEY:"AIzaSyCycXmFu0ArR7wtVA87GGzX22RLTbNCokw",DEEPGRAM_API_KEY:"****************************************",REACT_APP_API_URL:"https://api.interviewsupport-ai.com/index.php/",isDevelopment:!1,isProduction:!0,hasKey:function(e){return!!this[e]},hasGoogleSpeechAPI:function(){return!!this.GOOGLE_SPEECH_API_KEY},hasDeepgramAPI:function(){return!!this.DEEPGRAM_API_KEY},hasOpenAIAPI:function(){return!!this.OPENAI_API_KEY},getSampleDeepgramKey:function(){return this.isDevelopment?"3e94fc5a6e60d63146576d3bd188a8737cfeb360":null}},Fp=async(e,t)=>{let n=null;try{var r;let o={"Content-Type":"application/json"};const a=localStorage.getItem("token");a&&(o.Authorization="Bearer ".concat(a));const i=await fetch(Op.REACT_APP_API_URL+e,{method:"POST",headers:o,body:JSON.stringify(t)});n=await i.json(),Lp(null===(r=n)||void 0===r?void 0:r.error)}catch(o){console.error("Fetch error:",o)}finally{return n}},Lp=e=>{"loginUser"===e&&(localStorage.removeItem("userDetails"),localStorage.removeItem("token"),window.location.href="/")};const Dp=function(){const e=ge(),[t,n]=r.useState(!1),[o,a]=r.useState(""),[i,l]=r.useState(""),[s,c]=r.useState(!1),[u,d]=r.useState("");return(0,r.useEffect)((()=>{localStorage.getItem("userDetails")&&e("/home")}),[e]),(0,r.useEffect)((()=>{const e=localStorage.getItem("userDetails"),t=localStorage.getItem("userType");e&&"changePassword"===t&&c(!0)}),[e]),(0,pi.jsxs)(pi.Fragment,{children:[(0,pi.jsx)(Ti,{}),(0,pi.jsx)("div",{className:"login-container",children:(0,pi.jsxs)("div",{className:"screen",children:[(0,pi.jsx)("div",{className:"screen__content",children:(0,pi.jsxs)("div",{className:"login",children:[u&&(0,pi.jsx)("div",{style:{padding:"0 0 10px 0"},children:(0,pi.jsx)("span",{style:{color:"red",fontSize:14},children:u})}),(0,pi.jsx)("div",{className:"login__field",children:(0,pi.jsx)(ad,{id:"outlined-basic",label:"Username",variant:"outlined",value:o,onChange:e=>a(e.target.value),placeholder:"Enter username",className:"w-100"})}),(0,pi.jsx)("div",{className:"login__field",children:(0,pi.jsx)(ad,{id:"outlined-basic",label:"Password",variant:"outlined",value:i,type:t?"text":"password",InputProps:{endAdornment:(0,pi.jsx)(wd,{position:"end",children:(0,pi.jsx)(xp,{onClick:()=>{n((e=>!e))},edge:"end",children:t?(0,pi.jsx)(zp,{}):(0,pi.jsx)(Ip,{})})})},onChange:e=>l(e.target.value),className:"w-100"})}),(0,pi.jsxs)("button",{className:"button login__submit",onClick:async()=>{d("");const t=await Fp("interview-app/auth/login",{email:o,password:i});null!==t&&void 0!==t&&t.status&&null!==t&&void 0!==t&&t.result&&null!==t&&void 0!==t&&t.token?(localStorage.setItem("userDetails",JSON.stringify(t.result)),localStorage.setItem("token",t.token),e("home")):d((null===t||void 0===t?void 0:t.message)||"Invalid credentials. Please try again.")},children:[(0,pi.jsx)("span",{className:"button__text",children:"Log In Now"}),(0,pi.jsx)("i",{className:"button__icon fas fa-chevron-right"})]}),(0,pi.jsxs)("div",{style:{marginTop:16,textAlign:"center"},children:[(0,pi.jsx)("span",{children:"Don't have an account? "}),(0,pi.jsx)("a",{href:"/signup",className:"signup-link",children:"Sign up"})]})]})}),(0,pi.jsxs)("div",{className:"screen__background",children:[(0,pi.jsx)("span",{className:"screen__background__shape screen__background__shape4"}),(0,pi.jsx)("span",{className:"screen__background__shape screen__background__shape3"}),(0,pi.jsx)("span",{className:"screen__background__shape screen__background__shape2"}),(0,pi.jsx)("span",{className:"screen__background__shape screen__background__shape1"})]})]})}),s&&(0,pi.jsx)("div",{className:"modal-overlay",children:(0,pi.jsxs)("div",{className:"modal-container",children:[(0,pi.jsx)("div",{className:"modal-header",children:(0,pi.jsx)("h3",{children:"Update Password Required"})}),(0,pi.jsx)("div",{className:"modal-content",children:(0,pi.jsx)("p",{children:"For your security, please update your password now."})}),(0,pi.jsxs)("div",{className:"modal-footer",children:[(0,pi.jsx)(Mp,{variant:"contained",color:"primary",onClick:()=>{c(!1),e("/profile")},children:"Update Password"}),(0,pi.jsx)(Mp,{variant:"outlined",onClick:()=>c(!1),children:"Later"})]})]})}),(0,pi.jsx)(_i,{})]})};function Bp(e){return bi("MuiDialog",e)}const Wp=xi("MuiDialog",["root","scrollPaper","scrollBody","container","paper","paperScrollPaper","paperScrollBody","paperWidthFalse","paperWidthXs","paperWidthSm","paperWidthMd","paperWidthLg","paperWidthXl","paperFullWidth","paperFullScreen"]);const Hp=r.createContext({}),Up=["aria-describedby","aria-labelledby","aria-modal","BackdropComponent","BackdropProps","children","className","disableEscapeKeyDown","fullScreen","fullWidth","maxWidth","onClick","onClose","open","PaperComponent","PaperProps","scroll","slots","slotProps","TransitionComponent","transitionDuration","TransitionProps"],Vp=si(Dc,{name:"MuiDialog",slot:"Backdrop",overrides:(e,t)=>t.backdrop})({zIndex:-1}),$p=si(eu,{name:"MuiDialog",slot:"Root"})({"@media print":{position:"absolute !important"}}),Kp=si("div",{name:"MuiDialog",slot:"Container",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.container,t["scroll".concat(_t(n.scroll))]]}})({height:"100%","@media print":{height:"auto"},outline:0,variants:[{props:{scroll:"paper"},style:{display:"flex",justifyContent:"center",alignItems:"center"}},{props:{scroll:"body"},style:{overflowY:"auto",overflowX:"hidden",textAlign:"center","&::after":{content:'""',display:"inline-block",verticalAlign:"middle",height:"100%",width:"0"}}}]}),qp=si(ou,{name:"MuiDialog",slot:"Paper",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.paper,t["scrollPaper".concat(_t(n.scroll))],t["paperWidth".concat(_t(String(n.maxWidth)))],n.fullWidth&&t.paperFullWidth,n.fullScreen&&t.paperFullScreen]}})(ui((e=>{let{theme:t}=e;return{margin:32,position:"relative",overflowY:"auto","@media print":{overflowY:"visible",boxShadow:"none"},variants:[{props:{scroll:"paper"},style:{display:"flex",flexDirection:"column",maxHeight:"calc(100% - 64px)"}},{props:{scroll:"body"},style:{display:"inline-block",verticalAlign:"middle",textAlign:"initial"}},{props:e=>{let{ownerState:t}=e;return!t.maxWidth},style:{maxWidth:"calc(100% - 64px)"}},{props:{maxWidth:"xs"},style:{maxWidth:"px"===t.breakpoints.unit?Math.max(t.breakpoints.values.xs,444):"max(".concat(t.breakpoints.values.xs).concat(t.breakpoints.unit,", 444px)"),["&.".concat(Wp.paperScrollBody)]:{[t.breakpoints.down(Math.max(t.breakpoints.values.xs,444)+64)]:{maxWidth:"calc(100% - 64px)"}}}},...Object.keys(t.breakpoints.values).filter((e=>"xs"!==e)).map((e=>({props:{maxWidth:e},style:{maxWidth:"".concat(t.breakpoints.values[e]).concat(t.breakpoints.unit),["&.".concat(Wp.paperScrollBody)]:{[t.breakpoints.down(t.breakpoints.values[e]+64)]:{maxWidth:"calc(100% - 64px)"}}}}))),{props:e=>{let{ownerState:t}=e;return t.fullWidth},style:{width:"calc(100% - 64px)"}},{props:e=>{let{ownerState:t}=e;return t.fullScreen},style:{margin:0,width:"100%",maxWidth:"100%",height:"100%",maxHeight:"none",borderRadius:0,["&.".concat(Wp.paperScrollBody)]:{margin:0,maxWidth:"100%"}}}]}}))),Yp=r.forwardRef((function(e,t){const n=mi({props:e,name:"MuiDialog"}),o=mc(),a={enter:o.transitions.duration.enteringScreen,exit:o.transitions.duration.leavingScreen},{"aria-describedby":i,"aria-labelledby":s,"aria-modal":c=!0,BackdropComponent:u,BackdropProps:d,children:f,className:h,disableEscapeKeyDown:m=!1,fullScreen:g=!1,fullWidth:v=!1,maxWidth:y="sm",onClick:b,onClose:x,open:w,PaperComponent:S=ou,PaperProps:k={},scroll:C="paper",slots:E={},slotProps:j={},TransitionComponent:N=Ic,transitionDuration:P=a,TransitionProps:R}=n,T=l(n,Up),_=p(p({},n),{},{disableEscapeKeyDown:m,fullScreen:g,fullWidth:v,maxWidth:y,scroll:C}),A=(e=>{const{classes:t,scroll:n,maxWidth:r,fullWidth:o,fullScreen:a}=e;return Pt({root:["root"],container:["container","scroll".concat(_t(n))],paper:["paper","paperScroll".concat(_t(n)),"paperWidth".concat(_t(String(r))),o&&"paperFullWidth",a&&"paperFullScreen"]},Bp,t)})(_),M=r.useRef(),z=Ii(s),I=r.useMemo((()=>({titleId:z})),[z]),O={slots:p({transition:N},E),slotProps:p({transition:R,paper:k,backdrop:d},j)},[F,L]=ts("root",{elementType:$p,shouldForwardComponentProp:!0,externalForwardedProps:O,ownerState:_,className:Nt(A.root,h),ref:t}),[D,B]=ts("backdrop",{elementType:Vp,shouldForwardComponentProp:!0,externalForwardedProps:O,ownerState:_}),[W,H]=ts("paper",{elementType:qp,shouldForwardComponentProp:!0,externalForwardedProps:O,ownerState:_,className:Nt(A.paper,k.className)}),[U,V]=ts("container",{elementType:Kp,externalForwardedProps:O,ownerState:_,className:A.container}),[$,K]=ts("transition",{elementType:Ic,externalForwardedProps:O,ownerState:_,additionalProps:{appear:!0,in:w,timeout:P,role:"presentation"}});return(0,pi.jsx)(F,p(p(p({closeAfterTransition:!0,slots:{backdrop:D},slotProps:{backdrop:p({transitionDuration:P,as:u},B)},disableEscapeKeyDown:m,onClose:x,open:w,onClick:e=>{b&&b(e),M.current&&(M.current=null,x&&x(e,"backdropClick"))}},L),T),{},{children:(0,pi.jsx)($,p(p({},K),{},{children:(0,pi.jsx)(U,p(p({onMouseDown:e=>{M.current=e.target===e.currentTarget}},V),{},{children:(0,pi.jsx)(W,p(p({as:S,elevation:24,role:"dialog","aria-describedby":i,"aria-labelledby":z,"aria-modal":c},H),{},{children:(0,pi.jsx)(Hp.Provider,{value:I,children:f})}))}))}))}))})),Gp=Yp;function Qp(e){return bi("MuiDialogActions",e)}xi("MuiDialogActions",["root","spacing"]);const Xp=["className","disableSpacing"],Jp=si("div",{name:"MuiDialogActions",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,!n.disableSpacing&&t.spacing]}})({display:"flex",alignItems:"center",padding:8,justifyContent:"flex-end",flex:"0 0 auto",variants:[{props:e=>{let{ownerState:t}=e;return!t.disableSpacing},style:{"& > :not(style) ~ :not(style)":{marginLeft:8}}}]}),Zp=r.forwardRef((function(e,t){const n=mi({props:e,name:"MuiDialogActions"}),{className:r,disableSpacing:o=!1}=n,a=l(n,Xp),i=p(p({},n),{},{disableSpacing:o}),s=(e=>{const{classes:t,disableSpacing:n}=e;return Pt({root:["root",!n&&"spacing"]},Qp,t)})(i);return(0,pi.jsx)(Jp,p({className:Nt(s.root,r),ownerState:i,ref:t},a))}));function ef(e){return bi("MuiDialogContent",e)}xi("MuiDialogContent",["root","dividers"]);function tf(e){return bi("MuiDialogTitle",e)}const nf=xi("MuiDialogTitle",["root"]),rf=["className","dividers"],of=si("div",{name:"MuiDialogContent",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,n.dividers&&t.dividers]}})(ui((e=>{let{theme:t}=e;return{flex:"1 1 auto",WebkitOverflowScrolling:"touch",overflowY:"auto",padding:"20px 24px",variants:[{props:e=>{let{ownerState:t}=e;return t.dividers},style:{padding:"16px 24px",borderTop:"1px solid ".concat((t.vars||t).palette.divider),borderBottom:"1px solid ".concat((t.vars||t).palette.divider)}},{props:e=>{let{ownerState:t}=e;return!t.dividers},style:{[".".concat(nf.root," + &")]:{paddingTop:0}}}]}}))),af=r.forwardRef((function(e,t){const n=mi({props:e,name:"MuiDialogContent"}),{className:r,dividers:o=!1}=n,a=l(n,rf),i=p(p({},n),{},{dividers:o}),s=(e=>{const{classes:t,dividers:n}=e;return Pt({root:["root",n&&"dividers"]},ef,t)})(i);return(0,pi.jsx)(of,p({className:Nt(s.root,r),ownerState:i,ref:t},a))}));function lf(e){return bi("MuiDialogContentText",e)}xi("MuiDialogContentText",["root"]);const sf=["children","className"],cf=si(hd,{shouldForwardProp:e=>ii(e)||"classes"===e,name:"MuiDialogContentText",slot:"Root"})({}),uf=r.forwardRef((function(e,t){const n=mi({props:e,name:"MuiDialogContentText"}),{children:r,className:o}=n,a=l(n,sf),i=(e=>{const{classes:t}=e,n=Pt({root:["root"]},lf,t);return p(p({},t),n)})(a);return(0,pi.jsx)(cf,p(p({component:"p",variant:"body1",color:"textSecondary",ref:t,ownerState:a,className:Nt(i.root,o)},n),{},{classes:i}))})),df=["className","id"],pf=si(hd,{name:"MuiDialogTitle",slot:"Root"})({padding:"16px 24px",flex:"0 0 auto"}),ff=r.forwardRef((function(e,t){const n=mi({props:e,name:"MuiDialogTitle"}),{className:o,id:a}=n,i=l(n,df),s=n,c=(e=>{const{classes:t}=e;return Pt({root:["root"]},tf,t)})(s),{titleId:u=a}=r.useContext(Hp);return(0,pi.jsx)(pf,p({component:"h2",className:Nt(c.root,o),ownerState:s,ref:t,variant:"h6",id:null!==a&&void 0!==a?a:u},i))})),hf=ji((0,pi.jsx)("path",{d:"M12 14c1.66 0 2.99-1.34 2.99-3L15 5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3m5.3-3c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.48 6-3.3 6-6.72z"})),mf=ji((0,pi.jsx)("path",{d:"M6 6h12v12H6z"})),gf=ji((0,pi.jsx)("path",{d:"M8 5v14l11-7z"})),vf=ji((0,pi.jsx)("path",{d:"M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6zM19 4h-3.5l-1-1h-5l-1 1H5v2h14z"}));const yf=function(e){let{onStartSession:t}=e;const[n,o]=(0,r.useState)(""),[a,i]=(0,r.useState)(""),[l,s]=(0,r.useState)(""),[c,u]=(0,r.useState)([]),[d,p]=(0,r.useState)(null),[f,h]=(0,r.useState)(""),[m,g]=(0,r.useState)(""),[v,y]=(0,r.useState)(!1),[b,x]=(0,r.useState)(0),[w,S]=(0,r.useState)(null),[k,C]=(0,r.useState)(""),E=(0,r.useRef)(null),j=(0,r.useRef)(null),[N,P]=(0,r.useState)(!1),[R,T]=(0,r.useState)(5);(0,r.useEffect)((()=>{const e=localStorage.getItem("userDetails");if(e)try{const t=JSON.parse(e);t.timeRemaining&&T(t.timeRemaining)}catch(t){console.error("Error parsing user data:",t)}}),[]);const _=()=>{E.current&&v&&(E.current.stop(),clearInterval(j.current))},A=e=>{const t=Math.floor(e/60),n=e%60;return"".concat(t.toString().padStart(2,"0"),":").concat(n.toString().padStart(2,"0"))},M=e=>{if(!e)return!0;const t=e.trim().split(/\s+/).length;return t>1e4?(g("Job description exceeds 10,000 words (current: ".concat(t,")")),!1):(g(""),!0)},z=e=>{c.includes(e)?u(c.filter((t=>t!==e))):u([...c,e])};return(0,pi.jsxs)("div",{className:"interview-form",children:[(0,pi.jsx)("h2",{children:"Simulate Your Interview Session"}),(0,pi.jsxs)("form",{onSubmit:e=>{e.preventDefault(),n.trim()&&M(l)&&P(!0)},children:[(0,pi.jsxs)("div",{className:"form-group",children:[(0,pi.jsx)("label",{htmlFor:"jobTitle",children:"Job Title"}),(0,pi.jsx)("input",{type:"text",id:"jobTitle",value:n,onChange:e=>o(e.target.value),placeholder:"e.g. Frontend Developer",required:!0})]}),(0,pi.jsxs)("div",{className:"form-group",children:[(0,pi.jsx)("label",{htmlFor:"skillset",children:"Your Skillset"}),(0,pi.jsx)("input",{type:"text",id:"skillset",value:a,onChange:e=>i(e.target.value),placeholder:"Input skills separated by commas (e.g. React, JavaScript, CSS)"}),(0,pi.jsx)("p",{className:"help-text",children:"Enter your skills separated by commas"})]}),(0,pi.jsxs)("div",{className:"form-group",children:[(0,pi.jsx)("label",{htmlFor:"jobDescription",children:"Job Description"}),(0,pi.jsx)("textarea",{id:"jobDescription",value:l,onChange:e=>{s(e.target.value),M(e.target.value)},placeholder:"Paste the job description here (max 10,000 words)",rows:6,className:"job-description-textarea"}),m&&(0,pi.jsx)("p",{className:"error-text",children:m}),(0,pi.jsx)("p",{className:"help-text",children:"Paste the job description to get more targeted questions (max 10,000 words)"})]}),(0,pi.jsxs)("div",{className:"form-group",children:[(0,pi.jsx)("label",{children:"Focus Areas"}),(0,pi.jsxs)("div",{className:"checkbox-group",children:[(0,pi.jsxs)("label",{children:[(0,pi.jsx)("input",{type:"checkbox",checked:c.includes("technical"),onChange:()=>z("technical")}),"Technical Skills"]}),(0,pi.jsxs)("label",{children:[(0,pi.jsx)("input",{type:"checkbox",checked:c.includes("behavioral"),onChange:()=>z("behavioral")}),"Behavioral Questions"]}),(0,pi.jsxs)("label",{children:[(0,pi.jsx)("input",{type:"checkbox",checked:c.includes("problemSolving"),onChange:()=>z("problemSolving")}),"Problem Solving"]})]})]}),(0,pi.jsxs)("div",{className:"form-group",children:[(0,pi.jsx)("label",{htmlFor:"resumeUpload",children:"Resume (Optional)"}),(0,pi.jsxs)("div",{className:"file-upload-container",children:[(0,pi.jsxs)("div",{className:"file-input-wrapper",children:[(0,pi.jsx)("button",{type:"button",className:"file-input-button",children:"Choose File"}),(0,pi.jsx)("input",{type:"file",id:"resumeUpload",onChange:e=>{const t=e.target.files[0];if(h(""),!t)return void p(null);const n=t.type;return["application/pdf","application/vnd.openxmlformats-officedocument.wordprocessingml.document"].includes(n)?t.size>5242880?(h("File size should be less than 5MB"),p(null),void(e.target.value="")):void p(t):(h("Please upload a PDF or DOCX file"),p(null),void(e.target.value=""))},accept:".pdf,.docx,application/pdf,application/vnd.openxmlformats-officedocument.wordprocessingml.document",className:"file-input"}),(0,pi.jsx)("span",{className:"file-name",children:d?d.name:"No file chosen"})]}),d&&(0,pi.jsx)("button",{type:"button",className:"clear-file",onClick:()=>{p(null),h("");const e=document.getElementById("resumeUpload");e&&(e.value="")},children:"Remove"})]}),f&&(0,pi.jsx)("p",{className:"error-text",children:f}),(0,pi.jsx)("p",{className:"help-text",children:"Upload your resume to personalize interview questions (PDF or DOCX, max 5MB)"}),(0,pi.jsxs)("p",{className:"help-text",children:[(0,pi.jsx)("strong",{children:"Note:"})," Remove any sensitive information from your resume"]})]}),(0,pi.jsxs)("div",{className:"form-group",children:[(0,pi.jsx)("label",{children:"Record Your Voice (Optional - 2 mins max)"}),(0,pi.jsxs)("div",{className:"voice-recorder-container",children:[k?(0,pi.jsxs)("div",{className:"playback-controls",children:[(0,pi.jsx)("div",{className:"audio-info",children:(0,pi.jsxs)("span",{children:["Recording complete: ",A(b)]})}),(0,pi.jsxs)("div",{className:"audio-buttons",children:[(0,pi.jsxs)("button",{type:"button",className:"play-button",onClick:()=>{if(k){new Audio(k).play()}},children:[(0,pi.jsx)(gf,{})," Play"]}),(0,pi.jsxs)("button",{type:"button",className:"delete-button",onClick:()=>{k&&(URL.revokeObjectURL(k),S(null),C(""),x(0))},children:[(0,pi.jsx)(vf,{})," Delete"]})]})]}):(0,pi.jsxs)("div",{className:"recording-controls",children:[(0,pi.jsx)("button",{type:"button",className:"record-button ".concat(v?"recording":""),onClick:v?_:async()=>{try{x(0),S(null),C("");const e=await navigator.mediaDevices.getUserMedia({audio:!0}),t=new MediaRecorder(e);E.current=t;const n=[];t.ondataavailable=e=>{e.data.size>0&&n.push(e.data)},t.onstop=()=>{const t=new Blob(n,{type:"audio/wav"}),r=URL.createObjectURL(t);S(t),C(r),y(!1),e.getTracks().forEach((e=>e.stop()))},t.start(),y(!0);const r=120;let o=0;j.current=setInterval((()=>{o+=1,x(o),o>=r&&_()}),1e3)}catch(e){console.error("Error starting recording:",e),alert("Could not access microphone. Please check your permissions.")}},children:v?(0,pi.jsxs)(pi.Fragment,{children:[(0,pi.jsx)(mf,{})," Stop Recording (",A(b),")"]}):(0,pi.jsxs)(pi.Fragment,{children:[(0,pi.jsx)(hf,{})," Start Recording"]})}),v&&(0,pi.jsxs)("p",{className:"recording-indicator",children:["Recording in progress... (",A(b),")"]})]}),(0,pi.jsx)("p",{className:"help-text",children:"Recording your voice helps the AI understand your speaking style and accent"})]})]}),(0,pi.jsx)("button",{type:"submit",className:"start-button",children:"Start Interview"})]}),(0,pi.jsxs)(Gp,{open:N,onClose:()=>P(!1),"aria-labelledby":"alert-dialog-title","aria-describedby":"alert-dialog-description",children:[(0,pi.jsx)(ff,{id:"alert-dialog-title",children:"Session Time Information"}),(0,pi.jsx)(af,{children:(0,pi.jsxs)(uf,{id:"alert-dialog-description",children:["You have ",R," minutes remaining in your session. Would you like to continue with the interview?"]})}),(0,pi.jsxs)(Zp,{children:[(0,pi.jsx)(Mp,{onClick:()=>P(!1),color:"primary",children:"Cancel"}),(0,pi.jsx)(Mp,{onClick:()=>{P(!1);t({jobTitle:n,skillset:a,jobDescription:l,focusAreas:c,resumeFile:d,audioBlob:w,sessionTimeRemaining:R})},color:"primary",autoFocus:!0,children:"Continue"})]})]})]})},bf=ji((0,pi.jsx)("path",{d:"M2.01 21 23 12 2.01 3 2 10l15 2-15 2z"})),xf=ji([(0,pi.jsx)("path",{d:"M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2M12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8"},"0"),(0,pi.jsx)("path",{d:"M12.5 7H11v6l5.25 3.15.75-1.23-4.5-2.67z"},"1")]),wf=ji((0,pi.jsx)("path",{d:"M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"}));const Sf=function(){const[e,t]=(0,r.useState)(!1),[n,o]=(0,r.useState)(""),[a,i]=(0,r.useState)(""),[l,s]=(0,r.useState)(!1),c=300,[u,d]=(0,r.useState)(c),[p,f]=(0,r.useState)(!1),[h,m]=(0,r.useState)(!1),g=(0,r.useRef)(null),v=(0,r.useRef)(null),y=(0,r.useRef)(null),b=(0,r.useRef)(null),x=(0,r.useCallback)((e=>{const t=Math.floor(e/60),n=e%60;return"".concat(t.toString().padStart(2,"0"),":").concat(n.toString().padStart(2,"0"))}),[]),w=(0,r.useCallback)((()=>{d(c),m(!1),g.current&&clearInterval(g.current),g.current=setInterval((()=>{d((e=>{const t=e-1;return 60===t&&f(!0),t<=0?(clearInterval(g.current),g.current=null,m(!0),0):t}))}),1e3)}),[c,60]),S=(0,r.useCallback)((()=>{f(!1)}),[]),k=(0,r.useCallback)((()=>{f(!1),d(c),m(!1),g.current&&clearInterval(g.current),g.current=setInterval((()=>{d((e=>{const t=e-1;return 60===t&&f(!0),t<=0?(clearInterval(g.current),g.current=null,m(!0),0):t}))}),1e3)}),[c,60]);(0,r.useEffect)((()=>(w(),()=>{g.current&&clearInterval(g.current)})),[w]),(0,r.useEffect)((()=>{const e=window.SpeechRecognition||window.webkitSpeechRecognition;if(e){v.current=new e,v.current.lang="en-US",v.current.interimResults=!0,v.current.continuous=!0;let n="";v.current.onresult=e=>{let t="";for(let r=e.resultIndex;r<e.results.length;r++){const o=e.results[r][0].transcript;e.results[r].isFinal?n+=o+" ":t+=o}o(n+t)},v.current.onerror=e=>{console.error("Speech recognition error",e.error),alert("Error occurred: "+e.error),t(!1)}}else alert("Your browser doesn't support speech recognition. Try Chrome or Edge.");return()=>{v.current&&v.current.stop()}}),[]),(0,r.useEffect)((()=>{y.current&&(y.current.scrollTop=y.current.scrollHeight)}),[n]),(0,r.useEffect)((()=>{b.current&&(b.current.scrollTop=b.current.scrollHeight)}),[a]);const C=()=>{if(v.current&&e)try{v.current.stop(),t(!1)}catch(n){console.error("Speech recognition error:",n)}},E=(0,r.useCallback)((()=>{window.location.href="/home"}),[]);return(0,pi.jsxs)("div",{className:"speech-to-text",children:[(0,pi.jsx)("button",{className:"exit-button",onClick:E,title:"Exit",children:(0,pi.jsx)(wf,{})}),(0,pi.jsxs)("div",{className:"timer-container",children:[(0,pi.jsx)(xf,{className:"timer-icon"}),(0,pi.jsx)("span",{className:"timer-display session-timer",children:x(u)}),e&&(0,pi.jsx)("span",{className:"listening-indicator",children:"Recording..."})]}),(0,pi.jsxs)("div",{className:"flex-container",children:[(0,pi.jsxs)("div",{className:"answer-container",children:[(0,pi.jsx)("div",{className:"section-header",children:(0,pi.jsx)("h3",{children:"AI Response"})}),(0,pi.jsx)("textarea",{ref:b,className:"response-area",value:a,readOnly:!0,placeholder:"AI answer will appear here...",disabled:h})]}),(0,pi.jsxs)("div",{className:"question-container",children:[(0,pi.jsx)("textarea",{ref:y,className:"transcript-area",value:n,onChange:e=>o(e.target.value),placeholder:h?"Session expired. Please make a payment to continue.":"Type or record your question here...",disabled:h}),(0,pi.jsxs)("div",{className:"control-group",children:[(0,pi.jsx)("button",{className:"mic-button ".concat(e?"listening":""),onClick:e?C:()=>{if(!e&&!h)try{if(o(""),v.current)try{v.current.stop()}catch(lh){}const e=window.SpeechRecognition||window.webkitSpeechRecognition;v.current=new e,v.current.lang="en-US",v.current.interimResults=!0,v.current.continuous=!0;let n="";v.current.onresult=e=>{let t="";for(let r=e.resultIndex;r<e.results.length;r++){const o=e.results[r][0].transcript;e.results[r].isFinal?n+=o+" ":t+=o}o(n+t)},v.current.onerror=e=>{console.error("Speech recognition error",e.error),t(!1)},v.current.start(),t(!0)}catch(n){console.error("Speech recognition error:",n)}},title:e?"Stop Recording":"Start Recording",disabled:h,children:e?(0,pi.jsx)(mf,{}):(0,pi.jsx)(hf,{})}),(0,pi.jsx)("button",{className:"send-button",onClick:async()=>{const t=Op.OPENAI_API_KEY,r=n.trim();if(console.log("Environment:","production"),console.log("API Key available:",t?"Yes":"No"),!t)return void alert("Please add your OpenAI API key to the .env file as REACT_APP_OPENAI_API_KEY and restart the development server.");if(!r)return void alert("Please record or enter some text to send to GPT.");const a="In 5 lines, give only the definition and a simple example. ".concat(r);s(!0),i("");try{const e=await fetch("https://api.openai.com/v1/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t)},body:JSON.stringify({model:"gpt-4.1-nano",messages:[{role:"user",content:a}],stream:!0})});if(!e.ok||!e.body)throw new Error("Failed to stream response.");const n=e.body.getReader(),r=new TextDecoder("utf-8");let o="";for(;;){const{value:e,done:t}=await n.read();if(t)break;const a=r.decode(e,{stream:!0}).split("\n").filter((e=>e.trim().startsWith("data:")));for(const n of a){const e=n.replace(/^data: /,"");if("[DONE]"===e)break;try{var l,c,u;const t=null===(l=JSON.parse(e).choices)||void 0===l||null===(c=l[0])||void 0===c||null===(u=c.delta)||void 0===u?void 0:u.content;t&&(o+=t,i(o))}catch(lh){console.error("Error parsing JSON:",lh)}}}}catch(d){console.error("Streaming Error:",d),i("Error occurred: "+d.message)}finally{e&&C(),s(!1),o("")}},disabled:l||!n.trim()||h,title:"Get Answer",children:l?"...":(0,pi.jsx)(bf,{})})]})]})]}),(0,pi.jsxs)(Gp,{open:p,onClose:S,"aria-labelledby":"payment-dialog-title","aria-describedby":"payment-dialog-description",children:[(0,pi.jsx)(ff,{id:"payment-dialog-title",children:"Session Expiring Soon"}),(0,pi.jsx)(af,{children:(0,pi.jsx)(uf,{id:"payment-dialog-description",children:"Your session will expire in one minute. Would you like to make a payment to extend your session?"})}),(0,pi.jsxs)(Zp,{children:[(0,pi.jsx)(Mp,{onClick:S,color:"primary",children:"Not Now"}),(0,pi.jsx)(Mp,{onClick:k,color:"primary",autoFocus:!0,children:"Make Payment"})]})]}),(0,pi.jsxs)(Gp,{open:h,"aria-labelledby":"expired-dialog-title","aria-describedby":"expired-dialog-description",children:[(0,pi.jsx)(ff,{id:"expired-dialog-title",children:"Session Expired"}),(0,pi.jsx)(af,{children:(0,pi.jsx)(uf,{id:"expired-dialog-description",children:"Your session has expired. Please make a payment to continue using the service."})}),(0,pi.jsxs)(Zp,{children:[(0,pi.jsx)(Mp,{onClick:E,color:"primary",children:"Exit"}),(0,pi.jsx)(Mp,{onClick:k,color:"primary",autoFocus:!0,children:"Make Payment"})]})]})]})},kf=ji((0,pi.jsx)("path",{d:"M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2M8.5 13.5l2.5 3.01L14.5 12l4.5 6H5z"})),Cf=ji((0,pi.jsx)("path",{d:"M7 14H5v5h5v-2H7zm-2-4h2V7h3V5H5zm12 7h-3v2h5v-5h-2zM14 5v2h3v3h2V5z"})),Ef=ji((0,pi.jsx)("path",{d:"M5 16h3v3h2v-5H5zm3-8H5v2h5V5H8zm6 11h2v-3h3v-2h-5zm2-11V5h-2v5h5V8z"})),jf=ji((0,pi.jsx)("path",{d:"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14"})),Nf=ji((0,pi.jsx)("path",{d:"M9 21c0 .5.4 1 1 1h4c.6 0 1-.5 1-1v-1H9zm3-19C8.1 2 5 5.1 5 9c0 2.4 1.2 4.5 3 5.7V17c0 .5.4 1 1 1h6c.6 0 1-.5 1-1v-2.3c1.8-1.3 3-3.4 3-5.7 0-3.9-3.1-7-7-7"})),Pf=ji((0,pi.jsx)("path",{d:"M19.8 18.4 14 10.67V6.5l1.35-1.69c.26-.33.03-.81-.39-.81H9.04c-.42 0-.65.48-.39.81L10 6.5v4.17L4.2 18.4c-.49.66-.02 1.6.8 1.6h14c.82 0 1.29-.94.8-1.6"})),Rf=ji((0,pi.jsx)("path",{d:"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6z"})),Tf=ji((0,pi.jsx)("path",{d:"M6 10c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2m12 0c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2m-6 0c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2"})),_f=ji((0,pi.jsx)("path",{d:"M20 9V7c0-1.1-.9-2-2-2h-3c0-1.66-1.34-3-3-3S9 3.34 9 5H6c-1.1 0-2 .9-2 2v2c-1.66 0-3 1.34-3 3s1.34 3 3 3v4c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2v-4c1.66 0 3-1.34 3-3s-1.34-3-3-3M7.5 11.5c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5S9.83 13 9 13s-1.5-.67-1.5-1.5M16 17H8v-2h8zm-1-4c-.83 0-1.5-.67-1.5-1.5S14.17 10 15 10s1.5.67 1.5 1.5S15.83 13 15 13"})),Af=ji((0,pi.jsx)("path",{d:"M12 1c-4.97 0-9 4.03-9 9v7c0 1.66 1.34 3 3 3h3v-8H5v-2c0-3.87 3.13-7 7-7s7 3.13 7 7v2h-4v8h3c1.66 0 3-1.34 3-3v-7c0-4.97-4.03-9-9-9"})),Mf=ji((0,pi.jsx)("path",{d:"M17 7H7c-2.76 0-5 2.24-5 5s2.24 5 5 5h10c2.76 0 5-2.24 5-5s-2.24-5-5-5m0 8c-1.66 0-3-1.34-3-3s1.34-3 3-3 3 1.34 3 3-1.34 3-3 3"})),zf=ji((0,pi.jsx)("path",{d:"M17 7H7c-2.76 0-5 2.24-5 5s2.24 5 5 5h10c2.76 0 5-2.24 5-5s-2.24-5-5-5M7 15c-1.66 0-3-1.34-3-3s1.34-3 3-3 3 1.34 3 3-1.34 3-3 3"}));const If=function(e){let{onBack:t}=e;const[n,o]=(0,r.useState)(!1),[a,i]=(0,r.useState)(""),[l,s]=(0,r.useState)(!1),[c,u]=(0,r.useState)(!1),[d,f]=(0,r.useState)([]),[h,m]=(0,r.useState)("chat"),[g,v]=(0,r.useState)(!1),[y,b]=(0,r.useState)(""),[x,w]=(0,r.useState)(0),S=(0,r.useRef)(null),k=(0,r.useRef)(null),C=(0,r.useRef)(null),E=(0,r.useRef)(null),j=(0,r.useRef)(null),N=(0,r.useRef)(null),[P,R]=(0,r.useState)(!1),[T,_]=(0,r.useState)(null),A=(0,r.useRef)(null),M=(0,r.useRef)(null),z=(0,r.useCallback)((e=>{const t=Math.floor(e/60),n=e%60;return"".concat(t.toString().padStart(2,"0"),":").concat(n.toString().padStart(2,"0"))}),[]);(0,r.useEffect)((()=>{(()=>{var e;null===(e=j.current)||void 0===e||e.scrollIntoView({behavior:"smooth"})})()}),[d]);const I=(0,r.useCallback)((()=>{S.current&&(clearInterval(S.current),S.current=null)}),[]),O=(0,r.useCallback)((()=>{S.current||(w(0),S.current=setInterval((()=>{w((e=>e+1))}),1e3))}),[]),F=(0,r.useCallback)((()=>{k.current&&clearTimeout(k.current),k.current=setTimeout((()=>{if(g&&a.trim()&&n&&!l&&""!==a){if(a.trim()){const e=document.querySelector(".submit-button");e&&!e.disabled&&e.click()}}}),2e3)}),[g,a,n,l]);(0,r.useEffect)((()=>{const e=window.SpeechRecognition||window.webkitSpeechRecognition;if(e){C.current=new e,C.current.lang="en-US",C.current.interimResults=!0,C.current.continuous=!0;let t="";C.current.onresult=e=>{let n="";b(t+n);for(let o=e.resultIndex;o<e.results.length;o++){const r=e.results[o][0].transcript;e.results[o].isFinal?t+=r+" ":n+=r}const r=t+n;i(r),r!==y&&F()},C.current.onerror=e=>{console.error("Speech recognition error",e.error),o(!1),I()}}}),[I,F,y]),(0,r.useEffect)((()=>{const e=()=>{u(document.fullscreenElement||document.mozFullScreenElement||document.webkitFullscreenElement||document.msFullscreenElement)};return document.addEventListener("fullscreenchange",e),document.addEventListener("mozfullscreenchange",e),document.addEventListener("webkitfullscreenchange",e),document.addEventListener("msfullscreenchange",e),()=>{document.removeEventListener("fullscreenchange",e),document.removeEventListener("mozfullscreenchange",e),document.removeEventListener("webkitfullscreenchange",e),document.removeEventListener("msfullscreenchange",e)}}),[]);const L=(0,r.useCallback)((()=>{if(C.current&&!n)try{C.current.start(),o(!0),O()}catch(e){console.error("Speech recognition error:",e)}}),[n,O]),D=(0,r.useCallback)((()=>{if(C.current&&n)try{C.current.stop(),o(!1),I()}catch(e){console.error("Speech recognition error:",e)}}),[n,I]),B=()=>{T&&T.getTracks().forEach((e=>e.stop())),M.current&&(M.current.disconnect(),M.current=null),C.current&&(C.current.stop(),C.current.isTabCapture=!1),navigator.mediaDevices.getUserMedia({audio:!0}).then((e=>{e.getAudioTracks().forEach((e=>{e.enabled=!0,e.stop()}))})).catch((e=>console.error("Error re-enabling microphone:",e))),R(!1),I()},W=e=>{m(e)},H=(0,r.useCallback)((async()=>{const e=a.trim();if(!e)return;const t="********************************************************************************************************************************************************************";const r={role:"user",content:e,timestamp:new Date,action:h};f((e=>[...e,r])),i("");const o=Date.now();f((e=>[...e,{role:"assistant",content:"...",isLoading:!0,id:o,timestamp:new Date,action:h}])),s(!0);try{let n;if("image"===h){if(f((e=>e.map((e=>e.id===o?{role:"assistant",content:"Generating image...",isLoading:!0,isImageLoading:!0,id:o,timestamp:new Date,action:"image"}:e)))),n=await fetch("https://api.openai.com/v1/images/generations",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t)},body:JSON.stringify({model:"dall-e-3",prompt:e,n:1,size:"1024x1024",quality:"standard"})}),!n.ok)throw new Error("Failed to generate image");const r=(await n.json()).data[0].url;f((e=>e.map((e=>e.id===o?{role:"assistant",content:r,isImage:!0,timestamp:new Date,action:"image"}:e))))}else{let r="You are a helpful assistant. Keep your responses concise, limited to about 5 lines maximum.";switch(h){case"search":r="You are a search assistant. Provide concise, factual information with sources when possible. Limit your response to 5 lines.";break;case"reason":r="You are a reasoning assistant. Break down problems step by step, showing your logical process clearly. Keep your response to 5 lines maximum.";break;case"research":r="You are a research assistant. Provide concise analysis with key perspectives. Limit your response to 5 lines.";break;default:r="You are a helpful assistant. Keep your responses concise, limited to about 5 lines maximum."}if(n=await fetch("https://api.openai.com/v1/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t)},body:JSON.stringify({model:"gpt-4.1-nano",messages:[{role:"system",content:r},...d.filter((e=>!e.isLoading&&!e.isImage)).map((e=>({role:e.role,content:e.content}))),{role:"user",content:e}],stream:!0})}),!n.ok||!n.body)throw new Error("Failed to stream response.");const a=n.body.getReader(),i=new TextDecoder("utf-8");let l="";const s=async()=>{let e=l;for(;;){const{value:s,done:c}=await a.read();if(c)break;const u=i.decode(s,{stream:!0}).split("\n").filter((e=>e.trim().startsWith("data:")));for(const a of u){const i=a.replace(/^data: /,"");if("[DONE]"===i)break;try{var t,n,r;const a=null===(t=JSON.parse(i).choices)||void 0===t||null===(n=t[0])||void 0===n||null===(r=n.delta)||void 0===r?void 0:r.content;a&&(e+=a,l=e,f((t=>t.map((t=>t.id===o?p(p({},t),{},{content:e}):t)))))}catch(lh){console.error("Error parsing JSON:",lh)}}}};await s(),f((e=>e.map((e=>e.id===o?{role:"assistant",content:l,timestamp:new Date,action:h}:e))))}}catch(l){console.error("API Error:",l),f((e=>e.map((e=>e.id===o?{role:"assistant",content:"Error: ".concat(l.message),isError:!0,timestamp:new Date}:e))))}finally{n&&D(),s(!1)}}),[a,h,d,n,D]);return(0,pi.jsx)("div",{ref:N,className:"voice-transcriber-container ".concat(c?"fullscreen":""),children:(0,pi.jsxs)("div",{className:"voice-transcriber-fullscreen",children:[(0,pi.jsxs)("div",{className:"top-controls",children:[t&&(0,pi.jsx)("button",{className:"exit-button",onClick:t,title:"Exit",children:(0,pi.jsx)(Pi,{fontSize:"small"})}),(0,pi.jsxs)("div",{className:"control-buttons",children:[d.length>0&&(0,pi.jsx)("button",{className:"control-button",onClick:()=>{f([])},title:"Clear chat",children:(0,pi.jsx)(vf,{fontSize:"small"})}),(0,pi.jsx)("button",{className:"fullscreen-button",onClick:()=>{u((e=>!e)),c?document.exitFullscreen?document.exitFullscreen():document.mozCancelFullScreen?document.mozCancelFullScreen():document.webkitExitFullscreen?document.webkitExitFullscreen():document.msExitFullscreen&&document.msExitFullscreen():N.current.requestFullscreen?N.current.requestFullscreen():N.current.mozRequestFullScreen?N.current.mozRequestFullScreen():N.current.webkitRequestFullscreen?N.current.webkitRequestFullscreen():N.current.msRequestFullscreen&&N.current.msRequestFullscreen()},title:c?"Exit Fullscreen":"Enter Fullscreen",children:c?(0,pi.jsx)(Ef,{fontSize:"small"}):(0,pi.jsx)(Cf,{fontSize:"small"})})]})]}),(0,pi.jsxs)("div",{className:"chat-container",children:[(0,pi.jsxs)("div",{className:"messages-container",children:[d.map(((e,t)=>(0,pi.jsxs)("div",{className:"message ".concat("user"===e.role?"user-message":"assistant-message"),children:[(0,pi.jsx)("div",{className:"message-avatar",children:"user"===e.role?(0,pi.jsx)(Ri,{fontSize:"small"}):(0,pi.jsx)(_f,{fontSize:"small"})}),(0,pi.jsxs)("div",{className:"message-content",children:[e.isImage?(0,pi.jsx)("div",{className:"image-container",children:(0,pi.jsx)("img",{src:e.content,alt:"Generated",className:"generated-image"})}):(0,pi.jsx)("div",{className:"text-content ".concat(e.isLoading?"loading":""),children:e.content}),e.action&&(0,pi.jsxs)("div",{className:"message-action",children:["image"===e.action&&(0,pi.jsx)(kf,{fontSize:"small"}),"search"===e.action&&(0,pi.jsx)(jf,{fontSize:"small"}),"reason"===e.action&&(0,pi.jsx)(Nf,{fontSize:"small"}),"research"===e.action&&(0,pi.jsx)(Pf,{fontSize:"small"})]})]})]},t))),(0,pi.jsx)("div",{ref:j})]}),(0,pi.jsx)("div",{className:"input-section",children:(0,pi.jsxs)("div",{className:"search-container",children:[P&&(0,pi.jsxs)("div",{className:"recording-indicator tab-capture",children:[(0,pi.jsx)("div",{className:"recording-dot"}),(0,pi.jsx)("span",{children:"Capturing meeting audio"})]}),(0,pi.jsx)("textarea",{ref:E,className:"search-input",value:a,onChange:e=>i(e.target.value),placeholder:"Ask anything".concat("image"===h?" to generate an image":"search"===h?" to search":"reason"===h?" to reason about":"research"===h?" to research deeply":""),rows:1}),(0,pi.jsxs)("div",{className:"search-actions",children:[(0,pi.jsxs)("button",{className:"action-button ".concat("chat"===h?"active":""),onClick:()=>W("chat"),children:[(0,pi.jsx)(Rf,{fontSize:"small"}),(0,pi.jsx)("span",{children:"Chat"})]}),(0,pi.jsxs)("button",{className:"action-button ".concat("search"===h?"active":""),onClick:()=>W("search"),children:[(0,pi.jsx)(jf,{fontSize:"small"}),(0,pi.jsx)("span",{children:"Search"})]}),(0,pi.jsxs)("button",{className:"action-button ".concat("reason"===h?"active":""),onClick:()=>W("reason"),children:[(0,pi.jsx)(Nf,{fontSize:"small"}),(0,pi.jsx)("span",{children:"Reason"})]}),(0,pi.jsxs)("button",{className:"action-button ".concat("research"===h?"active":""),onClick:()=>W("research"),children:[(0,pi.jsx)(Pf,{fontSize:"small"}),(0,pi.jsx)("span",{children:"Deep research"})]}),(0,pi.jsxs)("button",{className:"action-button ".concat("image"===h?"active":""),onClick:()=>W("image"),children:[(0,pi.jsx)(kf,{fontSize:"small"}),(0,pi.jsx)("span",{children:"Create image"})]}),(0,pi.jsx)("button",{className:"action-button more",children:(0,pi.jsx)(Tf,{fontSize:"small"})}),(0,pi.jsxs)("div",{className:"voice-controls",children:[(0,pi.jsx)("button",{className:"voice-button ".concat(n?"listening":""),onClick:n?D:L,title:"Capture your microphone",children:(0,pi.jsx)(hf,{fontSize:"small"})}),(0,pi.jsx)("button",{className:"voice-button tab-capture ".concat(P?"capturing":""),onClick:P?B:async()=>{try{T&&T.getTracks().forEach((e=>e.stop()));const e=await navigator.mediaDevices.getDisplayMedia({video:!0,audio:!0});if(!e.getAudioTracks()[0])return alert("No audio track found. Please make sure to select 'Share audio' when sharing."),void e.getTracks().forEach((e=>e.stop()));if((await navigator.mediaDevices.getUserMedia({audio:!0})).getAudioTracks().forEach((e=>{e.enabled=!1})),_(e),R(!0),!A.current)if(window.AudioContext)A.current=new window.AudioContext;else try{A.current=new window.webkitAudioContext}catch(lh){console.error("AudioContext not supported in this browser"),A.current={createMediaStreamSource:()=>({connect:()=>{}})}}M.current=A.current.createMediaStreamSource(e),C.current&&C.current.stop();const t=window.SpeechRecognition||window.webkitSpeechRecognition;C.current=new t,C.current.lang="en-US",C.current.interimResults=!0,C.current.continuous=!0,C.current.isTabCapture=!0;let n="";C.current.onresult=e=>{let t="";for(let r=e.resultIndex;r<e.results.length;r++){const o=e.results[r][0].transcript;e.results[r].isFinal?n+=o+" ":t+=o}i(n+t)},C.current.onerror=e=>{console.error("Speech recognition error",e.error),R(!1)},C.current.start(),O(),e.getVideoTracks()[0].onended=()=>{B()}}catch(e){console.error("Error capturing tab audio:",e),alert("Failed to capture tab audio: "+e.message),R(!1)}},title:"Capture tab audio (for meetings)",children:(0,pi.jsx)(Af,{fontSize:"small"})}),(0,pi.jsx)("button",{className:"voice-button auto-submit ".concat(g?"active":""),onClick:()=>v((e=>!e)),title:g?"Auto-submit enabled":"Auto-submit disabled",children:g?(0,pi.jsx)(Mf,{fontSize:"small"}):(0,pi.jsx)(zf,{fontSize:"small"})}),(0,pi.jsx)("button",{className:"submit-button",onClick:H,disabled:l||!a.trim(),children:(0,pi.jsx)(bf,{fontSize:"small"})})]})]}),n&&(0,pi.jsxs)("div",{className:"recording-indicator",children:[(0,pi.jsx)("span",{className:"recording-dot"}),(0,pi.jsx)("span",{className:"recording-time",children:z(x)})]}),a&&(0,pi.jsx)("button",{className:"clear-search-btn",onClick:()=>{if(i(""),C.current){const t=n;try{t&&(C.current.stop(),o(!1)),setTimeout((()=>{const e=window.SpeechRecognition||window.webkitSpeechRecognition;C.current=new e,C.current.lang="en-US",C.current.interimResults=!0,C.current.continuous=!0;let n="";C.current.onresult=e=>{let t="";for(let r=e.resultIndex;r<e.results.length;r++){const o=e.results[r][0].transcript;e.results[r].isFinal?n+=o+" ":t+=o}i(n+t)},C.current.onerror=e=>{console.error("Speech recognition error",e.error),o(!1),I()},t&&(C.current.start(),o(!0))}),200)}catch(e){console.error("Error during transcript clearing:",e)}}},children:"\u2715"})]})})]})]})})},Of=ji((0,pi.jsx)("path",{d:"M10.09 15.59 11.5 17l5-5-5-5-1.41 1.41L12.67 11H3v2h9.67zM19 3H5c-1.11 0-2 .9-2 2v4h2V5h14v14H5v-4H3v4c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2"})),Ff=ji((0,pi.jsx)("path",{d:"M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2M6 9h12v2H6zm8 5H6v-2h8zm4-6H6V6h12z"}));const Lf=function(e){let{onBack:t}=e;const[n,o]=(0,r.useState)(!1),[a,i]=(0,r.useState)(""),[l,s]=(0,r.useState)([]),[c,u]=(0,r.useState)(!1),[d,f]=(0,r.useState)(!1),[h,m]=(0,r.useState)(null),[g,v]=(0,r.useState)(0),[y,b]=(0,r.useState)(!1),x=(0,r.useRef)(null),w=(0,r.useRef)(null),S=(0,r.useRef)(null),k=(0,r.useRef)(null),C=(0,r.useRef)(null),E=(0,r.useRef)(null),j=(0,r.useRef)(null),N=(0,r.useCallback)((()=>{b(!0)}),[]),P=(0,r.useCallback)((()=>{b(!1)}),[]),R=(0,r.useCallback)((()=>{v(0)}),[]),T=(0,r.useCallback)((()=>{h&&h.getTracks().forEach((e=>e.stop())),j.current&&(j.current.disconnect(),j.current=null),k.current&&k.current.stop(),f(!1),P()}),[h,P]);return(0,r.useEffect)((()=>{const e=()=>{o(!!document.fullscreenElement)};return document.addEventListener("fullscreenchange",e),()=>{document.removeEventListener("fullscreenchange",e)}}),[]),(0,r.useEffect)((()=>{S.current&&S.current.scrollIntoView({behavior:"smooth"})}),[l]),(0,r.useEffect)((()=>(y?C.current=setInterval((()=>{v((e=>e+1))}),1e3):clearInterval(C.current),()=>{clearInterval(C.current)})),[y]),(0,r.useEffect)((()=>()=>{T(),clearInterval(C.current)}),[T]),(0,pi.jsx)("div",{ref:x,className:"meeting-transcriber-container ".concat(n?"fullscreen":""),children:(0,pi.jsxs)("div",{className:"meeting-transcriber-content",children:[(0,pi.jsxs)("div",{className:"top-controls",children:[t&&(0,pi.jsx)("button",{className:"exit-button",onClick:t,title:"Exit",children:(0,pi.jsx)(Of,{fontSize:"small"})}),(0,pi.jsxs)("div",{className:"control-buttons",children:[l.length>0&&(0,pi.jsx)("button",{className:"control-button",onClick:()=>{s([])},title:"Clear chat",children:(0,pi.jsx)(vf,{fontSize:"small"})}),(0,pi.jsx)("button",{className:"fullscreen-button",onClick:()=>{document.fullscreenElement?document.exitFullscreen():x.current.requestFullscreen().catch((e=>{console.error("Error attempting to enable fullscreen: ".concat(e.message))}))},title:n?"Exit Fullscreen":"Enter Fullscreen",children:n?(0,pi.jsx)(Ef,{fontSize:"small"}):(0,pi.jsx)(Cf,{fontSize:"small"})})]})]}),(0,pi.jsxs)("div",{className:"chat-container",children:[(0,pi.jsxs)("div",{className:"messages-container",children:[l.map(((e,t)=>(0,pi.jsxs)("div",{className:"message ".concat("user"===e.role?"user-message":"assistant-message"),children:[(0,pi.jsx)("div",{className:"message-avatar",children:"user"===e.role?(0,pi.jsx)(Ri,{fontSize:"small"}):(0,pi.jsx)(Ff,{fontSize:"small"})}),(0,pi.jsx)("div",{className:"message-content",children:(0,pi.jsx)("div",{className:"text-content ".concat(e.isLoading?"loading":""),children:e.content})})]},t))),(0,pi.jsx)("div",{ref:S})]}),(0,pi.jsx)("div",{className:"input-section",children:(0,pi.jsxs)("div",{className:"search-container",children:[d&&(0,pi.jsxs)("div",{className:"recording-indicator",children:[(0,pi.jsx)("div",{className:"recording-dot"}),(0,pi.jsxs)("span",{children:["Capturing meeting audio (",(e=>{const t=Math.floor(e/60),n=e%60;return"".concat(t.toString().padStart(2,"0"),":").concat(n.toString().padStart(2,"0"))})(g),")"]})]}),(0,pi.jsx)("textarea",{ref:w,className:"search-input",value:a,onChange:e=>i(e.target.value),placeholder:"Meeting transcription will appear here...",rows:5}),a&&(0,pi.jsx)("button",{className:"clear-search-btn",onClick:()=>{if(i(""),k.current){const t=d;try{t&&(k.current.stop(),setTimeout((()=>{const e=window.SpeechRecognition||window.webkitSpeechRecognition;k.current=new e,k.current.lang="en-US",k.current.interimResults=!0,k.current.continuous=!0;let n="";k.current.onresult=e=>{let t="";for(let r=e.resultIndex;r<e.results.length;r++){const o=e.results[r][0].transcript;e.results[r].isFinal?n+=o+" ":t+=o}i(n+t)},k.current.onerror=e=>{console.error("Speech recognition error",e.error),f(!1),P()},t&&k.current.start()}),200))}catch(e){console.error("Error during transcript clearing:",e)}}},children:"\u2715"}),(0,pi.jsxs)("div",{className:"voice-controls",children:[(0,pi.jsx)("button",{className:"voice-button tab-capture ".concat(d?"capturing":""),onClick:d?T:async()=>{try{h&&h.getTracks().forEach((e=>e.stop()));const n=await navigator.mediaDevices.getDisplayMedia({video:!0,audio:!0});if(!n.getAudioTracks()[0])return alert("No audio track found. Please make sure to select 'Share audio' when sharing."),void n.getTracks().forEach((e=>e.stop()));if(m(n),f(!0),!E.current)try{E.current=new(window.AudioContext||window.webkitAudioContext)}catch(e){return console.error("Failed to create AudioContext:",e),alert("Your browser doesn't support audio processing. Try using Chrome or Edge."),n.getTracks().forEach((e=>e.stop())),void f(!1)}j.current=E.current.createMediaStreamSource(n),k.current&&k.current.stop();const r=window.SpeechRecognition||window.webkitSpeechRecognition;if(!r)return alert("Your browser doesn't support speech recognition. Try using Chrome or Edge."),n.getTracks().forEach((e=>e.stop())),void f(!1);k.current=new r,k.current.lang="en-US",k.current.interimResults=!0,k.current.continuous=!0;let o="";k.current.onresult=e=>{let t="";for(let n=e.resultIndex;n<e.results.length;n++){const r=e.results[n][0].transcript;e.results[n].isFinal?o+=r+" ":t+=r}i(o+t)},k.current.onerror=e=>{console.error("Speech recognition error",e.error),"no-speech"!==e.error&&console.warn("Speech recognition error:",e.error),"not-allowed"!==e.error&&"service-not-allowed"!==e.error||(alert("Microphone access denied. Please allow microphone access and try again."),f(!1),P())};try{k.current.start(),N(),R()}catch(t){if(console.error("Failed to start speech recognition:",t),!t.message||!t.message.includes("already started"))return alert("Failed to start speech recognition. Please try again."),n.getTracks().forEach((e=>e.stop())),void f(!1);k.current.stop(),setTimeout((()=>{k.current.start()}),100)}n.getVideoTracks()[0].onended=()=>{T()}}catch(n){console.error("Error capturing tab audio:",n),"NotAllowedError"===n.name?alert("Permission denied. Please allow screen sharing with audio to use this feature."):"NotFoundError"===n.name?alert("No audio source found. Make sure your system has audio output enabled."):"NotReadableError"===n.name?alert("Could not access your screen. Please try again or use a different browser."):alert("Failed to capture tab audio: "+n.message),f(!1)}},title:d?"Stop capturing meeting audio":"Capture meeting audio",children:(0,pi.jsx)(Af,{fontSize:"small"})}),(0,pi.jsx)("button",{className:"submit-button",onClick:async()=>{const e=a.trim();if(!e)return;const t="********************************************************************************************************************************************************************";const n={role:"user",content:e,timestamp:new Date};s((e=>[...e,n])),i("");const r=Date.now();s((e=>[...e,{role:"assistant",content:"...",isLoading:!0,id:r,timestamp:new Date}])),u(!0);try{const n=await fetch("https://api.openai.com/v1/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t)},body:JSON.stringify({model:"gpt-4.1-nano",messages:[{role:"system",content:"You are a helpful assistant. Keep your responses concise, limited to about 5 lines maximum."},...l.filter((e=>!e.isLoading)).map((e=>({role:e.role,content:e.content}))),{role:"user",content:e}],stream:!0})});if(!n.ok||!n.body)throw new Error("Failed to stream response.");const o=n.body.getReader(),a=new TextDecoder("utf-8");let i="";const c=async()=>{let e=i;for(;;){const{value:c,done:u}=await o.read();if(u)break;const d=a.decode(c,{stream:!0}).split("\n").filter((e=>e.trim().startsWith("data:")));for(const o of d){const a=o.replace(/^data: /,"");if("[DONE]"===a)break;try{var t,n,l;const o=null===(t=JSON.parse(a).choices)||void 0===t||null===(n=t[0])||void 0===n||null===(l=n.delta)||void 0===l?void 0:l.content;o&&(e+=o,i=e,s((t=>t.map((t=>t.id===r?p(p({},t),{},{content:e}):t)))))}catch(lh){console.error("Error parsing JSON:",lh)}}}};await c(),s((e=>e.map((e=>e.id===r?{role:"assistant",content:i,timestamp:new Date}:e))))}catch(o){console.error("API Error:",o),s((e=>e.map((e=>e.id===r?{role:"assistant",content:"Error: ".concat(o.message),isError:!0,timestamp:new Date}:e))))}finally{u(!1)}},disabled:c||!a.trim(),children:(0,pi.jsx)(bf,{fontSize:"small"})})]})]})})]})]})})};const Df=function(){const[e,t]=(0,r.useState)(!1),[n,o]=(0,r.useState)(!1),[a,i]=(0,r.useState)(!1);return r.useEffect((()=>{localStorage.getItem("userDetails")||(window.location.href="/login")}),[]),(0,pi.jsxs)("div",{className:"App",children:[!n&&!a&&!e&&(0,pi.jsx)(Ti,{}),(0,pi.jsx)("main",{className:n||a||e?"fullscreen":"",children:e?(0,pi.jsx)(Sf,{onBack:()=>{t(!1)}}):n?(0,pi.jsx)(If,{onBack:()=>{o(!1)}}):a?(0,pi.jsx)(Lf,{onBack:()=>{i(!1)}}):(0,pi.jsx)(yf,{onStartSession:e=>{if(e&&e.sessionTimeRemaining)try{const t=localStorage.getItem("userDetails");let n=t?JSON.parse(t):{};n.timeRemaining=e.sessionTimeRemaining,localStorage.setItem("userDetails",JSON.stringify(n))}catch(n){console.error("Error saving session time to localStorage:",n)}t(!0)}})})]})};function Bf(e){return bi("MuiAlert",e)}const Wf=xi("MuiAlert",["root","action","icon","message","filled","colorSuccess","colorInfo","colorWarning","colorError","filledSuccess","filledInfo","filledWarning","filledError","outlined","outlinedSuccess","outlinedInfo","outlinedWarning","outlinedError","standard","standardSuccess","standardInfo","standardWarning","standardError"]),Hf=ji((0,pi.jsx)("path",{d:"M20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4C12.76,4 13.5,4.11 14.2, 4.31L15.77,2.74C14.61,2.26 13.34,2 12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0, 0 22,12M7.91,10.08L6.5,11.5L11,16L21,6L19.59,4.58L11,13.17L7.91,10.08Z"})),Uf=ji((0,pi.jsx)("path",{d:"M12 5.99L19.53 19H4.47L12 5.99M12 2L1 21h22L12 2zm1 14h-2v2h2v-2zm0-6h-2v4h2v-4z"})),Vf=ji((0,pi.jsx)("path",{d:"M11 15h2v2h-2zm0-8h2v6h-2zm.99-5C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"})),$f=ji((0,pi.jsx)("path",{d:"M11,9H13V7H11M12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20, 12C20,16.41 16.41,20 12,20M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10, 10 0 0,0 12,2M11,17H13V11H11V17Z"})),Kf=ji((0,pi.jsx)("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"})),qf=["action","children","className","closeText","color","components","componentsProps","icon","iconMapping","onClose","role","severity","slotProps","slots","variant"],Yf=si(ou,{name:"MuiAlert",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,t[n.variant],t["".concat(n.variant).concat(_t(n.color||n.severity))]]}})(ui((e=>{let{theme:t}=e;const n="light"===t.palette.mode?Go:Xo,r="light"===t.palette.mode?Xo:Go;return p(p({},t.typography.body2),{},{backgroundColor:"transparent",display:"flex",padding:"6px 16px",variants:[...Object.entries(t.palette).filter(El(["light"])).map((e=>{let[o]=e;return{props:{colorSeverity:o,variant:"standard"},style:{color:t.vars?t.vars.palette.Alert["".concat(o,"Color")]:n(t.palette[o].light,.6),backgroundColor:t.vars?t.vars.palette.Alert["".concat(o,"StandardBg")]:r(t.palette[o].light,.9),["& .".concat(Wf.icon)]:t.vars?{color:t.vars.palette.Alert["".concat(o,"IconColor")]}:{color:t.palette[o].main}}}})),...Object.entries(t.palette).filter(El(["light"])).map((e=>{let[r]=e;return{props:{colorSeverity:r,variant:"outlined"},style:{color:t.vars?t.vars.palette.Alert["".concat(r,"Color")]:n(t.palette[r].light,.6),border:"1px solid ".concat((t.vars||t).palette[r].light),["& .".concat(Wf.icon)]:t.vars?{color:t.vars.palette.Alert["".concat(r,"IconColor")]}:{color:t.palette[r].main}}}})),...Object.entries(t.palette).filter(El(["dark"])).map((e=>{let[n]=e;return{props:{colorSeverity:n,variant:"filled"},style:p({fontWeight:t.typography.fontWeightMedium},t.vars?{color:t.vars.palette.Alert["".concat(n,"FilledColor")],backgroundColor:t.vars.palette.Alert["".concat(n,"FilledBg")]}:{backgroundColor:"dark"===t.palette.mode?t.palette[n].dark:t.palette[n].main,color:t.palette.getContrastText(t.palette[n].main)})}}))]})}))),Gf=si("div",{name:"MuiAlert",slot:"Icon"})({marginRight:12,padding:"7px 0",display:"flex",fontSize:22,opacity:.9}),Qf=si("div",{name:"MuiAlert",slot:"Message"})({padding:"8px 0",minWidth:0,overflow:"auto"}),Xf=si("div",{name:"MuiAlert",slot:"Action"})({display:"flex",alignItems:"flex-start",padding:"4px 0 0 16px",marginLeft:"auto",marginRight:-8}),Jf={success:(0,pi.jsx)(Hf,{fontSize:"inherit"}),warning:(0,pi.jsx)(Uf,{fontSize:"inherit"}),error:(0,pi.jsx)(Vf,{fontSize:"inherit"}),info:(0,pi.jsx)($f,{fontSize:"inherit"})},Zf=r.forwardRef((function(e,t){const n=mi({props:e,name:"MuiAlert"}),{action:r,children:o,className:a,closeText:i="Close",color:s,components:c={},componentsProps:u={},icon:d,iconMapping:f=Jf,onClose:h,role:m="alert",severity:g="success",slotProps:v={},slots:y={},variant:b="standard"}=n,x=l(n,qf),w=p(p({},n),{},{color:s,severity:g,variant:b,colorSeverity:s||g}),S=(e=>{const{variant:t,color:n,severity:r,classes:o}=e;return Pt({root:["root","color".concat(_t(n||r)),"".concat(t).concat(_t(n||r)),"".concat(t)],icon:["icon"],message:["message"],action:["action"]},Bf,o)})(w),k={slots:p({closeButton:c.CloseButton,closeIcon:c.CloseIcon},y),slotProps:p(p({},u),v)},[C,E]=ts("root",{ref:t,shouldForwardComponentProp:!0,className:Nt(S.root,a),elementType:Yf,externalForwardedProps:p(p({},k),x),ownerState:w,additionalProps:{role:m,elevation:0}}),[j,N]=ts("icon",{className:S.icon,elementType:Gf,externalForwardedProps:k,ownerState:w}),[P,R]=ts("message",{className:S.message,elementType:Qf,externalForwardedProps:k,ownerState:w}),[T,_]=ts("action",{className:S.action,elementType:Xf,externalForwardedProps:k,ownerState:w}),[A,M]=ts("closeButton",{elementType:xp,externalForwardedProps:k,ownerState:w}),[z,I]=ts("closeIcon",{elementType:Kf,externalForwardedProps:k,ownerState:w});return(0,pi.jsxs)(C,p(p({},E),{},{children:[!1!==d?(0,pi.jsx)(j,p(p({},N),{},{children:d||f[g]||Jf[g]})):null,(0,pi.jsx)(P,p(p({},R),{},{children:o})),null!=r?(0,pi.jsx)(T,p(p({},_),{},{children:r})):null,null==r&&h?(0,pi.jsx)(T,p(p({},_),{},{children:(0,pi.jsx)(A,p(p({size:"small","aria-label":i,title:i,color:"inherit",onClick:h},M),{},{children:(0,pi.jsx)(z,p({fontSize:"small"},I))}))})):null]}))})),eh=Zf;const th=function(){return(0,pi.jsx)("div",{className:"loader"})};const nh=function(){const e=ge(),{id:t}=ve(),[n,o]=(0,r.useState)(!1),[a,i]=(0,r.useState)(""),[l,s]=(0,r.useState)(""),[c,u]=(0,r.useState)(""),[d,p]=(0,r.useState)(!0),[f,h]=(0,r.useState)(""),[m,g]=(0,r.useState)(""),v=(0,r.useCallback)((async()=>{p(!0),h("invalid"),g("");try{const e=await(async e=>{let t=null;try{var n;let r={"Content-Type":"application/json"};const o=localStorage.getItem("token");o&&(r.Authorization="Bearer ".concat(o));const a=await fetch(Op.REACT_APP_API_URL+e,{method:"GET",headers:r});t=await a.json(),Lp(null===(n=t)||void 0===n?void 0:n.error)}catch(m){console.error("Fetch error:",m)}finally{return t}})("interview-app/auth/check/".concat(t));p(!1),null!==e&&void 0!==e&&e.status?h("valid"):g("The signup link is invalid or has expired.")}catch(e){p(!1),g("An error occurred while validating your signup link. Please try again later."),console.error("Validation error:",e)}}),[t]);return(0,r.useEffect)((()=>{v()}),[v]),(0,pi.jsx)(pi.Fragment,{children:(0,pi.jsx)("div",{className:"login-container",children:(0,pi.jsxs)("div",{className:"screen",children:[(0,pi.jsxs)("div",{className:"screen__content",children:[d&&(0,pi.jsx)("div",{style:{marginTop:20},children:(0,pi.jsx)(th,{})}),m&&!d&&(0,pi.jsx)("div",{style:{padding:"20px 30px 0"},children:(0,pi.jsx)(eh,{severity:"error",children:m})}),!d&&"valid"===f&&(0,pi.jsxs)("div",{className:"login",children:[(0,pi.jsx)("h2",{className:"signup-title",children:"Create Your Account"}),(0,pi.jsx)("div",{className:"login__field",children:(0,pi.jsx)(ad,{id:"outlined-basic",label:"Name",variant:"outlined",value:a,onChange:e=>i(e.target.value),placeholder:"Enter your full name",className:"w-100",fullWidth:!0})}),(0,pi.jsx)("div",{className:"login__field",children:(0,pi.jsx)(ad,{id:"outlined-basic",label:"Email",variant:"outlined",value:l,onChange:e=>s(e.target.value),placeholder:"Enter your email",className:"w-100",fullWidth:!0,type:"email"})}),(0,pi.jsx)("div",{className:"login__field",children:(0,pi.jsx)(ad,{id:"outlined-basic",label:"Password",variant:"outlined",value:c,type:n?"text":"password",InputProps:{endAdornment:(0,pi.jsx)(wd,{position:"end",children:(0,pi.jsx)(xp,{onClick:()=>{o((e=>!e))},edge:"end",children:n?(0,pi.jsx)(zp,{}):(0,pi.jsx)(Ip,{})})})},onChange:e=>u(e.target.value),className:"w-100",fullWidth:!0})}),(0,pi.jsxs)("button",{className:"button login__submit",onClick:async()=>{if(a.trim()?l.trim()?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(l)?c?!(c.length<6)||(g("Password must be at least 6 characters long"),0):(g("Please enter a password"),0):(g("Please enter a valid email address"),0):(g("Please enter your email"),0):(g("Please enter your name"),0)){p(!0),g("");try{const n=await Fp("interview-app/auth/setup",{id:t,username:l,firstName:a,password:c});p(!1),null!==n&&void 0!==n&&n.status?(h("redirectLogin"),setTimeout((()=>{e("/login")}),3e3)):g((null===n||void 0===n?void 0:n.message)||"Failed to create account. Please try again.")}catch(n){p(!1),g("An error occurred while creating your account. Please try again later."),console.error("Setup error:",n)}}},children:[(0,pi.jsx)("span",{className:"button__text",children:"Create Account"}),(0,pi.jsx)("i",{className:"button__icon fas fa-chevron-right"})]}),(0,pi.jsxs)("div",{className:"login-link",children:["Already have an account? ",(0,pi.jsx)("button",{className:"text-button",onClick:t=>{t.preventDefault(),e("/login")},children:"Log in"})]})]}),!d&&"invalid"===f&&(0,pi.jsxs)("div",{className:"login",children:[(0,pi.jsx)("h1",{children:"Invalid Signup Link"}),(0,pi.jsx)("p",{children:"The signup link you're using is invalid or has expired."}),(0,pi.jsx)(Mp,{variant:"contained",onClick:()=>e("/login"),style:{marginTop:20},children:"Go to Login"})]}),!d&&"redirectLogin"===f&&(0,pi.jsxs)("div",{className:"login",children:[(0,pi.jsx)("h3",{children:"Account Created Successfully!"}),(0,pi.jsx)("p",{children:"Redirecting you to the login page in a few seconds..."}),(0,pi.jsx)(Mp,{variant:"contained",onClick:()=>e("/login"),style:{marginTop:20},children:"Go to Login"})]})]}),(0,pi.jsxs)("div",{className:"screen__background",children:[(0,pi.jsx)("span",{className:"screen__background__shape screen__background__shape4"}),(0,pi.jsx)("span",{className:"screen__background__shape screen__background__shape3"}),(0,pi.jsx)("span",{className:"screen__background__shape screen__background__shape2"}),(0,pi.jsx)("span",{className:"screen__background__shape screen__background__shape1"})]})]})})})};const rh=function(){const e=ge(),[t,n]=(0,r.useState)(""),[o,a]=(0,r.useState)(""),[i,l]=(0,r.useState)(""),[s,c]=(0,r.useState)(!1),[u,d]=(0,r.useState)(""),[p,f]=(0,r.useState)("");return(0,pi.jsx)("div",{className:"login-container",children:(0,pi.jsxs)("div",{className:"screen",children:[(0,pi.jsxs)("div",{className:"screen__content",children:[s&&(0,pi.jsx)("div",{style:{marginTop:20},children:(0,pi.jsx)(th,{})}),p&&!s&&(0,pi.jsx)("div",{style:{padding:"20px 30px 0"},children:(0,pi.jsx)(eh,{severity:"error",children:p})}),!s&&!u&&(0,pi.jsxs)("div",{className:"login",children:[(0,pi.jsx)("h2",{className:"signup-title",children:"Create Your Account"}),(0,pi.jsx)("div",{className:"login__field",children:(0,pi.jsx)(ad,{id:"outlined-basic",label:"Name",variant:"outlined",value:t,onChange:e=>n(e.target.value),placeholder:"Enter your full name",className:"w-100",fullWidth:!0})}),(0,pi.jsx)("div",{className:"login__field",children:(0,pi.jsx)(ad,{id:"outlined-basic",label:"Email",variant:"outlined",value:o,onChange:e=>a(e.target.value),placeholder:"Enter your email",className:"w-100",fullWidth:!0,type:"email"})}),(0,pi.jsx)("div",{className:"login__field",children:(0,pi.jsx)(ad,{id:"outlined-basic",label:"Mobile (optional)",variant:"outlined",value:i,onChange:e=>l(e.target.value),placeholder:"Enter your mobile number",className:"w-100",fullWidth:!0,type:"tel"})}),(0,pi.jsxs)("button",{className:"button login__submit",onClick:async()=>{if(t.trim()?o.trim()?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(o)||(f("Please enter a valid email address"),0):(f("Please enter your email"),0):(f("Please enter your name"),0)){c(!0),f("");try{const n=await(async e=>{let t=null;try{var n;let r={"Content-Type":"application/json"};const o=await fetch(Op.REACT_APP_API_URL+"/interview-app/auth/setup",{method:"POST",headers:r,body:JSON.stringify(e)});t=await o.json(),Lp(null===(n=t)||void 0===n?void 0:n.error)}catch(p){console.error("Fetch error:",p)}finally{return t}})({firstName:t,email:o,mobile:i});c(!1),null!==n&&void 0!==n&&n.status?(d("redirectLogin"),setTimeout((()=>{e("/login")}),3e3)):f((null===n||void 0===n?void 0:n.message)||"Failed to create account. Please try again.")}catch(n){c(!1),f("An error occurred while creating your account. Please try again later."),console.error("Signup error:",n)}}},children:[(0,pi.jsx)("span",{className:"button__text",children:"Create Account"}),(0,pi.jsx)("i",{className:"button__icon fas fa-chevron-right"})]}),(0,pi.jsxs)("div",{className:"login-link",children:["Already have an account? ",(0,pi.jsx)("button",{className:"text-button",onClick:t=>{t.preventDefault(),e("/login")},children:"Log in"})]})]}),!s&&"redirectLogin"===u&&(0,pi.jsxs)("div",{className:"login",children:[(0,pi.jsx)("h3",{children:"Account Created Successfully!"}),(0,pi.jsx)("p",{children:"Please check your email for a temporary password to login."}),(0,pi.jsx)(Mp,{variant:"contained",onClick:()=>e("/login"),style:{marginTop:20},children:"Go to Login"})]})]}),(0,pi.jsxs)("div",{className:"screen__background",children:[(0,pi.jsx)("span",{className:"screen__background__shape screen__background__shape4"}),(0,pi.jsx)("span",{className:"screen__background__shape screen__background__shape3"}),(0,pi.jsx)("span",{className:"screen__background__shape screen__background__shape2"}),(0,pi.jsx)("span",{className:"screen__background__shape screen__background__shape1"})]})]})})};const oh=function(){const e=ge(),[t,n]=(0,r.useState)(""),[o,a]=(0,r.useState)(!1),[i,l]=(0,r.useState)(""),[s,c]=(0,r.useState)("");return(0,pi.jsxs)(pi.Fragment,{children:[(0,pi.jsx)(Ti,{}),(0,pi.jsx)("div",{className:"login-container",children:(0,pi.jsxs)("div",{className:"screen",children:[(0,pi.jsxs)("div",{className:"screen__content",children:[o&&(0,pi.jsx)("div",{style:{marginTop:20},children:(0,pi.jsx)(th,{})}),s&&!o&&(0,pi.jsx)("div",{style:{padding:"20px 30px 0"},children:(0,pi.jsx)(eh,{severity:"error",children:s})}),!o&&"success"===i&&(0,pi.jsxs)("div",{className:"login",children:[(0,pi.jsx)("h3",{children:"Password Updated Successfully!"}),(0,pi.jsx)(Mp,{variant:"contained",onClick:()=>e("/home"),style:{marginTop:20},children:"Go to Home"})]}),!o&&!i&&(0,pi.jsxs)("div",{className:"login",children:[(0,pi.jsx)("h2",{className:"signup-title",children:"Update Password"}),(0,pi.jsx)("div",{className:"login__field",children:(0,pi.jsx)(ad,{id:"outlined-password",label:"New Password",variant:"outlined",value:t,type:"password",onChange:e=>n(e.target.value),className:"w-100",fullWidth:!0})}),(0,pi.jsxs)("button",{className:"button login__submit",onClick:async()=>{if(t?!(t.length<6)||(c("Password must be at least 6 characters long"),0):(c("Please enter a password"),0)){a(!0),c("");try{const e=await(async e=>{let t=null;try{var n;let r={"Content-Type":"application/json"};const o=localStorage.getItem("token");o&&(r.Authorization="Bearer ".concat(o));const a=await fetch(Op.REACT_APP_API_URL+"/interview-app/users/changepassword",{method:"POST",headers:r,body:JSON.stringify(e)});t=await a.json(),Lp(null===(n=t)||void 0===n?void 0:n.error)}catch(s){console.error("Fetch error:",s)}finally{return t}})({password:t});a(!1),null!==e&&void 0!==e&&e.status?(l("success"),n("")):c((null===e||void 0===e?void 0:e.message)||"Failed to update password. Please try again.")}catch(e){a(!1),c("An error occurred while updating your password. Please try again later."),console.error("Profile error:",e)}}},children:[(0,pi.jsx)("span",{className:"button__text",children:"Update Password"}),(0,pi.jsx)("i",{className:"button__icon fas fa-chevron-right"})]})]})]}),(0,pi.jsxs)("div",{className:"screen__background",children:[(0,pi.jsx)("span",{className:"screen__background__shape screen__background__shape4"}),(0,pi.jsx)("span",{className:"screen__background__shape screen__background__shape3"}),(0,pi.jsx)("span",{className:"screen__background__shape screen__background__shape2"}),(0,pi.jsx)("span",{className:"screen__background__shape screen__background__shape1"})]})]})}),(0,pi.jsx)(_i,{})]})};const ah=function(){return(0,pi.jsxs)(pi.Fragment,{children:[(0,pi.jsx)(Ti,{}),(0,pi.jsx)("main",{className:"home-content",children:(0,pi.jsx)("section",{className:"pricing-section",style:{padding:"40px 0"},children:(0,pi.jsxs)("div",{className:"container",children:[(0,pi.jsx)("h2",{style:{textAlign:"center",marginBottom:40},children:"Choose Your Plan"}),(0,pi.jsxs)("div",{className:"pricing-grid",children:[(0,pi.jsxs)("div",{className:"pricing-card",children:[(0,pi.jsxs)("div",{className:"pricing-header",children:[(0,pi.jsx)("h3",{children:"FREE"}),(0,pi.jsxs)("div",{className:"price",children:[(0,pi.jsx)("span",{className:"currency",children:"$"}),(0,pi.jsx)("span",{className:"amount",children:"0"}),(0,pi.jsx)("span",{className:"period",children:"/ 5 mins session"})]})]}),(0,pi.jsxs)("div",{className:"pricing-features",children:[(0,pi.jsxs)("div",{className:"feature-item available",children:[(0,pi.jsx)("span",{className:"check",children:"\u2713"}),(0,pi.jsx)("span",{children:"5 mins session"})]}),(0,pi.jsxs)("div",{className:"feature-item available",children:[(0,pi.jsx)("span",{className:"check",children:"\u2713"}),(0,pi.jsx)("span",{children:"Browser based/app based meeting"})]}),(0,pi.jsxs)("div",{className:"feature-item available",children:[(0,pi.jsx)("span",{className:"check",children:"\u2713"}),(0,pi.jsx)("span",{children:"All meeting platform integration"})]}),(0,pi.jsxs)("div",{className:"feature-item available",children:[(0,pi.jsx)("span",{className:"check",children:"\u2713"}),(0,pi.jsx)("span",{children:"All industry/All Technology"})]}),(0,pi.jsxs)("div",{className:"feature-item available",children:[(0,pi.jsx)("span",{className:"check",children:"\u2713"}),(0,pi.jsx)("span",{children:"Instant Coding solution"})]}),(0,pi.jsxs)("div",{className:"feature-item available",children:[(0,pi.jsx)("span",{className:"check",children:"\u2713"}),(0,pi.jsx)("span",{children:"Instant Image creation"})]}),(0,pi.jsxs)("div",{className:"feature-item available",children:[(0,pi.jsx)("span",{className:"check",children:"\u2713"}),(0,pi.jsx)("span",{children:"Highly Customized AI playground"})]})]}),(0,pi.jsx)(yt,{to:"/signup",className:"pricing-cta",children:"Try for free"})]}),(0,pi.jsxs)("div",{className:"pricing-card",children:[(0,pi.jsxs)("div",{className:"pricing-header",children:[(0,pi.jsx)("h3",{children:"PRO"}),(0,pi.jsxs)("div",{className:"price",children:[(0,pi.jsx)("span",{className:"currency",children:"$"}),(0,pi.jsx)("span",{className:"amount",children:"4.99"}),(0,pi.jsx)("span",{className:"period",children:"/ 1 hour session"})]})]}),(0,pi.jsxs)("div",{className:"pricing-features",children:[(0,pi.jsxs)("div",{className:"feature-item available",children:[(0,pi.jsx)("span",{className:"check",children:"\u2713"}),(0,pi.jsx)("span",{children:"1 hour session"})]}),(0,pi.jsxs)("div",{className:"feature-item available",children:[(0,pi.jsx)("span",{className:"check",children:"\u2713"}),(0,pi.jsx)("span",{children:"Browser based/app based meeting"})]}),(0,pi.jsxs)("div",{className:"feature-item available",children:[(0,pi.jsx)("span",{className:"check",children:"\u2713"}),(0,pi.jsx)("span",{children:"All meeting platform integration"})]}),(0,pi.jsxs)("div",{className:"feature-item available",children:[(0,pi.jsx)("span",{className:"check",children:"\u2713"}),(0,pi.jsx)("span",{children:"All industry/All Technology"})]}),(0,pi.jsxs)("div",{className:"feature-item available",children:[(0,pi.jsx)("span",{className:"check",children:"\u2713"}),(0,pi.jsx)("span",{children:"Instant Coding solution"})]}),(0,pi.jsxs)("div",{className:"feature-item available",children:[(0,pi.jsx)("span",{className:"check",children:"\u2713"}),(0,pi.jsx)("span",{children:"Instant Image creation"})]}),(0,pi.jsxs)("div",{className:"feature-item available",children:[(0,pi.jsx)("span",{className:"check",children:"\u2713"}),(0,pi.jsx)("span",{children:"Highly Customized AI playground"})]})]}),(0,pi.jsx)(yt,{to:"/signup",className:"pricing-cta",children:"Get the plan"})]})]})]})})}),(0,pi.jsx)(_i,{})]})};const ih=function(){return(0,pi.jsx)(gt,{children:(0,pi.jsxs)(Ie,{children:[(0,pi.jsx)(Me,{path:"/",element:(0,pi.jsx)(Ai,{})}),(0,pi.jsx)(Me,{path:"/login",element:(0,pi.jsx)(Dp,{})}),(0,pi.jsx)(Me,{path:"/home",element:(0,pi.jsx)(Df,{})}),(0,pi.jsx)(Me,{path:"/signup/:id",element:(0,pi.jsx)(nh,{})}),(0,pi.jsx)(Me,{path:"/signup/free",element:(0,pi.jsx)(nh,{})}),(0,pi.jsx)(Me,{path:"/signup",element:(0,pi.jsx)(rh,{})}),(0,pi.jsx)(Me,{path:"/myprofile",element:(0,pi.jsx)(oh,{})}),(0,pi.jsx)(Me,{path:"/plans",element:(0,pi.jsx)(ah,{})}),(0,pi.jsx)(Me,{path:"*",element:(0,pi.jsx)(Ae,{to:"/",replace:!0})})]})})};(0,a.H)(document.getElementById("root")).render((0,pi.jsx)(r.StrictMode,{children:(0,pi.jsx)(ih,{})}))})();
//# sourceMappingURL=main.39dbf4ba.js.map