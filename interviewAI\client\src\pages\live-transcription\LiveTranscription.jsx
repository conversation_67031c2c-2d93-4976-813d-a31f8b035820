import React, { useState, useEffect, useRef } from 'react';

function Controls({ isRecording, onStart, onStop }) {
  return (
    <div style={{ margin: '10px 0' }}>
      <button onClick={onStart} disabled={isRecording} style={{ marginRight: 8 }}>
        Start Recording
      </button>
      <button onClick={onStop} disabled={!isRecording}>
        Stop Recording
      </button>
    </div>
  );
}

function LiveCaptions({ interimText }) {
  if (!interimText) return null;
  return (
    <div className="live-captions" style={{ border: '1px solid #ccc', padding: '10px', margin: '10px 0', minHeight: '30px', background: '#f9f9f9' }}>
      <strong>Live:</strong> {interimText}
    </div>
  );
}

function Transcript({ transcriptData }) {
  const downloadTranscript = () => {
    let content = "Conversation Transcript:\n\n";
    transcriptData.forEach(entry => {
      content += `[${entry.timestamp.toLocaleTimeString()}] ${entry.speaker}: ${entry.text}\n`;
    });
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'transcript.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="transcript" style={{ border: '1px solid #eee', padding: '10px', marginTop: '20px', background: '#fff' }}>
      <h2>Full Transcript</h2>
      {transcriptData.length === 0 && <p>No speech detected yet.</p>}
      <ul>
        {transcriptData.map((entry, index) => (
          <li key={index}>
            <small>[{entry.timestamp.toLocaleTimeString()}]</small> <strong>{entry.speaker}:</strong> {entry.text}
          </li>
        ))}
      </ul>
      {transcriptData.length > 0 && (
        <button onClick={downloadTranscript} style={{ marginTop: '10px' }}>
          Download Transcript
        </button>
      )}
    </div>
  );
}

function LiveTranscriptionPage() {
  const [isRecording, setIsRecording] = useState(false);
  const [currentSpeaker, setCurrentSpeaker] = useState('Speaker 1');
  const [interimTranscript, setInterimTranscript] = useState('');
  const [fullTranscript, setFullTranscript] = useState([]);
  const recognitionRef = useRef(null);

  useEffect(() => {
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    if (!SpeechRecognition) {
      alert('Speech Recognition API not supported in this browser. Try Chrome or Edge.');
      return;
    }
    const recognition = new SpeechRecognition();
    recognition.continuous = true;
    recognition.interimResults = true;
    recognition.lang = 'en-US';

    recognition.onstart = () => {
      setIsRecording(true);
    };

    recognition.onresult = (event) => {
      let interim = '';
      let final = '';
      for (let i = event.resultIndex; i < event.results.length; ++i) {
        if (event.results[i].isFinal) {
          final += event.results[i][0].transcript;
        } else {
          interim += event.results[i][0].transcript;
        }
      }
      if (interim) setInterimTranscript(interim);
      if (final) {
        setFullTranscript(prev => [
          ...prev,
          { speaker: currentSpeaker, text: final.trim(), timestamp: new Date() }
        ]);
        setInterimTranscript('');
      }
    };

    recognition.onerror = (event) => {
      if (event.error === 'no-speech') {
        // No speech detected, just stop
        setIsRecording(false);
      } else if (event.error === 'audio-capture') {
        alert('No microphone found. Ensure a microphone is installed and permissions are allowed.');
        setIsRecording(false);
      } else if (event.error === 'not-allowed') {
        alert('Permission to use microphone was denied. Please allow microphone access in browser settings.');
        setIsRecording(false);
      }
    };

    recognition.onend = () => {
      setIsRecording(false);
    };

    recognitionRef.current = recognition;
    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.stop();
      }
    };
  }, [currentSpeaker]);

  const startRecording = () => {
    if (isRecording || !recognitionRef.current) return;
    setInterimTranscript('');
    recognitionRef.current.start();
  };

  const stopRecording = () => {
    if (!isRecording || !recognitionRef.current) return;
    recognitionRef.current.stop();
    setIsRecording(false);
    if (interimTranscript.trim()) {
      setFullTranscript(prev => [
        ...prev,
        { speaker: currentSpeaker, text: interimTranscript.trim(), timestamp: new Date() }
      ]);
      setInterimTranscript('');
    }
  };

  const changeSpeaker = (speakerName) => {
    setCurrentSpeaker(speakerName);
  };

  return (
    <div className="live-transcription-page" style={{ maxWidth: 700, margin: '0 auto', padding: 24 }}>
      <h1>Real-time Transcription & Captioning</h1>
      <Controls isRecording={isRecording} onStart={startRecording} onStop={stopRecording} />
      <div style={{ margin: '10px 0' }}>
        Current Speaker:
        <button onClick={() => changeSpeaker('Speaker 1')} disabled={currentSpeaker === 'Speaker 1'} style={{ marginLeft: 8 }}>
          User 1
        </button>
        <button onClick={() => changeSpeaker('Speaker 2')} disabled={currentSpeaker === 'Speaker 2'} style={{ marginLeft: 8 }}>
          User 2
        </button>
      </div>
      <LiveCaptions interimText={interimTranscript} />
      <Transcript transcriptData={fullTranscript} />
    </div>
  );
}

export default LiveTranscriptionPage;
