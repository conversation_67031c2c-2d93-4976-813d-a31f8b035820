.diarized-transcription {
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.browser-warning {
  background-color: #fff3cd;
  color: #856404;
  padding: 12px 16px;
  border-radius: 4px;
  margin-bottom: 16px;
  border: 1px solid #ffeeba;
}

.transcript-display {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  min-height: 200px;
  max-height: 400px;
  overflow-y: auto;
}

.diarized-segments {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.segment {
  padding: 12px 16px;
  border-radius: 12px;
  position: relative;
  max-width: 85%;
  animation: fade-in 0.3s ease-out;
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.segment.speaker1 {
  background-color: #e3f2fd;
  border-left: 3px solid #2196f3;
  align-self: flex-start;
}

.segment.speaker2 {
  background-color: #f1f8e9;
  border-left: 3px solid #8bc34a;
  align-self: flex-end;
}

.segment.unknown {
  background-color: #f5f5f5;
  border-left: 3px solid #9e9e9e;
  align-self: center;
}

.segment.interim {
  opacity: 0.7;
  border-style: dashed;
}

.speaker-label {
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 4px;
  color: #555;
}

.segment.speaker1 .speaker-label {
  color: #1976d2;
}

.segment.speaker2 .speaker-label {
  color: #689f38;
}

.segment-text {
  font-size: 15px;
  line-height: 1.5;
  color: #333;
  word-break: break-word;
}

.placeholder {
  color: #999;
  text-align: center;
  font-style: italic;
  padding: 20px;
}

.controls {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  align-items: center;
}

.control-button {
  background-color: #4285f4;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.control-button:hover {
  background-color: #3367d6;
}

.control-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.control-button.listening {
  background-color: #ea4335;
}

.control-button.listening:hover {
  background-color: #d33426;
}

.control-button.tab-audio {
  background-color: #34a853;
  margin-left: 10px;
}

.control-button.tab-audio:hover {
  background-color: #2d9249;
}

.control-button.tab-audio.capturing {
  background-color: #fbbc05;
  color: #333;
}

.control-button.tab-audio.capturing:hover {
  background-color: #f0b400;
}

.status {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #555;
  margin-left: 10px;
}

.listening-indicator {
  width: 10px;
  height: 10px;
  background-color: #ea4335;
  border-radius: 50%;
  margin-right: 8px;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .segment {
    max-width: 95%;
  }
  
  .controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .control-button.tab-audio {
    margin-left: 0;
  }
  
  .status {
    margin-left: 0;
    margin-top: 8px;
    justify-content: center;
  }
}
