{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\interview-ai-app\\\\interviewAI\\\\client\\\\src\\\\pages\\\\live-transcription\\\\GoogleSpeechDiarization.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport './GoogleSpeechDiarization.css';\nimport env from '../../utils/env';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction GoogleSpeechDiarization() {\n  _s();\n  // State for recording and transcription\n  const [isRecording, setIsRecording] = useState(false);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [transcriptSegments, setTranscriptSegments] = useState([]);\n  const [errorMessage, setErrorMessage] = useState('');\n  const [audioSource, setAudioSource] = useState('microphone'); // 'microphone' or 'tab'\n  const [apiKeyStatus, setApiKeyStatus] = useState('unknown'); // 'unknown', 'valid', 'invalid'\n  const [isTestingApiKey, setIsTestingApiKey] = useState(false);\n\n  // Refs for audio handling\n  const mediaRecorderRef = useRef(null);\n  const audioChunksRef = useRef([]);\n  const mediaStreamRef = useRef(null);\n\n  // Clean up function for audio resources\n  const cleanupAudio = () => {\n    if (mediaRecorderRef.current) {\n      if (mediaRecorderRef.current.state !== 'inactive') {\n        mediaRecorderRef.current.stop();\n      }\n      mediaRecorderRef.current = null;\n    }\n    if (mediaStreamRef.current) {\n      mediaStreamRef.current.getTracks().forEach(track => track.stop());\n      mediaStreamRef.current = null;\n    }\n    audioChunksRef.current = [];\n  };\n\n  // Clean up on component unmount\n  useEffect(() => {\n    return () => {\n      cleanupAudio();\n    };\n  }, []);\n\n  // Test the Google Speech API key on component mount\n  useEffect(() => {\n    testApiKey();\n  }, []);\n\n  // Function to test if the API key is valid\n  const testApiKey = async () => {\n    try {\n      setIsTestingApiKey(true);\n      setErrorMessage('');\n\n      // Get API key from environment variables\n      const apiKey = env.GOOGLE_SPEECH_API_KEY;\n      if (!apiKey) {\n        throw new Error(\"Google Speech API key not found. Please add it to your .env file as REACT_APP_GOOGLE_SPEECH_API_KEY\");\n      }\n\n      // Make a simple request to the Google Speech API\n      const response = await fetch(`https://speech.googleapis.com/v1p1beta1/projects.locations.recognizers?key=${apiKey}`, {\n        method: 'GET'\n      });\n      if (response.ok) {\n        console.log(\"Google Speech API key is valid\");\n        setApiKeyStatus('valid');\n      } else {\n        var _errorData$error;\n        const errorData = await response.json();\n        console.error(\"Google Speech API key validation failed:\", errorData);\n        setApiKeyStatus('invalid');\n        setErrorMessage(`API Key Error: ${((_errorData$error = errorData.error) === null || _errorData$error === void 0 ? void 0 : _errorData$error.message) || 'Invalid API key'}`);\n      }\n    } catch (error) {\n      console.error(\"Error testing API key:\", error);\n      setApiKeyStatus('invalid');\n      setErrorMessage(`Error testing API key: ${error.message}`);\n    } finally {\n      setIsTestingApiKey(false);\n    }\n  };\n\n  // Start recording from microphone\n  const startMicrophoneRecording = async () => {\n    try {\n      cleanupAudio();\n      setErrorMessage('');\n      setAudioSource('microphone');\n\n      // Get microphone stream\n      const stream = await navigator.mediaDevices.getUserMedia({\n        audio: true\n      });\n      mediaStreamRef.current = stream;\n\n      // Create media recorder\n      const mediaRecorder = new MediaRecorder(stream);\n      mediaRecorderRef.current = mediaRecorder;\n\n      // Set up data handling\n      mediaRecorder.ondataavailable = event => {\n        if (event.data.size > 0) {\n          audioChunksRef.current.push(event.data);\n        }\n      };\n\n      // Handle recording stop\n      mediaRecorder.onstop = () => {\n        processAudioForTranscription();\n      };\n\n      // Start recording\n      mediaRecorder.start(1000); // Collect data every second\n      setIsRecording(true);\n      console.log(\"Microphone recording started\");\n    } catch (error) {\n      console.error(\"Error starting microphone recording:\", error);\n      setErrorMessage(`Failed to start microphone: ${error.message}`);\n    }\n  };\n\n  // Start recording from tab audio\n  const startTabAudioRecording = async () => {\n    try {\n      cleanupAudio();\n      setErrorMessage('');\n      setAudioSource('tab');\n\n      // Request screen sharing with audio\n      const stream = await navigator.mediaDevices.getDisplayMedia({\n        video: true,\n        audio: true\n      });\n\n      // Check if audio is included\n      const audioTracks = stream.getAudioTracks();\n      if (audioTracks.length === 0) {\n        throw new Error(\"No audio track found. Please make sure to select 'Share audio' when sharing the tab.\");\n      }\n      mediaStreamRef.current = stream;\n\n      // Create media recorder\n      const mediaRecorder = new MediaRecorder(stream);\n      mediaRecorderRef.current = mediaRecorder;\n\n      // Set up data handling\n      mediaRecorder.ondataavailable = event => {\n        if (event.data.size > 0) {\n          audioChunksRef.current.push(event.data);\n        }\n      };\n\n      // Handle recording stop\n      mediaRecorder.onstop = () => {\n        processAudioForTranscription();\n      };\n\n      // Handle stream ending (user stops sharing)\n      stream.getVideoTracks()[0].onended = () => {\n        stopRecording();\n      };\n\n      // Start recording\n      mediaRecorder.start(1000); // Collect data every second\n      setIsRecording(true);\n      console.log(\"Tab audio recording started\");\n    } catch (error) {\n      console.error(\"Error starting tab audio recording:\", error);\n      setErrorMessage(`Failed to capture tab audio: ${error.message}`);\n    }\n  };\n\n  // Stop recording\n  const stopRecording = () => {\n    if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {\n      mediaRecorderRef.current.stop();\n    }\n    setIsRecording(false);\n  };\n\n  // Process recorded audio and send to Google Speech-to-Text API\n  const processAudioForTranscription = async () => {\n    if (audioChunksRef.current.length === 0) {\n      console.log(\"No audio data to process\");\n      return;\n    }\n    try {\n      setIsProcessing(true);\n\n      // Create audio blob from chunks\n      const audioBlob = new Blob(audioChunksRef.current, {\n        type: 'audio/webm'\n      });\n\n      // Convert blob to base64\n      const reader = new FileReader();\n      reader.readAsDataURL(audioBlob);\n      reader.onloadend = async () => {\n        // Get base64 data (remove the data URL prefix)\n        const base64Audio = reader.result.split(',')[1];\n\n        // Get API key from environment variables\n        const apiKey = env.GOOGLE_SPEECH_API_KEY;\n        if (!apiKey) {\n          throw new Error(\"Google Speech API key not found. Please add it to your .env file as REACT_APP_GOOGLE_SPEECH_API_KEY\");\n        }\n        console.log(\"Using Google Speech API key:\", apiKey);\n\n        // Prepare request to Google Speech-to-Text API\n        const response = await fetch(`https://speech.googleapis.com/v1p1beta1/speech:recognize?key=${apiKey}`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            config: {\n              encoding: 'WEBM_OPUS',\n              sampleRateHertz: 48000,\n              languageCode: 'en-US',\n              enableAutomaticPunctuation: true,\n              enableSpeakerDiarization: true,\n              diarizationSpeakerCount: 2,\n              model: 'default'\n            },\n            audio: {\n              content: base64Audio\n            }\n          })\n        });\n        if (!response.ok) {\n          var _errorData$error2;\n          const errorData = await response.json();\n          throw new Error(`Google API error: ${((_errorData$error2 = errorData.error) === null || _errorData$error2 === void 0 ? void 0 : _errorData$error2.message) || response.statusText}`);\n        }\n        const data = await response.json();\n        processTranscriptionResponse(data);\n\n        // Clear audio chunks for next recording\n        audioChunksRef.current = [];\n        setIsProcessing(false);\n      };\n      reader.onerror = error => {\n        throw new Error(`Error reading audio data: ${error}`);\n      };\n    } catch (error) {\n      console.error(\"Error processing audio:\", error);\n      setErrorMessage(`Error processing audio: ${error.message}`);\n      setIsProcessing(false);\n    }\n  };\n\n  // Process the response from Google Speech-to-Text API\n  const processTranscriptionResponse = data => {\n    if (!data.results || data.results.length === 0) {\n      console.log(\"No transcription results returned\");\n      return;\n    }\n    try {\n      // Get the result with diarization\n      const result = data.results[data.results.length - 1];\n      if (!result.alternatives || !result.alternatives[0]) {\n        console.log(\"No alternatives in result\");\n        return;\n      }\n      const alternative = result.alternatives[0];\n      const transcript = alternative.transcript;\n\n      // Process speaker diarization if available\n      if (alternative.words && alternative.words.length > 0) {\n        let currentSpeaker = null;\n        let currentText = '';\n        let segments = [];\n\n        // Group words by speaker\n        alternative.words.forEach(word => {\n          const speakerTag = word.speakerTag || 0;\n\n          // If speaker changed or this is the first word\n          if (speakerTag !== currentSpeaker) {\n            // Save previous segment if it exists\n            if (currentText) {\n              segments.push({\n                speaker: currentSpeaker === 1 ? 'Speaker 1' : 'Speaker 2',\n                text: currentText.trim(),\n                timestamp: new Date()\n              });\n            }\n\n            // Start new segment\n            currentSpeaker = speakerTag;\n            currentText = word.word + ' ';\n          } else {\n            // Continue current segment\n            currentText += word.word + ' ';\n          }\n        });\n\n        // Add the last segment\n        if (currentText) {\n          segments.push({\n            speaker: currentSpeaker === 1 ? 'Speaker 1' : 'Speaker 2',\n            text: currentText.trim(),\n            timestamp: new Date()\n          });\n        }\n\n        // Update transcript segments\n        setTranscriptSegments(prev => [...prev, ...segments]);\n      } else {\n        // No speaker diarization, add as single segment\n        setTranscriptSegments(prev => [...prev, {\n          speaker: audioSource === 'microphone' ? 'Candidate' : 'Interviewer',\n          text: transcript,\n          timestamp: new Date()\n        }]);\n      }\n    } catch (error) {\n      console.error(\"Error processing transcription response:\", error);\n      setErrorMessage(`Error processing transcription: ${error.message}`);\n    }\n  };\n\n  // Download transcript as text file\n  const downloadTranscript = () => {\n    if (transcriptSegments.length === 0) return;\n    let content = \"Conversation Transcript:\\n\\n\";\n    transcriptSegments.forEach(segment => {\n      content += `[${segment.timestamp.toLocaleTimeString()}] ${segment.speaker}: ${segment.text}\\n`;\n    });\n    const blob = new Blob([content], {\n      type: 'text/plain'\n    });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = 'google_transcript.txt';\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"google-speech-diarization\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Google Speech API with Speaker Diarization\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 357,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"description\",\n        children: \"This page uses Google's Speech-to-Text API to transcribe audio with speaker diarization.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 358,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 356,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"controls-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"recording-controls\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: `control-button ${isRecording && audioSource === 'microphone' ? 'recording' : ''}`,\n          onClick: isRecording ? stopRecording : startMicrophoneRecording,\n          disabled: isProcessing,\n          children: isRecording && audioSource === 'microphone' ? 'Stop Recording' : 'Start Microphone Recording'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `control-button tab-audio ${isRecording && audioSource === 'tab' ? 'recording' : ''}`,\n          onClick: isRecording ? stopRecording : startTabAudioRecording,\n          disabled: isProcessing,\n          children: isRecording && audioSource === 'tab' ? 'Stop Tab Recording' : 'Start Tab Audio Recording'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 364,\n        columnNumber: 9\n      }, this), isProcessing && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"processing-indicator\",\n        children: \"Processing audio... This may take a moment.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 387,\n        columnNumber: 11\n      }, this), errorMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-message\",\n        children: errorMessage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 393,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 363,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"transcript-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"transcript-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Transcript with Speaker Diarization\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 401,\n          columnNumber: 11\n        }, this), transcriptSegments.length > 0 && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"download-button\",\n          onClick: downloadTranscript,\n          children: \"Download Transcript\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 400,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"transcript-content\",\n        children: transcriptSegments.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"empty-state\",\n          children: \"No transcription yet. Start recording to see results.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 414,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"transcript-segments\",\n          children: transcriptSegments.map((segment, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `transcript-segment ${segment.speaker.toLowerCase().replace(' ', '-')}`,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"segment-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"timestamp\",\n                children: segment.timestamp.toLocaleTimeString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 425,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"speaker-label\",\n                children: segment.speaker\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 426,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 424,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"segment-text\",\n              children: segment.text\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 412,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 399,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 355,\n    columnNumber: 5\n  }, this);\n}\n_s(GoogleSpeechDiarization, \"PuHNEyT1NEel8gEMHGM3OTsp48A=\");\n_c = GoogleSpeechDiarization;\nexport default GoogleSpeechDiarization;\nvar _c;\n$RefreshReg$(_c, \"GoogleSpeechDiarization\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "env", "jsxDEV", "_jsxDEV", "GoogleSpeechDiarization", "_s", "isRecording", "setIsRecording", "isProcessing", "setIsProcessing", "transcriptSegments", "setTranscriptSegments", "errorMessage", "setErrorMessage", "audioSource", "setAudioSource", "apiKeyStatus", "setApiKeyStatus", "isTestingApiKey", "setIsTestingApiKey", "mediaRecorderRef", "audioChunksRef", "mediaStreamRef", "cleanupAudio", "current", "state", "stop", "getTracks", "for<PERSON>ach", "track", "test<PERSON>pi<PERSON>ey", "<PERSON><PERSON><PERSON><PERSON>", "GOOGLE_SPEECH_API_KEY", "Error", "response", "fetch", "method", "ok", "console", "log", "_errorData$error", "errorData", "json", "error", "message", "startMicrophoneRecording", "stream", "navigator", "mediaDevices", "getUserMedia", "audio", "mediaRecorder", "MediaRecorder", "ondataavailable", "event", "data", "size", "push", "onstop", "processAudioForTranscription", "start", "startTabAudioRecording", "getDisplayMedia", "video", "audioTracks", "getAudioTracks", "length", "getVideoTracks", "onended", "stopRecording", "audioBlob", "Blob", "type", "reader", "FileReader", "readAsDataURL", "onloadend", "base64Audio", "result", "split", "headers", "body", "JSON", "stringify", "config", "encoding", "sampleRateHertz", "languageCode", "enableAutomaticPunctuation", "enableSpeakerDiarization", "diarizationSpeakerCount", "model", "content", "_errorData$error2", "statusText", "processTranscriptionResponse", "onerror", "results", "alternatives", "alternative", "transcript", "words", "currentSpeaker", "currentText", "segments", "word", "speakerTag", "speaker", "text", "trim", "timestamp", "Date", "prev", "downloadTranscript", "segment", "toLocaleTimeString", "blob", "url", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "map", "index", "toLowerCase", "replace", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/interview-ai-app/interviewAI/client/src/pages/live-transcription/GoogleSpeechDiarization.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport './GoogleSpeechDiarization.css';\nimport env from '../../utils/env';\n\nfunction GoogleSpeechDiarization() {\n  // State for recording and transcription\n  const [isRecording, setIsRecording] = useState(false);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [transcriptSegments, setTranscriptSegments] = useState([]);\n  const [errorMessage, setErrorMessage] = useState('');\n  const [audioSource, setAudioSource] = useState('microphone'); // 'microphone' or 'tab'\n  const [apiKeyStatus, setApiKeyStatus] = useState('unknown'); // 'unknown', 'valid', 'invalid'\n  const [isTestingApiKey, setIsTestingApiKey] = useState(false);\n\n  // Refs for audio handling\n  const mediaRecorderRef = useRef(null);\n  const audioChunksRef = useRef([]);\n  const mediaStreamRef = useRef(null);\n\n  // Clean up function for audio resources\n  const cleanupAudio = () => {\n    if (mediaRecorderRef.current) {\n      if (mediaRecorderRef.current.state !== 'inactive') {\n        mediaRecorderRef.current.stop();\n      }\n      mediaRecorderRef.current = null;\n    }\n\n    if (mediaStreamRef.current) {\n      mediaStreamRef.current.getTracks().forEach(track => track.stop());\n      mediaStreamRef.current = null;\n    }\n\n    audioChunksRef.current = [];\n  };\n\n  // Clean up on component unmount\n  useEffect(() => {\n    return () => {\n      cleanupAudio();\n    };\n  }, []);\n\n  // Test the Google Speech API key on component mount\n  useEffect(() => {\n    testApiKey();\n  }, []);\n\n  // Function to test if the API key is valid\n  const testApiKey = async () => {\n    try {\n      setIsTestingApiKey(true);\n      setErrorMessage('');\n\n      // Get API key from environment variables\n      const apiKey = env.GOOGLE_SPEECH_API_KEY;\n      if (!apiKey) {\n        throw new Error(\"Google Speech API key not found. Please add it to your .env file as REACT_APP_GOOGLE_SPEECH_API_KEY\");\n      }\n\n      // Make a simple request to the Google Speech API\n      const response = await fetch(`https://speech.googleapis.com/v1p1beta1/projects.locations.recognizers?key=${apiKey}`, {\n        method: 'GET'\n      });\n\n      if (response.ok) {\n        console.log(\"Google Speech API key is valid\");\n        setApiKeyStatus('valid');\n      } else {\n        const errorData = await response.json();\n        console.error(\"Google Speech API key validation failed:\", errorData);\n        setApiKeyStatus('invalid');\n        setErrorMessage(`API Key Error: ${errorData.error?.message || 'Invalid API key'}`);\n      }\n    } catch (error) {\n      console.error(\"Error testing API key:\", error);\n      setApiKeyStatus('invalid');\n      setErrorMessage(`Error testing API key: ${error.message}`);\n    } finally {\n      setIsTestingApiKey(false);\n    }\n  };\n\n  // Start recording from microphone\n  const startMicrophoneRecording = async () => {\n    try {\n      cleanupAudio();\n      setErrorMessage('');\n      setAudioSource('microphone');\n\n      // Get microphone stream\n      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });\n      mediaStreamRef.current = stream;\n\n      // Create media recorder\n      const mediaRecorder = new MediaRecorder(stream);\n      mediaRecorderRef.current = mediaRecorder;\n\n      // Set up data handling\n      mediaRecorder.ondataavailable = (event) => {\n        if (event.data.size > 0) {\n          audioChunksRef.current.push(event.data);\n        }\n      };\n\n      // Handle recording stop\n      mediaRecorder.onstop = () => {\n        processAudioForTranscription();\n      };\n\n      // Start recording\n      mediaRecorder.start(1000); // Collect data every second\n      setIsRecording(true);\n\n      console.log(\"Microphone recording started\");\n    } catch (error) {\n      console.error(\"Error starting microphone recording:\", error);\n      setErrorMessage(`Failed to start microphone: ${error.message}`);\n    }\n  };\n\n  // Start recording from tab audio\n  const startTabAudioRecording = async () => {\n    try {\n      cleanupAudio();\n      setErrorMessage('');\n      setAudioSource('tab');\n\n      // Request screen sharing with audio\n      const stream = await navigator.mediaDevices.getDisplayMedia({\n        video: true,\n        audio: true\n      });\n\n      // Check if audio is included\n      const audioTracks = stream.getAudioTracks();\n      if (audioTracks.length === 0) {\n        throw new Error(\"No audio track found. Please make sure to select 'Share audio' when sharing the tab.\");\n      }\n\n      mediaStreamRef.current = stream;\n\n      // Create media recorder\n      const mediaRecorder = new MediaRecorder(stream);\n      mediaRecorderRef.current = mediaRecorder;\n\n      // Set up data handling\n      mediaRecorder.ondataavailable = (event) => {\n        if (event.data.size > 0) {\n          audioChunksRef.current.push(event.data);\n        }\n      };\n\n      // Handle recording stop\n      mediaRecorder.onstop = () => {\n        processAudioForTranscription();\n      };\n\n      // Handle stream ending (user stops sharing)\n      stream.getVideoTracks()[0].onended = () => {\n        stopRecording();\n      };\n\n      // Start recording\n      mediaRecorder.start(1000); // Collect data every second\n      setIsRecording(true);\n\n      console.log(\"Tab audio recording started\");\n    } catch (error) {\n      console.error(\"Error starting tab audio recording:\", error);\n      setErrorMessage(`Failed to capture tab audio: ${error.message}`);\n    }\n  };\n\n  // Stop recording\n  const stopRecording = () => {\n    if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {\n      mediaRecorderRef.current.stop();\n    }\n    setIsRecording(false);\n  };\n\n  // Process recorded audio and send to Google Speech-to-Text API\n  const processAudioForTranscription = async () => {\n    if (audioChunksRef.current.length === 0) {\n      console.log(\"No audio data to process\");\n      return;\n    }\n\n    try {\n      setIsProcessing(true);\n\n      // Create audio blob from chunks\n      const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });\n\n      // Convert blob to base64\n      const reader = new FileReader();\n      reader.readAsDataURL(audioBlob);\n\n      reader.onloadend = async () => {\n        // Get base64 data (remove the data URL prefix)\n        const base64Audio = reader.result.split(',')[1];\n\n        // Get API key from environment variables\n        const apiKey = env.GOOGLE_SPEECH_API_KEY;\n        if (!apiKey) {\n          throw new Error(\"Google Speech API key not found. Please add it to your .env file as REACT_APP_GOOGLE_SPEECH_API_KEY\");\n        }\n        console.log(\"Using Google Speech API key:\", apiKey);\n\n        // Prepare request to Google Speech-to-Text API\n        const response = await fetch(`https://speech.googleapis.com/v1p1beta1/speech:recognize?key=${apiKey}`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            config: {\n              encoding: 'WEBM_OPUS',\n              sampleRateHertz: 48000,\n              languageCode: 'en-US',\n              enableAutomaticPunctuation: true,\n              enableSpeakerDiarization: true,\n              diarizationSpeakerCount: 2,\n              model: 'default'\n            },\n            audio: {\n              content: base64Audio\n            }\n          })\n        });\n\n        if (!response.ok) {\n          const errorData = await response.json();\n          throw new Error(`Google API error: ${errorData.error?.message || response.statusText}`);\n        }\n\n        const data = await response.json();\n        processTranscriptionResponse(data);\n\n        // Clear audio chunks for next recording\n        audioChunksRef.current = [];\n        setIsProcessing(false);\n      };\n\n      reader.onerror = (error) => {\n        throw new Error(`Error reading audio data: ${error}`);\n      };\n\n    } catch (error) {\n      console.error(\"Error processing audio:\", error);\n      setErrorMessage(`Error processing audio: ${error.message}`);\n      setIsProcessing(false);\n    }\n  };\n\n  // Process the response from Google Speech-to-Text API\n  const processTranscriptionResponse = (data) => {\n    if (!data.results || data.results.length === 0) {\n      console.log(\"No transcription results returned\");\n      return;\n    }\n\n    try {\n      // Get the result with diarization\n      const result = data.results[data.results.length - 1];\n\n      if (!result.alternatives || !result.alternatives[0]) {\n        console.log(\"No alternatives in result\");\n        return;\n      }\n\n      const alternative = result.alternatives[0];\n      const transcript = alternative.transcript;\n\n      // Process speaker diarization if available\n      if (alternative.words && alternative.words.length > 0) {\n        let currentSpeaker = null;\n        let currentText = '';\n        let segments = [];\n\n        // Group words by speaker\n        alternative.words.forEach(word => {\n          const speakerTag = word.speakerTag || 0;\n\n          // If speaker changed or this is the first word\n          if (speakerTag !== currentSpeaker) {\n            // Save previous segment if it exists\n            if (currentText) {\n              segments.push({\n                speaker: currentSpeaker === 1 ? 'Speaker 1' : 'Speaker 2',\n                text: currentText.trim(),\n                timestamp: new Date()\n              });\n            }\n\n            // Start new segment\n            currentSpeaker = speakerTag;\n            currentText = word.word + ' ';\n          } else {\n            // Continue current segment\n            currentText += word.word + ' ';\n          }\n        });\n\n        // Add the last segment\n        if (currentText) {\n          segments.push({\n            speaker: currentSpeaker === 1 ? 'Speaker 1' : 'Speaker 2',\n            text: currentText.trim(),\n            timestamp: new Date()\n          });\n        }\n\n        // Update transcript segments\n        setTranscriptSegments(prev => [...prev, ...segments]);\n      } else {\n        // No speaker diarization, add as single segment\n        setTranscriptSegments(prev => [\n          ...prev,\n          {\n            speaker: audioSource === 'microphone' ? 'Candidate' : 'Interviewer',\n            text: transcript,\n            timestamp: new Date()\n          }\n        ]);\n      }\n    } catch (error) {\n      console.error(\"Error processing transcription response:\", error);\n      setErrorMessage(`Error processing transcription: ${error.message}`);\n    }\n  };\n\n  // Download transcript as text file\n  const downloadTranscript = () => {\n    if (transcriptSegments.length === 0) return;\n\n    let content = \"Conversation Transcript:\\n\\n\";\n    transcriptSegments.forEach(segment => {\n      content += `[${segment.timestamp.toLocaleTimeString()}] ${segment.speaker}: ${segment.text}\\n`;\n    });\n\n    const blob = new Blob([content], { type: 'text/plain' });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = 'google_transcript.txt';\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n\n  return (\n    <div className=\"google-speech-diarization\">\n      <div className=\"page-header\">\n        <h1>Google Speech API with Speaker Diarization</h1>\n        <p className=\"description\">\n          This page uses Google's Speech-to-Text API to transcribe audio with speaker diarization.\n        </p>\n      </div>\n\n      <div className=\"controls-container\">\n        <div className=\"recording-controls\">\n          <button\n            className={`control-button ${isRecording && audioSource === 'microphone' ? 'recording' : ''}`}\n            onClick={isRecording ? stopRecording : startMicrophoneRecording}\n            disabled={isProcessing}\n          >\n            {isRecording && audioSource === 'microphone'\n              ? 'Stop Recording'\n              : 'Start Microphone Recording'}\n          </button>\n\n          <button\n            className={`control-button tab-audio ${isRecording && audioSource === 'tab' ? 'recording' : ''}`}\n            onClick={isRecording ? stopRecording : startTabAudioRecording}\n            disabled={isProcessing}\n          >\n            {isRecording && audioSource === 'tab'\n              ? 'Stop Tab Recording'\n              : 'Start Tab Audio Recording'}\n          </button>\n        </div>\n\n        {isProcessing && (\n          <div className=\"processing-indicator\">\n            Processing audio... This may take a moment.\n          </div>\n        )}\n\n        {errorMessage && (\n          <div className=\"error-message\">\n            {errorMessage}\n          </div>\n        )}\n      </div>\n\n      <div className=\"transcript-container\">\n        <div className=\"transcript-header\">\n          <h2>Transcript with Speaker Diarization</h2>\n          {transcriptSegments.length > 0 && (\n            <button\n              className=\"download-button\"\n              onClick={downloadTranscript}\n            >\n              Download Transcript\n            </button>\n          )}\n        </div>\n\n        <div className=\"transcript-content\">\n          {transcriptSegments.length === 0 ? (\n            <div className=\"empty-state\">\n              No transcription yet. Start recording to see results.\n            </div>\n          ) : (\n            <div className=\"transcript-segments\">\n              {transcriptSegments.map((segment, index) => (\n                <div\n                  key={index}\n                  className={`transcript-segment ${segment.speaker.toLowerCase().replace(' ', '-')}`}\n                >\n                  <div className=\"segment-header\">\n                    <span className=\"timestamp\">{segment.timestamp.toLocaleTimeString()}</span>\n                    <span className=\"speaker-label\">{segment.speaker}</span>\n                  </div>\n                  <div className=\"segment-text\">{segment.text}</div>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default GoogleSpeechDiarization;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,+BAA+B;AACtC,OAAOC,GAAG,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,SAASC,uBAAuBA,CAAA,EAAG;EAAAC,EAAA;EACjC;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGT,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACU,YAAY,EAAEC,eAAe,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACY,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAACc,YAAY,EAAEC,eAAe,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACgB,WAAW,EAAEC,cAAc,CAAC,GAAGjB,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC;EAC9D,MAAM,CAACkB,YAAY,EAAEC,eAAe,CAAC,GAAGnB,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;EAC7D,MAAM,CAACoB,eAAe,EAAEC,kBAAkB,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACA,MAAMsB,gBAAgB,GAAGpB,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMqB,cAAc,GAAGrB,MAAM,CAAC,EAAE,CAAC;EACjC,MAAMsB,cAAc,GAAGtB,MAAM,CAAC,IAAI,CAAC;;EAEnC;EACA,MAAMuB,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIH,gBAAgB,CAACI,OAAO,EAAE;MAC5B,IAAIJ,gBAAgB,CAACI,OAAO,CAACC,KAAK,KAAK,UAAU,EAAE;QACjDL,gBAAgB,CAACI,OAAO,CAACE,IAAI,CAAC,CAAC;MACjC;MACAN,gBAAgB,CAACI,OAAO,GAAG,IAAI;IACjC;IAEA,IAAIF,cAAc,CAACE,OAAO,EAAE;MAC1BF,cAAc,CAACE,OAAO,CAACG,SAAS,CAAC,CAAC,CAACC,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACH,IAAI,CAAC,CAAC,CAAC;MACjEJ,cAAc,CAACE,OAAO,GAAG,IAAI;IAC/B;IAEAH,cAAc,CAACG,OAAO,GAAG,EAAE;EAC7B,CAAC;;EAED;EACAzB,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACXwB,YAAY,CAAC,CAAC;IAChB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAxB,SAAS,CAAC,MAAM;IACd+B,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMA,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFX,kBAAkB,CAAC,IAAI,CAAC;MACxBN,eAAe,CAAC,EAAE,CAAC;;MAEnB;MACA,MAAMkB,MAAM,GAAG9B,GAAG,CAAC+B,qBAAqB;MACxC,IAAI,CAACD,MAAM,EAAE;QACX,MAAM,IAAIE,KAAK,CAAC,qGAAqG,CAAC;MACxH;;MAEA;MACA,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,8EAA8EJ,MAAM,EAAE,EAAE;QACnHK,MAAM,EAAE;MACV,CAAC,CAAC;MAEF,IAAIF,QAAQ,CAACG,EAAE,EAAE;QACfC,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;QAC7CtB,eAAe,CAAC,OAAO,CAAC;MAC1B,CAAC,MAAM;QAAA,IAAAuB,gBAAA;QACL,MAAMC,SAAS,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;QACvCJ,OAAO,CAACK,KAAK,CAAC,0CAA0C,EAAEF,SAAS,CAAC;QACpExB,eAAe,CAAC,SAAS,CAAC;QAC1BJ,eAAe,CAAC,kBAAkB,EAAA2B,gBAAA,GAAAC,SAAS,CAACE,KAAK,cAAAH,gBAAA,uBAAfA,gBAAA,CAAiBI,OAAO,KAAI,iBAAiB,EAAE,CAAC;MACpF;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C1B,eAAe,CAAC,SAAS,CAAC;MAC1BJ,eAAe,CAAC,0BAA0B8B,KAAK,CAACC,OAAO,EAAE,CAAC;IAC5D,CAAC,SAAS;MACRzB,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;;EAED;EACA,MAAM0B,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3C,IAAI;MACFtB,YAAY,CAAC,CAAC;MACdV,eAAe,CAAC,EAAE,CAAC;MACnBE,cAAc,CAAC,YAAY,CAAC;;MAE5B;MACA,MAAM+B,MAAM,GAAG,MAAMC,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;QAAEC,KAAK,EAAE;MAAK,CAAC,CAAC;MACzE5B,cAAc,CAACE,OAAO,GAAGsB,MAAM;;MAE/B;MACA,MAAMK,aAAa,GAAG,IAAIC,aAAa,CAACN,MAAM,CAAC;MAC/C1B,gBAAgB,CAACI,OAAO,GAAG2B,aAAa;;MAExC;MACAA,aAAa,CAACE,eAAe,GAAIC,KAAK,IAAK;QACzC,IAAIA,KAAK,CAACC,IAAI,CAACC,IAAI,GAAG,CAAC,EAAE;UACvBnC,cAAc,CAACG,OAAO,CAACiC,IAAI,CAACH,KAAK,CAACC,IAAI,CAAC;QACzC;MACF,CAAC;;MAED;MACAJ,aAAa,CAACO,MAAM,GAAG,MAAM;QAC3BC,4BAA4B,CAAC,CAAC;MAChC,CAAC;;MAED;MACAR,aAAa,CAACS,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;MAC3BrD,cAAc,CAAC,IAAI,CAAC;MAEpB+B,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;IAC7C,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D9B,eAAe,CAAC,+BAA+B8B,KAAK,CAACC,OAAO,EAAE,CAAC;IACjE;EACF,CAAC;;EAED;EACA,MAAMiB,sBAAsB,GAAG,MAAAA,CAAA,KAAY;IACzC,IAAI;MACFtC,YAAY,CAAC,CAAC;MACdV,eAAe,CAAC,EAAE,CAAC;MACnBE,cAAc,CAAC,KAAK,CAAC;;MAErB;MACA,MAAM+B,MAAM,GAAG,MAAMC,SAAS,CAACC,YAAY,CAACc,eAAe,CAAC;QAC1DC,KAAK,EAAE,IAAI;QACXb,KAAK,EAAE;MACT,CAAC,CAAC;;MAEF;MACA,MAAMc,WAAW,GAAGlB,MAAM,CAACmB,cAAc,CAAC,CAAC;MAC3C,IAAID,WAAW,CAACE,MAAM,KAAK,CAAC,EAAE;QAC5B,MAAM,IAAIjC,KAAK,CAAC,sFAAsF,CAAC;MACzG;MAEAX,cAAc,CAACE,OAAO,GAAGsB,MAAM;;MAE/B;MACA,MAAMK,aAAa,GAAG,IAAIC,aAAa,CAACN,MAAM,CAAC;MAC/C1B,gBAAgB,CAACI,OAAO,GAAG2B,aAAa;;MAExC;MACAA,aAAa,CAACE,eAAe,GAAIC,KAAK,IAAK;QACzC,IAAIA,KAAK,CAACC,IAAI,CAACC,IAAI,GAAG,CAAC,EAAE;UACvBnC,cAAc,CAACG,OAAO,CAACiC,IAAI,CAACH,KAAK,CAACC,IAAI,CAAC;QACzC;MACF,CAAC;;MAED;MACAJ,aAAa,CAACO,MAAM,GAAG,MAAM;QAC3BC,4BAA4B,CAAC,CAAC;MAChC,CAAC;;MAED;MACAb,MAAM,CAACqB,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,OAAO,GAAG,MAAM;QACzCC,aAAa,CAAC,CAAC;MACjB,CAAC;;MAED;MACAlB,aAAa,CAACS,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;MAC3BrD,cAAc,CAAC,IAAI,CAAC;MAEpB+B,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;IAC5C,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D9B,eAAe,CAAC,gCAAgC8B,KAAK,CAACC,OAAO,EAAE,CAAC;IAClE;EACF,CAAC;;EAED;EACA,MAAMyB,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIjD,gBAAgB,CAACI,OAAO,IAAIJ,gBAAgB,CAACI,OAAO,CAACC,KAAK,KAAK,UAAU,EAAE;MAC7EL,gBAAgB,CAACI,OAAO,CAACE,IAAI,CAAC,CAAC;IACjC;IACAnB,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC;;EAED;EACA,MAAMoD,4BAA4B,GAAG,MAAAA,CAAA,KAAY;IAC/C,IAAItC,cAAc,CAACG,OAAO,CAAC0C,MAAM,KAAK,CAAC,EAAE;MACvC5B,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MACvC;IACF;IAEA,IAAI;MACF9B,eAAe,CAAC,IAAI,CAAC;;MAErB;MACA,MAAM6D,SAAS,GAAG,IAAIC,IAAI,CAAClD,cAAc,CAACG,OAAO,EAAE;QAAEgD,IAAI,EAAE;MAAa,CAAC,CAAC;;MAE1E;MACA,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,aAAa,CAACL,SAAS,CAAC;MAE/BG,MAAM,CAACG,SAAS,GAAG,YAAY;QAC7B;QACA,MAAMC,WAAW,GAAGJ,MAAM,CAACK,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;QAE/C;QACA,MAAMhD,MAAM,GAAG9B,GAAG,CAAC+B,qBAAqB;QACxC,IAAI,CAACD,MAAM,EAAE;UACX,MAAM,IAAIE,KAAK,CAAC,qGAAqG,CAAC;QACxH;QACAK,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAER,MAAM,CAAC;;QAEnD;QACA,MAAMG,QAAQ,GAAG,MAAMC,KAAK,CAAC,gEAAgEJ,MAAM,EAAE,EAAE;UACrGK,MAAM,EAAE,MAAM;UACd4C,OAAO,EAAE;YACP,cAAc,EAAE;UAClB,CAAC;UACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YACnBC,MAAM,EAAE;cACNC,QAAQ,EAAE,WAAW;cACrBC,eAAe,EAAE,KAAK;cACtBC,YAAY,EAAE,OAAO;cACrBC,0BAA0B,EAAE,IAAI;cAChCC,wBAAwB,EAAE,IAAI;cAC9BC,uBAAuB,EAAE,CAAC;cAC1BC,KAAK,EAAE;YACT,CAAC;YACDzC,KAAK,EAAE;cACL0C,OAAO,EAAEf;YACX;UACF,CAAC;QACH,CAAC,CAAC;QAEF,IAAI,CAAC3C,QAAQ,CAACG,EAAE,EAAE;UAAA,IAAAwD,iBAAA;UAChB,MAAMpD,SAAS,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;UACvC,MAAM,IAAIT,KAAK,CAAC,qBAAqB,EAAA4D,iBAAA,GAAApD,SAAS,CAACE,KAAK,cAAAkD,iBAAA,uBAAfA,iBAAA,CAAiBjD,OAAO,KAAIV,QAAQ,CAAC4D,UAAU,EAAE,CAAC;QACzF;QAEA,MAAMvC,IAAI,GAAG,MAAMrB,QAAQ,CAACQ,IAAI,CAAC,CAAC;QAClCqD,4BAA4B,CAACxC,IAAI,CAAC;;QAElC;QACAlC,cAAc,CAACG,OAAO,GAAG,EAAE;QAC3Bf,eAAe,CAAC,KAAK,CAAC;MACxB,CAAC;MAEDgE,MAAM,CAACuB,OAAO,GAAIrD,KAAK,IAAK;QAC1B,MAAM,IAAIV,KAAK,CAAC,6BAA6BU,KAAK,EAAE,CAAC;MACvD,CAAC;IAEH,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C9B,eAAe,CAAC,2BAA2B8B,KAAK,CAACC,OAAO,EAAE,CAAC;MAC3DnC,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAMsF,4BAA4B,GAAIxC,IAAI,IAAK;IAC7C,IAAI,CAACA,IAAI,CAAC0C,OAAO,IAAI1C,IAAI,CAAC0C,OAAO,CAAC/B,MAAM,KAAK,CAAC,EAAE;MAC9C5B,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;MAChD;IACF;IAEA,IAAI;MACF;MACA,MAAMuC,MAAM,GAAGvB,IAAI,CAAC0C,OAAO,CAAC1C,IAAI,CAAC0C,OAAO,CAAC/B,MAAM,GAAG,CAAC,CAAC;MAEpD,IAAI,CAACY,MAAM,CAACoB,YAAY,IAAI,CAACpB,MAAM,CAACoB,YAAY,CAAC,CAAC,CAAC,EAAE;QACnD5D,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;QACxC;MACF;MAEA,MAAM4D,WAAW,GAAGrB,MAAM,CAACoB,YAAY,CAAC,CAAC,CAAC;MAC1C,MAAME,UAAU,GAAGD,WAAW,CAACC,UAAU;;MAEzC;MACA,IAAID,WAAW,CAACE,KAAK,IAAIF,WAAW,CAACE,KAAK,CAACnC,MAAM,GAAG,CAAC,EAAE;QACrD,IAAIoC,cAAc,GAAG,IAAI;QACzB,IAAIC,WAAW,GAAG,EAAE;QACpB,IAAIC,QAAQ,GAAG,EAAE;;QAEjB;QACAL,WAAW,CAACE,KAAK,CAACzE,OAAO,CAAC6E,IAAI,IAAI;UAChC,MAAMC,UAAU,GAAGD,IAAI,CAACC,UAAU,IAAI,CAAC;;UAEvC;UACA,IAAIA,UAAU,KAAKJ,cAAc,EAAE;YACjC;YACA,IAAIC,WAAW,EAAE;cACfC,QAAQ,CAAC/C,IAAI,CAAC;gBACZkD,OAAO,EAAEL,cAAc,KAAK,CAAC,GAAG,WAAW,GAAG,WAAW;gBACzDM,IAAI,EAAEL,WAAW,CAACM,IAAI,CAAC,CAAC;gBACxBC,SAAS,EAAE,IAAIC,IAAI,CAAC;cACtB,CAAC,CAAC;YACJ;;YAEA;YACAT,cAAc,GAAGI,UAAU;YAC3BH,WAAW,GAAGE,IAAI,CAACA,IAAI,GAAG,GAAG;UAC/B,CAAC,MAAM;YACL;YACAF,WAAW,IAAIE,IAAI,CAACA,IAAI,GAAG,GAAG;UAChC;QACF,CAAC,CAAC;;QAEF;QACA,IAAIF,WAAW,EAAE;UACfC,QAAQ,CAAC/C,IAAI,CAAC;YACZkD,OAAO,EAAEL,cAAc,KAAK,CAAC,GAAG,WAAW,GAAG,WAAW;YACzDM,IAAI,EAAEL,WAAW,CAACM,IAAI,CAAC,CAAC;YACxBC,SAAS,EAAE,IAAIC,IAAI,CAAC;UACtB,CAAC,CAAC;QACJ;;QAEA;QACApG,qBAAqB,CAACqG,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAGR,QAAQ,CAAC,CAAC;MACvD,CAAC,MAAM;QACL;QACA7F,qBAAqB,CAACqG,IAAI,IAAI,CAC5B,GAAGA,IAAI,EACP;UACEL,OAAO,EAAE7F,WAAW,KAAK,YAAY,GAAG,WAAW,GAAG,aAAa;UACnE8F,IAAI,EAAER,UAAU;UAChBU,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC,CACF,CAAC;MACJ;IACF,CAAC,CAAC,OAAOpE,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChE9B,eAAe,CAAC,mCAAmC8B,KAAK,CAACC,OAAO,EAAE,CAAC;IACrE;EACF,CAAC;;EAED;EACA,MAAMqE,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAIvG,kBAAkB,CAACwD,MAAM,KAAK,CAAC,EAAE;IAErC,IAAI0B,OAAO,GAAG,8BAA8B;IAC5ClF,kBAAkB,CAACkB,OAAO,CAACsF,OAAO,IAAI;MACpCtB,OAAO,IAAI,IAAIsB,OAAO,CAACJ,SAAS,CAACK,kBAAkB,CAAC,CAAC,KAAKD,OAAO,CAACP,OAAO,KAAKO,OAAO,CAACN,IAAI,IAAI;IAChG,CAAC,CAAC;IAEF,MAAMQ,IAAI,GAAG,IAAI7C,IAAI,CAAC,CAACqB,OAAO,CAAC,EAAE;MAAEpB,IAAI,EAAE;IAAa,CAAC,CAAC;IACxD,MAAM6C,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACH,IAAI,CAAC;IACrC,MAAMI,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACrCF,CAAC,CAACG,IAAI,GAAGN,GAAG;IACZG,CAAC,CAACI,QAAQ,GAAG,uBAAuB;IACpCH,QAAQ,CAACxC,IAAI,CAAC4C,WAAW,CAACL,CAAC,CAAC;IAC5BA,CAAC,CAACM,KAAK,CAAC,CAAC;IACTL,QAAQ,CAACxC,IAAI,CAAC8C,WAAW,CAACP,CAAC,CAAC;IAC5BF,GAAG,CAACU,eAAe,CAACX,GAAG,CAAC;EAC1B,CAAC;EAED,oBACElH,OAAA;IAAK8H,SAAS,EAAC,2BAA2B;IAAAC,QAAA,gBACxC/H,OAAA;MAAK8H,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1B/H,OAAA;QAAA+H,QAAA,EAAI;MAA0C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnDnI,OAAA;QAAG8H,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAE3B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAENnI,OAAA;MAAK8H,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBACjC/H,OAAA;QAAK8H,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjC/H,OAAA;UACE8H,SAAS,EAAE,kBAAkB3H,WAAW,IAAIQ,WAAW,KAAK,YAAY,GAAG,WAAW,GAAG,EAAE,EAAG;UAC9FyH,OAAO,EAAEjI,WAAW,GAAG+D,aAAa,GAAGxB,wBAAyB;UAChE2F,QAAQ,EAAEhI,YAAa;UAAA0H,QAAA,EAEtB5H,WAAW,IAAIQ,WAAW,KAAK,YAAY,GACxC,gBAAgB,GAChB;QAA4B;UAAAqH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eAETnI,OAAA;UACE8H,SAAS,EAAE,4BAA4B3H,WAAW,IAAIQ,WAAW,KAAK,KAAK,GAAG,WAAW,GAAG,EAAE,EAAG;UACjGyH,OAAO,EAAEjI,WAAW,GAAG+D,aAAa,GAAGR,sBAAuB;UAC9D2E,QAAQ,EAAEhI,YAAa;UAAA0H,QAAA,EAEtB5H,WAAW,IAAIQ,WAAW,KAAK,KAAK,GACjC,oBAAoB,GACpB;QAA2B;UAAAqH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAEL9H,YAAY,iBACXL,OAAA;QAAK8H,SAAS,EAAC,sBAAsB;QAAAC,QAAA,EAAC;MAEtC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CACN,EAEA1H,YAAY,iBACXT,OAAA;QAAK8H,SAAS,EAAC,eAAe;QAAAC,QAAA,EAC3BtH;MAAY;QAAAuH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAENnI,OAAA;MAAK8H,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBACnC/H,OAAA;QAAK8H,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC/H,OAAA;UAAA+H,QAAA,EAAI;QAAmC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAC3C5H,kBAAkB,CAACwD,MAAM,GAAG,CAAC,iBAC5B/D,OAAA;UACE8H,SAAS,EAAC,iBAAiB;UAC3BM,OAAO,EAAEtB,kBAAmB;UAAAiB,QAAA,EAC7B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENnI,OAAA;QAAK8H,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAChCxH,kBAAkB,CAACwD,MAAM,KAAK,CAAC,gBAC9B/D,OAAA;UAAK8H,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAE7B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,gBAENnI,OAAA;UAAK8H,SAAS,EAAC,qBAAqB;UAAAC,QAAA,EACjCxH,kBAAkB,CAAC+H,GAAG,CAAC,CAACvB,OAAO,EAAEwB,KAAK,kBACrCvI,OAAA;YAEE8H,SAAS,EAAE,sBAAsBf,OAAO,CAACP,OAAO,CAACgC,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,EAAG;YAAAV,QAAA,gBAEnF/H,OAAA;cAAK8H,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7B/H,OAAA;gBAAM8H,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAEhB,OAAO,CAACJ,SAAS,CAACK,kBAAkB,CAAC;cAAC;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3EnI,OAAA;gBAAM8H,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAEhB,OAAO,CAACP;cAAO;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC,eACNnI,OAAA;cAAK8H,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAEhB,OAAO,CAACN;YAAI;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,GAP7CI,KAAK;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAQP,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACjI,EAAA,CAhbQD,uBAAuB;AAAAyI,EAAA,GAAvBzI,uBAAuB;AAkbhC,eAAeA,uBAAuB;AAAC,IAAAyI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}