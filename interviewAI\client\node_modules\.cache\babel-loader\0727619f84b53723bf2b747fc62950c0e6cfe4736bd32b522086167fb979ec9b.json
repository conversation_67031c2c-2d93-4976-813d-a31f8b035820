{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\interview-ai-app\\\\interviewAI\\\\client\\\\src\\\\pages\\\\live-transcription\\\\LiveTranscription.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Controls({\n  isRecording,\n  onStart,\n  onStop\n}) {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      margin: '10px 0'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: onStart,\n      disabled: isRecording,\n      style: {\n        marginRight: 8\n      },\n      children: \"Start Recording\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: onStop,\n      disabled: !isRecording,\n      children: \"Stop Recording\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n}\n_c = Controls;\nfunction LiveCaptions({\n  interimText\n}) {\n  if (!interimText) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"live-captions\",\n    style: {\n      border: '1px solid #ccc',\n      padding: '10px',\n      margin: '10px 0',\n      minHeight: '30px',\n      background: '#f9f9f9'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n      children: \"Live:\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this), \" \", interimText]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 5\n  }, this);\n}\n_c2 = LiveCaptions;\nfunction Transcript({\n  transcriptData\n}) {\n  const downloadTranscript = () => {\n    let content = \"Conversation Transcript:\\n\\n\";\n    transcriptData.forEach(entry => {\n      content += `[${entry.timestamp.toLocaleTimeString()}] ${entry.speaker}: ${entry.text}\\n`;\n    });\n    const blob = new Blob([content], {\n      type: 'text/plain'\n    });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = 'transcript.txt';\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"transcript\",\n    style: {\n      border: '1px solid #eee',\n      padding: '10px',\n      marginTop: '20px',\n      background: '#fff'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Full Transcript\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this), transcriptData.length === 0 && /*#__PURE__*/_jsxDEV(\"p\", {\n      children: \"No speech detected yet.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 39\n    }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n      children: transcriptData.map((entry, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n        children: [/*#__PURE__*/_jsxDEV(\"small\", {\n          children: [\"[\", entry.timestamp.toLocaleTimeString(), \"]\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 13\n        }, this), \" \", /*#__PURE__*/_jsxDEV(\"strong\", {\n          children: [entry.speaker, \":\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 69\n        }, this), \" \", entry.text]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this), transcriptData.length > 0 && /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: downloadTranscript,\n      style: {\n        marginTop: '10px'\n      },\n      children: \"Download Transcript\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 43,\n    columnNumber: 5\n  }, this);\n}\n_c3 = Transcript;\nfunction LiveTranscriptionPage() {\n  _s();\n  const [isRecording, setIsRecording] = useState(false);\n  const [currentSpeaker, setCurrentSpeaker] = useState('Speaker 1');\n  const [interimTranscript, setInterimTranscript] = useState('');\n  const [fullTranscript, setFullTranscript] = useState([]);\n  const recognitionRef = useRef(null);\n  useEffect(() => {\n    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n    if (!SpeechRecognition) {\n      alert('Speech Recognition API not supported in this browser. Try Chrome or Edge.');\n      return;\n    }\n    const recognition = new SpeechRecognition();\n    recognition.continuous = true;\n    recognition.interimResults = true;\n    recognition.lang = 'en-US';\n    recognition.onstart = () => {\n      setIsRecording(true);\n    };\n    recognition.onresult = event => {\n      let interim = '';\n      let final = '';\n      for (let i = event.resultIndex; i < event.results.length; ++i) {\n        if (event.results[i].isFinal) {\n          final += event.results[i][0].transcript;\n        } else {\n          interim += event.results[i][0].transcript;\n        }\n      }\n      if (interim) setInterimTranscript(interim);\n      if (final) {\n        setFullTranscript(prev => [...prev, {\n          speaker: currentSpeaker,\n          text: final.trim(),\n          timestamp: new Date()\n        }]);\n        setInterimTranscript('');\n      }\n    };\n    recognition.onerror = event => {\n      if (event.error === 'no-speech') {\n        // No speech detected, just stop\n        setIsRecording(false);\n      } else if (event.error === 'audio-capture') {\n        alert('No microphone found. Ensure a microphone is installed and permissions are allowed.');\n        setIsRecording(false);\n      } else if (event.error === 'not-allowed') {\n        alert('Permission to use microphone was denied. Please allow microphone access in browser settings.');\n        setIsRecording(false);\n      }\n    };\n    recognition.onend = () => {\n      setIsRecording(false);\n    };\n    recognitionRef.current = recognition;\n    return () => {\n      if (recognitionRef.current) {\n        recognitionRef.current.stop();\n      }\n    };\n  }, [currentSpeaker]);\n  const startRecording = () => {\n    if (isRecording || !recognitionRef.current) return;\n    setInterimTranscript('');\n    recognitionRef.current.start();\n  };\n  const stopRecording = () => {\n    if (!isRecording || !recognitionRef.current) return;\n    recognitionRef.current.stop();\n    setIsRecording(false);\n    if (interimTranscript.trim()) {\n      setFullTranscript(prev => [...prev, {\n        speaker: currentSpeaker,\n        text: interimTranscript.trim(),\n        timestamp: new Date()\n      }]);\n      setInterimTranscript('');\n    }\n  };\n  const changeSpeaker = speakerName => {\n    setCurrentSpeaker(speakerName);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"live-transcription-page\",\n    style: {\n      maxWidth: 700,\n      margin: '0 auto',\n      padding: 24\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"Real-time Transcription & Captioning\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Controls, {\n      isRecording: isRecording,\n      onStart: startRecording,\n      onStop: stopRecording\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        margin: '10px 0'\n      },\n      children: [\"Current Speaker:\", /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => changeSpeaker('Speaker 1'),\n        disabled: currentSpeaker === 'Speaker 1',\n        style: {\n          marginLeft: 8\n        },\n        children: \"User 1\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => changeSpeaker('Speaker 2'),\n        disabled: currentSpeaker === 'Speaker 2',\n        style: {\n          marginLeft: 8\n        },\n        children: \"User 2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(LiveCaptions, {\n      interimText: interimTranscript\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 165,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Transcript, {\n      transcriptData: fullTranscript\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 153,\n    columnNumber: 5\n  }, this);\n}\n_s(LiveTranscriptionPage, \"sbWgdDDU3WPL9NDmbLuIIJRVDOY=\");\n_c4 = LiveTranscriptionPage;\nexport default LiveTranscriptionPage;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"Controls\");\n$RefreshReg$(_c2, \"LiveCaptions\");\n$RefreshReg$(_c3, \"Transcript\");\n$RefreshReg$(_c4, \"LiveTranscriptionPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "Controls", "isRecording", "onStart", "onStop", "style", "margin", "children", "onClick", "disabled", "marginRight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "LiveCaptions", "interimText", "className", "border", "padding", "minHeight", "background", "_c2", "Transcript", "transcriptData", "downloadTranscript", "content", "for<PERSON>ach", "entry", "timestamp", "toLocaleTimeString", "speaker", "text", "blob", "Blob", "type", "url", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "marginTop", "length", "map", "index", "_c3", "LiveTranscriptionPage", "_s", "setIsRecording", "currentSpeaker", "setCurrentSpeaker", "interimTranscript", "setInterimTranscript", "fullTranscript", "setFullTranscript", "recognitionRef", "SpeechRecognition", "window", "webkitSpeechRecognition", "alert", "recognition", "continuous", "interimResults", "lang", "onstart", "on<PERSON>ult", "event", "interim", "final", "i", "resultIndex", "results", "isFinal", "transcript", "prev", "trim", "Date", "onerror", "error", "onend", "current", "stop", "startRecording", "start", "stopRecording", "changeSpeaker", "<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "marginLeft", "_c4", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/interview-ai-app/interviewAI/client/src/pages/live-transcription/LiveTranscription.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\n\nfunction Controls({ isRecording, onStart, onStop }) {\n  return (\n    <div style={{ margin: '10px 0' }}>\n      <button onClick={onStart} disabled={isRecording} style={{ marginRight: 8 }}>\n        Start Recording\n      </button>\n      <button onClick={onStop} disabled={!isRecording}>\n        Stop Recording\n      </button>\n    </div>\n  );\n}\n\nfunction LiveCaptions({ interimText }) {\n  if (!interimText) return null;\n  return (\n    <div className=\"live-captions\" style={{ border: '1px solid #ccc', padding: '10px', margin: '10px 0', minHeight: '30px', background: '#f9f9f9' }}>\n      <strong>Live:</strong> {interimText}\n    </div>\n  );\n}\n\nfunction Transcript({ transcriptData }) {\n  const downloadTranscript = () => {\n    let content = \"Conversation Transcript:\\n\\n\";\n    transcriptData.forEach(entry => {\n      content += `[${entry.timestamp.toLocaleTimeString()}] ${entry.speaker}: ${entry.text}\\n`;\n    });\n    const blob = new Blob([content], { type: 'text/plain' });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = 'transcript.txt';\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n\n  return (\n    <div className=\"transcript\" style={{ border: '1px solid #eee', padding: '10px', marginTop: '20px', background: '#fff' }}>\n      <h2>Full Transcript</h2>\n      {transcriptData.length === 0 && <p>No speech detected yet.</p>}\n      <ul>\n        {transcriptData.map((entry, index) => (\n          <li key={index}>\n            <small>[{entry.timestamp.toLocaleTimeString()}]</small> <strong>{entry.speaker}:</strong> {entry.text}\n          </li>\n        ))}\n      </ul>\n      {transcriptData.length > 0 && (\n        <button onClick={downloadTranscript} style={{ marginTop: '10px' }}>\n          Download Transcript\n        </button>\n      )}\n    </div>\n  );\n}\n\nfunction LiveTranscriptionPage() {\n  const [isRecording, setIsRecording] = useState(false);\n  const [currentSpeaker, setCurrentSpeaker] = useState('Speaker 1');\n  const [interimTranscript, setInterimTranscript] = useState('');\n  const [fullTranscript, setFullTranscript] = useState([]);\n  const recognitionRef = useRef(null);\n\n  useEffect(() => {\n    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n    if (!SpeechRecognition) {\n      alert('Speech Recognition API not supported in this browser. Try Chrome or Edge.');\n      return;\n    }\n    const recognition = new SpeechRecognition();\n    recognition.continuous = true;\n    recognition.interimResults = true;\n    recognition.lang = 'en-US';\n\n    recognition.onstart = () => {\n      setIsRecording(true);\n    };\n\n    recognition.onresult = (event) => {\n      let interim = '';\n      let final = '';\n      for (let i = event.resultIndex; i < event.results.length; ++i) {\n        if (event.results[i].isFinal) {\n          final += event.results[i][0].transcript;\n        } else {\n          interim += event.results[i][0].transcript;\n        }\n      }\n      if (interim) setInterimTranscript(interim);\n      if (final) {\n        setFullTranscript(prev => [\n          ...prev,\n          { speaker: currentSpeaker, text: final.trim(), timestamp: new Date() }\n        ]);\n        setInterimTranscript('');\n      }\n    };\n\n    recognition.onerror = (event) => {\n      if (event.error === 'no-speech') {\n        // No speech detected, just stop\n        setIsRecording(false);\n      } else if (event.error === 'audio-capture') {\n        alert('No microphone found. Ensure a microphone is installed and permissions are allowed.');\n        setIsRecording(false);\n      } else if (event.error === 'not-allowed') {\n        alert('Permission to use microphone was denied. Please allow microphone access in browser settings.');\n        setIsRecording(false);\n      }\n    };\n\n    recognition.onend = () => {\n      setIsRecording(false);\n    };\n\n    recognitionRef.current = recognition;\n    return () => {\n      if (recognitionRef.current) {\n        recognitionRef.current.stop();\n      }\n    };\n  }, [currentSpeaker]);\n\n  const startRecording = () => {\n    if (isRecording || !recognitionRef.current) return;\n    setInterimTranscript('');\n    recognitionRef.current.start();\n  };\n\n  const stopRecording = () => {\n    if (!isRecording || !recognitionRef.current) return;\n    recognitionRef.current.stop();\n    setIsRecording(false);\n    if (interimTranscript.trim()) {\n      setFullTranscript(prev => [\n        ...prev,\n        { speaker: currentSpeaker, text: interimTranscript.trim(), timestamp: new Date() }\n      ]);\n      setInterimTranscript('');\n    }\n  };\n\n  const changeSpeaker = (speakerName) => {\n    setCurrentSpeaker(speakerName);\n  };\n\n  return (\n    <div className=\"live-transcription-page\" style={{ maxWidth: 700, margin: '0 auto', padding: 24 }}>\n      <h1>Real-time Transcription & Captioning</h1>\n      <Controls isRecording={isRecording} onStart={startRecording} onStop={stopRecording} />\n      <div style={{ margin: '10px 0' }}>\n        Current Speaker:\n        <button onClick={() => changeSpeaker('Speaker 1')} disabled={currentSpeaker === 'Speaker 1'} style={{ marginLeft: 8 }}>\n          User 1\n        </button>\n        <button onClick={() => changeSpeaker('Speaker 2')} disabled={currentSpeaker === 'Speaker 2'} style={{ marginLeft: 8 }}>\n          User 2\n        </button>\n      </div>\n      <LiveCaptions interimText={interimTranscript} />\n      <Transcript transcriptData={fullTranscript} />\n    </div>\n  );\n}\n\nexport default LiveTranscriptionPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,SAASC,QAAQA,CAAC;EAAEC,WAAW;EAAEC,OAAO;EAAEC;AAAO,CAAC,EAAE;EAClD,oBACEJ,OAAA;IAAKK,KAAK,EAAE;MAAEC,MAAM,EAAE;IAAS,CAAE;IAAAC,QAAA,gBAC/BP,OAAA;MAAQQ,OAAO,EAAEL,OAAQ;MAACM,QAAQ,EAAEP,WAAY;MAACG,KAAK,EAAE;QAAEK,WAAW,EAAE;MAAE,CAAE;MAAAH,QAAA,EAAC;IAE5E;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eACTd,OAAA;MAAQQ,OAAO,EAAEJ,MAAO;MAACK,QAAQ,EAAE,CAACP,WAAY;MAAAK,QAAA,EAAC;IAEjD;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV;AAACC,EAAA,GAXQd,QAAQ;AAajB,SAASe,YAAYA,CAAC;EAAEC;AAAY,CAAC,EAAE;EACrC,IAAI,CAACA,WAAW,EAAE,OAAO,IAAI;EAC7B,oBACEjB,OAAA;IAAKkB,SAAS,EAAC,eAAe;IAACb,KAAK,EAAE;MAAEc,MAAM,EAAE,gBAAgB;MAAEC,OAAO,EAAE,MAAM;MAAEd,MAAM,EAAE,QAAQ;MAAEe,SAAS,EAAE,MAAM;MAAEC,UAAU,EAAE;IAAU,CAAE;IAAAf,QAAA,gBAC9IP,OAAA;MAAAO,QAAA,EAAQ;IAAK;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,KAAC,EAACG,WAAW;EAAA;IAAAN,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAChC,CAAC;AAEV;AAACS,GAAA,GAPQP,YAAY;AASrB,SAASQ,UAAUA,CAAC;EAAEC;AAAe,CAAC,EAAE;EACtC,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAIC,OAAO,GAAG,8BAA8B;IAC5CF,cAAc,CAACG,OAAO,CAACC,KAAK,IAAI;MAC9BF,OAAO,IAAI,IAAIE,KAAK,CAACC,SAAS,CAACC,kBAAkB,CAAC,CAAC,KAAKF,KAAK,CAACG,OAAO,KAAKH,KAAK,CAACI,IAAI,IAAI;IAC1F,CAAC,CAAC;IACF,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACR,OAAO,CAAC,EAAE;MAAES,IAAI,EAAE;IAAa,CAAC,CAAC;IACxD,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;IACrC,MAAMM,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACrCF,CAAC,CAACG,IAAI,GAAGN,GAAG;IACZG,CAAC,CAACI,QAAQ,GAAG,gBAAgB;IAC7BH,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,CAAC,CAAC;IAC5BA,CAAC,CAACO,KAAK,CAAC,CAAC;IACTN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,CAAC,CAAC;IAC5BF,GAAG,CAACW,eAAe,CAACZ,GAAG,CAAC;EAC1B,CAAC;EAED,oBACErC,OAAA;IAAKkB,SAAS,EAAC,YAAY;IAACb,KAAK,EAAE;MAAEc,MAAM,EAAE,gBAAgB;MAAEC,OAAO,EAAE,MAAM;MAAE8B,SAAS,EAAE,MAAM;MAAE5B,UAAU,EAAE;IAAO,CAAE;IAAAf,QAAA,gBACtHP,OAAA;MAAAO,QAAA,EAAI;IAAe;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EACvBW,cAAc,CAAC0B,MAAM,KAAK,CAAC,iBAAInD,OAAA;MAAAO,QAAA,EAAG;IAAuB;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eAC9Dd,OAAA;MAAAO,QAAA,EACGkB,cAAc,CAAC2B,GAAG,CAAC,CAACvB,KAAK,EAAEwB,KAAK,kBAC/BrD,OAAA;QAAAO,QAAA,gBACEP,OAAA;UAAAO,QAAA,GAAO,GAAC,EAACsB,KAAK,CAACC,SAAS,CAACC,kBAAkB,CAAC,CAAC,EAAC,GAAC;QAAA;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,KAAC,eAAAd,OAAA;UAAAO,QAAA,GAASsB,KAAK,CAACG,OAAO,EAAC,GAAC;QAAA;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,EAACe,KAAK,CAACI,IAAI;MAAA,GAD9FoB,KAAK;QAAA1C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEV,CACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,EACJW,cAAc,CAAC0B,MAAM,GAAG,CAAC,iBACxBnD,OAAA;MAAQQ,OAAO,EAAEkB,kBAAmB;MAACrB,KAAK,EAAE;QAAE6C,SAAS,EAAE;MAAO,CAAE;MAAA3C,QAAA,EAAC;IAEnE;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CACT;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACwC,GAAA,GAnCQ9B,UAAU;AAqCnB,SAAS+B,qBAAqBA,CAAA,EAAG;EAAAC,EAAA;EAC/B,MAAM,CAACtD,WAAW,EAAEuD,cAAc,CAAC,GAAG7D,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC8D,cAAc,EAAEC,iBAAiB,CAAC,GAAG/D,QAAQ,CAAC,WAAW,CAAC;EACjE,MAAM,CAACgE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGjE,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACkE,cAAc,EAAEC,iBAAiB,CAAC,GAAGnE,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAMoE,cAAc,GAAGlE,MAAM,CAAC,IAAI,CAAC;EAEnCD,SAAS,CAAC,MAAM;IACd,MAAMoE,iBAAiB,GAAGC,MAAM,CAACD,iBAAiB,IAAIC,MAAM,CAACC,uBAAuB;IACpF,IAAI,CAACF,iBAAiB,EAAE;MACtBG,KAAK,CAAC,2EAA2E,CAAC;MAClF;IACF;IACA,MAAMC,WAAW,GAAG,IAAIJ,iBAAiB,CAAC,CAAC;IAC3CI,WAAW,CAACC,UAAU,GAAG,IAAI;IAC7BD,WAAW,CAACE,cAAc,GAAG,IAAI;IACjCF,WAAW,CAACG,IAAI,GAAG,OAAO;IAE1BH,WAAW,CAACI,OAAO,GAAG,MAAM;MAC1BhB,cAAc,CAAC,IAAI,CAAC;IACtB,CAAC;IAEDY,WAAW,CAACK,QAAQ,GAAIC,KAAK,IAAK;MAChC,IAAIC,OAAO,GAAG,EAAE;MAChB,IAAIC,KAAK,GAAG,EAAE;MACd,KAAK,IAAIC,CAAC,GAAGH,KAAK,CAACI,WAAW,EAAED,CAAC,GAAGH,KAAK,CAACK,OAAO,CAAC7B,MAAM,EAAE,EAAE2B,CAAC,EAAE;QAC7D,IAAIH,KAAK,CAACK,OAAO,CAACF,CAAC,CAAC,CAACG,OAAO,EAAE;UAC5BJ,KAAK,IAAIF,KAAK,CAACK,OAAO,CAACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAACI,UAAU;QACzC,CAAC,MAAM;UACLN,OAAO,IAAID,KAAK,CAACK,OAAO,CAACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAACI,UAAU;QAC3C;MACF;MACA,IAAIN,OAAO,EAAEf,oBAAoB,CAACe,OAAO,CAAC;MAC1C,IAAIC,KAAK,EAAE;QACTd,iBAAiB,CAACoB,IAAI,IAAI,CACxB,GAAGA,IAAI,EACP;UAAEnD,OAAO,EAAE0B,cAAc;UAAEzB,IAAI,EAAE4C,KAAK,CAACO,IAAI,CAAC,CAAC;UAAEtD,SAAS,EAAE,IAAIuD,IAAI,CAAC;QAAE,CAAC,CACvE,CAAC;QACFxB,oBAAoB,CAAC,EAAE,CAAC;MAC1B;IACF,CAAC;IAEDQ,WAAW,CAACiB,OAAO,GAAIX,KAAK,IAAK;MAC/B,IAAIA,KAAK,CAACY,KAAK,KAAK,WAAW,EAAE;QAC/B;QACA9B,cAAc,CAAC,KAAK,CAAC;MACvB,CAAC,MAAM,IAAIkB,KAAK,CAACY,KAAK,KAAK,eAAe,EAAE;QAC1CnB,KAAK,CAAC,oFAAoF,CAAC;QAC3FX,cAAc,CAAC,KAAK,CAAC;MACvB,CAAC,MAAM,IAAIkB,KAAK,CAACY,KAAK,KAAK,aAAa,EAAE;QACxCnB,KAAK,CAAC,8FAA8F,CAAC;QACrGX,cAAc,CAAC,KAAK,CAAC;MACvB;IACF,CAAC;IAEDY,WAAW,CAACmB,KAAK,GAAG,MAAM;MACxB/B,cAAc,CAAC,KAAK,CAAC;IACvB,CAAC;IAEDO,cAAc,CAACyB,OAAO,GAAGpB,WAAW;IACpC,OAAO,MAAM;MACX,IAAIL,cAAc,CAACyB,OAAO,EAAE;QAC1BzB,cAAc,CAACyB,OAAO,CAACC,IAAI,CAAC,CAAC;MAC/B;IACF,CAAC;EACH,CAAC,EAAE,CAAChC,cAAc,CAAC,CAAC;EAEpB,MAAMiC,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIzF,WAAW,IAAI,CAAC8D,cAAc,CAACyB,OAAO,EAAE;IAC5C5B,oBAAoB,CAAC,EAAE,CAAC;IACxBG,cAAc,CAACyB,OAAO,CAACG,KAAK,CAAC,CAAC;EAChC,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI,CAAC3F,WAAW,IAAI,CAAC8D,cAAc,CAACyB,OAAO,EAAE;IAC7CzB,cAAc,CAACyB,OAAO,CAACC,IAAI,CAAC,CAAC;IAC7BjC,cAAc,CAAC,KAAK,CAAC;IACrB,IAAIG,iBAAiB,CAACwB,IAAI,CAAC,CAAC,EAAE;MAC5BrB,iBAAiB,CAACoB,IAAI,IAAI,CACxB,GAAGA,IAAI,EACP;QAAEnD,OAAO,EAAE0B,cAAc;QAAEzB,IAAI,EAAE2B,iBAAiB,CAACwB,IAAI,CAAC,CAAC;QAAEtD,SAAS,EAAE,IAAIuD,IAAI,CAAC;MAAE,CAAC,CACnF,CAAC;MACFxB,oBAAoB,CAAC,EAAE,CAAC;IAC1B;EACF,CAAC;EAED,MAAMiC,aAAa,GAAIC,WAAW,IAAK;IACrCpC,iBAAiB,CAACoC,WAAW,CAAC;EAChC,CAAC;EAED,oBACE/F,OAAA;IAAKkB,SAAS,EAAC,yBAAyB;IAACb,KAAK,EAAE;MAAE2F,QAAQ,EAAE,GAAG;MAAE1F,MAAM,EAAE,QAAQ;MAAEc,OAAO,EAAE;IAAG,CAAE;IAAAb,QAAA,gBAC/FP,OAAA;MAAAO,QAAA,EAAI;IAAoC;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC7Cd,OAAA,CAACC,QAAQ;MAACC,WAAW,EAAEA,WAAY;MAACC,OAAO,EAAEwF,cAAe;MAACvF,MAAM,EAAEyF;IAAc;MAAAlF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACtFd,OAAA;MAAKK,KAAK,EAAE;QAAEC,MAAM,EAAE;MAAS,CAAE;MAAAC,QAAA,GAAC,kBAEhC,eAAAP,OAAA;QAAQQ,OAAO,EAAEA,CAAA,KAAMsF,aAAa,CAAC,WAAW,CAAE;QAACrF,QAAQ,EAAEiD,cAAc,KAAK,WAAY;QAACrD,KAAK,EAAE;UAAE4F,UAAU,EAAE;QAAE,CAAE;QAAA1F,QAAA,EAAC;MAEvH;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTd,OAAA;QAAQQ,OAAO,EAAEA,CAAA,KAAMsF,aAAa,CAAC,WAAW,CAAE;QAACrF,QAAQ,EAAEiD,cAAc,KAAK,WAAY;QAACrD,KAAK,EAAE;UAAE4F,UAAU,EAAE;QAAE,CAAE;QAAA1F,QAAA,EAAC;MAEvH;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eACNd,OAAA,CAACgB,YAAY;MAACC,WAAW,EAAE2C;IAAkB;MAAAjD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAChDd,OAAA,CAACwB,UAAU;MAACC,cAAc,EAAEqC;IAAe;MAAAnD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC3C,CAAC;AAEV;AAAC0C,EAAA,CA3GQD,qBAAqB;AAAA2C,GAAA,GAArB3C,qBAAqB;AA6G9B,eAAeA,qBAAqB;AAAC,IAAAxC,EAAA,EAAAQ,GAAA,EAAA+B,GAAA,EAAA4C,GAAA;AAAAC,YAAA,CAAApF,EAAA;AAAAoF,YAAA,CAAA5E,GAAA;AAAA4E,YAAA,CAAA7C,GAAA;AAAA6C,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}