{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\interview-ai-app\\\\interviewAI\\\\client\\\\src\\\\pages\\\\live-transcription\\\\GoogleSpeechDiarization.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport './GoogleSpeechDiarization.css';\nimport env from '../../utils/env';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction GoogleSpeechDiarization() {\n  _s();\n  // State for recording and transcription\n  const [isRecording, setIsRecording] = useState(false);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [transcriptSegments, setTranscriptSegments] = useState([]);\n  const [errorMessage, setErrorMessage] = useState('');\n  const [audioSource, setAudioSource] = useState('microphone'); // 'microphone' or 'tab'\n  const [apiKeyStatus, setApiKeyStatus] = useState('unknown'); // 'unknown', 'valid', 'invalid'\n  const [isTestingApiKey, setIsTestingApiKey] = useState(false);\n\n  // Refs for audio handling\n  const mediaRecorderRef = useRef(null);\n  const audioChunksRef = useRef([]);\n  const mediaStreamRef = useRef(null);\n\n  // Clean up function for audio resources\n  const cleanupAudio = () => {\n    if (mediaRecorderRef.current) {\n      if (mediaRecorderRef.current.state !== 'inactive') {\n        mediaRecorderRef.current.stop();\n      }\n      mediaRecorderRef.current = null;\n    }\n    if (mediaStreamRef.current) {\n      mediaStreamRef.current.getTracks().forEach(track => track.stop());\n      mediaStreamRef.current = null;\n    }\n    audioChunksRef.current = [];\n  };\n\n  // Clean up on component unmount\n  useEffect(() => {\n    return () => {\n      cleanupAudio();\n    };\n  }, []);\n\n  // Test the Google Speech API key on component mount\n  useEffect(() => {\n    testApiKey();\n  }, []);\n\n  // Function to test if the API key is valid\n  const testApiKey = async () => {\n    try {\n      setIsTestingApiKey(true);\n      setErrorMessage('');\n\n      // Get API key from environment variables\n      const apiKey = env.GOOGLE_SPEECH_API_KEY;\n      if (!apiKey) {\n        throw new Error(\"Google Speech API key not found. Please add it to your .env file as REACT_APP_GOOGLE_SPEECH_API_KEY\");\n      }\n\n      // Create a minimal audio sample for testing\n      // This is a very short, empty audio buffer encoded as base64\n      const testAudioContent = \"UklGRiQAAABXQVZFZm10IBAAAAABAAEARKwAAIhYAQACABAAZGF0YQAAAAA=\";\n\n      // Make a simple request to the Google Speech API with minimal audio data\n      const response = await fetch(`https://speech.googleapis.com/v1p1beta1/speech:recognize?key=${apiKey}`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          config: {\n            encoding: 'LINEAR16',\n            sampleRateHertz: 44100,\n            // Updated to match WAV header\n            languageCode: 'en-US'\n          },\n          audio: {\n            content: testAudioContent\n          }\n        })\n      });\n      const data = await response.json();\n\n      // Check if the response contains an error\n      if (data.error) {\n        // If the error is about invalid audio, that means the API key is valid\n        // but our test audio is too small (which is expected)\n        if (data.error.message && data.error.message.includes('audio')) {\n          console.log(\"Google Speech API key is valid (audio error is expected)\");\n          setApiKeyStatus('valid');\n        } else {\n          // Other errors likely mean the API key is invalid\n          console.error(\"Google Speech API key validation failed:\", data.error);\n          setApiKeyStatus('invalid');\n          setErrorMessage(`API Key Error: ${data.error.message || 'Invalid API key'}`);\n        }\n      } else {\n        // If no error, the key is definitely valid\n        console.log(\"Google Speech API key is valid\");\n        setApiKeyStatus('valid');\n      }\n    } catch (error) {\n      console.error(\"Error testing API key:\", error);\n\n      // Check for specific network errors\n      if (error.message === 'Failed to fetch') {\n        setErrorMessage(`Network error: Could not connect to Google API. This could be due to:\n          1. No internet connection\n          2. CORS restrictions in your browser\n          3. A firewall blocking the request\n\n          Try using the API key directly in your application instead.`);\n      } else {\n        setErrorMessage(`Error testing API key: ${error.message}`);\n      }\n      setApiKeyStatus('unknown'); // Set to unknown instead of invalid for network errors\n    } finally {\n      setIsTestingApiKey(false);\n    }\n  };\n\n  // Start recording from microphone\n  const startMicrophoneRecording = async () => {\n    try {\n      cleanupAudio();\n      setErrorMessage('');\n      setAudioSource('microphone');\n\n      // Get microphone stream\n      const stream = await navigator.mediaDevices.getUserMedia({\n        audio: true\n      });\n      mediaStreamRef.current = stream;\n\n      // Create media recorder\n      const mediaRecorder = new MediaRecorder(stream);\n      mediaRecorderRef.current = mediaRecorder;\n\n      // Set up data handling\n      mediaRecorder.ondataavailable = event => {\n        if (event.data.size > 0) {\n          audioChunksRef.current.push(event.data);\n        }\n      };\n\n      // Handle recording stop\n      mediaRecorder.onstop = () => {\n        processAudioForTranscription();\n      };\n\n      // Start recording\n      mediaRecorder.start(1000); // Collect data every second\n      setIsRecording(true);\n      console.log(\"Microphone recording started\");\n    } catch (error) {\n      console.error(\"Error starting microphone recording:\", error);\n      setErrorMessage(`Failed to start microphone: ${error.message}`);\n    }\n  };\n\n  // Start recording from tab audio\n  const startTabAudioRecording = async () => {\n    try {\n      cleanupAudio();\n      setErrorMessage('');\n      setAudioSource('tab');\n\n      // Request screen sharing with audio\n      const stream = await navigator.mediaDevices.getDisplayMedia({\n        video: true,\n        audio: true\n      });\n\n      // Check if audio is included\n      const audioTracks = stream.getAudioTracks();\n      if (audioTracks.length === 0) {\n        throw new Error(\"No audio track found. Please make sure to select 'Share audio' when sharing the tab.\");\n      }\n      mediaStreamRef.current = stream;\n\n      // Create media recorder\n      const mediaRecorder = new MediaRecorder(stream);\n      mediaRecorderRef.current = mediaRecorder;\n\n      // Set up data handling\n      mediaRecorder.ondataavailable = event => {\n        if (event.data.size > 0) {\n          audioChunksRef.current.push(event.data);\n        }\n      };\n\n      // Handle recording stop\n      mediaRecorder.onstop = () => {\n        processAudioForTranscription();\n      };\n\n      // Handle stream ending (user stops sharing)\n      stream.getVideoTracks()[0].onended = () => {\n        stopRecording();\n      };\n\n      // Start recording\n      mediaRecorder.start(1000); // Collect data every second\n      setIsRecording(true);\n      console.log(\"Tab audio recording started\");\n    } catch (error) {\n      console.error(\"Error starting tab audio recording:\", error);\n      setErrorMessage(`Failed to capture tab audio: ${error.message}`);\n    }\n  };\n\n  // Stop recording\n  const stopRecording = () => {\n    if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {\n      mediaRecorderRef.current.stop();\n    }\n    setIsRecording(false);\n  };\n\n  // Process recorded audio and send to Google Speech-to-Text API\n  const processAudioForTranscription = async () => {\n    if (audioChunksRef.current.length === 0) {\n      console.log(\"No audio data to process\");\n      return;\n    }\n    try {\n      setIsProcessing(true);\n\n      // Create audio blob from chunks\n      const audioBlob = new Blob(audioChunksRef.current, {\n        type: 'audio/webm'\n      });\n      console.log(\"Audio blob created:\", audioBlob.size, \"bytes\");\n\n      // Log audio details for debugging\n      const audioUrl = URL.createObjectURL(audioBlob);\n      console.log(\"Audio URL for debugging:\", audioUrl);\n\n      // Convert blob to base64\n      const reader = new FileReader();\n      reader.readAsDataURL(audioBlob);\n      reader.onloadend = async () => {\n        // Get base64 data (remove the data URL prefix)\n        const base64Audio = reader.result.split(',')[1];\n        console.log(\"Base64 audio length:\", base64Audio.length);\n\n        // Get API key from environment variables\n        const apiKey = env.GOOGLE_SPEECH_API_KEY;\n        if (!apiKey) {\n          throw new Error(\"Google Speech API key not found. Please add it to your .env file as REACT_APP_GOOGLE_SPEECH_API_KEY\");\n        }\n        console.log(\"Using Google Speech API key:\", apiKey);\n\n        // Prepare request to Google Speech-to-Text API\n        console.log(\"Sending request to Google Speech API...\");\n        const response = await fetch(`https://speech.googleapis.com/v1p1beta1/speech:recognize?key=${apiKey}`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            config: {\n              encoding: 'WEBM_OPUS',\n              // Don't specify sampleRateHertz to let Google detect it automatically\n              languageCode: 'en-US',\n              enableAutomaticPunctuation: true,\n              enableSpeakerDiarization: true,\n              diarizationSpeakerCount: 2,\n              model: 'default',\n              // Add additional settings for better results\n              useEnhanced: true,\n              metadata: {\n                interactionType: 'DISCUSSION',\n                microphoneDistance: 'NEARFIELD',\n                recordingDeviceType: audioSource === 'microphone' ? 'PC_MIC' : 'OTHER'\n              }\n            },\n            audio: {\n              content: base64Audio\n            }\n          })\n        });\n        console.log(\"Response status:\", response.status);\n        if (!response.ok) {\n          var _errorData$error;\n          const errorData = await response.json();\n          console.error(\"API Error details:\", errorData);\n          throw new Error(`Google API error: ${((_errorData$error = errorData.error) === null || _errorData$error === void 0 ? void 0 : _errorData$error.message) || response.statusText}`);\n        }\n        const data = await response.json();\n        console.log(\"Received transcription data:\", data);\n\n        // Add a default transcript if no speaker diarization is available\n        if (!data.results || data.results.length === 0) {\n          setTranscriptSegments(prev => [...prev, {\n            speaker: audioSource === 'microphone' ? 'Candidate' : 'Interviewer',\n            text: \"No speech detected. Please try speaking louder or check your microphone.\",\n            timestamp: new Date()\n          }]);\n        } else {\n          processTranscriptionResponse(data);\n        }\n\n        // Clear audio chunks for next recording\n        audioChunksRef.current = [];\n        setIsProcessing(false);\n      };\n      reader.onerror = error => {\n        console.error(\"FileReader error:\", error);\n        throw new Error(`Error reading audio data: ${error}`);\n      };\n    } catch (error) {\n      console.error(\"Error processing audio:\", error);\n      setErrorMessage(`Error processing audio: ${error.message}`);\n\n      // Add error to transcript for user visibility\n      setTranscriptSegments(prev => [...prev, {\n        speaker: 'System',\n        text: `Error: ${error.message}. Please try again.`,\n        timestamp: new Date()\n      }]);\n      setIsProcessing(false);\n    }\n  };\n\n  // Process the response from Google Speech-to-Text API\n  const processTranscriptionResponse = data => {\n    if (!data.results || data.results.length === 0) {\n      console.log(\"No transcription results returned\");\n      return;\n    }\n    try {\n      console.log(\"Processing transcription results:\", data.results);\n\n      // First, try to get a complete transcript\n      let fullTranscript = '';\n      data.results.forEach(result => {\n        if (result.alternatives && result.alternatives[0]) {\n          fullTranscript += result.alternatives[0].transcript + ' ';\n        }\n      });\n      fullTranscript = fullTranscript.trim();\n      console.log(\"Full transcript:\", fullTranscript);\n\n      // Check if we have any words with speaker tags\n      let hasWordLevelDiarization = false;\n      let diarizedWords = [];\n\n      // Look through all results for word-level speaker diarization\n      data.results.forEach(result => {\n        if (result.alternatives && result.alternatives[0] && result.alternatives[0].words) {\n          const words = result.alternatives[0].words;\n          if (words.length > 0 && words[0].speakerTag) {\n            hasWordLevelDiarization = true;\n            diarizedWords = diarizedWords.concat(words);\n          }\n        }\n      });\n      if (hasWordLevelDiarization && diarizedWords.length > 0) {\n        console.log(\"Found word-level diarization with\", diarizedWords.length, \"words\");\n\n        // Group words by speaker\n        let currentSpeaker = null;\n        let currentText = '';\n        let segments = [];\n        diarizedWords.forEach(word => {\n          const speakerTag = word.speakerTag || 0;\n\n          // If speaker changed or this is the first word\n          if (speakerTag !== currentSpeaker) {\n            // Save previous segment if it exists\n            if (currentText) {\n              // Map speaker tags to meaningful names\n              let speakerName;\n              if (currentSpeaker === 1) {\n                speakerName = audioSource === 'tab' ? 'Interviewer' : 'Speaker 1';\n              } else if (currentSpeaker === 2) {\n                speakerName = audioSource === 'microphone' ? 'Candidate' : 'Speaker 2';\n              } else {\n                speakerName = `Speaker ${currentSpeaker}`;\n              }\n              segments.push({\n                speaker: speakerName,\n                text: currentText.trim(),\n                timestamp: new Date()\n              });\n            }\n\n            // Start new segment\n            currentSpeaker = speakerTag;\n            currentText = word.word + ' ';\n          } else {\n            // Continue current segment\n            currentText += word.word + ' ';\n          }\n        });\n\n        // Add the last segment\n        if (currentText) {\n          // Map speaker tags to meaningful names\n          let speakerName;\n          if (currentSpeaker === 1) {\n            speakerName = audioSource === 'tab' ? 'Interviewer' : 'Speaker 1';\n          } else if (currentSpeaker === 2) {\n            speakerName = audioSource === 'microphone' ? 'Candidate' : 'Speaker 2';\n          } else {\n            speakerName = `Speaker ${currentSpeaker}`;\n          }\n          segments.push({\n            speaker: speakerName,\n            text: currentText.trim(),\n            timestamp: new Date()\n          });\n        }\n        console.log(\"Created segments:\", segments);\n\n        // Update transcript segments\n        setTranscriptSegments(prev => [...prev, ...segments]);\n      } else {\n        // No speaker diarization, add as single segment based on audio source\n        console.log(\"No word-level diarization found, using audio source for speaker identification\");\n        if (fullTranscript) {\n          setTranscriptSegments(prev => [...prev, {\n            speaker: audioSource === 'microphone' ? 'Candidate' : 'Interviewer',\n            text: fullTranscript,\n            timestamp: new Date()\n          }]);\n        } else {\n          console.log(\"No transcript text found in the response\");\n        }\n      }\n    } catch (error) {\n      console.error(\"Error processing transcription response:\", error);\n      setErrorMessage(`Error processing transcription: ${error.message}`);\n\n      // Add error to transcript for user visibility\n      setTranscriptSegments(prev => [...prev, {\n        speaker: 'System',\n        text: `Error processing speech: ${error.message}`,\n        timestamp: new Date()\n      }]);\n    }\n  };\n\n  // Download transcript as text file\n  const downloadTranscript = () => {\n    if (transcriptSegments.length === 0) return;\n    let content = \"Conversation Transcript:\\n\\n\";\n    transcriptSegments.forEach(segment => {\n      content += `[${segment.timestamp.toLocaleTimeString()}] ${segment.speaker}: ${segment.text}\\n`;\n    });\n    const blob = new Blob([content], {\n      type: 'text/plain'\n    });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = 'google_transcript.txt';\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"google-speech-diarization\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Google Speech API with Speaker Diarization\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 496,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"description\",\n        children: \"This page uses Google's Speech-to-Text API to transcribe audio with speaker diarization.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 497,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 495,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"api-status\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: `api-indicator ${apiKeyStatus}`,\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"status-dot\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 504,\n          columnNumber: 11\n        }, this), isTestingApiKey ? 'Testing API key...' : apiKeyStatus === 'valid' ? 'API Key: Valid' : apiKeyStatus === 'invalid' ? 'API Key: Invalid' : 'API Key: Not verified']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 503,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"api-buttons\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"test-api-button\",\n          onClick: testApiKey,\n          disabled: isTestingApiKey,\n          children: \"Test API Key\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 511,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: `https://console.cloud.google.com/apis/credentials`,\n          target: \"_blank\",\n          rel: \"noopener noreferrer\",\n          className: \"verify-link\",\n          children: \"Verify in Google Console\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 518,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 510,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 502,\n      columnNumber: 7\n    }, this), apiKeyStatus === 'unknown' && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"api-instructions\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"How to Verify Your API Key Manually:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 531,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"ol\", {\n        children: [/*#__PURE__*/_jsxDEV(\"li\", {\n          children: [\"Go to the \", /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"https://console.cloud.google.com/apis/credentials\",\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            children: \"Google Cloud Console\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 533,\n            columnNumber: 27\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 533,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"Select your project\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 534,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"Go to \\\"APIs & Services\\\" \\u2192 \\\"Credentials\\\"\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 535,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"Check that your API key exists and is not restricted in a way that prevents browser usage\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 536,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"Make sure the Speech-to-Text API is enabled for your project\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 537,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 532,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Your current API key is: \", /*#__PURE__*/_jsxDEV(\"code\", {\n          children: env.GOOGLE_SPEECH_API_KEY\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 539,\n          columnNumber: 39\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 539,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 530,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"controls-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"recording-controls\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: `control-button ${isRecording && audioSource === 'microphone' ? 'recording' : ''}`,\n          onClick: isRecording ? stopRecording : startMicrophoneRecording,\n          disabled: isProcessing || isTestingApiKey,\n          children: isRecording && audioSource === 'microphone' ? 'Stop Recording' : 'Start Microphone Recording'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 545,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `control-button tab-audio ${isRecording && audioSource === 'tab' ? 'recording' : ''}`,\n          onClick: isRecording ? stopRecording : startTabAudioRecording,\n          disabled: isProcessing || isTestingApiKey,\n          children: isRecording && audioSource === 'tab' ? 'Stop Tab Recording' : 'Start Tab Audio Recording'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 555,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 544,\n        columnNumber: 9\n      }, this), isProcessing && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"processing-indicator\",\n        children: \"Processing audio... This may take a moment.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 567,\n        columnNumber: 11\n      }, this), errorMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-message\",\n        children: errorMessage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 573,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 543,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"transcript-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"transcript-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Transcript with Speaker Diarization\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 581,\n          columnNumber: 11\n        }, this), transcriptSegments.length > 0 && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"download-button\",\n          onClick: downloadTranscript,\n          children: \"Download Transcript\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 583,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 580,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"transcript-content\",\n        children: transcriptSegments.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"empty-state\",\n          children: \"No transcription yet. Start recording to see results.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 594,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"transcript-segments\",\n          children: transcriptSegments.map((segment, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `transcript-segment ${segment.speaker.toLowerCase().replace(' ', '-')}`,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"segment-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"timestamp\",\n                children: segment.timestamp.toLocaleTimeString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 605,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"speaker-label\",\n                children: segment.speaker\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 606,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 604,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"segment-text\",\n              children: segment.text\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 608,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 600,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 598,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 592,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 579,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 494,\n    columnNumber: 5\n  }, this);\n}\n_s(GoogleSpeechDiarization, \"PuHNEyT1NEel8gEMHGM3OTsp48A=\");\n_c = GoogleSpeechDiarization;\nexport default GoogleSpeechDiarization;\nvar _c;\n$RefreshReg$(_c, \"GoogleSpeechDiarization\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "env", "jsxDEV", "_jsxDEV", "GoogleSpeechDiarization", "_s", "isRecording", "setIsRecording", "isProcessing", "setIsProcessing", "transcriptSegments", "setTranscriptSegments", "errorMessage", "setErrorMessage", "audioSource", "setAudioSource", "apiKeyStatus", "setApiKeyStatus", "isTestingApiKey", "setIsTestingApiKey", "mediaRecorderRef", "audioChunksRef", "mediaStreamRef", "cleanupAudio", "current", "state", "stop", "getTracks", "for<PERSON>ach", "track", "test<PERSON>pi<PERSON>ey", "<PERSON><PERSON><PERSON><PERSON>", "GOOGLE_SPEECH_API_KEY", "Error", "test<PERSON><PERSON>o<PERSON><PERSON><PERSON>", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "config", "encoding", "sampleRateHertz", "languageCode", "audio", "content", "data", "json", "error", "message", "includes", "console", "log", "startMicrophoneRecording", "stream", "navigator", "mediaDevices", "getUserMedia", "mediaRecorder", "MediaRecorder", "ondataavailable", "event", "size", "push", "onstop", "processAudioForTranscription", "start", "startTabAudioRecording", "getDisplayMedia", "video", "audioTracks", "getAudioTracks", "length", "getVideoTracks", "onended", "stopRecording", "audioBlob", "Blob", "type", "audioUrl", "URL", "createObjectURL", "reader", "FileReader", "readAsDataURL", "onloadend", "base64Audio", "result", "split", "enableAutomaticPunctuation", "enableSpeakerDiarization", "diarizationSpeakerCount", "model", "useEnhanced", "metadata", "interactionType", "microphoneDistance", "recordingDeviceType", "status", "ok", "_errorData$error", "errorData", "statusText", "results", "prev", "speaker", "text", "timestamp", "Date", "processTranscriptionResponse", "onerror", "fullTranscript", "alternatives", "transcript", "trim", "hasWordLevelDiarization", "diarizedWords", "words", "speakerTag", "concat", "currentSpeaker", "currentText", "segments", "word", "<PERSON><PERSON><PERSON>", "downloadTranscript", "segment", "toLocaleTimeString", "blob", "url", "a", "document", "createElement", "href", "download", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "target", "rel", "map", "index", "toLowerCase", "replace", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/interview-ai-app/interviewAI/client/src/pages/live-transcription/GoogleSpeechDiarization.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport './GoogleSpeechDiarization.css';\nimport env from '../../utils/env';\n\nfunction GoogleSpeechDiarization() {\n  // State for recording and transcription\n  const [isRecording, setIsRecording] = useState(false);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [transcriptSegments, setTranscriptSegments] = useState([]);\n  const [errorMessage, setErrorMessage] = useState('');\n  const [audioSource, setAudioSource] = useState('microphone'); // 'microphone' or 'tab'\n  const [apiKeyStatus, setApiKeyStatus] = useState('unknown'); // 'unknown', 'valid', 'invalid'\n  const [isTestingApiKey, setIsTestingApiKey] = useState(false);\n\n  // Refs for audio handling\n  const mediaRecorderRef = useRef(null);\n  const audioChunksRef = useRef([]);\n  const mediaStreamRef = useRef(null);\n\n  // Clean up function for audio resources\n  const cleanupAudio = () => {\n    if (mediaRecorderRef.current) {\n      if (mediaRecorderRef.current.state !== 'inactive') {\n        mediaRecorderRef.current.stop();\n      }\n      mediaRecorderRef.current = null;\n    }\n\n    if (mediaStreamRef.current) {\n      mediaStreamRef.current.getTracks().forEach(track => track.stop());\n      mediaStreamRef.current = null;\n    }\n\n    audioChunksRef.current = [];\n  };\n\n  // Clean up on component unmount\n  useEffect(() => {\n    return () => {\n      cleanupAudio();\n    };\n  }, []);\n\n  // Test the Google Speech API key on component mount\n  useEffect(() => {\n    testApiKey();\n  }, []);\n\n  // Function to test if the API key is valid\n  const testApiKey = async () => {\n    try {\n      setIsTestingApiKey(true);\n      setErrorMessage('');\n\n      // Get API key from environment variables\n      const apiKey = env.GOOGLE_SPEECH_API_KEY;\n      if (!apiKey) {\n        throw new Error(\"Google Speech API key not found. Please add it to your .env file as REACT_APP_GOOGLE_SPEECH_API_KEY\");\n      }\n\n      // Create a minimal audio sample for testing\n      // This is a very short, empty audio buffer encoded as base64\n      const testAudioContent = \"UklGRiQAAABXQVZFZm10IBAAAAABAAEARKwAAIhYAQACABAAZGF0YQAAAAA=\";\n\n      // Make a simple request to the Google Speech API with minimal audio data\n      const response = await fetch(`https://speech.googleapis.com/v1p1beta1/speech:recognize?key=${apiKey}`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          config: {\n            encoding: 'LINEAR16',\n            sampleRateHertz: 44100, // Updated to match WAV header\n            languageCode: 'en-US',\n          },\n          audio: {\n            content: testAudioContent\n          }\n        })\n      });\n\n      const data = await response.json();\n\n      // Check if the response contains an error\n      if (data.error) {\n        // If the error is about invalid audio, that means the API key is valid\n        // but our test audio is too small (which is expected)\n        if (data.error.message && data.error.message.includes('audio')) {\n          console.log(\"Google Speech API key is valid (audio error is expected)\");\n          setApiKeyStatus('valid');\n        } else {\n          // Other errors likely mean the API key is invalid\n          console.error(\"Google Speech API key validation failed:\", data.error);\n          setApiKeyStatus('invalid');\n          setErrorMessage(`API Key Error: ${data.error.message || 'Invalid API key'}`);\n        }\n      } else {\n        // If no error, the key is definitely valid\n        console.log(\"Google Speech API key is valid\");\n        setApiKeyStatus('valid');\n      }\n    } catch (error) {\n      console.error(\"Error testing API key:\", error);\n\n      // Check for specific network errors\n      if (error.message === 'Failed to fetch') {\n        setErrorMessage(`Network error: Could not connect to Google API. This could be due to:\n          1. No internet connection\n          2. CORS restrictions in your browser\n          3. A firewall blocking the request\n\n          Try using the API key directly in your application instead.`);\n      } else {\n        setErrorMessage(`Error testing API key: ${error.message}`);\n      }\n\n      setApiKeyStatus('unknown'); // Set to unknown instead of invalid for network errors\n    } finally {\n      setIsTestingApiKey(false);\n    }\n  };\n\n  // Start recording from microphone\n  const startMicrophoneRecording = async () => {\n    try {\n      cleanupAudio();\n      setErrorMessage('');\n      setAudioSource('microphone');\n\n      // Get microphone stream\n      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });\n      mediaStreamRef.current = stream;\n\n      // Create media recorder\n      const mediaRecorder = new MediaRecorder(stream);\n      mediaRecorderRef.current = mediaRecorder;\n\n      // Set up data handling\n      mediaRecorder.ondataavailable = (event) => {\n        if (event.data.size > 0) {\n          audioChunksRef.current.push(event.data);\n        }\n      };\n\n      // Handle recording stop\n      mediaRecorder.onstop = () => {\n        processAudioForTranscription();\n      };\n\n      // Start recording\n      mediaRecorder.start(1000); // Collect data every second\n      setIsRecording(true);\n\n      console.log(\"Microphone recording started\");\n    } catch (error) {\n      console.error(\"Error starting microphone recording:\", error);\n      setErrorMessage(`Failed to start microphone: ${error.message}`);\n    }\n  };\n\n  // Start recording from tab audio\n  const startTabAudioRecording = async () => {\n    try {\n      cleanupAudio();\n      setErrorMessage('');\n      setAudioSource('tab');\n\n      // Request screen sharing with audio\n      const stream = await navigator.mediaDevices.getDisplayMedia({\n        video: true,\n        audio: true\n      });\n\n      // Check if audio is included\n      const audioTracks = stream.getAudioTracks();\n      if (audioTracks.length === 0) {\n        throw new Error(\"No audio track found. Please make sure to select 'Share audio' when sharing the tab.\");\n      }\n\n      mediaStreamRef.current = stream;\n\n      // Create media recorder\n      const mediaRecorder = new MediaRecorder(stream);\n      mediaRecorderRef.current = mediaRecorder;\n\n      // Set up data handling\n      mediaRecorder.ondataavailable = (event) => {\n        if (event.data.size > 0) {\n          audioChunksRef.current.push(event.data);\n        }\n      };\n\n      // Handle recording stop\n      mediaRecorder.onstop = () => {\n        processAudioForTranscription();\n      };\n\n      // Handle stream ending (user stops sharing)\n      stream.getVideoTracks()[0].onended = () => {\n        stopRecording();\n      };\n\n      // Start recording\n      mediaRecorder.start(1000); // Collect data every second\n      setIsRecording(true);\n\n      console.log(\"Tab audio recording started\");\n    } catch (error) {\n      console.error(\"Error starting tab audio recording:\", error);\n      setErrorMessage(`Failed to capture tab audio: ${error.message}`);\n    }\n  };\n\n  // Stop recording\n  const stopRecording = () => {\n    if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {\n      mediaRecorderRef.current.stop();\n    }\n    setIsRecording(false);\n  };\n\n  // Process recorded audio and send to Google Speech-to-Text API\n  const processAudioForTranscription = async () => {\n    if (audioChunksRef.current.length === 0) {\n      console.log(\"No audio data to process\");\n      return;\n    }\n\n    try {\n      setIsProcessing(true);\n\n      // Create audio blob from chunks\n      const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });\n      console.log(\"Audio blob created:\", audioBlob.size, \"bytes\");\n\n      // Log audio details for debugging\n      const audioUrl = URL.createObjectURL(audioBlob);\n      console.log(\"Audio URL for debugging:\", audioUrl);\n\n      // Convert blob to base64\n      const reader = new FileReader();\n      reader.readAsDataURL(audioBlob);\n\n      reader.onloadend = async () => {\n        // Get base64 data (remove the data URL prefix)\n        const base64Audio = reader.result.split(',')[1];\n        console.log(\"Base64 audio length:\", base64Audio.length);\n\n        // Get API key from environment variables\n        const apiKey = env.GOOGLE_SPEECH_API_KEY;\n        if (!apiKey) {\n          throw new Error(\"Google Speech API key not found. Please add it to your .env file as REACT_APP_GOOGLE_SPEECH_API_KEY\");\n        }\n        console.log(\"Using Google Speech API key:\", apiKey);\n\n        // Prepare request to Google Speech-to-Text API\n        console.log(\"Sending request to Google Speech API...\");\n        const response = await fetch(`https://speech.googleapis.com/v1p1beta1/speech:recognize?key=${apiKey}`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            config: {\n              encoding: 'WEBM_OPUS',\n              // Don't specify sampleRateHertz to let Google detect it automatically\n              languageCode: 'en-US',\n              enableAutomaticPunctuation: true,\n              enableSpeakerDiarization: true,\n              diarizationSpeakerCount: 2,\n              model: 'default',\n              // Add additional settings for better results\n              useEnhanced: true,\n              metadata: {\n                interactionType: 'DISCUSSION',\n                microphoneDistance: 'NEARFIELD',\n                recordingDeviceType: audioSource === 'microphone' ? 'PC_MIC' : 'OTHER'\n              }\n            },\n            audio: {\n              content: base64Audio\n            }\n          })\n        });\n\n        console.log(\"Response status:\", response.status);\n\n        if (!response.ok) {\n          const errorData = await response.json();\n          console.error(\"API Error details:\", errorData);\n          throw new Error(`Google API error: ${errorData.error?.message || response.statusText}`);\n        }\n\n        const data = await response.json();\n        console.log(\"Received transcription data:\", data);\n\n        // Add a default transcript if no speaker diarization is available\n        if (!data.results || data.results.length === 0) {\n          setTranscriptSegments(prev => [\n            ...prev,\n            {\n              speaker: audioSource === 'microphone' ? 'Candidate' : 'Interviewer',\n              text: \"No speech detected. Please try speaking louder or check your microphone.\",\n              timestamp: new Date()\n            }\n          ]);\n        } else {\n          processTranscriptionResponse(data);\n        }\n\n        // Clear audio chunks for next recording\n        audioChunksRef.current = [];\n        setIsProcessing(false);\n      };\n\n      reader.onerror = (error) => {\n        console.error(\"FileReader error:\", error);\n        throw new Error(`Error reading audio data: ${error}`);\n      };\n\n    } catch (error) {\n      console.error(\"Error processing audio:\", error);\n      setErrorMessage(`Error processing audio: ${error.message}`);\n\n      // Add error to transcript for user visibility\n      setTranscriptSegments(prev => [\n        ...prev,\n        {\n          speaker: 'System',\n          text: `Error: ${error.message}. Please try again.`,\n          timestamp: new Date()\n        }\n      ]);\n\n      setIsProcessing(false);\n    }\n  };\n\n  // Process the response from Google Speech-to-Text API\n  const processTranscriptionResponse = (data) => {\n    if (!data.results || data.results.length === 0) {\n      console.log(\"No transcription results returned\");\n      return;\n    }\n\n    try {\n      console.log(\"Processing transcription results:\", data.results);\n\n      // First, try to get a complete transcript\n      let fullTranscript = '';\n      data.results.forEach(result => {\n        if (result.alternatives && result.alternatives[0]) {\n          fullTranscript += result.alternatives[0].transcript + ' ';\n        }\n      });\n\n      fullTranscript = fullTranscript.trim();\n      console.log(\"Full transcript:\", fullTranscript);\n\n      // Check if we have any words with speaker tags\n      let hasWordLevelDiarization = false;\n      let diarizedWords = [];\n\n      // Look through all results for word-level speaker diarization\n      data.results.forEach(result => {\n        if (result.alternatives && result.alternatives[0] && result.alternatives[0].words) {\n          const words = result.alternatives[0].words;\n          if (words.length > 0 && words[0].speakerTag) {\n            hasWordLevelDiarization = true;\n            diarizedWords = diarizedWords.concat(words);\n          }\n        }\n      });\n\n      if (hasWordLevelDiarization && diarizedWords.length > 0) {\n        console.log(\"Found word-level diarization with\", diarizedWords.length, \"words\");\n\n        // Group words by speaker\n        let currentSpeaker = null;\n        let currentText = '';\n        let segments = [];\n\n        diarizedWords.forEach(word => {\n          const speakerTag = word.speakerTag || 0;\n\n          // If speaker changed or this is the first word\n          if (speakerTag !== currentSpeaker) {\n            // Save previous segment if it exists\n            if (currentText) {\n              // Map speaker tags to meaningful names\n              let speakerName;\n              if (currentSpeaker === 1) {\n                speakerName = audioSource === 'tab' ? 'Interviewer' : 'Speaker 1';\n              } else if (currentSpeaker === 2) {\n                speakerName = audioSource === 'microphone' ? 'Candidate' : 'Speaker 2';\n              } else {\n                speakerName = `Speaker ${currentSpeaker}`;\n              }\n\n              segments.push({\n                speaker: speakerName,\n                text: currentText.trim(),\n                timestamp: new Date()\n              });\n            }\n\n            // Start new segment\n            currentSpeaker = speakerTag;\n            currentText = word.word + ' ';\n          } else {\n            // Continue current segment\n            currentText += word.word + ' ';\n          }\n        });\n\n        // Add the last segment\n        if (currentText) {\n          // Map speaker tags to meaningful names\n          let speakerName;\n          if (currentSpeaker === 1) {\n            speakerName = audioSource === 'tab' ? 'Interviewer' : 'Speaker 1';\n          } else if (currentSpeaker === 2) {\n            speakerName = audioSource === 'microphone' ? 'Candidate' : 'Speaker 2';\n          } else {\n            speakerName = `Speaker ${currentSpeaker}`;\n          }\n\n          segments.push({\n            speaker: speakerName,\n            text: currentText.trim(),\n            timestamp: new Date()\n          });\n        }\n\n        console.log(\"Created segments:\", segments);\n\n        // Update transcript segments\n        setTranscriptSegments(prev => [...prev, ...segments]);\n      } else {\n        // No speaker diarization, add as single segment based on audio source\n        console.log(\"No word-level diarization found, using audio source for speaker identification\");\n\n        if (fullTranscript) {\n          setTranscriptSegments(prev => [\n            ...prev,\n            {\n              speaker: audioSource === 'microphone' ? 'Candidate' : 'Interviewer',\n              text: fullTranscript,\n              timestamp: new Date()\n            }\n          ]);\n        } else {\n          console.log(\"No transcript text found in the response\");\n        }\n      }\n    } catch (error) {\n      console.error(\"Error processing transcription response:\", error);\n      setErrorMessage(`Error processing transcription: ${error.message}`);\n\n      // Add error to transcript for user visibility\n      setTranscriptSegments(prev => [\n        ...prev,\n        {\n          speaker: 'System',\n          text: `Error processing speech: ${error.message}`,\n          timestamp: new Date()\n        }\n      ]);\n    }\n  };\n\n  // Download transcript as text file\n  const downloadTranscript = () => {\n    if (transcriptSegments.length === 0) return;\n\n    let content = \"Conversation Transcript:\\n\\n\";\n    transcriptSegments.forEach(segment => {\n      content += `[${segment.timestamp.toLocaleTimeString()}] ${segment.speaker}: ${segment.text}\\n`;\n    });\n\n    const blob = new Blob([content], { type: 'text/plain' });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = 'google_transcript.txt';\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n\n  return (\n    <div className=\"google-speech-diarization\">\n      <div className=\"page-header\">\n        <h1>Google Speech API with Speaker Diarization</h1>\n        <p className=\"description\">\n          This page uses Google's Speech-to-Text API to transcribe audio with speaker diarization.\n        </p>\n      </div>\n\n      <div className=\"api-status\">\n        <div className={`api-indicator ${apiKeyStatus}`}>\n          <span className=\"status-dot\"></span>\n          {isTestingApiKey ? 'Testing API key...' :\n            apiKeyStatus === 'valid' ? 'API Key: Valid' :\n            apiKeyStatus === 'invalid' ? 'API Key: Invalid' :\n            'API Key: Not verified'}\n        </div>\n        <div className=\"api-buttons\">\n          <button\n            className=\"test-api-button\"\n            onClick={testApiKey}\n            disabled={isTestingApiKey}\n          >\n            Test API Key\n          </button>\n          <a\n            href={`https://console.cloud.google.com/apis/credentials`}\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n            className=\"verify-link\"\n          >\n            Verify in Google Console\n          </a>\n        </div>\n      </div>\n\n      {apiKeyStatus === 'unknown' && (\n        <div className=\"api-instructions\">\n          <h3>How to Verify Your API Key Manually:</h3>\n          <ol>\n            <li>Go to the <a href=\"https://console.cloud.google.com/apis/credentials\" target=\"_blank\" rel=\"noopener noreferrer\">Google Cloud Console</a></li>\n            <li>Select your project</li>\n            <li>Go to \"APIs & Services\" → \"Credentials\"</li>\n            <li>Check that your API key exists and is not restricted in a way that prevents browser usage</li>\n            <li>Make sure the Speech-to-Text API is enabled for your project</li>\n          </ol>\n          <p>Your current API key is: <code>{env.GOOGLE_SPEECH_API_KEY}</code></p>\n        </div>\n      )}\n\n      <div className=\"controls-container\">\n        <div className=\"recording-controls\">\n          <button\n            className={`control-button ${isRecording && audioSource === 'microphone' ? 'recording' : ''}`}\n            onClick={isRecording ? stopRecording : startMicrophoneRecording}\n            disabled={isProcessing || isTestingApiKey}\n          >\n            {isRecording && audioSource === 'microphone'\n              ? 'Stop Recording'\n              : 'Start Microphone Recording'}\n          </button>\n\n          <button\n            className={`control-button tab-audio ${isRecording && audioSource === 'tab' ? 'recording' : ''}`}\n            onClick={isRecording ? stopRecording : startTabAudioRecording}\n            disabled={isProcessing || isTestingApiKey}\n          >\n            {isRecording && audioSource === 'tab'\n              ? 'Stop Tab Recording'\n              : 'Start Tab Audio Recording'}\n          </button>\n        </div>\n\n        {isProcessing && (\n          <div className=\"processing-indicator\">\n            Processing audio... This may take a moment.\n          </div>\n        )}\n\n        {errorMessage && (\n          <div className=\"error-message\">\n            {errorMessage}\n          </div>\n        )}\n      </div>\n\n      <div className=\"transcript-container\">\n        <div className=\"transcript-header\">\n          <h2>Transcript with Speaker Diarization</h2>\n          {transcriptSegments.length > 0 && (\n            <button\n              className=\"download-button\"\n              onClick={downloadTranscript}\n            >\n              Download Transcript\n            </button>\n          )}\n        </div>\n\n        <div className=\"transcript-content\">\n          {transcriptSegments.length === 0 ? (\n            <div className=\"empty-state\">\n              No transcription yet. Start recording to see results.\n            </div>\n          ) : (\n            <div className=\"transcript-segments\">\n              {transcriptSegments.map((segment, index) => (\n                <div\n                  key={index}\n                  className={`transcript-segment ${segment.speaker.toLowerCase().replace(' ', '-')}`}\n                >\n                  <div className=\"segment-header\">\n                    <span className=\"timestamp\">{segment.timestamp.toLocaleTimeString()}</span>\n                    <span className=\"speaker-label\">{segment.speaker}</span>\n                  </div>\n                  <div className=\"segment-text\">{segment.text}</div>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default GoogleSpeechDiarization;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,+BAA+B;AACtC,OAAOC,GAAG,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,SAASC,uBAAuBA,CAAA,EAAG;EAAAC,EAAA;EACjC;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGT,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACU,YAAY,EAAEC,eAAe,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACY,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAACc,YAAY,EAAEC,eAAe,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACgB,WAAW,EAAEC,cAAc,CAAC,GAAGjB,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC;EAC9D,MAAM,CAACkB,YAAY,EAAEC,eAAe,CAAC,GAAGnB,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;EAC7D,MAAM,CAACoB,eAAe,EAAEC,kBAAkB,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACA,MAAMsB,gBAAgB,GAAGpB,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMqB,cAAc,GAAGrB,MAAM,CAAC,EAAE,CAAC;EACjC,MAAMsB,cAAc,GAAGtB,MAAM,CAAC,IAAI,CAAC;;EAEnC;EACA,MAAMuB,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIH,gBAAgB,CAACI,OAAO,EAAE;MAC5B,IAAIJ,gBAAgB,CAACI,OAAO,CAACC,KAAK,KAAK,UAAU,EAAE;QACjDL,gBAAgB,CAACI,OAAO,CAACE,IAAI,CAAC,CAAC;MACjC;MACAN,gBAAgB,CAACI,OAAO,GAAG,IAAI;IACjC;IAEA,IAAIF,cAAc,CAACE,OAAO,EAAE;MAC1BF,cAAc,CAACE,OAAO,CAACG,SAAS,CAAC,CAAC,CAACC,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACH,IAAI,CAAC,CAAC,CAAC;MACjEJ,cAAc,CAACE,OAAO,GAAG,IAAI;IAC/B;IAEAH,cAAc,CAACG,OAAO,GAAG,EAAE;EAC7B,CAAC;;EAED;EACAzB,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACXwB,YAAY,CAAC,CAAC;IAChB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAxB,SAAS,CAAC,MAAM;IACd+B,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMA,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFX,kBAAkB,CAAC,IAAI,CAAC;MACxBN,eAAe,CAAC,EAAE,CAAC;;MAEnB;MACA,MAAMkB,MAAM,GAAG9B,GAAG,CAAC+B,qBAAqB;MACxC,IAAI,CAACD,MAAM,EAAE;QACX,MAAM,IAAIE,KAAK,CAAC,qGAAqG,CAAC;MACxH;;MAEA;MACA;MACA,MAAMC,gBAAgB,GAAG,8DAA8D;;MAEvF;MACA,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,gEAAgEL,MAAM,EAAE,EAAE;QACrGM,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBC,MAAM,EAAE;YACNC,QAAQ,EAAE,UAAU;YACpBC,eAAe,EAAE,KAAK;YAAE;YACxBC,YAAY,EAAE;UAChB,CAAC;UACDC,KAAK,EAAE;YACLC,OAAO,EAAEb;UACX;QACF,CAAC;MACH,CAAC,CAAC;MAEF,MAAMc,IAAI,GAAG,MAAMb,QAAQ,CAACc,IAAI,CAAC,CAAC;;MAElC;MACA,IAAID,IAAI,CAACE,KAAK,EAAE;QACd;QACA;QACA,IAAIF,IAAI,CAACE,KAAK,CAACC,OAAO,IAAIH,IAAI,CAACE,KAAK,CAACC,OAAO,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;UAC9DC,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;UACvErC,eAAe,CAAC,OAAO,CAAC;QAC1B,CAAC,MAAM;UACL;UACAoC,OAAO,CAACH,KAAK,CAAC,0CAA0C,EAAEF,IAAI,CAACE,KAAK,CAAC;UACrEjC,eAAe,CAAC,SAAS,CAAC;UAC1BJ,eAAe,CAAC,kBAAkBmC,IAAI,CAACE,KAAK,CAACC,OAAO,IAAI,iBAAiB,EAAE,CAAC;QAC9E;MACF,CAAC,MAAM;QACL;QACAE,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;QAC7CrC,eAAe,CAAC,OAAO,CAAC;MAC1B;IACF,CAAC,CAAC,OAAOiC,KAAK,EAAE;MACdG,OAAO,CAACH,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;;MAE9C;MACA,IAAIA,KAAK,CAACC,OAAO,KAAK,iBAAiB,EAAE;QACvCtC,eAAe,CAAC;AACxB;AACA;AACA;AACA;AACA,sEAAsE,CAAC;MACjE,CAAC,MAAM;QACLA,eAAe,CAAC,0BAA0BqC,KAAK,CAACC,OAAO,EAAE,CAAC;MAC5D;MAEAlC,eAAe,CAAC,SAAS,CAAC,CAAC,CAAC;IAC9B,CAAC,SAAS;MACRE,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;;EAED;EACA,MAAMoC,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3C,IAAI;MACFhC,YAAY,CAAC,CAAC;MACdV,eAAe,CAAC,EAAE,CAAC;MACnBE,cAAc,CAAC,YAAY,CAAC;;MAE5B;MACA,MAAMyC,MAAM,GAAG,MAAMC,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;QAAEb,KAAK,EAAE;MAAK,CAAC,CAAC;MACzExB,cAAc,CAACE,OAAO,GAAGgC,MAAM;;MAE/B;MACA,MAAMI,aAAa,GAAG,IAAIC,aAAa,CAACL,MAAM,CAAC;MAC/CpC,gBAAgB,CAACI,OAAO,GAAGoC,aAAa;;MAExC;MACAA,aAAa,CAACE,eAAe,GAAIC,KAAK,IAAK;QACzC,IAAIA,KAAK,CAACf,IAAI,CAACgB,IAAI,GAAG,CAAC,EAAE;UACvB3C,cAAc,CAACG,OAAO,CAACyC,IAAI,CAACF,KAAK,CAACf,IAAI,CAAC;QACzC;MACF,CAAC;;MAED;MACAY,aAAa,CAACM,MAAM,GAAG,MAAM;QAC3BC,4BAA4B,CAAC,CAAC;MAChC,CAAC;;MAED;MACAP,aAAa,CAACQ,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;MAC3B7D,cAAc,CAAC,IAAI,CAAC;MAEpB8C,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;IAC7C,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACdG,OAAO,CAACH,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5DrC,eAAe,CAAC,+BAA+BqC,KAAK,CAACC,OAAO,EAAE,CAAC;IACjE;EACF,CAAC;;EAED;EACA,MAAMkB,sBAAsB,GAAG,MAAAA,CAAA,KAAY;IACzC,IAAI;MACF9C,YAAY,CAAC,CAAC;MACdV,eAAe,CAAC,EAAE,CAAC;MACnBE,cAAc,CAAC,KAAK,CAAC;;MAErB;MACA,MAAMyC,MAAM,GAAG,MAAMC,SAAS,CAACC,YAAY,CAACY,eAAe,CAAC;QAC1DC,KAAK,EAAE,IAAI;QACXzB,KAAK,EAAE;MACT,CAAC,CAAC;;MAEF;MACA,MAAM0B,WAAW,GAAGhB,MAAM,CAACiB,cAAc,CAAC,CAAC;MAC3C,IAAID,WAAW,CAACE,MAAM,KAAK,CAAC,EAAE;QAC5B,MAAM,IAAIzC,KAAK,CAAC,sFAAsF,CAAC;MACzG;MAEAX,cAAc,CAACE,OAAO,GAAGgC,MAAM;;MAE/B;MACA,MAAMI,aAAa,GAAG,IAAIC,aAAa,CAACL,MAAM,CAAC;MAC/CpC,gBAAgB,CAACI,OAAO,GAAGoC,aAAa;;MAExC;MACAA,aAAa,CAACE,eAAe,GAAIC,KAAK,IAAK;QACzC,IAAIA,KAAK,CAACf,IAAI,CAACgB,IAAI,GAAG,CAAC,EAAE;UACvB3C,cAAc,CAACG,OAAO,CAACyC,IAAI,CAACF,KAAK,CAACf,IAAI,CAAC;QACzC;MACF,CAAC;;MAED;MACAY,aAAa,CAACM,MAAM,GAAG,MAAM;QAC3BC,4BAA4B,CAAC,CAAC;MAChC,CAAC;;MAED;MACAX,MAAM,CAACmB,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,OAAO,GAAG,MAAM;QACzCC,aAAa,CAAC,CAAC;MACjB,CAAC;;MAED;MACAjB,aAAa,CAACQ,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;MAC3B7D,cAAc,CAAC,IAAI,CAAC;MAEpB8C,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;IAC5C,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACdG,OAAO,CAACH,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3DrC,eAAe,CAAC,gCAAgCqC,KAAK,CAACC,OAAO,EAAE,CAAC;IAClE;EACF,CAAC;;EAED;EACA,MAAM0B,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIzD,gBAAgB,CAACI,OAAO,IAAIJ,gBAAgB,CAACI,OAAO,CAACC,KAAK,KAAK,UAAU,EAAE;MAC7EL,gBAAgB,CAACI,OAAO,CAACE,IAAI,CAAC,CAAC;IACjC;IACAnB,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC;;EAED;EACA,MAAM4D,4BAA4B,GAAG,MAAAA,CAAA,KAAY;IAC/C,IAAI9C,cAAc,CAACG,OAAO,CAACkD,MAAM,KAAK,CAAC,EAAE;MACvCrB,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MACvC;IACF;IAEA,IAAI;MACF7C,eAAe,CAAC,IAAI,CAAC;;MAErB;MACA,MAAMqE,SAAS,GAAG,IAAIC,IAAI,CAAC1D,cAAc,CAACG,OAAO,EAAE;QAAEwD,IAAI,EAAE;MAAa,CAAC,CAAC;MAC1E3B,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEwB,SAAS,CAACd,IAAI,EAAE,OAAO,CAAC;;MAE3D;MACA,MAAMiB,QAAQ,GAAGC,GAAG,CAACC,eAAe,CAACL,SAAS,CAAC;MAC/CzB,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE2B,QAAQ,CAAC;;MAEjD;MACA,MAAMG,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,aAAa,CAACR,SAAS,CAAC;MAE/BM,MAAM,CAACG,SAAS,GAAG,YAAY;QAC7B;QACA,MAAMC,WAAW,GAAGJ,MAAM,CAACK,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC/CrC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEkC,WAAW,CAACd,MAAM,CAAC;;QAEvD;QACA,MAAM3C,MAAM,GAAG9B,GAAG,CAAC+B,qBAAqB;QACxC,IAAI,CAACD,MAAM,EAAE;UACX,MAAM,IAAIE,KAAK,CAAC,qGAAqG,CAAC;QACxH;QACAoB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEvB,MAAM,CAAC;;QAEnD;QACAsB,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;QACtD,MAAMnB,QAAQ,GAAG,MAAMC,KAAK,CAAC,gEAAgEL,MAAM,EAAE,EAAE;UACrGM,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YACP,cAAc,EAAE;UAClB,CAAC;UACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YACnBC,MAAM,EAAE;cACNC,QAAQ,EAAE,WAAW;cACrB;cACAE,YAAY,EAAE,OAAO;cACrB8C,0BAA0B,EAAE,IAAI;cAChCC,wBAAwB,EAAE,IAAI;cAC9BC,uBAAuB,EAAE,CAAC;cAC1BC,KAAK,EAAE,SAAS;cAChB;cACAC,WAAW,EAAE,IAAI;cACjBC,QAAQ,EAAE;gBACRC,eAAe,EAAE,YAAY;gBAC7BC,kBAAkB,EAAE,WAAW;gBAC/BC,mBAAmB,EAAErF,WAAW,KAAK,YAAY,GAAG,QAAQ,GAAG;cACjE;YACF,CAAC;YACDgC,KAAK,EAAE;cACLC,OAAO,EAAEyC;YACX;UACF,CAAC;QACH,CAAC,CAAC;QAEFnC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEnB,QAAQ,CAACiE,MAAM,CAAC;QAEhD,IAAI,CAACjE,QAAQ,CAACkE,EAAE,EAAE;UAAA,IAAAC,gBAAA;UAChB,MAAMC,SAAS,GAAG,MAAMpE,QAAQ,CAACc,IAAI,CAAC,CAAC;UACvCI,OAAO,CAACH,KAAK,CAAC,oBAAoB,EAAEqD,SAAS,CAAC;UAC9C,MAAM,IAAItE,KAAK,CAAC,qBAAqB,EAAAqE,gBAAA,GAAAC,SAAS,CAACrD,KAAK,cAAAoD,gBAAA,uBAAfA,gBAAA,CAAiBnD,OAAO,KAAIhB,QAAQ,CAACqE,UAAU,EAAE,CAAC;QACzF;QAEA,MAAMxD,IAAI,GAAG,MAAMb,QAAQ,CAACc,IAAI,CAAC,CAAC;QAClCI,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEN,IAAI,CAAC;;QAEjD;QACA,IAAI,CAACA,IAAI,CAACyD,OAAO,IAAIzD,IAAI,CAACyD,OAAO,CAAC/B,MAAM,KAAK,CAAC,EAAE;UAC9C/D,qBAAqB,CAAC+F,IAAI,IAAI,CAC5B,GAAGA,IAAI,EACP;YACEC,OAAO,EAAE7F,WAAW,KAAK,YAAY,GAAG,WAAW,GAAG,aAAa;YACnE8F,IAAI,EAAE,0EAA0E;YAChFC,SAAS,EAAE,IAAIC,IAAI,CAAC;UACtB,CAAC,CACF,CAAC;QACJ,CAAC,MAAM;UACLC,4BAA4B,CAAC/D,IAAI,CAAC;QACpC;;QAEA;QACA3B,cAAc,CAACG,OAAO,GAAG,EAAE;QAC3Bf,eAAe,CAAC,KAAK,CAAC;MACxB,CAAC;MAED2E,MAAM,CAAC4B,OAAO,GAAI9D,KAAK,IAAK;QAC1BG,OAAO,CAACH,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;QACzC,MAAM,IAAIjB,KAAK,CAAC,6BAA6BiB,KAAK,EAAE,CAAC;MACvD,CAAC;IAEH,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdG,OAAO,CAACH,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CrC,eAAe,CAAC,2BAA2BqC,KAAK,CAACC,OAAO,EAAE,CAAC;;MAE3D;MACAxC,qBAAqB,CAAC+F,IAAI,IAAI,CAC5B,GAAGA,IAAI,EACP;QACEC,OAAO,EAAE,QAAQ;QACjBC,IAAI,EAAE,UAAU1D,KAAK,CAACC,OAAO,qBAAqB;QAClD0D,SAAS,EAAE,IAAIC,IAAI,CAAC;MACtB,CAAC,CACF,CAAC;MAEFrG,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAMsG,4BAA4B,GAAI/D,IAAI,IAAK;IAC7C,IAAI,CAACA,IAAI,CAACyD,OAAO,IAAIzD,IAAI,CAACyD,OAAO,CAAC/B,MAAM,KAAK,CAAC,EAAE;MAC9CrB,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;MAChD;IACF;IAEA,IAAI;MACFD,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEN,IAAI,CAACyD,OAAO,CAAC;;MAE9D;MACA,IAAIQ,cAAc,GAAG,EAAE;MACvBjE,IAAI,CAACyD,OAAO,CAAC7E,OAAO,CAAC6D,MAAM,IAAI;QAC7B,IAAIA,MAAM,CAACyB,YAAY,IAAIzB,MAAM,CAACyB,YAAY,CAAC,CAAC,CAAC,EAAE;UACjDD,cAAc,IAAIxB,MAAM,CAACyB,YAAY,CAAC,CAAC,CAAC,CAACC,UAAU,GAAG,GAAG;QAC3D;MACF,CAAC,CAAC;MAEFF,cAAc,GAAGA,cAAc,CAACG,IAAI,CAAC,CAAC;MACtC/D,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE2D,cAAc,CAAC;;MAE/C;MACA,IAAII,uBAAuB,GAAG,KAAK;MACnC,IAAIC,aAAa,GAAG,EAAE;;MAEtB;MACAtE,IAAI,CAACyD,OAAO,CAAC7E,OAAO,CAAC6D,MAAM,IAAI;QAC7B,IAAIA,MAAM,CAACyB,YAAY,IAAIzB,MAAM,CAACyB,YAAY,CAAC,CAAC,CAAC,IAAIzB,MAAM,CAACyB,YAAY,CAAC,CAAC,CAAC,CAACK,KAAK,EAAE;UACjF,MAAMA,KAAK,GAAG9B,MAAM,CAACyB,YAAY,CAAC,CAAC,CAAC,CAACK,KAAK;UAC1C,IAAIA,KAAK,CAAC7C,MAAM,GAAG,CAAC,IAAI6C,KAAK,CAAC,CAAC,CAAC,CAACC,UAAU,EAAE;YAC3CH,uBAAuB,GAAG,IAAI;YAC9BC,aAAa,GAAGA,aAAa,CAACG,MAAM,CAACF,KAAK,CAAC;UAC7C;QACF;MACF,CAAC,CAAC;MAEF,IAAIF,uBAAuB,IAAIC,aAAa,CAAC5C,MAAM,GAAG,CAAC,EAAE;QACvDrB,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEgE,aAAa,CAAC5C,MAAM,EAAE,OAAO,CAAC;;QAE/E;QACA,IAAIgD,cAAc,GAAG,IAAI;QACzB,IAAIC,WAAW,GAAG,EAAE;QACpB,IAAIC,QAAQ,GAAG,EAAE;QAEjBN,aAAa,CAAC1F,OAAO,CAACiG,IAAI,IAAI;UAC5B,MAAML,UAAU,GAAGK,IAAI,CAACL,UAAU,IAAI,CAAC;;UAEvC;UACA,IAAIA,UAAU,KAAKE,cAAc,EAAE;YACjC;YACA,IAAIC,WAAW,EAAE;cACf;cACA,IAAIG,WAAW;cACf,IAAIJ,cAAc,KAAK,CAAC,EAAE;gBACxBI,WAAW,GAAGhH,WAAW,KAAK,KAAK,GAAG,aAAa,GAAG,WAAW;cACnE,CAAC,MAAM,IAAI4G,cAAc,KAAK,CAAC,EAAE;gBAC/BI,WAAW,GAAGhH,WAAW,KAAK,YAAY,GAAG,WAAW,GAAG,WAAW;cACxE,CAAC,MAAM;gBACLgH,WAAW,GAAG,WAAWJ,cAAc,EAAE;cAC3C;cAEAE,QAAQ,CAAC3D,IAAI,CAAC;gBACZ0C,OAAO,EAAEmB,WAAW;gBACpBlB,IAAI,EAAEe,WAAW,CAACP,IAAI,CAAC,CAAC;gBACxBP,SAAS,EAAE,IAAIC,IAAI,CAAC;cACtB,CAAC,CAAC;YACJ;;YAEA;YACAY,cAAc,GAAGF,UAAU;YAC3BG,WAAW,GAAGE,IAAI,CAACA,IAAI,GAAG,GAAG;UAC/B,CAAC,MAAM;YACL;YACAF,WAAW,IAAIE,IAAI,CAACA,IAAI,GAAG,GAAG;UAChC;QACF,CAAC,CAAC;;QAEF;QACA,IAAIF,WAAW,EAAE;UACf;UACA,IAAIG,WAAW;UACf,IAAIJ,cAAc,KAAK,CAAC,EAAE;YACxBI,WAAW,GAAGhH,WAAW,KAAK,KAAK,GAAG,aAAa,GAAG,WAAW;UACnE,CAAC,MAAM,IAAI4G,cAAc,KAAK,CAAC,EAAE;YAC/BI,WAAW,GAAGhH,WAAW,KAAK,YAAY,GAAG,WAAW,GAAG,WAAW;UACxE,CAAC,MAAM;YACLgH,WAAW,GAAG,WAAWJ,cAAc,EAAE;UAC3C;UAEAE,QAAQ,CAAC3D,IAAI,CAAC;YACZ0C,OAAO,EAAEmB,WAAW;YACpBlB,IAAI,EAAEe,WAAW,CAACP,IAAI,CAAC,CAAC;YACxBP,SAAS,EAAE,IAAIC,IAAI,CAAC;UACtB,CAAC,CAAC;QACJ;QAEAzD,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEsE,QAAQ,CAAC;;QAE1C;QACAjH,qBAAqB,CAAC+F,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAGkB,QAAQ,CAAC,CAAC;MACvD,CAAC,MAAM;QACL;QACAvE,OAAO,CAACC,GAAG,CAAC,gFAAgF,CAAC;QAE7F,IAAI2D,cAAc,EAAE;UAClBtG,qBAAqB,CAAC+F,IAAI,IAAI,CAC5B,GAAGA,IAAI,EACP;YACEC,OAAO,EAAE7F,WAAW,KAAK,YAAY,GAAG,WAAW,GAAG,aAAa;YACnE8F,IAAI,EAAEK,cAAc;YACpBJ,SAAS,EAAE,IAAIC,IAAI,CAAC;UACtB,CAAC,CACF,CAAC;QACJ,CAAC,MAAM;UACLzD,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;QACzD;MACF;IACF,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACdG,OAAO,CAACH,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChErC,eAAe,CAAC,mCAAmCqC,KAAK,CAACC,OAAO,EAAE,CAAC;;MAEnE;MACAxC,qBAAqB,CAAC+F,IAAI,IAAI,CAC5B,GAAGA,IAAI,EACP;QACEC,OAAO,EAAE,QAAQ;QACjBC,IAAI,EAAE,4BAA4B1D,KAAK,CAACC,OAAO,EAAE;QACjD0D,SAAS,EAAE,IAAIC,IAAI,CAAC;MACtB,CAAC,CACF,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMiB,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAIrH,kBAAkB,CAACgE,MAAM,KAAK,CAAC,EAAE;IAErC,IAAI3B,OAAO,GAAG,8BAA8B;IAC5CrC,kBAAkB,CAACkB,OAAO,CAACoG,OAAO,IAAI;MACpCjF,OAAO,IAAI,IAAIiF,OAAO,CAACnB,SAAS,CAACoB,kBAAkB,CAAC,CAAC,KAAKD,OAAO,CAACrB,OAAO,KAAKqB,OAAO,CAACpB,IAAI,IAAI;IAChG,CAAC,CAAC;IAEF,MAAMsB,IAAI,GAAG,IAAInD,IAAI,CAAC,CAAChC,OAAO,CAAC,EAAE;MAAEiC,IAAI,EAAE;IAAa,CAAC,CAAC;IACxD,MAAMmD,GAAG,GAAGjD,GAAG,CAACC,eAAe,CAAC+C,IAAI,CAAC;IACrC,MAAME,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACrCF,CAAC,CAACG,IAAI,GAAGJ,GAAG;IACZC,CAAC,CAACI,QAAQ,GAAG,uBAAuB;IACpCH,QAAQ,CAAC9F,IAAI,CAACkG,WAAW,CAACL,CAAC,CAAC;IAC5BA,CAAC,CAACM,KAAK,CAAC,CAAC;IACTL,QAAQ,CAAC9F,IAAI,CAACoG,WAAW,CAACP,CAAC,CAAC;IAC5BlD,GAAG,CAAC0D,eAAe,CAACT,GAAG,CAAC;EAC1B,CAAC;EAED,oBACEhI,OAAA;IAAK0I,SAAS,EAAC,2BAA2B;IAAAC,QAAA,gBACxC3I,OAAA;MAAK0I,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1B3I,OAAA;QAAA2I,QAAA,EAAI;MAA0C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnD/I,OAAA;QAAG0I,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAE3B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAEN/I,OAAA;MAAK0I,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzB3I,OAAA;QAAK0I,SAAS,EAAE,iBAAiB7H,YAAY,EAAG;QAAA8H,QAAA,gBAC9C3I,OAAA;UAAM0I,SAAS,EAAC;QAAY;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACnChI,eAAe,GAAG,oBAAoB,GACrCF,YAAY,KAAK,OAAO,GAAG,gBAAgB,GAC3CA,YAAY,KAAK,SAAS,GAAG,kBAAkB,GAC/C,uBAAuB;MAAA;QAAA+H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC,eACN/I,OAAA;QAAK0I,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B3I,OAAA;UACE0I,SAAS,EAAC,iBAAiB;UAC3BM,OAAO,EAAErH,UAAW;UACpBsH,QAAQ,EAAElI,eAAgB;UAAA4H,QAAA,EAC3B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT/I,OAAA;UACEoI,IAAI,EAAE,mDAAoD;UAC1Dc,MAAM,EAAC,QAAQ;UACfC,GAAG,EAAC,qBAAqB;UACzBT,SAAS,EAAC,aAAa;UAAAC,QAAA,EACxB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELlI,YAAY,KAAK,SAAS,iBACzBb,OAAA;MAAK0I,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/B3I,OAAA;QAAA2I,QAAA,EAAI;MAAoC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7C/I,OAAA;QAAA2I,QAAA,gBACE3I,OAAA;UAAA2I,QAAA,GAAI,YAAU,eAAA3I,OAAA;YAAGoI,IAAI,EAAC,mDAAmD;YAACc,MAAM,EAAC,QAAQ;YAACC,GAAG,EAAC,qBAAqB;YAAAR,QAAA,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjJ/I,OAAA;UAAA2I,QAAA,EAAI;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5B/I,OAAA;UAAA2I,QAAA,EAAI;QAAuC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChD/I,OAAA;UAAA2I,QAAA,EAAI;QAAyF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClG/I,OAAA;UAAA2I,QAAA,EAAI;QAA4D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE,CAAC,eACL/I,OAAA;QAAA2I,QAAA,GAAG,2BAAyB,eAAA3I,OAAA;UAAA2I,QAAA,EAAO7I,GAAG,CAAC+B;QAAqB;UAAA+G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrE,CACN,eAED/I,OAAA;MAAK0I,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBACjC3I,OAAA;QAAK0I,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjC3I,OAAA;UACE0I,SAAS,EAAE,kBAAkBvI,WAAW,IAAIQ,WAAW,KAAK,YAAY,GAAG,WAAW,GAAG,EAAE,EAAG;UAC9FqI,OAAO,EAAE7I,WAAW,GAAGuE,aAAa,GAAGtB,wBAAyB;UAChE6F,QAAQ,EAAE5I,YAAY,IAAIU,eAAgB;UAAA4H,QAAA,EAEzCxI,WAAW,IAAIQ,WAAW,KAAK,YAAY,GACxC,gBAAgB,GAChB;QAA4B;UAAAiI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eAET/I,OAAA;UACE0I,SAAS,EAAE,4BAA4BvI,WAAW,IAAIQ,WAAW,KAAK,KAAK,GAAG,WAAW,GAAG,EAAE,EAAG;UACjGqI,OAAO,EAAE7I,WAAW,GAAGuE,aAAa,GAAGR,sBAAuB;UAC9D+E,QAAQ,EAAE5I,YAAY,IAAIU,eAAgB;UAAA4H,QAAA,EAEzCxI,WAAW,IAAIQ,WAAW,KAAK,KAAK,GACjC,oBAAoB,GACpB;QAA2B;UAAAiI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAEL1I,YAAY,iBACXL,OAAA;QAAK0I,SAAS,EAAC,sBAAsB;QAAAC,QAAA,EAAC;MAEtC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CACN,EAEAtI,YAAY,iBACXT,OAAA;QAAK0I,SAAS,EAAC,eAAe;QAAAC,QAAA,EAC3BlI;MAAY;QAAAmI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEN/I,OAAA;MAAK0I,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBACnC3I,OAAA;QAAK0I,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC3I,OAAA;UAAA2I,QAAA,EAAI;QAAmC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAC3CxI,kBAAkB,CAACgE,MAAM,GAAG,CAAC,iBAC5BvE,OAAA;UACE0I,SAAS,EAAC,iBAAiB;UAC3BM,OAAO,EAAEpB,kBAAmB;UAAAe,QAAA,EAC7B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEN/I,OAAA;QAAK0I,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAChCpI,kBAAkB,CAACgE,MAAM,KAAK,CAAC,gBAC9BvE,OAAA;UAAK0I,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAE7B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,gBAEN/I,OAAA;UAAK0I,SAAS,EAAC,qBAAqB;UAAAC,QAAA,EACjCpI,kBAAkB,CAAC6I,GAAG,CAAC,CAACvB,OAAO,EAAEwB,KAAK,kBACrCrJ,OAAA;YAEE0I,SAAS,EAAE,sBAAsBb,OAAO,CAACrB,OAAO,CAAC8C,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,EAAG;YAAAZ,QAAA,gBAEnF3I,OAAA;cAAK0I,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7B3I,OAAA;gBAAM0I,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAEd,OAAO,CAACnB,SAAS,CAACoB,kBAAkB,CAAC;cAAC;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3E/I,OAAA;gBAAM0I,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAEd,OAAO,CAACrB;cAAO;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC,eACN/I,OAAA;cAAK0I,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAEd,OAAO,CAACpB;YAAI;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,GAP7CM,KAAK;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAQP,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC7I,EAAA,CApmBQD,uBAAuB;AAAAuJ,EAAA,GAAvBvJ,uBAAuB;AAsmBhC,eAAeA,uBAAuB;AAAC,IAAAuJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}