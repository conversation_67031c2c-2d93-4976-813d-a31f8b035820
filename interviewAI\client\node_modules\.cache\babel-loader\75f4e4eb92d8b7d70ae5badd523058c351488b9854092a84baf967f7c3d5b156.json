{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\interview-ai-app\\\\interviewAI\\\\client\\\\src\\\\pages\\\\admin-home\\\\index.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\n// import { useNavigate } from 'react-router-dom';\nimport Header from '../../components/Header';\nimport InterviewForm from '../../components/InterviewForm';\nimport InterviewSession from '../../components/InterviewSession';\nimport SpeechToText from '../../components/SpeechToText';\nimport VoiceTranscriber from '../../components/VoiceTranscriber';\nimport MeetingTranscriber from '../../components/MeetingTranscriber';\nimport './admin-home.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction AdminHomeScreen() {\n  _s();\n  // const navigate = useNavigate();\n  const [sessionActive, setSessionActive] = useState(false);\n  const [sessionConfig, setSessionConfig] = useState(null);\n  const [showSpeechToText, setShowSpeechToText] = useState(false);\n  const [showVoiceTranscriber, setShowVoiceTranscriber] = useState(false);\n  const [showMeetingTranscriber, setShowMeetingTranscriber] = useState(false);\n  React.useEffect(() => {\n    const userDetails = localStorage.getItem(\"userDetails\");\n    if (!userDetails) {\n      window.location.href = \"/login\";\n    }\n  }, []);\n  const startSession = config => {\n    // Store session time in localStorage if provided\n    if (config && config.sessionTimeRemaining) {\n      try {\n        const userDetails = localStorage.getItem('userDetails');\n        let userData = userDetails ? JSON.parse(userDetails) : {};\n        userData.timeRemaining = config.sessionTimeRemaining;\n        localStorage.setItem('userDetails', JSON.stringify(userData));\n      } catch (error) {\n        console.error('Error saving session time to localStorage:', error);\n      }\n    }\n\n    // Directly show the SpeechToText component\n    setShowSpeechToText(true);\n  };\n  const endSession = () => {\n    setSessionActive(false);\n    setSessionConfig(null);\n  };\n\n  // const toggleSpeechToText = () => {\n  //   setShowSpeechToText(!showSpeechToText);\n  //   if (!showSpeechToText) {\n  //     setSessionActive(false);\n  //     setShowVoiceTranscriber(false);\n  //     setShowMeetingTranscriber(false);\n  //   }\n  // };\n\n  // const toggleVoiceTranscriber = () => {\n  //   setShowVoiceTranscriber(!showVoiceTranscriber);\n  //   if (!showVoiceTranscriber) {\n  //     setSessionActive(false);\n  //     setShowSpeechToText(false);\n  //     setShowMeetingTranscriber(false);\n  //   }\n  // };\n\n  // const toggleMeetingTranscriber = () => {\n  //   setShowMeetingTranscriber(!showMeetingTranscriber);\n  //   if (!showMeetingTranscriber) {\n  //     setSessionActive(false);\n  //     setShowSpeechToText(false);\n  //     setShowVoiceTranscriber(false);\n  //   }\n  // };\n\n  const handleBackFromVoiceTranscriber = () => {\n    setShowVoiceTranscriber(false);\n  };\n  const handleBackFromMeetingTranscriber = () => {\n    setShowMeetingTranscriber(false);\n  };\n\n  // const navigateToDeepgramTranscription = () => {\n  //   navigate('/deepgram-transcription');\n  // };\n\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"App\",\n    children: [!showVoiceTranscriber && !showMeetingTranscriber && !showSpeechToText && /*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 81\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: showVoiceTranscriber || showMeetingTranscriber || showSpeechToText ? 'fullscreen' : '',\n      children: showSpeechToText ? /*#__PURE__*/_jsxDEV(SpeechToText, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 11\n      }, this) : showVoiceTranscriber ? /*#__PURE__*/_jsxDEV(VoiceTranscriber, {\n        onBack: handleBackFromVoiceTranscriber\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 11\n      }, this) : showMeetingTranscriber ? /*#__PURE__*/_jsxDEV(MeetingTranscriber, {\n        onBack: handleBackFromMeetingTranscriber\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 11\n      }, this) : !sessionActive ? /*#__PURE__*/_jsxDEV(InterviewForm, {\n        onStartSession: startSession\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(InterviewSession, {\n        config: sessionConfig,\n        onEndSession: endSession\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 132,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 89,\n    columnNumber: 5\n  }, this);\n}\n_s(AdminHomeScreen, \"EeRVtFJx0WSSDdsPVy/44c4oJLA=\");\n_c = AdminHomeScreen;\nexport default AdminHomeScreen;\nvar _c;\n$RefreshReg$(_c, \"AdminHomeScreen\");", "map": {"version": 3, "names": ["React", "useState", "Header", "InterviewForm", "InterviewSession", "SpeechToText", "VoiceTranscriber", "MeetingTranscriber", "jsxDEV", "_jsxDEV", "AdminHomeScreen", "_s", "sessionActive", "setSessionActive", "sessionConfig", "setSessionConfig", "showSpeechToText", "setShowSpeechToText", "showVoiceTranscriber", "setShowVoiceTranscriber", "showMeetingTranscriber", "setShowMeetingTranscriber", "useEffect", "userDetails", "localStorage", "getItem", "window", "location", "href", "startSession", "config", "sessionTimeRemaining", "userData", "JSON", "parse", "timeRemaining", "setItem", "stringify", "error", "console", "endSession", "handleBackFromVoiceTranscriber", "handleBackFromMeetingTranscriber", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onBack", "onStartSession", "onEndSession", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/interview-ai-app/interviewAI/client/src/pages/admin-home/index.jsx"], "sourcesContent": ["import React, { useState } from 'react';\r\n// import { useNavigate } from 'react-router-dom';\r\nimport Header from '../../components/Header';\r\nimport InterviewForm from '../../components/InterviewForm';\r\nimport InterviewSession from '../../components/InterviewSession';\r\nimport SpeechToText from '../../components/SpeechToText';\r\nimport VoiceTranscriber from '../../components/VoiceTranscriber';\r\nimport MeetingTranscriber from '../../components/MeetingTranscriber';\r\nimport './admin-home.css';\r\n\r\nfunction AdminHomeScreen() {\r\n  // const navigate = useNavigate();\r\n  const [sessionActive, setSessionActive] = useState(false);\r\n  const [sessionConfig, setSessionConfig] = useState(null);\r\n  const [showSpeechToText, setShowSpeechToText] = useState(false);\r\n  const [showVoiceTranscriber, setShowVoiceTranscriber] = useState(false);\r\n  const [showMeetingTranscriber, setShowMeetingTranscriber] = useState(false);\r\n\r\n  React.useEffect(() => {\r\n    const userDetails = localStorage.getItem(\"userDetails\");\r\n    if (!userDetails) {\r\n      window.location.href = \"/login\";\r\n    }\r\n  }, []);\r\n\r\n  const startSession = (config) => {\r\n    // Store session time in localStorage if provided\r\n    if (config && config.sessionTimeRemaining) {\r\n      try {\r\n        const userDetails = localStorage.getItem('userDetails');\r\n        let userData = userDetails ? JSON.parse(userDetails) : {};\r\n\r\n        userData.timeRemaining = config.sessionTimeRemaining;\r\n        localStorage.setItem('userDetails', JSON.stringify(userData));\r\n      } catch (error) {\r\n        console.error('Error saving session time to localStorage:', error);\r\n      }\r\n    }\r\n\r\n    // Directly show the SpeechToText component\r\n    setShowSpeechToText(true);\r\n  };\r\n\r\n  const endSession = () => {\r\n    setSessionActive(false);\r\n    setSessionConfig(null);\r\n  };\r\n\r\n  // const toggleSpeechToText = () => {\r\n  //   setShowSpeechToText(!showSpeechToText);\r\n  //   if (!showSpeechToText) {\r\n  //     setSessionActive(false);\r\n  //     setShowVoiceTranscriber(false);\r\n  //     setShowMeetingTranscriber(false);\r\n  //   }\r\n  // };\r\n\r\n  // const toggleVoiceTranscriber = () => {\r\n  //   setShowVoiceTranscriber(!showVoiceTranscriber);\r\n  //   if (!showVoiceTranscriber) {\r\n  //     setSessionActive(false);\r\n  //     setShowSpeechToText(false);\r\n  //     setShowMeetingTranscriber(false);\r\n  //   }\r\n  // };\r\n\r\n  // const toggleMeetingTranscriber = () => {\r\n  //   setShowMeetingTranscriber(!showMeetingTranscriber);\r\n  //   if (!showMeetingTranscriber) {\r\n  //     setSessionActive(false);\r\n  //     setShowSpeechToText(false);\r\n  //     setShowVoiceTranscriber(false);\r\n  //   }\r\n  // };\r\n\r\n  const handleBackFromVoiceTranscriber = () => {\r\n    setShowVoiceTranscriber(false);\r\n  };\r\n\r\n  const handleBackFromMeetingTranscriber = () => {\r\n    setShowMeetingTranscriber(false);\r\n  };\r\n\r\n  // const navigateToDeepgramTranscription = () => {\r\n  //   navigate('/deepgram-transcription');\r\n  // };\r\n\r\n  return (\r\n    <div className=\"App\">\r\n      {!showVoiceTranscriber && !showMeetingTranscriber && !showSpeechToText && <Header />}\r\n\r\n      {/* {!showVoiceTranscriber && !showMeetingTranscriber && !showSpeechToText && (\r\n        <div className=\"nav-buttons\">\r\n          <button\r\n            onClick={() => {\r\n              setShowSpeechToText(false);\r\n              setShowVoiceTranscriber(false);\r\n              setShowMeetingTranscriber(false);\r\n              setSessionActive(false);\r\n            }}\r\n            className={!sessionActive && !showSpeechToText && !showVoiceTranscriber && !showMeetingTranscriber ? 'active' : ''}\r\n          >\r\n            Home\r\n          </button>\r\n          <button\r\n            onClick={toggleSpeechToText}\r\n            className={showSpeechToText ? 'active' : ''}\r\n          >\r\n            Interview AI Tool\r\n          </button>\r\n          <button\r\n            onClick={toggleVoiceTranscriber}\r\n            className={showVoiceTranscriber ? 'active' : ''}\r\n          >\r\n            Voice Transcriber\r\n          </button>\r\n          <button\r\n            onClick={toggleMeetingTranscriber}\r\n            className={showMeetingTranscriber ? 'active' : ''}\r\n          >\r\n            Meeting Transcriber\r\n          </button>\r\n          <button\r\n            onClick={navigateToDeepgramTranscription}\r\n            className=\"highlight\"\r\n          >\r\n            Deepgram Transcription\r\n          </button>\r\n        </div>\r\n      )} */}\r\n\r\n      <main className={showVoiceTranscriber || showMeetingTranscriber || showSpeechToText ? 'fullscreen' : ''}>\r\n        {showSpeechToText ? (\r\n          <SpeechToText />\r\n        ) : showVoiceTranscriber ? (\r\n          <VoiceTranscriber onBack={handleBackFromVoiceTranscriber} />\r\n        ) : showMeetingTranscriber ? (\r\n          <MeetingTranscriber onBack={handleBackFromMeetingTranscriber} />\r\n        ) : !sessionActive ? (\r\n          <InterviewForm onStartSession={startSession} />\r\n        ) : (\r\n          <InterviewSession config={sessionConfig} onEndSession={endSession} />\r\n        )}\r\n      </main>\r\n    </div>\r\n  );\r\n}\r\nexport default AdminHomeScreen;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC;AACA,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,aAAa,MAAM,gCAAgC;AAC1D,OAAOC,gBAAgB,MAAM,mCAAmC;AAChE,OAAOC,YAAY,MAAM,+BAA+B;AACxD,OAAOC,gBAAgB,MAAM,mCAAmC;AAChE,OAAOC,kBAAkB,MAAM,qCAAqC;AACpE,OAAO,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,SAASC,eAAeA,CAAA,EAAG;EAAAC,EAAA;EACzB;EACA,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACa,aAAa,EAAEC,gBAAgB,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACe,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACiB,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACmB,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EAE3ED,KAAK,CAACsB,SAAS,CAAC,MAAM;IACpB,MAAMC,WAAW,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IACvD,IAAI,CAACF,WAAW,EAAE;MAChBG,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;IACjC;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,YAAY,GAAIC,MAAM,IAAK;IAC/B;IACA,IAAIA,MAAM,IAAIA,MAAM,CAACC,oBAAoB,EAAE;MACzC,IAAI;QACF,MAAMR,WAAW,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;QACvD,IAAIO,QAAQ,GAAGT,WAAW,GAAGU,IAAI,CAACC,KAAK,CAACX,WAAW,CAAC,GAAG,CAAC,CAAC;QAEzDS,QAAQ,CAACG,aAAa,GAAGL,MAAM,CAACC,oBAAoB;QACpDP,YAAY,CAACY,OAAO,CAAC,aAAa,EAAEH,IAAI,CAACI,SAAS,CAACL,QAAQ,CAAC,CAAC;MAC/D,CAAC,CAAC,OAAOM,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;MACpE;IACF;;IAEA;IACArB,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAMuB,UAAU,GAAGA,CAAA,KAAM;IACvB3B,gBAAgB,CAAC,KAAK,CAAC;IACvBE,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA,MAAM0B,8BAA8B,GAAGA,CAAA,KAAM;IAC3CtB,uBAAuB,CAAC,KAAK,CAAC;EAChC,CAAC;EAED,MAAMuB,gCAAgC,GAAGA,CAAA,KAAM;IAC7CrB,yBAAyB,CAAC,KAAK,CAAC;EAClC,CAAC;;EAED;EACA;EACA;;EAEA,oBACEZ,OAAA;IAAKkC,SAAS,EAAC,KAAK;IAAAC,QAAA,GACjB,CAAC1B,oBAAoB,IAAI,CAACE,sBAAsB,IAAI,CAACJ,gBAAgB,iBAAIP,OAAA,CAACP,MAAM;MAAA2C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eA0CpFvC,OAAA;MAAMkC,SAAS,EAAEzB,oBAAoB,IAAIE,sBAAsB,IAAIJ,gBAAgB,GAAG,YAAY,GAAG,EAAG;MAAA4B,QAAA,EACrG5B,gBAAgB,gBACfP,OAAA,CAACJ,YAAY;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,GACd9B,oBAAoB,gBACtBT,OAAA,CAACH,gBAAgB;QAAC2C,MAAM,EAAER;MAA+B;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,GAC1D5B,sBAAsB,gBACxBX,OAAA,CAACF,kBAAkB;QAAC0C,MAAM,EAAEP;MAAiC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,GAC9D,CAACpC,aAAa,gBAChBH,OAAA,CAACN,aAAa;QAAC+C,cAAc,EAAErB;MAAa;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAE/CvC,OAAA,CAACL,gBAAgB;QAAC0B,MAAM,EAAEhB,aAAc;QAACqC,YAAY,EAAEX;MAAW;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IACrE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAACrC,EAAA,CAxIQD,eAAe;AAAA0C,EAAA,GAAf1C,eAAe;AAyIxB,eAAeA,eAAe;AAAC,IAAA0C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}