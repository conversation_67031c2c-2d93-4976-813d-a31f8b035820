.deepgram-transcript-container {
  max-width: 900px;
  margin: 0 auto;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 12px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  position: relative;
  min-height: 90vh;
  display: flex;
  flex-direction: column;
}

/* Exit button in top right corner */
.exit-button {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.8);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.exit-button:hover {
  background-color: #f44336;
  color: white;
  transform: scale(1.1);
}

/* Timer at the top */
.timer-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
  padding: 8px;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.timer-icon {
  color: #4285f4;
  margin-right: 5px;
}

.timer-display {
  display: inline-block;
  font-family: monospace;
  font-size: 18px;
  font-weight: bold;
  color: #333;
  padding: 2px 8px;
}

.session-timer {
  color: #2c3e50;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 4px;
  padding: 4px 8px;
}

.listening-indicator {
  display: inline-block;
  color: #e74c3c;
  margin-left: 10px;
  font-weight: 500;
  animation: pulse 1.5s infinite;
}

.deepgram-transcript-title {
  font-size: 1.7rem;
  font-weight: 600;
  color: #1976d2;
  margin-bottom: 18px;
  letter-spacing: 0.5px;
  text-align: center;
}

/* Controls section */
.deepgram-controls {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

.record-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px 16px;
  background-color: #4285f4;
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.record-button:hover:not(:disabled) {
  background-color: #3367d6;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.record-button.recording {
  background-color: #ea4335;
  animation: pulse 1.5s infinite;
}

.clear-button, .download-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  padding: 10px 16px;
  background-color: #f0f0f0;
  color: #555;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.clear-button:hover:not(:disabled), .download-button:hover:not(:disabled) {
  background-color: #e0e0e0;
  transform: translateY(-2px);
}

.record-button:disabled, .clear-button:disabled, .download-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Content layout */
.deepgram-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
  flex: 1;
  overflow: hidden;
}

.transcript-section {
  display: flex;
  flex-direction: column;
  gap: 15px;
  flex: 1;
  min-height: 0;
}

.transcript-container {
  flex: 1;
  overflow-y: auto;
  background: white;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  min-height: 200px;
  max-height: 300px;
}

.empty-transcript {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #666;
  font-style: italic;
}

.deepgram-segment {
  margin-bottom: 12px;
  padding: 12px 18px;
  border-radius: 8px;
  background: #f8f9fa;
  border-left: 4px solid #1976d2;
  display: flex;
  align-items: flex-start;
  gap: 10px;
  animation: fade-in 0.3s ease-out;
}

.deepgram-speaker {
  font-weight: 500;
  color: #1976d2;
  margin-right: 8px;
  min-width: 90px;
  display: inline-block;
}

/* Selected text section */
.selected-text-container {
  background: #f0f7ff;
  border-radius: 8px;
  padding: 15px;
  border: 1px solid #d0e3ff;
}

.selected-text-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.selected-text-header h3 {
  margin: 0;
  font-size: 16px;
  color: #1976d2;
}

.selected-text {
  font-size: 15px;
  line-height: 1.5;
  color: #333;
  white-space: pre-wrap;
}

.send-to-gpt-button {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 6px 12px;
  background-color: #34a853;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.send-to-gpt-button:hover:not(:disabled) {
  background-color: #2d9249;
}

.send-to-gpt-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* GPT response section */
.gpt-response-section {
  background: white;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-top: 10px;
}

.gpt-response-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.gpt-response-header h3 {
  margin: 0;
  font-size: 16px;
  color: #34a853;
}

.gpt-response {
  font-size: 15px;
  line-height: 1.6;
  color: #333;
  white-space: pre-wrap;
  max-height: 200px;
  overflow-y: auto;
  padding: 10px;
  background: #f9f9f9;
  border-radius: 4px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  gap: 10px;
  color: #666;
}

/* Animations */
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes fade-in {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .deepgram-transcript-container {
    padding: 15px;
    margin: 0;
    border-radius: 0;
    min-height: 100vh;
  }

  .deepgram-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .transcript-container {
    max-height: 250px;
  }

  .gpt-response {
    max-height: 150px;
  }

  .deepgram-segment {
    padding: 10px 12px;
    flex-direction: column;
    gap: 5px;
  }

  .deepgram-speaker {
    min-width: auto;
  }
}
