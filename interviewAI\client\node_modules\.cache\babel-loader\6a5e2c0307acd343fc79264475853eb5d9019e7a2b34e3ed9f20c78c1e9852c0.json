{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\interview-ai-app\\\\interviewAI\\\\client\\\\src\\\\pages\\\\live-transcription\\\\AutomaticDiarization.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport './AutomaticDiarization.css';\n\n// Controls component for starting/stopping recording\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Controls({\n  isRecording,\n  onStart,\n  onStop,\n  onCaptureTab\n}) {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"transcription-controls\",\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      className: `control-button ${isRecording ? 'recording' : ''}`,\n      onClick: isRecording ? onStop : onStart,\n      children: isRecording ? 'Stop Recording' : 'Start Microphone'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"control-button tab-audio\",\n      onClick: onCaptureTab,\n      disabled: isRecording,\n      children: \"Capture Tab Audio (Interviewer)\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this);\n}\n\n// Live captions component\n_c = Controls;\nfunction LiveCaptions({\n  interimText,\n  currentSpeaker\n}) {\n  if (!interimText) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `live-captions ${currentSpeaker.toLowerCase().replace(' ', '-')}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"speaker-label\",\n      children: currentSpeaker\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"caption-text\",\n      children: interimText\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 5\n  }, this);\n}\n\n// Transcript component\n_c2 = LiveCaptions;\nfunction Transcript({\n  transcriptData\n}) {\n  _s();\n  const transcriptRef = useRef(null);\n  useEffect(() => {\n    // Auto-scroll to bottom when new entries are added\n    if (transcriptRef.current) {\n      transcriptRef.current.scrollTop = transcriptRef.current.scrollHeight;\n    }\n  }, [transcriptData]);\n  const downloadTranscript = () => {\n    let content = \"Conversation Transcript:\\n\\n\";\n    transcriptData.forEach(entry => {\n      content += `[${entry.timestamp.toLocaleTimeString()}] ${entry.speaker}: ${entry.text}\\n`;\n    });\n    const blob = new Blob([content], {\n      type: 'text/plain'\n    });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = 'transcript.txt';\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"transcript-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"transcript-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Full Transcript\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 9\n      }, this), transcriptData.length > 0 && /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: downloadTranscript,\n        className: \"download-button\",\n        children: \"Download Transcript\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"transcript-content\",\n      ref: transcriptRef,\n      children: [transcriptData.length === 0 && /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"empty-state\",\n        children: \"No speech detected yet.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 41\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"transcript-entries\",\n        children: transcriptData.map((entry, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `transcript-entry ${entry.speaker.toLowerCase().replace(' ', '-')}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"entry-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"timestamp\",\n              children: entry.timestamp.toLocaleTimeString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"speaker\",\n              children: entry.speaker\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"entry-text\",\n            children: entry.text\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 64,\n    columnNumber: 5\n  }, this);\n}\n\n// Main component\n_s(Transcript, \"VXGlKFKpyVYwt/tuZbNn9xFPm/o=\");\n_c3 = Transcript;\nfunction AutomaticDiarization() {\n  _s2();\n  const [isRecording, setIsRecording] = useState(false);\n  const [isCapturingTab, setIsCapturingTab] = useState(false);\n  const [currentSpeaker, setCurrentSpeaker] = useState('Interviewer');\n  const [interimTranscript, setInterimTranscript] = useState('');\n  const [fullTranscript, setFullTranscript] = useState([]);\n\n  // Refs\n  const recognitionRef = useRef(null);\n  const audioContextRef = useRef(null);\n  const analyserRef = useRef(null);\n  const mediaStreamRef = useRef(null);\n  const audioDataRef = useRef({\n    interviewer: {\n      voicePrint: null,\n      samples: []\n    },\n    candidate: {\n      voicePrint: null,\n      samples: []\n    }\n  });\n\n  // Initialize speech recognition\n  useEffect(() => {\n    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n    if (!SpeechRecognition) {\n      alert('Speech Recognition API not supported in this browser. Try Chrome or Edge.');\n      return;\n    }\n\n    // Clean up function\n    return () => {\n      if (recognitionRef.current) {\n        recognitionRef.current.stop();\n      }\n      if (mediaStreamRef.current) {\n        mediaStreamRef.current.getTracks().forEach(track => track.stop());\n      }\n      if (audioContextRef.current) {\n        audioContextRef.current.close();\n      }\n    };\n  }, []);\n\n  // Start microphone recording\n  const startRecording = async () => {\n    try {\n      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n      recognitionRef.current = new SpeechRecognition();\n      recognitionRef.current.continuous = true;\n      recognitionRef.current.interimResults = true;\n      recognitionRef.current.lang = 'en-US';\n\n      // Set up audio context for voice analysis\n      if (!audioContextRef.current) {\n        // eslint-disable-next-line\n        audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();\n      }\n\n      // Get microphone stream\n      const stream = await navigator.mediaDevices.getUserMedia({\n        audio: true\n      });\n      mediaStreamRef.current = stream;\n\n      // Set default speaker to Candidate for microphone\n      setCurrentSpeaker('Candidate');\n\n      // Set up recognition handlers\n      setupRecognitionHandlers();\n\n      // Start recognition\n      recognitionRef.current.start();\n      setIsRecording(true);\n      setIsCapturingTab(false);\n    } catch (error) {\n      console.error(\"Error starting recording:\", error);\n      alert(\"Failed to start recording: \" + error.message);\n    }\n  };\n\n  // Start tab audio capture\n  const startTabCapture = async () => {\n    try {\n      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n      recognitionRef.current = new SpeechRecognition();\n      recognitionRef.current.continuous = true;\n      recognitionRef.current.interimResults = true;\n      recognitionRef.current.lang = 'en-US';\n\n      // Request screen sharing with audio\n      const stream = await navigator.mediaDevices.getDisplayMedia({\n        video: true,\n        audio: true\n      });\n\n      // Check if audio is included\n      const audioTracks = stream.getAudioTracks();\n      if (audioTracks.length === 0) {\n        alert(\"No audio track found. Please make sure to select 'Share audio' when sharing the tab.\");\n        stream.getTracks().forEach(track => track.stop());\n        return;\n      }\n      mediaStreamRef.current = stream;\n\n      // Set up audio context\n      if (!audioContextRef.current) {\n        // eslint-disable-next-line\n        audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();\n      }\n\n      // Set default speaker to Interviewer for tab audio\n      setCurrentSpeaker('Interviewer');\n\n      // Set up recognition handlers\n      setupRecognitionHandlers();\n\n      // Start recognition\n      recognitionRef.current.start();\n      setIsRecording(true);\n      setIsCapturingTab(true);\n\n      // Handle stream ending\n      stream.getVideoTracks()[0].onended = () => {\n        stopRecording();\n      };\n    } catch (error) {\n      console.error(\"Error capturing tab audio:\", error);\n      alert(\"Failed to capture tab audio: \" + error.message);\n    }\n  };\n\n  // Stop recording\n  const stopRecording = () => {\n    if (recognitionRef.current) {\n      recognitionRef.current.stop();\n    }\n    if (mediaStreamRef.current) {\n      mediaStreamRef.current.getTracks().forEach(track => track.stop());\n      mediaStreamRef.current = null;\n    }\n    setIsRecording(false);\n    setIsCapturingTab(false);\n\n    // Add any remaining interim transcript to the full transcript\n    if (interimTranscript.trim()) {\n      setFullTranscript(prev => [...prev, {\n        speaker: currentSpeaker,\n        text: interimTranscript.trim(),\n        timestamp: new Date()\n      }]);\n      setInterimTranscript('');\n    }\n  };\n\n  // Set up recognition event handlers\n  const setupRecognitionHandlers = () => {\n    if (!recognitionRef.current) return;\n    recognitionRef.current.onstart = () => {\n      setIsRecording(true);\n    };\n    recognitionRef.current.onresult = event => {\n      let interim = '';\n      let final = '';\n      for (let i = event.resultIndex; i < event.results.length; ++i) {\n        if (event.results[i].isFinal) {\n          final += event.results[i][0].transcript;\n        } else {\n          interim += event.results[i][0].transcript;\n        }\n      }\n      if (interim) setInterimTranscript(interim);\n      if (final) {\n        // Add to full transcript with current speaker\n        setFullTranscript(prev => [...prev, {\n          speaker: currentSpeaker,\n          text: final.trim(),\n          timestamp: new Date()\n        }]);\n        setInterimTranscript('');\n      }\n    };\n    recognitionRef.current.onerror = event => {\n      console.error(\"Recognition error:\", event.error);\n      if (event.error !== 'no-speech') {\n        stopRecording();\n      }\n    };\n    recognitionRef.current.onend = () => {\n      setIsRecording(false);\n      // Restart if we're still supposed to be recording\n      if (isRecording) {\n        try {\n          recognitionRef.current.start();\n        } catch (error) {\n          console.error(\"Failed to restart recognition:\", error);\n        }\n      }\n    };\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"automatic-diarization\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Automatic Speaker Diarization\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 316,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"description\",\n        children: \"This page automatically identifies speakers based on audio source. Tab audio is identified as \\\"Interviewer\\\" and microphone as \\\"Candidate\\\".\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 317,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 315,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Controls, {\n      isRecording: isRecording,\n      onStart: startRecording,\n      onStop: stopRecording,\n      onCaptureTab: startTabCapture\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 323,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"status-indicator\",\n      children: isRecording && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"recording-status\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: `status-dot ${isCapturingTab ? 'interviewer' : 'candidate'}`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 13\n        }, this), isCapturingTab ? 'Capturing Tab Audio (Interviewer)' : 'Recording Microphone (Candidate)']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 332,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 330,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(LiveCaptions, {\n      interimText: interimTranscript,\n      currentSpeaker: currentSpeaker\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 341,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Transcript, {\n      transcriptData: fullTranscript\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 346,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 314,\n    columnNumber: 5\n  }, this);\n}\n_s2(AutomaticDiarization, \"U6/vtaiY9RPi7X46pCYla6VpVYg=\");\n_c4 = AutomaticDiarization;\nexport default AutomaticDiarization;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"Controls\");\n$RefreshReg$(_c2, \"LiveCaptions\");\n$RefreshReg$(_c3, \"Transcript\");\n$RefreshReg$(_c4, \"AutomaticDiarization\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "Controls", "isRecording", "onStart", "onStop", "onCaptureTab", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "disabled", "_c", "LiveCaptions", "interimText", "currentSpeaker", "toLowerCase", "replace", "_c2", "Transcript", "transcriptData", "_s", "transcriptRef", "current", "scrollTop", "scrollHeight", "downloadTranscript", "content", "for<PERSON>ach", "entry", "timestamp", "toLocaleTimeString", "speaker", "text", "blob", "Blob", "type", "url", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "length", "ref", "map", "index", "_c3", "AutomaticDiarization", "_s2", "setIsRecording", "isCapturingTab", "setIsCapturingTab", "setCurrentSpeaker", "interimTranscript", "setInterimTranscript", "fullTranscript", "setFullTranscript", "recognitionRef", "audioContextRef", "analyserRef", "mediaStreamRef", "audioDataRef", "interviewer", "voicePrint", "samples", "candidate", "SpeechRecognition", "window", "webkitSpeechRecognition", "alert", "stop", "getTracks", "track", "close", "startRecording", "continuous", "interimResults", "lang", "AudioContext", "webkitAudioContext", "stream", "navigator", "mediaDevices", "getUserMedia", "audio", "setupRecognitionHandlers", "start", "error", "console", "message", "startTabCapture", "getDisplayMedia", "video", "audioTracks", "getAudioTracks", "getVideoTracks", "onended", "stopRecording", "trim", "prev", "Date", "onstart", "on<PERSON>ult", "event", "interim", "final", "i", "resultIndex", "results", "isFinal", "transcript", "onerror", "onend", "_c4", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/interview-ai-app/interviewAI/client/src/pages/live-transcription/AutomaticDiarization.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport './AutomaticDiarization.css';\n\n// Controls component for starting/stopping recording\nfunction Controls({ isRecording, onStart, onStop, onCaptureTab }) {\n  return (\n    <div className=\"transcription-controls\">\n      <button \n        className={`control-button ${isRecording ? 'recording' : ''}`}\n        onClick={isRecording ? onStop : onStart}\n      >\n        {isRecording ? 'Stop Recording' : 'Start Microphone'}\n      </button>\n      <button \n        className=\"control-button tab-audio\"\n        onClick={onCaptureTab}\n        disabled={isRecording}\n      >\n        Capture Tab Audio (Interviewer)\n      </button>\n    </div>\n  );\n}\n\n// Live captions component\nfunction LiveCaptions({ interimText, currentSpeaker }) {\n  if (!interimText) return null;\n  return (\n    <div className={`live-captions ${currentSpeaker.toLowerCase().replace(' ', '-')}`}>\n      <div className=\"speaker-label\">{currentSpeaker}</div>\n      <div className=\"caption-text\">{interimText}</div>\n    </div>\n  );\n}\n\n// Transcript component\nfunction Transcript({ transcriptData }) {\n  const transcriptRef = useRef(null);\n  \n  useEffect(() => {\n    // Auto-scroll to bottom when new entries are added\n    if (transcriptRef.current) {\n      transcriptRef.current.scrollTop = transcriptRef.current.scrollHeight;\n    }\n  }, [transcriptData]);\n  \n  const downloadTranscript = () => {\n    let content = \"Conversation Transcript:\\n\\n\";\n    transcriptData.forEach(entry => {\n      content += `[${entry.timestamp.toLocaleTimeString()}] ${entry.speaker}: ${entry.text}\\n`;\n    });\n    const blob = new Blob([content], { type: 'text/plain' });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = 'transcript.txt';\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n\n  return (\n    <div className=\"transcript-container\">\n      <div className=\"transcript-header\">\n        <h2>Full Transcript</h2>\n        {transcriptData.length > 0 && (\n          <button onClick={downloadTranscript} className=\"download-button\">\n            Download Transcript\n          </button>\n        )}\n      </div>\n      <div className=\"transcript-content\" ref={transcriptRef}>\n        {transcriptData.length === 0 && <p className=\"empty-state\">No speech detected yet.</p>}\n        <div className=\"transcript-entries\">\n          {transcriptData.map((entry, index) => (\n            <div \n              key={index} \n              className={`transcript-entry ${entry.speaker.toLowerCase().replace(' ', '-')}`}\n            >\n              <div className=\"entry-header\">\n                <span className=\"timestamp\">{entry.timestamp.toLocaleTimeString()}</span>\n                <span className=\"speaker\">{entry.speaker}</span>\n              </div>\n              <div className=\"entry-text\">{entry.text}</div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n}\n\n// Main component\nfunction AutomaticDiarization() {\n  const [isRecording, setIsRecording] = useState(false);\n  const [isCapturingTab, setIsCapturingTab] = useState(false);\n  const [currentSpeaker, setCurrentSpeaker] = useState('Interviewer');\n  const [interimTranscript, setInterimTranscript] = useState('');\n  const [fullTranscript, setFullTranscript] = useState([]);\n  \n  // Refs\n  const recognitionRef = useRef(null);\n  const audioContextRef = useRef(null);\n  const analyserRef = useRef(null);\n  const mediaStreamRef = useRef(null);\n  const audioDataRef = useRef({\n    interviewer: {\n      voicePrint: null,\n      samples: []\n    },\n    candidate: {\n      voicePrint: null,\n      samples: []\n    }\n  });\n  \n  // Initialize speech recognition\n  useEffect(() => {\n    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n    if (!SpeechRecognition) {\n      alert('Speech Recognition API not supported in this browser. Try Chrome or Edge.');\n      return;\n    }\n    \n    // Clean up function\n    return () => {\n      if (recognitionRef.current) {\n        recognitionRef.current.stop();\n      }\n      if (mediaStreamRef.current) {\n        mediaStreamRef.current.getTracks().forEach(track => track.stop());\n      }\n      if (audioContextRef.current) {\n        audioContextRef.current.close();\n      }\n    };\n  }, []);\n  \n  // Start microphone recording\n  const startRecording = async () => {\n    try {\n      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n      recognitionRef.current = new SpeechRecognition();\n      recognitionRef.current.continuous = true;\n      recognitionRef.current.interimResults = true;\n      recognitionRef.current.lang = 'en-US';\n      \n      // Set up audio context for voice analysis\n      if (!audioContextRef.current) {\n        // eslint-disable-next-line\n        audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();\n      }\n      \n      // Get microphone stream\n      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });\n      mediaStreamRef.current = stream;\n      \n      // Set default speaker to Candidate for microphone\n      setCurrentSpeaker('Candidate');\n      \n      // Set up recognition handlers\n      setupRecognitionHandlers();\n      \n      // Start recognition\n      recognitionRef.current.start();\n      setIsRecording(true);\n      setIsCapturingTab(false);\n      \n    } catch (error) {\n      console.error(\"Error starting recording:\", error);\n      alert(\"Failed to start recording: \" + error.message);\n    }\n  };\n  \n  // Start tab audio capture\n  const startTabCapture = async () => {\n    try {\n      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n      recognitionRef.current = new SpeechRecognition();\n      recognitionRef.current.continuous = true;\n      recognitionRef.current.interimResults = true;\n      recognitionRef.current.lang = 'en-US';\n      \n      // Request screen sharing with audio\n      const stream = await navigator.mediaDevices.getDisplayMedia({\n        video: true,\n        audio: true\n      });\n      \n      // Check if audio is included\n      const audioTracks = stream.getAudioTracks();\n      if (audioTracks.length === 0) {\n        alert(\"No audio track found. Please make sure to select 'Share audio' when sharing the tab.\");\n        stream.getTracks().forEach(track => track.stop());\n        return;\n      }\n      \n      mediaStreamRef.current = stream;\n      \n      // Set up audio context\n      if (!audioContextRef.current) {\n        // eslint-disable-next-line\n        audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();\n      }\n      \n      // Set default speaker to Interviewer for tab audio\n      setCurrentSpeaker('Interviewer');\n      \n      // Set up recognition handlers\n      setupRecognitionHandlers();\n      \n      // Start recognition\n      recognitionRef.current.start();\n      setIsRecording(true);\n      setIsCapturingTab(true);\n      \n      // Handle stream ending\n      stream.getVideoTracks()[0].onended = () => {\n        stopRecording();\n      };\n      \n    } catch (error) {\n      console.error(\"Error capturing tab audio:\", error);\n      alert(\"Failed to capture tab audio: \" + error.message);\n    }\n  };\n  \n  // Stop recording\n  const stopRecording = () => {\n    if (recognitionRef.current) {\n      recognitionRef.current.stop();\n    }\n    \n    if (mediaStreamRef.current) {\n      mediaStreamRef.current.getTracks().forEach(track => track.stop());\n      mediaStreamRef.current = null;\n    }\n    \n    setIsRecording(false);\n    setIsCapturingTab(false);\n    \n    // Add any remaining interim transcript to the full transcript\n    if (interimTranscript.trim()) {\n      setFullTranscript(prev => [\n        ...prev,\n        { \n          speaker: currentSpeaker, \n          text: interimTranscript.trim(), \n          timestamp: new Date() \n        }\n      ]);\n      setInterimTranscript('');\n    }\n  };\n  \n  // Set up recognition event handlers\n  const setupRecognitionHandlers = () => {\n    if (!recognitionRef.current) return;\n    \n    recognitionRef.current.onstart = () => {\n      setIsRecording(true);\n    };\n    \n    recognitionRef.current.onresult = (event) => {\n      let interim = '';\n      let final = '';\n      \n      for (let i = event.resultIndex; i < event.results.length; ++i) {\n        if (event.results[i].isFinal) {\n          final += event.results[i][0].transcript;\n        } else {\n          interim += event.results[i][0].transcript;\n        }\n      }\n      \n      if (interim) setInterimTranscript(interim);\n      \n      if (final) {\n        // Add to full transcript with current speaker\n        setFullTranscript(prev => [\n          ...prev,\n          { \n            speaker: currentSpeaker, \n            text: final.trim(), \n            timestamp: new Date() \n          }\n        ]);\n        setInterimTranscript('');\n      }\n    };\n    \n    recognitionRef.current.onerror = (event) => {\n      console.error(\"Recognition error:\", event.error);\n      if (event.error !== 'no-speech') {\n        stopRecording();\n      }\n    };\n    \n    recognitionRef.current.onend = () => {\n      setIsRecording(false);\n      // Restart if we're still supposed to be recording\n      if (isRecording) {\n        try {\n          recognitionRef.current.start();\n        } catch (error) {\n          console.error(\"Failed to restart recognition:\", error);\n        }\n      }\n    };\n  };\n  \n  return (\n    <div className=\"automatic-diarization\">\n      <div className=\"page-header\">\n        <h1>Automatic Speaker Diarization</h1>\n        <p className=\"description\">\n          This page automatically identifies speakers based on audio source.\n          Tab audio is identified as \"Interviewer\" and microphone as \"Candidate\".\n        </p>\n      </div>\n      \n      <Controls \n        isRecording={isRecording} \n        onStart={startRecording} \n        onStop={stopRecording}\n        onCaptureTab={startTabCapture}\n      />\n      \n      <div className=\"status-indicator\">\n        {isRecording && (\n          <div className=\"recording-status\">\n            <span className={`status-dot ${isCapturingTab ? 'interviewer' : 'candidate'}`}></span>\n            {isCapturingTab \n              ? 'Capturing Tab Audio (Interviewer)' \n              : 'Recording Microphone (Candidate)'}\n          </div>\n        )}\n      </div>\n      \n      <LiveCaptions \n        interimText={interimTranscript} \n        currentSpeaker={currentSpeaker} \n      />\n      \n      <Transcript transcriptData={fullTranscript} />\n    </div>\n  );\n}\n\nexport default AutomaticDiarization;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,4BAA4B;;AAEnC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,SAASC,QAAQA,CAAC;EAAEC,WAAW;EAAEC,OAAO;EAAEC,MAAM;EAAEC;AAAa,CAAC,EAAE;EAChE,oBACEL,OAAA;IAAKM,SAAS,EAAC,wBAAwB;IAAAC,QAAA,gBACrCP,OAAA;MACEM,SAAS,EAAE,kBAAkBJ,WAAW,GAAG,WAAW,GAAG,EAAE,EAAG;MAC9DM,OAAO,EAAEN,WAAW,GAAGE,MAAM,GAAGD,OAAQ;MAAAI,QAAA,EAEvCL,WAAW,GAAG,gBAAgB,GAAG;IAAkB;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9C,CAAC,eACTZ,OAAA;MACEM,SAAS,EAAC,0BAA0B;MACpCE,OAAO,EAAEH,YAAa;MACtBQ,QAAQ,EAAEX,WAAY;MAAAK,QAAA,EACvB;IAED;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV;;AAEA;AAAAE,EAAA,GApBSb,QAAQ;AAqBjB,SAASc,YAAYA,CAAC;EAAEC,WAAW;EAAEC;AAAe,CAAC,EAAE;EACrD,IAAI,CAACD,WAAW,EAAE,OAAO,IAAI;EAC7B,oBACEhB,OAAA;IAAKM,SAAS,EAAE,iBAAiBW,cAAc,CAACC,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,EAAG;IAAAZ,QAAA,gBAChFP,OAAA;MAAKM,SAAS,EAAC,eAAe;MAAAC,QAAA,EAAEU;IAAc;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACrDZ,OAAA;MAAKM,SAAS,EAAC,cAAc;MAAAC,QAAA,EAAES;IAAW;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC9C,CAAC;AAEV;;AAEA;AAAAQ,GAAA,GAVSL,YAAY;AAWrB,SAASM,UAAUA,CAAC;EAAEC;AAAe,CAAC,EAAE;EAAAC,EAAA;EACtC,MAAMC,aAAa,GAAG1B,MAAM,CAAC,IAAI,CAAC;EAElCD,SAAS,CAAC,MAAM;IACd;IACA,IAAI2B,aAAa,CAACC,OAAO,EAAE;MACzBD,aAAa,CAACC,OAAO,CAACC,SAAS,GAAGF,aAAa,CAACC,OAAO,CAACE,YAAY;IACtE;EACF,CAAC,EAAE,CAACL,cAAc,CAAC,CAAC;EAEpB,MAAMM,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAIC,OAAO,GAAG,8BAA8B;IAC5CP,cAAc,CAACQ,OAAO,CAACC,KAAK,IAAI;MAC9BF,OAAO,IAAI,IAAIE,KAAK,CAACC,SAAS,CAACC,kBAAkB,CAAC,CAAC,KAAKF,KAAK,CAACG,OAAO,KAAKH,KAAK,CAACI,IAAI,IAAI;IAC1F,CAAC,CAAC;IACF,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACR,OAAO,CAAC,EAAE;MAAES,IAAI,EAAE;IAAa,CAAC,CAAC;IACxD,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;IACrC,MAAMM,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACrCF,CAAC,CAACG,IAAI,GAAGN,GAAG;IACZG,CAAC,CAACI,QAAQ,GAAG,gBAAgB;IAC7BH,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,CAAC,CAAC;IAC5BA,CAAC,CAACO,KAAK,CAAC,CAAC;IACTN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,CAAC,CAAC;IAC5BF,GAAG,CAACW,eAAe,CAACZ,GAAG,CAAC;EAC1B,CAAC;EAED,oBACEvC,OAAA;IAAKM,SAAS,EAAC,sBAAsB;IAAAC,QAAA,gBACnCP,OAAA;MAAKM,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCP,OAAA;QAAAO,QAAA,EAAI;MAAe;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EACvBU,cAAc,CAAC8B,MAAM,GAAG,CAAC,iBACxBpD,OAAA;QAAQQ,OAAO,EAAEoB,kBAAmB;QAACtB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAC;MAEjE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eACNZ,OAAA;MAAKM,SAAS,EAAC,oBAAoB;MAAC+C,GAAG,EAAE7B,aAAc;MAAAjB,QAAA,GACpDe,cAAc,CAAC8B,MAAM,KAAK,CAAC,iBAAIpD,OAAA;QAAGM,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAAuB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACtFZ,OAAA;QAAKM,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAChCe,cAAc,CAACgC,GAAG,CAAC,CAACvB,KAAK,EAAEwB,KAAK,kBAC/BvD,OAAA;UAEEM,SAAS,EAAE,oBAAoByB,KAAK,CAACG,OAAO,CAAChB,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,EAAG;UAAAZ,QAAA,gBAE/EP,OAAA;YAAKM,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BP,OAAA;cAAMM,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAEwB,KAAK,CAACC,SAAS,CAACC,kBAAkB,CAAC;YAAC;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACzEZ,OAAA;cAAMM,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAEwB,KAAK,CAACG;YAAO;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,eACNZ,OAAA;YAAKM,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAEwB,KAAK,CAACI;UAAI;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA,GAPzC2C,KAAK;UAAA9C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAQP,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;;AAEA;AAAAW,EAAA,CAzDSF,UAAU;AAAAmC,GAAA,GAAVnC,UAAU;AA0DnB,SAASoC,oBAAoBA,CAAA,EAAG;EAAAC,GAAA;EAC9B,MAAM,CAACxD,WAAW,EAAEyD,cAAc,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACgE,cAAc,EAAEC,iBAAiB,CAAC,GAAGjE,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACqB,cAAc,EAAE6C,iBAAiB,CAAC,GAAGlE,QAAQ,CAAC,aAAa,CAAC;EACnE,MAAM,CAACmE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGpE,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACqE,cAAc,EAAEC,iBAAiB,CAAC,GAAGtE,QAAQ,CAAC,EAAE,CAAC;;EAExD;EACA,MAAMuE,cAAc,GAAGrE,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMsE,eAAe,GAAGtE,MAAM,CAAC,IAAI,CAAC;EACpC,MAAMuE,WAAW,GAAGvE,MAAM,CAAC,IAAI,CAAC;EAChC,MAAMwE,cAAc,GAAGxE,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMyE,YAAY,GAAGzE,MAAM,CAAC;IAC1B0E,WAAW,EAAE;MACXC,UAAU,EAAE,IAAI;MAChBC,OAAO,EAAE;IACX,CAAC;IACDC,SAAS,EAAE;MACTF,UAAU,EAAE,IAAI;MAChBC,OAAO,EAAE;IACX;EACF,CAAC,CAAC;;EAEF;EACA7E,SAAS,CAAC,MAAM;IACd,MAAM+E,iBAAiB,GAAGC,MAAM,CAACD,iBAAiB,IAAIC,MAAM,CAACC,uBAAuB;IACpF,IAAI,CAACF,iBAAiB,EAAE;MACtBG,KAAK,CAAC,2EAA2E,CAAC;MAClF;IACF;;IAEA;IACA,OAAO,MAAM;MACX,IAAIZ,cAAc,CAAC1C,OAAO,EAAE;QAC1B0C,cAAc,CAAC1C,OAAO,CAACuD,IAAI,CAAC,CAAC;MAC/B;MACA,IAAIV,cAAc,CAAC7C,OAAO,EAAE;QAC1B6C,cAAc,CAAC7C,OAAO,CAACwD,SAAS,CAAC,CAAC,CAACnD,OAAO,CAACoD,KAAK,IAAIA,KAAK,CAACF,IAAI,CAAC,CAAC,CAAC;MACnE;MACA,IAAIZ,eAAe,CAAC3C,OAAO,EAAE;QAC3B2C,eAAe,CAAC3C,OAAO,CAAC0D,KAAK,CAAC,CAAC;MACjC;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,MAAMR,iBAAiB,GAAGC,MAAM,CAACD,iBAAiB,IAAIC,MAAM,CAACC,uBAAuB;MACpFX,cAAc,CAAC1C,OAAO,GAAG,IAAImD,iBAAiB,CAAC,CAAC;MAChDT,cAAc,CAAC1C,OAAO,CAAC4D,UAAU,GAAG,IAAI;MACxClB,cAAc,CAAC1C,OAAO,CAAC6D,cAAc,GAAG,IAAI;MAC5CnB,cAAc,CAAC1C,OAAO,CAAC8D,IAAI,GAAG,OAAO;;MAErC;MACA,IAAI,CAACnB,eAAe,CAAC3C,OAAO,EAAE;QAC5B;QACA2C,eAAe,CAAC3C,OAAO,GAAG,KAAKoD,MAAM,CAACW,YAAY,IAAIX,MAAM,CAACY,kBAAkB,EAAE,CAAC;MACpF;;MAEA;MACA,MAAMC,MAAM,GAAG,MAAMC,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;QAAEC,KAAK,EAAE;MAAK,CAAC,CAAC;MACzExB,cAAc,CAAC7C,OAAO,GAAGiE,MAAM;;MAE/B;MACA5B,iBAAiB,CAAC,WAAW,CAAC;;MAE9B;MACAiC,wBAAwB,CAAC,CAAC;;MAE1B;MACA5B,cAAc,CAAC1C,OAAO,CAACuE,KAAK,CAAC,CAAC;MAC9BrC,cAAc,CAAC,IAAI,CAAC;MACpBE,iBAAiB,CAAC,KAAK,CAAC;IAE1B,CAAC,CAAC,OAAOoC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDlB,KAAK,CAAC,6BAA6B,GAAGkB,KAAK,CAACE,OAAO,CAAC;IACtD;EACF,CAAC;;EAED;EACA,MAAMC,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMxB,iBAAiB,GAAGC,MAAM,CAACD,iBAAiB,IAAIC,MAAM,CAACC,uBAAuB;MACpFX,cAAc,CAAC1C,OAAO,GAAG,IAAImD,iBAAiB,CAAC,CAAC;MAChDT,cAAc,CAAC1C,OAAO,CAAC4D,UAAU,GAAG,IAAI;MACxClB,cAAc,CAAC1C,OAAO,CAAC6D,cAAc,GAAG,IAAI;MAC5CnB,cAAc,CAAC1C,OAAO,CAAC8D,IAAI,GAAG,OAAO;;MAErC;MACA,MAAMG,MAAM,GAAG,MAAMC,SAAS,CAACC,YAAY,CAACS,eAAe,CAAC;QAC1DC,KAAK,EAAE,IAAI;QACXR,KAAK,EAAE;MACT,CAAC,CAAC;;MAEF;MACA,MAAMS,WAAW,GAAGb,MAAM,CAACc,cAAc,CAAC,CAAC;MAC3C,IAAID,WAAW,CAACnD,MAAM,KAAK,CAAC,EAAE;QAC5B2B,KAAK,CAAC,sFAAsF,CAAC;QAC7FW,MAAM,CAACT,SAAS,CAAC,CAAC,CAACnD,OAAO,CAACoD,KAAK,IAAIA,KAAK,CAACF,IAAI,CAAC,CAAC,CAAC;QACjD;MACF;MAEAV,cAAc,CAAC7C,OAAO,GAAGiE,MAAM;;MAE/B;MACA,IAAI,CAACtB,eAAe,CAAC3C,OAAO,EAAE;QAC5B;QACA2C,eAAe,CAAC3C,OAAO,GAAG,KAAKoD,MAAM,CAACW,YAAY,IAAIX,MAAM,CAACY,kBAAkB,EAAE,CAAC;MACpF;;MAEA;MACA3B,iBAAiB,CAAC,aAAa,CAAC;;MAEhC;MACAiC,wBAAwB,CAAC,CAAC;;MAE1B;MACA5B,cAAc,CAAC1C,OAAO,CAACuE,KAAK,CAAC,CAAC;MAC9BrC,cAAc,CAAC,IAAI,CAAC;MACpBE,iBAAiB,CAAC,IAAI,CAAC;;MAEvB;MACA6B,MAAM,CAACe,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,OAAO,GAAG,MAAM;QACzCC,aAAa,CAAC,CAAC;MACjB,CAAC;IAEH,CAAC,CAAC,OAAOV,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDlB,KAAK,CAAC,+BAA+B,GAAGkB,KAAK,CAACE,OAAO,CAAC;IACxD;EACF,CAAC;;EAED;EACA,MAAMQ,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIxC,cAAc,CAAC1C,OAAO,EAAE;MAC1B0C,cAAc,CAAC1C,OAAO,CAACuD,IAAI,CAAC,CAAC;IAC/B;IAEA,IAAIV,cAAc,CAAC7C,OAAO,EAAE;MAC1B6C,cAAc,CAAC7C,OAAO,CAACwD,SAAS,CAAC,CAAC,CAACnD,OAAO,CAACoD,KAAK,IAAIA,KAAK,CAACF,IAAI,CAAC,CAAC,CAAC;MACjEV,cAAc,CAAC7C,OAAO,GAAG,IAAI;IAC/B;IAEAkC,cAAc,CAAC,KAAK,CAAC;IACrBE,iBAAiB,CAAC,KAAK,CAAC;;IAExB;IACA,IAAIE,iBAAiB,CAAC6C,IAAI,CAAC,CAAC,EAAE;MAC5B1C,iBAAiB,CAAC2C,IAAI,IAAI,CACxB,GAAGA,IAAI,EACP;QACE3E,OAAO,EAAEjB,cAAc;QACvBkB,IAAI,EAAE4B,iBAAiB,CAAC6C,IAAI,CAAC,CAAC;QAC9B5E,SAAS,EAAE,IAAI8E,IAAI,CAAC;MACtB,CAAC,CACF,CAAC;MACF9C,oBAAoB,CAAC,EAAE,CAAC;IAC1B;EACF,CAAC;;EAED;EACA,MAAM+B,wBAAwB,GAAGA,CAAA,KAAM;IACrC,IAAI,CAAC5B,cAAc,CAAC1C,OAAO,EAAE;IAE7B0C,cAAc,CAAC1C,OAAO,CAACsF,OAAO,GAAG,MAAM;MACrCpD,cAAc,CAAC,IAAI,CAAC;IACtB,CAAC;IAEDQ,cAAc,CAAC1C,OAAO,CAACuF,QAAQ,GAAIC,KAAK,IAAK;MAC3C,IAAIC,OAAO,GAAG,EAAE;MAChB,IAAIC,KAAK,GAAG,EAAE;MAEd,KAAK,IAAIC,CAAC,GAAGH,KAAK,CAACI,WAAW,EAAED,CAAC,GAAGH,KAAK,CAACK,OAAO,CAAClE,MAAM,EAAE,EAAEgE,CAAC,EAAE;QAC7D,IAAIH,KAAK,CAACK,OAAO,CAACF,CAAC,CAAC,CAACG,OAAO,EAAE;UAC5BJ,KAAK,IAAIF,KAAK,CAACK,OAAO,CAACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAACI,UAAU;QACzC,CAAC,MAAM;UACLN,OAAO,IAAID,KAAK,CAACK,OAAO,CAACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAACI,UAAU;QAC3C;MACF;MAEA,IAAIN,OAAO,EAAElD,oBAAoB,CAACkD,OAAO,CAAC;MAE1C,IAAIC,KAAK,EAAE;QACT;QACAjD,iBAAiB,CAAC2C,IAAI,IAAI,CACxB,GAAGA,IAAI,EACP;UACE3E,OAAO,EAAEjB,cAAc;UACvBkB,IAAI,EAAEgF,KAAK,CAACP,IAAI,CAAC,CAAC;UAClB5E,SAAS,EAAE,IAAI8E,IAAI,CAAC;QACtB,CAAC,CACF,CAAC;QACF9C,oBAAoB,CAAC,EAAE,CAAC;MAC1B;IACF,CAAC;IAEDG,cAAc,CAAC1C,OAAO,CAACgG,OAAO,GAAIR,KAAK,IAAK;MAC1Cf,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEgB,KAAK,CAAChB,KAAK,CAAC;MAChD,IAAIgB,KAAK,CAAChB,KAAK,KAAK,WAAW,EAAE;QAC/BU,aAAa,CAAC,CAAC;MACjB;IACF,CAAC;IAEDxC,cAAc,CAAC1C,OAAO,CAACiG,KAAK,GAAG,MAAM;MACnC/D,cAAc,CAAC,KAAK,CAAC;MACrB;MACA,IAAIzD,WAAW,EAAE;QACf,IAAI;UACFiE,cAAc,CAAC1C,OAAO,CAACuE,KAAK,CAAC,CAAC;QAChC,CAAC,CAAC,OAAOC,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACxD;MACF;IACF,CAAC;EACH,CAAC;EAED,oBACEjG,OAAA;IAAKM,SAAS,EAAC,uBAAuB;IAAAC,QAAA,gBACpCP,OAAA;MAAKM,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BP,OAAA;QAAAO,QAAA,EAAI;MAA6B;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtCZ,OAAA;QAAGM,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAG3B;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAENZ,OAAA,CAACC,QAAQ;MACPC,WAAW,EAAEA,WAAY;MACzBC,OAAO,EAAEiF,cAAe;MACxBhF,MAAM,EAAEuG,aAAc;MACtBtG,YAAY,EAAE+F;IAAgB;MAAA3F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC,eAEFZ,OAAA;MAAKM,SAAS,EAAC,kBAAkB;MAAAC,QAAA,EAC9BL,WAAW,iBACVF,OAAA;QAAKM,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BP,OAAA;UAAMM,SAAS,EAAE,cAAcsD,cAAc,GAAG,aAAa,GAAG,WAAW;QAAG;UAAAnD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACrFgD,cAAc,GACX,mCAAmC,GACnC,kCAAkC;MAAA;QAAAnD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAENZ,OAAA,CAACe,YAAY;MACXC,WAAW,EAAE+C,iBAAkB;MAC/B9C,cAAc,EAAEA;IAAe;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC,CAAC,eAEFZ,OAAA,CAACqB,UAAU;MAACC,cAAc,EAAE2C;IAAe;MAAAxD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC3C,CAAC;AAEV;AAAC8C,GAAA,CA9PQD,oBAAoB;AAAAkE,GAAA,GAApBlE,oBAAoB;AAgQ7B,eAAeA,oBAAoB;AAAC,IAAA3C,EAAA,EAAAM,GAAA,EAAAoC,GAAA,EAAAmE,GAAA;AAAAC,YAAA,CAAA9G,EAAA;AAAA8G,YAAA,CAAAxG,GAAA;AAAAwG,YAAA,CAAApE,GAAA;AAAAoE,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}