import React, { useEffect, useRef, useState, useCallback } from 'react';
import MicRecorder from 'mic-recorder-to-mp3';
import './DeepgramLiveTranscript.css';
import env from '../utils/env';
import CloseIcon from '@mui/icons-material/Close';
import MicIcon from '@mui/icons-material/Mic';
import StopIcon from '@mui/icons-material/Stop';
import SendIcon from '@mui/icons-material/Send';
import DeleteIcon from '@mui/icons-material/Delete';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import Button from '@mui/material/Button';
import CircularProgress from '@mui/material/CircularProgress';
import Alert from '@mui/material/Alert';
import Snackbar from '@mui/material/Snackbar';

const DeepgramLiveTranscript = ({ onBack }) => {
  // State for transcription
  const [transcripts, setTranscripts] = useState([]);
  const [isRecording, setIsRecording] = useState(false);
  const [apiKeyStatus, setApiKeyStatus] = useState('unknown'); // 'unknown', 'valid', 'invalid'
  const [apiKeyError, setApiKeyError] = useState('');
  const [showApiKeyError, setShowApiKeyError] = useState(false);

  // State for GPT integration
  const [gptResponse, setGptResponse] = useState('');
  const [isLoadingGpt, setIsLoadingGpt] = useState(false);
  const [selectedText, setSelectedText] = useState('');

  // Session timer state (15 minutes = 900 seconds)
  const SESSION_DURATION = 900; // 15 minutes in seconds
  const WARNING_TIME = 60; // Show warning when 60 seconds remain
  const [sessionTimeRemaining, setSessionTimeRemaining] = useState(SESSION_DURATION);
  const [showPaymentDialog, setShowPaymentDialog] = useState(false);
  const [sessionExpired, setSessionExpired] = useState(false);

  // Refs
  const wsRef = useRef(null);
  const recorder = useRef(new MicRecorder({ bitRate: 128 }));
  const intervalRef = useRef(null);
  const sessionTimerRef = useRef(null);
  const transcriptContainerRef = useRef(null);

  // Format session time remaining (MM:SS)
  const formatSessionTime = useCallback((totalSeconds) => {
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }, []);

  // Start the session timer
  const startSessionTimer = useCallback(() => {
    // Reset session timer when starting
    setSessionTimeRemaining(SESSION_DURATION);
    setSessionExpired(false);

    // Clear any existing interval
    if (sessionTimerRef.current) {
      clearInterval(sessionTimerRef.current);
    }

    // Start a new interval that counts down
    sessionTimerRef.current = setInterval(() => {
      setSessionTimeRemaining(prev => {
        const newTime = prev - 1;

        // Show payment dialog when 1 minute remains
        if (newTime === WARNING_TIME) {
          setShowPaymentDialog(true);
        }

        // Session expired
        if (newTime <= 0) {
          clearInterval(sessionTimerRef.current);
          sessionTimerRef.current = null;
          setSessionExpired(true);
          stopRecording();
          return 0;
        }

        return newTime;
      });
    }, 1000);
  }, [SESSION_DURATION, WARNING_TIME]);

  // Handle payment dialog close
  const handlePaymentDialogClose = useCallback(() => {
    setShowPaymentDialog(false);
  }, []);

  // Handle payment confirmation
  const handlePaymentConfirm = useCallback(() => {
    // Reset the session timer
    setShowPaymentDialog(false);
    setSessionTimeRemaining(SESSION_DURATION);
    setSessionExpired(false);

    // Restart the session timer
    if (sessionTimerRef.current) {
      clearInterval(sessionTimerRef.current);
    }

    sessionTimerRef.current = setInterval(() => {
      setSessionTimeRemaining(prev => {
        const newTime = prev - 1;

        // Show payment dialog when 1 minute remains
        if (newTime === WARNING_TIME) {
          setShowPaymentDialog(true);
        }

        // Session expired
        if (newTime <= 0) {
          clearInterval(sessionTimerRef.current);
          sessionTimerRef.current = null;
          setSessionExpired(true);
          stopRecording();
          return 0;
        }

        return newTime;
      });
    }, 1000);
  }, [SESSION_DURATION, WARNING_TIME]);

  // Start session timer on component mount
  useEffect(() => {
    startSessionTimer();

    // Clean up session timer on unmount
    return () => {
      if (sessionTimerRef.current) {
        clearInterval(sessionTimerRef.current);
      }
    };
  }, [startSessionTimer]);

  // Verify Deepgram API key
  const verifyDeepgramApiKey = useCallback(async () => {
    try {
      // Get API key from environment variables
      const DEEPGRAM_API_KEY = env.DEEPGRAM_API_KEY;

      if (!DEEPGRAM_API_KEY) {
        setApiKeyStatus('invalid');
        setApiKeyError('Deepgram API key is missing. Please add it to your environment variables as REACT_APP_DEEPGRAM_API_KEY.');
        setShowApiKeyError(true);
        return false;
      }

      // Test the API key with a simple request
      const response = await fetch('https://api.deepgram.com/v1/projects', {
        method: 'GET',
        headers: {
          'Authorization': `Token ${DEEPGRAM_API_KEY}`
        }
      });

      if (response.ok) {
        setApiKeyStatus('valid');
        return true;
      } else {
        setApiKeyStatus('invalid');
        setApiKeyError('Invalid Deepgram API key. Please check your API key and try again.');
        setShowApiKeyError(true);
        return false;
      }
    } catch (error) {
      console.error('Error verifying Deepgram API key:', error);
      setApiKeyStatus('invalid');
      setApiKeyError('Error verifying Deepgram API key: ' + error.message);
      setShowApiKeyError(true);
      return false;
    }
  }, []);

  // Start recording
  const startRecording = useCallback(async () => {
    if (sessionExpired) return;

    // Verify API key first
    const isApiKeyValid = await verifyDeepgramApiKey();
    if (!isApiKeyValid) return;

    try {
      const DEEPGRAM_API_KEY = env.DEEPGRAM_API_KEY;

      // Create WebSocket connection
      const socket = new WebSocket(`wss://api.deepgram.com/v1/listen?punctuate=true&diarize=true`);

      // Set up authorization header
      socket.onopen = async () => {
        console.log('WebSocket connected');

        // Send authorization header
        socket.send(JSON.stringify({
          authorization: DEEPGRAM_API_KEY
        }));

        // Start recording
        await recorder.current.start();
        setIsRecording(true);

        // Send audio data to Deepgram
        intervalRef.current = setInterval(async () => {
          try {
            const [buffer, blob] = await recorder.current.getMp3();
            const reader = new FileReader();
            reader.readAsArrayBuffer(blob);
            reader.onloadend = () => {
              if (socket.readyState === WebSocket.OPEN) {
                socket.send(reader.result);
              }
            };
          } catch (error) {
            console.error('Error getting MP3 data:', error);
          }
        }, 1000);

        wsRef.current = socket;
      };

      // Handle incoming messages
      socket.onmessage = (message) => {
        try {
          const data = JSON.parse(message.data);
          const words = data.channel?.alternatives[0]?.words || [];

          if (words.length === 0) return;

          const groupedBySpeaker = words.reduce((acc, word) => {
            if (!word.speaker) return acc;
            const lastGroup = acc[acc.length - 1];

            if (lastGroup && lastGroup.speaker === word.speaker) {
              lastGroup.words.push(word.word);
            } else {
              acc.push({
                speaker: word.speaker,
                words: [word.word],
                timestamp: new Date().toISOString()
              });
            }

            return acc;
          }, []);

          if (groupedBySpeaker.length > 0) {
            setTranscripts(prev => {
              // Merge with existing transcripts if the speaker is the same
              const newTranscripts = [...prev];

              groupedBySpeaker.forEach(group => {
                const lastGroup = newTranscripts[newTranscripts.length - 1];

                if (lastGroup && lastGroup.speaker === group.speaker) {
                  // Merge with the last group
                  lastGroup.words = [...lastGroup.words, ...group.words];
                } else {
                  // Add as a new group
                  newTranscripts.push(group);
                }
              });

              return newTranscripts;
            });

            // Auto-scroll to the bottom
            if (transcriptContainerRef.current) {
              transcriptContainerRef.current.scrollTop = transcriptContainerRef.current.scrollHeight;
            }
          }
        } catch (error) {
          console.error('Error parsing message:', error);
        }
      };

      // Handle errors
      socket.onerror = (error) => {
        console.error('WebSocket error:', error);
        setApiKeyError('WebSocket error: ' + error.message);
        setShowApiKeyError(true);
      };

      // Handle connection close
      socket.onclose = () => {
        console.log('WebSocket closed');
        stopRecording();
      };
    } catch (error) {
      console.error('Error starting recording:', error);
      setApiKeyError('Error starting recording: ' + error.message);
      setShowApiKeyError(true);
    }
  }, [sessionExpired, verifyDeepgramApiKey]);

  // Stop recording
  const stopRecording = useCallback(() => {
    setIsRecording(false);

    // Stop the recorder
    if (recorder.current) {
      recorder.current.stop();
    }

    // Clear the interval
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }

    // Close the WebSocket
    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }
  }, []);

  // Clean up on unmount
  useEffect(() => {
    return () => {
      stopRecording();

      if (sessionTimerRef.current) {
        clearInterval(sessionTimerRef.current);
      }
    };
  }, [stopRecording]);

  // Handle text selection for GPT
  const handleTextSelection = useCallback(() => {
    const selection = window.getSelection();
    const selectedText = selection.toString().trim();

    if (selectedText) {
      setSelectedText(selectedText);
    }
  }, []);

  // Send selected text to GPT
  const sendToGPT = useCallback(async () => {
    if (!selectedText || isLoadingGpt || sessionExpired) return;

    const apiKey = env.OPENAI_API_KEY;

    if (!apiKey) {
      setApiKeyError('OpenAI API key is missing. Please add it to your environment variables as REACT_APP_OPENAI_API_KEY.');
      setShowApiKeyError(true);
      return;
    }

    setIsLoadingGpt(true);

    try {
      const response = await fetch("https://api.openai.com/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${apiKey}`
        },
        body: JSON.stringify({
          model: "gpt-4.1-nano",
          messages: [
            {
              role: "system",
              content: "You are an AI assistant helping with interview transcripts. Provide concise, helpful responses."
            },
            {
              role: "user",
              content: `Please analyze this interview transcript and provide insights or suggestions: "${selectedText}"`
            }
          ],
          stream: true
        })
      });

      if (!response.ok) {
        throw new Error(`API error: ${response.status} ${response.statusText}`);
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder("utf-8");
      let result = "";

      while (true) {
        const { value, done } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value, { stream: true });
        const lines = chunk.split("\n").filter(line => line.trim().startsWith("data:"));

        for (const line of lines) {
          const data = line.replace(/^data: /, '');
          if (data === "[DONE]") break;

          try {
            const json = JSON.parse(data);
            const content = json.choices?.[0]?.delta?.content;
            if (content) {
              result += content;
              setGptResponse(result);
            }
          } catch (e) {
            console.error("Error parsing JSON:", e);
          }
        }
      }
    } catch (error) {
      console.error("GPT API Error:", error);
      setGptResponse("Error: " + error.message);
    } finally {
      setIsLoadingGpt(false);
    }
  }, [selectedText, isLoadingGpt, sessionExpired]);

  // Clear transcripts
  const clearTranscripts = useCallback(() => {
    setTranscripts([]);
    setGptResponse('');
    setSelectedText('');
  }, []);

  // Clear GPT response
  const clearGptResponse = useCallback(() => {
    setGptResponse('');
    setSelectedText('');
  }, []);

  // Handle exit button click
  const handleExit = useCallback(() => {
    stopRecording();
    if (onBack) onBack();
  }, [stopRecording, onBack]);

  // Handle API key error close
  const handleApiKeyErrorClose = useCallback(() => {
    setShowApiKeyError(false);
  }, []);

  // Get full transcript text
  const getFullTranscript = useCallback(() => {
    return transcripts.map(segment =>
      `Speaker ${segment.speaker}: ${segment.words.join(' ')}`
    ).join('\n');
  }, [transcripts]);

  // Download transcript
  const downloadTranscript = useCallback(() => {
    const fullText = getFullTranscript();
    if (!fullText) return;

    const blob = new Blob([fullText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `transcript-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [getFullTranscript]);

  return (
    <div className="deepgram-transcript-container">
      {/* Exit button in top right corner */}
      <button className="exit-button" onClick={handleExit} title="Exit">
        <CloseIcon />
      </button>

      {/* Session timer display at the top */}
      <div className="timer-container">
        <AccessTimeIcon className="timer-icon" />
        <span className="timer-display session-timer">
          Session: {formatSessionTime(sessionTimeRemaining)}
        </span>
        {isRecording && <span className="listening-indicator">Recording...</span>}
      </div>

      <h2 className="deepgram-transcript-title">Live Transcript with Speaker Diarization</h2>

      <div className="deepgram-controls">
        <button
          className={`record-button ${isRecording ? 'recording' : ''}`}
          onClick={isRecording ? stopRecording : startRecording}
          disabled={sessionExpired || apiKeyStatus === 'invalid'}
          title={isRecording ? "Stop Recording" : "Start Recording"}
        >
          {isRecording ? <StopIcon /> : <MicIcon />}
          {isRecording ? "Stop Recording" : "Start Recording"}
        </button>

        <button
          className="clear-button"
          onClick={clearTranscripts}
          disabled={transcripts.length === 0 || sessionExpired}
          title="Clear Transcripts"
        >
          <DeleteIcon fontSize="small" />
          Clear
        </button>

        <button
          className="download-button"
          onClick={downloadTranscript}
          disabled={transcripts.length === 0 || sessionExpired}
          title="Download Transcript"
        >
          Download
        </button>
      </div>

      <div className="deepgram-content">
        <div className="transcript-section">
          <div
            className="transcript-container"
            ref={transcriptContainerRef}
            onMouseUp={handleTextSelection}
          >
            {transcripts.length === 0 ? (
              <div className="empty-transcript">
                {apiKeyStatus === 'invalid' ? (
                  <p>API key validation failed. Please check your Deepgram API key.</p>
                ) : (
                  <p>No transcripts yet. Click "Start Recording" to begin.</p>
                )}
              </div>
            ) : (
              transcripts.map((segment, index) => (
                <div className="deepgram-segment" key={index}>
                  <span className="deepgram-speaker">Speaker {segment.speaker}:</span>
                  <span>{segment.words.join(' ')}</span>
                </div>
              ))
            )}
          </div>

          {selectedText && (
            <div className="selected-text-container">
              <div className="selected-text-header">
                <h3>Selected Text</h3>
                <button
                  className="send-to-gpt-button"
                  onClick={sendToGPT}
                  disabled={!selectedText || isLoadingGpt || sessionExpired}
                  title="Analyze with GPT"
                >
                  {isLoadingGpt ? <CircularProgress size={20} /> : <SendIcon />}
                  Analyze with GPT
                </button>
              </div>
              <div className="selected-text">{selectedText}</div>
            </div>
          )}
        </div>

        {(gptResponse || isLoadingGpt) && (
          <div className="gpt-response-section">
            <div className="gpt-response-header">
              <h3>AI Analysis</h3>
              <button
                className="clear-button"
                onClick={clearGptResponse}
                disabled={!gptResponse && !isLoadingGpt}
                title="Clear Analysis"
              >
                <DeleteIcon fontSize="small" />
                Clear
              </button>
            </div>
            <div className="gpt-response">
              {isLoadingGpt && !gptResponse ? (
                <div className="loading-container">
                  <CircularProgress size={30} />
                  <p>Analyzing transcript...</p>
                </div>
              ) : (
                gptResponse
              )}
            </div>
          </div>
        )}
      </div>

      {/* Payment Dialog */}
      <Dialog
        open={showPaymentDialog}
        onClose={handlePaymentDialogClose}
        aria-labelledby="payment-dialog-title"
        aria-describedby="payment-dialog-description"
      >
        <DialogTitle id="payment-dialog-title">
          {"Session Expiring Soon"}
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="payment-dialog-description">
            Your session will expire in one minute. Would you like to make a payment to extend your session?
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handlePaymentDialogClose} color="primary">
            Not Now
          </Button>
          <Button onClick={handlePaymentConfirm} color="primary" autoFocus>
            Make Payment
          </Button>
        </DialogActions>
      </Dialog>

      {/* Session Expired Dialog */}
      <Dialog
        open={sessionExpired}
        aria-labelledby="expired-dialog-title"
        aria-describedby="expired-dialog-description"
      >
        <DialogTitle id="expired-dialog-title">
          {"Session Expired"}
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="expired-dialog-description">
            Your session has expired. Please make a payment to continue using the service.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleExit} color="primary">
            Exit
          </Button>
          <Button onClick={handlePaymentConfirm} color="primary" autoFocus>
            Make Payment
          </Button>
        </DialogActions>
      </Dialog>

      {/* API Key Error Snackbar */}
      <Snackbar
        open={showApiKeyError}
        autoHideDuration={6000}
        onClose={handleApiKeyErrorClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleApiKeyErrorClose} severity="error" sx={{ width: '100%' }}>
          {apiKeyError}
        </Alert>
      </Snackbar>
    </div>
  );
};

export default DeepgramLiveTranscript;
