import React, { useEffect, useRef, useState } from 'react';
import MicRecorder from 'mic-recorder-to-mp3';
import './DeepgramLiveTranscript.css';

const DeepgramLiveTranscript = () => {
  const [transcripts, setTranscripts] = useState([]);
  const wsRef = useRef(null);
  const recorder = useRef(new MicRecorder({ bitRate: 128 }));
  const intervalRef = useRef(null);

  useEffect(() => {
    const DEEPGRAM_API_KEY = '****************************************'; // <--- Replace with your key
    const socket = new WebSocket(`wss://api.deepgram.com/v1/listen?punctuate=true&diarize=true`);

    socket.onopen = async () => {
      console.log('WebSocket connected');

      await recorder.current.start();

      intervalRef.current = setInterval(async () => {
        const [buffer, blob] = await recorder.current.getMp3();
        const reader = new FileReader();
        reader.readAsArrayBuffer(blob);
        reader.onloadend = () => {
          if (socket.readyState === WebSocket.OPEN) {
            socket.send(reader.result);
          }
        };
      }, 1000);

      wsRef.current = socket;
    };

    socket.onmessage = (message) => {
      const data = JSON.parse(message.data);
      const words = data.channel?.alternatives[0]?.words || [];

      const groupedBySpeaker = words.reduce((acc, word) => {
        if (!word.speaker) return acc;
        const lastGroup = acc[acc.length - 1];

        if (lastGroup && lastGroup.speaker === word.speaker) {
          lastGroup.words.push(word.word);
        } else {
          acc.push({ speaker: word.speaker, words: [word.word] });
        }

        return acc;
      }, []);

      setTranscripts(groupedBySpeaker);
    };

    socket.onerror = console.error;

    return () => {
      socket.close();
      recorder.current.stop();
      clearInterval(intervalRef.current);
    };
  }, []);

  return (
    <div className="deepgram-transcript-container">
      <h2 className="deepgram-transcript-title">Live Transcript with Speaker Diarization</h2>
      {transcripts.map((segment, index) => (
        <div className="deepgram-segment" key={index}>
          <span className="deepgram-speaker">Speaker {segment.speaker}:</span>
          <span>{segment.words.join(' ')}</span>
        </div>
      ))}
    </div>
  );
};

export default DeepgramLiveTranscript;
