import React, { useState } from 'react';
import AutomatedInterview from '../components/AutomatedInterview';
import SimpleTranscriber from '../components/SimpleTranscriber';
import './AutomatedInterviewPage.css';

function AutomatedInterviewPage() {
  const [showSimpleTranscriber, setShowSimpleTranscriber] = useState(true);
  const [transcript, setTranscript] = useState('');

  const handleTranscriptChange = (newTranscript) => {
    setTranscript(newTranscript);
    console.log("Transcript updated:", newTranscript);
  };

  return (
    <div className="automated-interview-page">
      <div className="interview-header">
        <h1>Automated Interview</h1>
        <div className="toggle-container">
          <label className="toggle-label">
            <input
              type="checkbox"
              checked={showSimpleTranscriber}
              onChange={() => setShowSimpleTranscriber(!showSimpleTranscriber)}
            />
            Show Simple Transcriber
          </label>
        </div>
      </div>

      {showSimpleTranscriber && (
        <div className="simple-transcriber-container">
          <h2>Simple Transcriber</h2>
          <p className="transcriber-info">
            This is a simplified transcriber that should work in most browsers.
            Use this if you're having trouble with the main interview interface.
          </p>
          <SimpleTranscriber onTranscriptChange={handleTranscriptChange} />

          {transcript && (
            <div className="transcript-preview">
              <h3>Current Transcript:</h3>
              <div className="transcript-content">{transcript}</div>
            </div>
          )}
        </div>
      )}

      <AutomatedInterview />
    </div>
  );
}

export default AutomatedInterviewPage;
