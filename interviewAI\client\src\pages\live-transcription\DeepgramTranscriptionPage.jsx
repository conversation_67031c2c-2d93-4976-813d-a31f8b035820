import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import DeepgramLiveTranscript from '../../components/DeepgramLiveTranscript';
import Header from '../../components/Header';
import './TranscriptionPage.css';
import env from '../../utils/env';

function DeepgramTranscriptionPage() {
  const navigate = useNavigate();
  const [apiKeyStatus, setApiKeyStatus] = useState('checking'); // 'checking', 'valid', 'invalid'
  const [showHeader, setShowHeader] = useState(true);

  // Check if API keys are configured
  useEffect(() => {
    const checkApiKeys = async () => {
      // Check if Deepgram API key is configured
      const hasDeepgramKey = env.hasDeepgramAPI();

      // If no key is configured, use a sample key for development
      if (!hasDeepgramKey && env.isDevelopment) {
        // This is just for development purposes
        env.DEEPGRAM_API_KEY = env.getSampleDeepgramKey();

        // Even with the sample key, we'll set status to 'ready' rather than 'valid'
        // The actual validation will happen when the user starts the transcription
        setApiKeyStatus('ready');
      } else if (hasDeepgramKey) {
        // We have a key, but we'll validate it when the user starts the transcription
        setApiKeyStatus('ready');
      } else {
        setApiKeyStatus('invalid');
      }
    };

    checkApiKeys();
  }, []);

  // Handle back button click
  const handleBack = () => {
    setShowHeader(true);
    navigate('/home');
  };

  // Handle start button click
  const handleStart = () => {
    setShowHeader(false);
  };

  return (
    <div className="transcription-page">
      {showHeader && <Header />}

      {showHeader ? (
        <div className="transcription-intro">
          <h1>Deepgram Live Transcription with Speaker Diarization</h1>

          <div className="feature-badges">
            <span className="feature-badge">Automatic Speaker Detection</span>
            <span className="feature-badge highlight">Real-time Live Transcription</span>
            <span className="feature-badge">Voice-Based Identification</span>
            <span className="feature-badge">GPT Analysis Integration</span>
          </div>

          <div className="intro-description">
            <p>
              This feature uses Deepgram's API to automatically identify speakers and transcribe conversations in real-time.
              The advanced speaker diarization technology can distinguish between different speakers without manual switching.
            </p>

            <h3>Key Features:</h3>
            <ul>
              <li><strong>Automatic Speaker Identification:</strong> Automatically detects and labels different speakers</li>
              <li><strong>Real-time Transcription:</strong> See the transcript as people speak</li>
              <li><strong>GPT Analysis:</strong> Select any part of the transcript to get AI analysis</li>
              <li><strong>Download Transcripts:</strong> Save your transcriptions for later reference</li>
            </ul>

            {apiKeyStatus === 'checking' && (
              <div className="api-status checking">
                <p>Checking API configuration...</p>
              </div>
            )}

            {apiKeyStatus === 'ready' && (
              <div className="api-status ready">
                <p>
                  <strong>API Key Found:</strong> Deepgram API key is configured. The key will be validated when you start the transcription.
                </p>
              </div>
            )}

            {apiKeyStatus === 'invalid' && (
              <div className="api-status invalid">
                <p>
                  <strong>API Key Missing:</strong> To use this feature, you need to configure a Deepgram API key.
                  Add your key to the environment variables as REACT_APP_DEEPGRAM_API_KEY.
                </p>
              </div>
            )}
          </div>

          <div className="action-buttons">
            <button
              className="start-button"
              onClick={handleStart}
              disabled={apiKeyStatus === 'checking' || apiKeyStatus === 'invalid'}
            >
              {apiKeyStatus === 'checking' ? 'Checking API Key...' : 'Start Transcription'}
            </button>

            <button
              className="back-button"
              onClick={handleBack}
            >
              Back to Home
            </button>
          </div>
        </div>
      ) : (
        <DeepgramLiveTranscript onBack={handleBack} />
      )}
    </div>
  );
}

export default DeepgramTranscriptionPage;
