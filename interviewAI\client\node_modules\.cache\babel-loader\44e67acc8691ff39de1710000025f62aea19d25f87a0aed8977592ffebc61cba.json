{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\interview-ai-app\\\\interviewAI\\\\client\\\\src\\\\pages\\\\live-transcription\\\\DeepgramTranscriptionPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport DeepgramLiveTranscript from '../../components/DeepgramLiveTranscript';\nimport Header from '../../components/Header';\nimport './TranscriptionPage.css';\nimport env from '../../utils/env';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction DeepgramTranscriptionPage() {\n  _s();\n  const navigate = useNavigate();\n  const [apiKeyStatus, setApiKeyStatus] = useState('checking'); // 'checking', 'valid', 'invalid'\n  const [showHeader, setShowHeader] = useState(true);\n\n  // Check if API keys are configured\n  useEffect(() => {\n    const checkApiKeys = async () => {\n      // Check if Deepgram API key is configured\n      const hasDeepgramKey = env.hasDeepgramAPI();\n\n      // If no key is configured, use a sample key for development\n      if (!hasDeepgramKey && env.isDevelopment) {\n        // This is just for development purposes\n        env.DEEPGRAM_API_KEY = env.getSampleDeepgramKey();\n\n        // Even with the sample key, we'll set status to 'ready' rather than 'valid'\n        // The actual validation will happen when the user starts the transcription\n        setApiKeyStatus('ready');\n      } else if (hasDeepgramKey) {\n        // We have a key, but we'll validate it when the user starts the transcription\n        setApiKeyStatus('ready');\n      } else {\n        setApiKeyStatus('invalid');\n      }\n    };\n    checkApiKeys();\n  }, []);\n\n  // Handle back button click\n  const handleBack = () => {\n    setShowHeader(true);\n    navigate('/home');\n  };\n\n  // Handle start button click\n  const handleStart = () => {\n    setShowHeader(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"transcription-page\",\n    children: [showHeader && /*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 22\n    }, this), showHeader ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"transcription-intro\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Deepgram Live Transcription with Speaker Diarization\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"feature-badges\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"feature-badge\",\n          children: \"Automatic Speaker Detection\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"feature-badge highlight\",\n          children: \"Real-time Live Transcription\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"feature-badge\",\n          children: \"Voice-Based Identification\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"feature-badge\",\n          children: \"GPT Analysis Integration\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"intro-description\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"This feature uses Deepgram's API to automatically identify speakers and transcribe conversations in real-time. The advanced speaker diarization technology can distinguish between different speakers without manual switching.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Key Features:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Automatic Speaker Identification:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 72,\n              columnNumber: 19\n            }, this), \" Automatically detects and labels different speakers\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Real-time Transcription:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 19\n            }, this), \" See the transcript as people speak\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"GPT Analysis:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 19\n            }, this), \" Select any part of the transcript to get AI analysis\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Download Transcripts:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 19\n            }, this), \" Save your transcriptions for later reference\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 13\n        }, this), apiKeyStatus === 'checking' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"api-status checking\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Checking API configuration...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 15\n        }, this), apiKeyStatus === 'ready' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"api-status ready\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"API Key Found:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 19\n            }, this), \" Deepgram API key is configured. The key will be validated when you start the transcription.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 15\n        }, this), apiKeyStatus === 'invalid' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"api-status invalid\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"API Key Missing:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 19\n            }, this), \" To use this feature, you need to configure a Deepgram API key. Add your key to the environment variables as REACT_APP_DEEPGRAM_API_KEY.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"action-buttons\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"start-button\",\n          onClick: handleStart,\n          disabled: apiKeyStatus === 'checking' || apiKeyStatus === 'invalid',\n          children: \"Start Transcription\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"back-button\",\n          onClick: handleBack,\n          children: \"Back to Home\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(DeepgramLiveTranscript, {\n      onBack: handleBack\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 50,\n    columnNumber: 5\n  }, this);\n}\n_s(DeepgramTranscriptionPage, \"pyYZpVuU7nfGUOJCTOqKB2HWSCo=\", false, function () {\n  return [useNavigate];\n});\n_c = DeepgramTranscriptionPage;\nexport default DeepgramTranscriptionPage;\nvar _c;\n$RefreshReg$(_c, \"DeepgramTranscriptionPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "DeepgramLiveTranscript", "Header", "env", "jsxDEV", "_jsxDEV", "DeepgramTranscriptionPage", "_s", "navigate", "apiKeyStatus", "setApiKeyStatus", "showHeader", "setS<PERSON>Header", "checkApiKeys", "hasDeepgram<PERSON>ey", "hasDeepgramAPI", "isDevelopment", "DEEPGRAM_API_KEY", "getSampleDeepgramKey", "handleBack", "handleStart", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "onBack", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/interview-ai-app/interviewAI/client/src/pages/live-transcription/DeepgramTranscriptionPage.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport DeepgramLiveTranscript from '../../components/DeepgramLiveTranscript';\nimport Header from '../../components/Header';\nimport './TranscriptionPage.css';\nimport env from '../../utils/env';\n\nfunction DeepgramTranscriptionPage() {\n  const navigate = useNavigate();\n  const [apiKeyStatus, setApiKeyStatus] = useState('checking'); // 'checking', 'valid', 'invalid'\n  const [showHeader, setShowHeader] = useState(true);\n\n  // Check if API keys are configured\n  useEffect(() => {\n    const checkApiKeys = async () => {\n      // Check if Deepgram API key is configured\n      const hasDeepgramKey = env.hasDeepgramAPI();\n\n      // If no key is configured, use a sample key for development\n      if (!hasDeepgramKey && env.isDevelopment) {\n        // This is just for development purposes\n        env.DEEPGRAM_API_KEY = env.getSampleDeepgramKey();\n\n        // Even with the sample key, we'll set status to 'ready' rather than 'valid'\n        // The actual validation will happen when the user starts the transcription\n        setApiKeyStatus('ready');\n      } else if (hasDeepgramKey) {\n        // We have a key, but we'll validate it when the user starts the transcription\n        setApiKeyStatus('ready');\n      } else {\n        setApiKeyStatus('invalid');\n      }\n    };\n\n    checkApiKeys();\n  }, []);\n\n  // Handle back button click\n  const handleBack = () => {\n    setShowHeader(true);\n    navigate('/home');\n  };\n\n  // Handle start button click\n  const handleStart = () => {\n    setShowHeader(false);\n  };\n\n  return (\n    <div className=\"transcription-page\">\n      {showHeader && <Header />}\n\n      {showHeader ? (\n        <div className=\"transcription-intro\">\n          <h1>Deepgram Live Transcription with Speaker Diarization</h1>\n\n          <div className=\"feature-badges\">\n            <span className=\"feature-badge\">Automatic Speaker Detection</span>\n            <span className=\"feature-badge highlight\">Real-time Live Transcription</span>\n            <span className=\"feature-badge\">Voice-Based Identification</span>\n            <span className=\"feature-badge\">GPT Analysis Integration</span>\n          </div>\n\n          <div className=\"intro-description\">\n            <p>\n              This feature uses Deepgram's API to automatically identify speakers and transcribe conversations in real-time.\n              The advanced speaker diarization technology can distinguish between different speakers without manual switching.\n            </p>\n\n            <h3>Key Features:</h3>\n            <ul>\n              <li><strong>Automatic Speaker Identification:</strong> Automatically detects and labels different speakers</li>\n              <li><strong>Real-time Transcription:</strong> See the transcript as people speak</li>\n              <li><strong>GPT Analysis:</strong> Select any part of the transcript to get AI analysis</li>\n              <li><strong>Download Transcripts:</strong> Save your transcriptions for later reference</li>\n            </ul>\n\n            {apiKeyStatus === 'checking' && (\n              <div className=\"api-status checking\">\n                <p>Checking API configuration...</p>\n              </div>\n            )}\n\n            {apiKeyStatus === 'ready' && (\n              <div className=\"api-status ready\">\n                <p>\n                  <strong>API Key Found:</strong> Deepgram API key is configured. The key will be validated when you start the transcription.\n                </p>\n              </div>\n            )}\n\n            {apiKeyStatus === 'invalid' && (\n              <div className=\"api-status invalid\">\n                <p>\n                  <strong>API Key Missing:</strong> To use this feature, you need to configure a Deepgram API key.\n                  Add your key to the environment variables as REACT_APP_DEEPGRAM_API_KEY.\n                </p>\n              </div>\n            )}\n          </div>\n\n          <div className=\"action-buttons\">\n            <button\n              className=\"start-button\"\n              onClick={handleStart}\n              disabled={apiKeyStatus === 'checking' || apiKeyStatus === 'invalid'}\n            >\n              Start Transcription\n            </button>\n\n            <button\n              className=\"back-button\"\n              onClick={handleBack}\n            >\n              Back to Home\n            </button>\n          </div>\n        </div>\n      ) : (\n        <DeepgramLiveTranscript onBack={handleBack} />\n      )}\n    </div>\n  );\n}\n\nexport default DeepgramTranscriptionPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,sBAAsB,MAAM,yCAAyC;AAC5E,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAO,yBAAyB;AAChC,OAAOC,GAAG,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,SAASC,yBAAyBA,CAAA,EAAG;EAAAC,EAAA;EACnC,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACS,YAAY,EAAEC,eAAe,CAAC,GAAGZ,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC;EAC9D,MAAM,CAACa,UAAU,EAAEC,aAAa,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMc,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B;MACA,MAAMC,cAAc,GAAGX,GAAG,CAACY,cAAc,CAAC,CAAC;;MAE3C;MACA,IAAI,CAACD,cAAc,IAAIX,GAAG,CAACa,aAAa,EAAE;QACxC;QACAb,GAAG,CAACc,gBAAgB,GAAGd,GAAG,CAACe,oBAAoB,CAAC,CAAC;;QAEjD;QACA;QACAR,eAAe,CAAC,OAAO,CAAC;MAC1B,CAAC,MAAM,IAAII,cAAc,EAAE;QACzB;QACAJ,eAAe,CAAC,OAAO,CAAC;MAC1B,CAAC,MAAM;QACLA,eAAe,CAAC,SAAS,CAAC;MAC5B;IACF,CAAC;IAEDG,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMM,UAAU,GAAGA,CAAA,KAAM;IACvBP,aAAa,CAAC,IAAI,CAAC;IACnBJ,QAAQ,CAAC,OAAO,CAAC;EACnB,CAAC;;EAED;EACA,MAAMY,WAAW,GAAGA,CAAA,KAAM;IACxBR,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,oBACEP,OAAA;IAAKgB,SAAS,EAAC,oBAAoB;IAAAC,QAAA,GAChCX,UAAU,iBAAIN,OAAA,CAACH,MAAM;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAExBf,UAAU,gBACTN,OAAA;MAAKgB,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClCjB,OAAA;QAAAiB,QAAA,EAAI;MAAoD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAE7DrB,OAAA;QAAKgB,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BjB,OAAA;UAAMgB,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAA2B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAClErB,OAAA;UAAMgB,SAAS,EAAC,yBAAyB;UAAAC,QAAA,EAAC;QAA4B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC7ErB,OAAA;UAAMgB,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAA0B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjErB,OAAA;UAAMgB,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CAAC,eAENrB,OAAA;QAAKgB,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCjB,OAAA;UAAAiB,QAAA,EAAG;QAGH;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAEJrB,OAAA;UAAAiB,QAAA,EAAI;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtBrB,OAAA;UAAAiB,QAAA,gBACEjB,OAAA;YAAAiB,QAAA,gBAAIjB,OAAA;cAAAiB,QAAA,EAAQ;YAAiC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,wDAAoD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/GrB,OAAA;YAAAiB,QAAA,gBAAIjB,OAAA;cAAAiB,QAAA,EAAQ;YAAwB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,uCAAmC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrFrB,OAAA;YAAAiB,QAAA,gBAAIjB,OAAA;cAAAiB,QAAA,EAAQ;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,yDAAqD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5FrB,OAAA;YAAAiB,QAAA,gBAAIjB,OAAA;cAAAiB,QAAA,EAAQ;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,iDAA6C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1F,CAAC,EAEJjB,YAAY,KAAK,UAAU,iBAC1BJ,OAAA;UAAKgB,SAAS,EAAC,qBAAqB;UAAAC,QAAA,eAClCjB,OAAA;YAAAiB,QAAA,EAAG;UAA6B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CACN,EAEAjB,YAAY,KAAK,OAAO,iBACvBJ,OAAA;UAAKgB,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/BjB,OAAA;YAAAiB,QAAA,gBACEjB,OAAA;cAAAiB,QAAA,EAAQ;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,gGACjC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACN,EAEAjB,YAAY,KAAK,SAAS,iBACzBJ,OAAA;UAAKgB,SAAS,EAAC,oBAAoB;UAAAC,QAAA,eACjCjB,OAAA;YAAAiB,QAAA,gBACEjB,OAAA;cAAAiB,QAAA,EAAQ;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,4IAEnC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENrB,OAAA;QAAKgB,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BjB,OAAA;UACEgB,SAAS,EAAC,cAAc;UACxBM,OAAO,EAAEP,WAAY;UACrBQ,QAAQ,EAAEnB,YAAY,KAAK,UAAU,IAAIA,YAAY,KAAK,SAAU;UAAAa,QAAA,EACrE;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETrB,OAAA;UACEgB,SAAS,EAAC,aAAa;UACvBM,OAAO,EAAER,UAAW;UAAAG,QAAA,EACrB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,gBAENrB,OAAA,CAACJ,sBAAsB;MAAC4B,MAAM,EAAEV;IAAW;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAC9C;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACnB,EAAA,CApHQD,yBAAyB;EAAA,QACfN,WAAW;AAAA;AAAA8B,EAAA,GADrBxB,yBAAyB;AAsHlC,eAAeA,yBAAyB;AAAC,IAAAwB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}